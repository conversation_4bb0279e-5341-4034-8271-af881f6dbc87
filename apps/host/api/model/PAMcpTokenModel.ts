import { Database, getModelForClass, modelOptions, Prop, Severity } from '@gulux/gulux/typegoose';
import { McpToken } from '@shared/mcp/mcp-info';

/**
 * MCP Token 模型，用于存储用户的 MCP Token
 * 实现 McpToken 接口
 */
@Database('main')
@modelOptions({ options: { customName: 'PAMcpToken', allowMixed: Severity.ALLOW } })
export class PAMcpTokenTable implements McpToken {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  token: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const PAMcpTokenModel = getModelForClass(PAMcpTokenTable);
