import { Inject, Injectable } from '@gulux/gulux';
import { ModelType } from '@gulux/gulux/typegoose';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { PAMcpTokenTable } from '../model/PAMcpTokenModel';
import { McpToken } from '@shared/mcp/mcp-info';

/**
 * MCP Token 数据访问对象，提供对 PAMcpToken 表的操作
 */
@Injectable()
export default class McpTokenDao {
  @Inject(PAMcpTokenTable)
  private paMcpTokenModel: ModelType<PAMcpTokenTable>;

  @Inject()
  logger: BytedLogger;

  /**
   * 创建或更新 token
   * @param email 用户邮箱
   * @param token 加密后的 token
   * @returns 创建或更新后的 token 记录
   */
  async createOrUpdateToken(email: string, token: string) {
    const query = { email };
    const update: Partial<McpToken> = {
      email,
      token,
      updatedAt: new Date(),
    };
    const options = { upsert: true, new: true };

    const result = await this.paMcpTokenModel.findOneAndUpdate(query, update, options);

    if (!result) {
      this.logger.error(`创建或更新 token 失败: ${email}`);
      return;
    }

    return {
      email: result.email,
      token: result.token,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };
  }

  /**
   * 根据邮箱查询 token 记录
   * @param email 用户邮箱
   * @returns token 记录，如果不存在则返回 null
   */
  async findByEmail(email: string): Promise<McpToken | null> {
    try {
      const tokenRecord = await this.paMcpTokenModel.findOne({ email }).exec();
      if (!tokenRecord) {
        return null;
      }

      return {
        email: tokenRecord.email,
        token: tokenRecord.token,
        createdAt: tokenRecord.createdAt,
        updatedAt: tokenRecord.updatedAt,
      };
    } catch (error) {
      this.logger.error(`查询 token 失败: ${JSON.stringify(error)}`);
      return null;
    }
  }

  /**
   * 删除 token
   * @param email 用户邮箱
   * @returns 是否删除成功
   */
  async deleteToken(email: string) {
    try {
      const result = await this.paMcpTokenModel.deleteOne({ email }).exec();
    } catch (error) {
      this.logger.error(`删除 token 失败: ${JSON.stringify(error)}`);
      return false;
    }
  }

  /**
   * 获取所有 token 记录
   * @returns token 记录列表
   */
  async findAll(): Promise<McpToken[]> {
    try {
      const records = await this.paMcpTokenModel.find().exec();
      return records.map(record => ({
        email: record.email,
        token: record.token,
        createdAt: record.createdAt,
        updatedAt: record.updatedAt,
      }));
    } catch (error) {
      this.logger.error(`获取所有 token 失败: ${JSON.stringify(error)}`);
      return [];
    }
  }
}
