import { Api, Data, Post, useInject } from '@edenx/runtime/bff';
import { z } from 'zod';
import { TBCCodeSyncService } from '../service/tbc/TBCCodeSyncService';
import { TBCMrSyncService } from '../service/tbc/TBCMrSyncService';
import { TBCTTPCodeFrozenService } from '../service/tbc/TBCTTPCodeFrozenService';
import { TBCAdCodeSyncService } from '../service/tbc/TBCAdCodeSyncService';
export const tbcCheckIfValidForInProcessAdCodeSyncMr = Api(
  Post('/tbc/test_close'),
  Data(z.object({})),
  async ({ data }) => {
    const service = useInject(TBCAdCodeSyncService);
    return await service.checkIfValidForInProcessAdCodeSyncMr();
  },
);
export const test = Api(Post('/tbc/test'), Data(z.object({})), async ({ data }) => {
  const service = useInject(TBCAdCodeSyncService);
  return await service.syncAdBranches();
});
export const tbcSyncAdBranches = Api(Post('/tbc/sync_ad_branches'), Data(z.object({})), async ({ data }) => {
  const service = useInject(TBCAdCodeSyncService);
  return await service.syncAdBranches();
});
export const getAdCodeSyncRecords = Api(Post('/tbc/ad-code-sync/records'), Data(z.object({})), async () => {
  const service = useInject(TBCAdCodeSyncService);
  return await service.getRecords();
});

export const syncAdCodeBranchAndCreateMr = Api(
  Post('/tbc/ad-code-sync/sync'),
  Data(
    z.object({
      projectId: z.number(),
      sourceBranch: z.string(),
      targetBranch: z.string(),
      userName: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(TBCAdCodeSyncService);
    return await service.syncBranchAndCreateMr(
      data.projectId,
      data.sourceBranch,
      data.targetBranch,
      data.userName,
      true,
      false,
    );
  },
);

export const tbcCodeSyncNotify = Api(
  Post('/tbc/code_sync_notify'),
  Data(
    z.object({
      jobId: z.number(),
      retCode: z.number(),
      timestamp: z.number(),
      appName: z.string(),
      syncBranch: z.string(),
      message: z.string().optional(),
      commitHash: z.string().optional(),
      commitHashUrl: z.string().optional(),
      commitMessage: z.string().optional(),
      sendPrivate: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(TBCCodeSyncService);
    return await service.notifyResult(data);
  },
);

export const tbcFetchMrList = Api(
  Post('/tbc/get_mr_list'),
  Data(
    z.object({
      group_name: z.string(),
      state: z.string().optional(),
      author_name: z.string().optional(),
      wip: z.number().optional(),
      conflicted: z.number().optional(),
      target_project_id: z.number().optional(),
      target_branch: z.string().optional(),
      source_branch: z.string().optional(),
      mr_type: z.string().optional(),
      last_id: z.number().optional(),
      keyword: z.string().optional(),
      reviewer_name: z.string().optional(),
      source: z.string().optional(),
      product_version: z.string().optional(),
      review_state: z.string().optional(),
      created_time: z.string().optional(),
      merged_time: z.string().optional(),
      sort: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(TBCMrSyncService);
    return await service.fetchMrList(data);
  },
);

export const tbcBranchSync = Api(
  Post('/tbc/branch_sync'),
  Data(
    z.object({
      projectId: z.number(),
      sourceBranch: z.string(),
      targetBranch: z.string(),
      userName: z.string(),
      raiseReview: z.boolean(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(TBCCodeSyncService);
    return await service.syncBranch(
      data.projectId,
      data.sourceBranch,
      data.targetBranch,
      data.userName,
      data.raiseReview,
    );
  },
);

export const tbcCheckIfValidForInProcessBranchSyncMr = Api(
  Post('/tbc/check_if_valid_for_in_process_branch_sync_mr'),
  Data(z.object({})),
  async ({ data }) => {
    const service = useInject(TBCCodeSyncService);
    return await service.checkIfValidForInProcessBranchSyncMr();
  },
);

// export const tbcCheckIfValidForInProcessAdCodeSyncMr = Api(
//   Post('/tbc/check_if_valid_for_in_process_ad_code_sync_mr'),
//   Data(z.object({})),
//   async ({ data }) => {
//     const service = useInject(TBCAdCodeSyncService);
//     return await service.checkIfValidForInProcessAdCodeSyncMr();
//   },
// );
//
// export const tbcSyncAdBranches = Api(
//   Post('/tbc/sync_ad_branches'),
//   Data(z.object({})),
//   async ({ data }) => {
//     const service = useInject(TBCAdCodeSyncService);
//     return await service.syncAdBranches();
//   },
// );

// @FIXME 这个目前只是本地 Debug 情况下手动触发，请勿使用（里面有 hardcode 逻辑）
export const fetchTTPOpenedMrList = Api(Post('/tbc/fetch_ttp_opened_mr_list'), Data(z.object({})), async ({ data }) => {
  const service = useInject(TBCTTPCodeFrozenService);
  return await service.fetchOpenedMrListAndGroupBy();
});

// @FIXME 这个目前只是本地 Debug 情况下手动触发，请勿使用（里面有 hardcode 逻辑）
export const createTTPReleaseBranchAndMR = Api(
  Post('/tbc/create_ttp_release_branch_and_mr'),
  Data(
    z.object({
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(TBCTTPCodeFrozenService);
    return await service.createTTPReleaseBranchAndMR(data.version);
  },
);

// @FIXME 这个目前只是本地 Debug 情况下手动触发，请勿使用（里面有 hardcode 逻辑）
export const ttpCodeFrozenFinishedNotify = Api(
  Post('/tbc/ttp_code_frozen_finished_notify'),
  Data(z.object({})),
  async ({ data }) => {
    const service = useInject(TBCTTPCodeFrozenService);
    return await service.ttpCodeFrozenFinishedNotify();
  },
);

// @FIXME 这个目前只是本地 Debug 情况下手动触发，请勿使用（里面有 hardcode 逻辑）
export const tbcCreateMr = Api(Post('/tbc/create_mr'), Data(z.object({})), async ({ data }) => {
  const service = useInject(TBCCodeSyncService);
  return await service.tbcCreateMr();
});
