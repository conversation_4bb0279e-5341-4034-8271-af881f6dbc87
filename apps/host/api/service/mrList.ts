import { Inject, Injectable } from '@gulux/gulux';
import { PlatformType } from '@pa/shared/dist/src/core';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import BitsService from './third/bits';
import { dayjs } from '@pa/shared/dist/src/utils/dayjs';
import versionUtils from '../utils/versionUtils';
import { add_suffix_ne, wait } from '@pa/shared/dist/src/utils/tools';
import meegoUtils from '../utils/meegoUtils';
import { FieldValue } from '@shared/meego/MeegoCommon';
import VersionProcessInfoDao from './dao/releasePlatform/VersionProcessInfoDao';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LarkService from '@pa/backend/dist/src/third/lark';
import MeegoService from './third/meego';
import VersionFeatureService from './releasePlatform/versionFeatureService';
import { PaAlarmService } from '@pa/backend/dist/src/utils/alarm';
import { ReleasePlatformCheckoutRecordDao } from './dao/releasePlatform/ReleasePlatformCheckoutRecord';
import { SpaceType } from '@shared/bus/busType';
import { isOverseas } from '@shared/utils/VersionCommonUtils';
import { MrRelationInfo } from '@shared/bits/mrRelationInfo';
import appRepos from '@shared/gitlab/appRepos';

@Injectable()
export default class MrListService {
  @Inject()
  private logger: BytedLogger;

  @Inject()
  private bits: BitsService;

  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;

  @Inject()
  private lark: LarkService;

  @Inject()
  private meego: MeegoService;

  @Inject()
  private versionFeatureService: VersionFeatureService;

  @Inject()
  private alarmService: PaAlarmService;

  @Inject()
  private releasePlatformCheckoutRecordDao: ReleasePlatformCheckoutRecordDao;

  private versionStringToNumber(version: string): number {
    return parseInt(version.split('.').join(''), 10);
  }

  async getMrAfterSystemTestImpl(version: string, platform: PlatformType, beforeGray = false) {
    const lvVersion = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: platform === PlatformType.Android ? AppSettingId.LV_ANDROID : AppSettingId.LV_IOS,
      version,
    });
    const lvPre = lvVersion?.version_stages?.find(v => v.display_name === '灰度')?.start_time;
    const lvIntegration = lvVersion?.version_stages?.find(v => v.display_name === '封版')?.start_time;
    const mrList = await Promise.all(
      ['LV-Android', 'LV-iOS', 'LV-Windows']
        .flatMap(space =>
          [`release/${version}`, `overseas/release/${versionUtils.lv2ccVersion(version)}`].map(branch => ({
            space,
            branch,
          })),
        )
        .map(({ space, branch }) =>
          this.bits.searchAllMr({
            group_name: space,
            state: 'merged',
            target_branch: branch,
            mr_type: 'bug',
            merged_time: beforeGray
              ? JSON.stringify({
                  gte: lvIntegration,
                  lte: lvPre,
                })
              : JSON.stringify({
                  gte: lvPre,
                  lte: dayjs().unix(),
                }),
          }),
        ),
    );
    // 1640后分仓
    const versionNumber = this.versionStringToNumber(version);
    if (versionNumber >= 1640) {
      const mrListCC = await Promise.all(
        [SpaceType.TTP_Android, SpaceType.TTP_iOS]
          .flatMap(space =>
            [`release/${version}`, `overseas/release/${versionUtils.lv2ccVersion(version)}`].map(branch => ({
              space,
              branch,
            })),
          )
          .map(({ space, branch }) =>
            this.bits.searchAllMr({
              group_name: space,
              state: 'merged',
              target_branch: branch,
              mr_type: 'bug',
              merged_time: beforeGray
                ? JSON.stringify({
                    gte: lvIntegration,
                    lte: lvPre,
                  })
                : JSON.stringify({
                    gte: lvPre,
                    lte: dayjs().unix(),
                  }),
            }),
          ),
      );
      mrList.push(...mrListCC);
    }

    const mrs = Array.from(new Set(mrList.flat()));
    const grayStage = lvVersion?.version_stages?.find(s => s.display_name === '灰度');
    const ccVersion = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: platform === PlatformType.Android ? AppSettingId.CC_ANDROID : AppSettingId.CC_IOS,
      version: versionUtils.lv2ccVersion(version),
    });
    const ccGrayStage = ccVersion?.version_stages?.find(s => s.display_name === '灰度');
    const mrInfos: any[] = [];
    const overseaCheckoutTime: number | undefined = (
      await this.releasePlatformCheckoutRecordDao.queryRecord(1775, version)
    )?.create_time;
    for (const id of mrs) {
      const mrInfo = await this.bits.getMrInfo({ mrId: id });
      if (!mrInfo) {
        continue;
      }
      const realMrs = await this.bits.getMrRealLvPlatform(mrInfo);
      if (!realMrs.has(platform)) {
        // 没有当前平台的子仓MR
        continue;
      }
      const meegoResult = await this.bits.getBindMeegoTaskInfo({
        mr_id: id,
      });
      const user = await this.lark.getUserIdByEmail(add_suffix_ne('@bytedance.com')(mrInfo?.author ?? ''));
      const department = await this.lark.getUserDepartment(user?.open_id ?? '').catch(() => '未知');
      const lvGrayNum =
        grayStage?.sub_stages?.find(
          s =>
            s.extra_data &&
            mrInfo &&
            (s.extra_data.releaseTime === -1 ? s.extra_data.packageTime : s.extra_data.releaseTime) >
              mrInfo?.merged_time,
        )?.display_name ?? '未知轮次';
      const ccGrayNum =
        ccGrayStage?.sub_stages?.find(
          s =>
            s.extra_data &&
            mrInfo &&
            (s.extra_data.releaseTime === -1 ? s.extra_data.packageTime : s.extra_data.releaseTime) >
              mrInfo?.merged_time,
        )?.display_name ?? '未知轮次';
      const mrMeego = meegoResult.find(v => v.task_type === 'issue');
      if (mrMeego) {
        await wait(100);
      }
      const meegoInfo = mrMeego
        ? await this.meego.requestTaskInfo(mrMeego.platform_project_name, mrMeego.task_type, {
            work_item_ids: [parseInt(mrMeego.task_id)],
            fields: [
              'priority',
              'field_df36d9',
              'field_828485',
              'field_62420e',
              'business',
              'bug_channel',
              'field_fbc20d',
              'issue_stage',
              'field_3d9501',
            ],
          })
        : null;
      const meegoItem = meegoInfo?.data[0];
      if (mrMeego && !meegoItem) {
        this.alarmService.reportError(
          `Meego信息获取失败 res:${JSON.stringify(meegoInfo)} req:${JSON.stringify(mrMeego)}`,
        );
      }
      const priorityValue = meegoItem?.fields.find(v => v.field_key === 'priority');
      const categoryValue = meegoItem?.fields.find(v => v.field_key === 'field_df36d9');
      const moduleName = categoryValue ? meegoUtils.getIssueModelName(categoryValue) : 'Unknown';
      const preDiscover = (meegoItem?.fields.find(v => v.field_key === 'field_828485')?.field_value as FieldValue)
        ?.label;
      const cantDiscoverReason = (
        meegoItem?.fields.find(v => v.field_key === 'field_62420e')?.field_value as FieldValue
      )?.label;
      const bugFindMethod = (meegoItem?.fields.find(v => v.field_key === 'bug_channel')?.field_value as FieldValue)
        ?.label;
      const bugStageIntroduction = (
        meegoItem?.fields.find(v => v.field_key === 'field_fbc20d')?.field_value as FieldValue
      )?.label;
      const bugStageFound = (meegoItem?.fields.find(v => v.field_key === 'issue_stage')?.field_value as FieldValue)
        ?.label;
      const bugType = (meegoItem?.fields.find(v => v.field_key === 'field_3d9501')?.field_value as FieldValue)?.label;

      const businessField = meegoItem?.fields?.find(value => value.field_key === 'business');
      const business_line_value = businessField?.field_value;
      let business = '';
      if (typeof business_line_value === 'string') {
        const result = this.versionFeatureService.getMeegoBusinessLineLabel(business_line_value);
        business = result.join('/');
      }
      mrInfos.push({
        mr_id: id,
        Bug标题: mrMeego?.task_title ?? '',
        Meego链接: mrMeego?.task_url ?? '',
        Meego业务线: business,
        Bug优先级: (priorityValue?.field_value as FieldValue)?.label ?? '',
        缺陷关联模块: moduleName,
        是否稳定性: moduleName.includes('Slardar稳定性') || moduleName.includes('基础性能自动化'),
        MR标题: mrInfo?.title,
        MR链接: mrInfo?.mr_detail_url,
        RD: mrInfo?.author,
        合入时间: dayjs
          .unix(mrInfo?.merged_time ?? 0)
          .utcOffset(8)
          .format(),
        合入目标分支: mrInfo?.target_branch,
        Bug提交时间: meegoItem
          ? dayjs
              .unix(meegoItem.created_at / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
        MR提交时间: dayjs
          .unix(mrInfo?.create_time ?? 0)
          .utcOffset(8)
          .format(),
        是否可前置发现: preDiscover,
        不可前置发现原因: cantDiscoverReason,
        MR提交人业务线: department,
        合入的剪映灰度轮次: lvGrayNum,
        合入的CapCut灰度轮次: ccGrayNum,
        BUG发现方式: bugFindMethod,
        引入阶段: bugStageIntroduction,
        发现阶段: bugStageFound,
        缺陷分类: bugType,
        拉出海外分支时间: overseaCheckoutTime
          ? dayjs
              .unix(overseaCheckoutTime / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
      });
    }
    mrInfos.sort((a, b) => Number(new Date(a['合入时间'].toString())) - Number(new Date(b['合入时间'].toString())));
    return mrInfos;
  }
  // 剪映
  async getMrAfterSystemTestImplOfLV(version: string, platform: PlatformType, beforeGray = false) {
    const lvVersion = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: platform === PlatformType.Android ? AppSettingId.LV_ANDROID : AppSettingId.LV_IOS,
      version,
    });
    const lvPre = lvVersion?.version_stages?.find(v => v.display_name === '灰度')?.start_time;
    const lvIntegration = lvVersion?.version_stages?.find(v => v.display_name === '封版')?.start_time;
    const mrList = await Promise.all(
      ['LV-Android', 'LV-iOS', 'LV-Windows']
        .flatMap(space =>
          [`release/${version}`, `overseas/release/${versionUtils.lv2ccVersion(version)}`].map(branch => ({
            space,
            branch,
          })),
        )
        .map(({ space, branch }) =>
          this.bits.searchAllMr({
            group_name: space,
            state: 'merged',
            target_branch: branch,
            mr_type: 'bug',
            merged_time: beforeGray
              ? JSON.stringify({
                  gte: lvIntegration,
                  lte: lvPre,
                })
              : JSON.stringify({
                  gte: lvPre,
                  lte: dayjs().unix(),
                }),
          }),
        ),
    );
    const mrs = Array.from(new Set(mrList.flat()));
    const grayStage = lvVersion?.version_stages?.find(s => s.display_name === '灰度');
    const ccVersion = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: platform === PlatformType.Android ? AppSettingId.CC_ANDROID : AppSettingId.CC_IOS,
      version: versionUtils.lv2ccVersion(version),
    });
    const ccGrayStage = ccVersion?.version_stages?.find(s => s.display_name === '灰度');
    const mrInfos: any[] = [];
    const overseaCheckoutTime: number | undefined = (
      await this.releasePlatformCheckoutRecordDao.queryRecord(1775, version)
    )?.create_time;
    for (const id of mrs) {
      const mrInfo = await this.bits.getMrInfo({ mrId: id });
      if (!mrInfo) {
        continue;
      }
      const realMrs = await this.bits.getMrRealLvPlatform(mrInfo);
      if (!realMrs.has(platform)) {
        // 没有当前平台的子仓MR
        continue;
      }
      const meegoResult = await this.bits.getBindMeegoTaskInfo({
        mr_id: id,
      });
      const user = await this.lark.getUserIdByEmail(add_suffix_ne('@bytedance.com')(mrInfo?.author ?? ''));
      const department = await this.lark.getUserDepartment(user?.open_id ?? '').catch(() => '未知');
      const lvGrayNum =
        grayStage?.sub_stages?.find(
          s =>
            s.extra_data &&
            mrInfo &&
            (s.extra_data.releaseTime === -1 ? s.extra_data.packageTime : s.extra_data.releaseTime) >
              mrInfo?.merged_time,
        )?.display_name ?? '未知轮次';
      const ccGrayNum =
        ccGrayStage?.sub_stages?.find(
          s =>
            s.extra_data &&
            mrInfo &&
            (s.extra_data.releaseTime === -1 ? s.extra_data.packageTime : s.extra_data.releaseTime) >
              mrInfo?.merged_time,
        )?.display_name ?? '未知轮次';
      const mrMeego = meegoResult.find(v => v.task_type === 'issue');
      if (mrMeego) {
        await wait(100);
      }
      const meegoInfo = mrMeego
        ? await this.meego.requestTaskInfo(mrMeego.platform_project_name, mrMeego.task_type, {
            work_item_ids: [parseInt(mrMeego.task_id)],
            fields: [
              'priority',
              'field_df36d9',
              'field_828485',
              'field_62420e',
              'business',
              'bug_channel',
              'field_fbc20d',
              'issue_stage',
              'field_3d9501',
            ],
          })
        : null;
      const meegoItem = meegoInfo?.data[0];
      if (mrMeego && !meegoItem) {
        this.alarmService.reportError(
          `Meego信息获取失败 res:${JSON.stringify(meegoInfo)} req:${JSON.stringify(mrMeego)}`,
        );
      }
      const priorityValue = meegoItem?.fields.find(v => v.field_key === 'priority');
      const categoryValue = meegoItem?.fields.find(v => v.field_key === 'field_df36d9');
      const moduleName = categoryValue ? meegoUtils.getIssueModelName(categoryValue) : 'Unknown';
      const preDiscover = (meegoItem?.fields.find(v => v.field_key === 'field_828485')?.field_value as FieldValue)
        ?.label;
      const cantDiscoverReason = (
        meegoItem?.fields.find(v => v.field_key === 'field_62420e')?.field_value as FieldValue
      )?.label;
      const bugFindMethod = (meegoItem?.fields.find(v => v.field_key === 'bug_channel')?.field_value as FieldValue)
        ?.label;
      const bugStageIntroduction = (
        meegoItem?.fields.find(v => v.field_key === 'field_fbc20d')?.field_value as FieldValue
      )?.label;
      const bugStageFound = (meegoItem?.fields.find(v => v.field_key === 'issue_stage')?.field_value as FieldValue)
        ?.label;
      const bugType = (meegoItem?.fields.find(v => v.field_key === 'field_3d9501')?.field_value as FieldValue)?.label;

      const businessField = meegoItem?.fields?.find(value => value.field_key === 'business');
      const business_line_value = businessField?.field_value;
      let business = '';
      if (typeof business_line_value === 'string') {
        const result = this.versionFeatureService.getMeegoBusinessLineLabel(business_line_value);
        business = result.join('/');
      }
      mrInfos.push({
        mr_id: id,
        Bug标题: mrMeego?.task_title ?? '',
        Meego链接: mrMeego?.task_url ?? '',
        Meego业务线: business,
        Bug优先级: (priorityValue?.field_value as FieldValue)?.label ?? '',
        缺陷关联模块: moduleName,
        是否稳定性: moduleName.includes('Slardar稳定性') || moduleName.includes('基础性能自动化'),
        MR标题: mrInfo?.title,
        MR链接: mrInfo?.mr_detail_url,
        RD: mrInfo?.author,
        合入时间: dayjs
          .unix(mrInfo?.merged_time ?? 0)
          .utcOffset(8)
          .format(),
        合入目标分支: mrInfo?.target_branch,
        Bug提交时间: meegoItem
          ? dayjs
              .unix(meegoItem.created_at / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
        MR提交时间: dayjs
          .unix(mrInfo?.create_time ?? 0)
          .utcOffset(8)
          .format(),
        是否可前置发现: preDiscover,
        不可前置发现原因: cantDiscoverReason,
        MR提交人业务线: department,
        合入的剪映灰度轮次: lvGrayNum,
        合入的CapCut灰度轮次: ccGrayNum,
        BUG发现方式: bugFindMethod,
        引入阶段: bugStageIntroduction,
        发现阶段: bugStageFound,
        缺陷分类: bugType,
        拉出海外分支时间: overseaCheckoutTime
          ? dayjs
              .unix(overseaCheckoutTime / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
      });
    }
    mrInfos.sort((a, b) => Number(new Date(a['合入时间'].toString())) - Number(new Date(b['合入时间'].toString())));
    return mrInfos;
  }
  async getMrAfterSystemTestImplOfCC(version: string, platform: PlatformType, beforeGray = false) {
    const lvVersion = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: platform === PlatformType.Android ? AppSettingId.LV_ANDROID : AppSettingId.LV_IOS,
      version,
    });
    const ccVersion = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: platform === PlatformType.Android ? AppSettingId.CC_ANDROID : AppSettingId.CC_IOS,
      version,
    });
    const lvPre = ccVersion?.version_stages?.find(v => v.display_name === '灰度')?.start_time;
    const lvIntegration = ccVersion?.version_stages?.find(v => v.display_name === '封版')?.start_time;
    const mrList = await Promise.all(
      [SpaceType.TTP_Android, SpaceType.TTP_iOS]
        .flatMap(space =>
          [`overseas/release/${version}`].map(branch => ({
            space,
            branch,
          })),
        )
        .map(({ space, branch }) =>
          this.bits.searchAllMr({
            group_name: space,
            state: 'merged',
            target_branch: branch,
            mr_type: 'bug',
            merged_time: beforeGray
              ? JSON.stringify({
                  gte: lvIntegration,
                  lte: lvPre,
                })
              : JSON.stringify({
                  gte: lvPre,
                  lte: dayjs().unix(),
                }),
          }),
        ),
    );

    const mrs = Array.from(new Set(mrList.flat()));
    const grayStage = lvVersion?.version_stages?.find(s => s.display_name === '灰度');

    const ccGrayStage = ccVersion?.version_stages?.find(s => s.display_name === '灰度');
    const mrInfos: any[] = [];
    const overseaCheckoutTime: number | undefined = (
      await this.releasePlatformCheckoutRecordDao.queryRecord(1775, version)
    )?.create_time;
    for (const id of mrs) {
      const mrInfo = await this.bits.getMrInfo({ mrId: id });
      if (!mrInfo) {
        continue;
      }
      const realMrs = await this.bits.getMrRealLvPlatform(mrInfo);
      if (!realMrs.has(platform)) {
        // 没有当前平台的子仓MR
        continue;
      }
      const meegoResult = await this.bits.getBindMeegoTaskInfo({
        mr_id: id,
      });
      const user = await this.lark.getUserIdByEmail(add_suffix_ne('@bytedance.com')(mrInfo?.author ?? ''));
      const department = await this.lark.getUserDepartment(user?.open_id ?? '').catch(() => '未知');
      const lvGrayNum =
        grayStage?.sub_stages?.find(
          s =>
            s.extra_data &&
            mrInfo &&
            (s.extra_data.releaseTime === -1 ? s.extra_data.packageTime : s.extra_data.releaseTime) >
              mrInfo?.merged_time,
        )?.display_name ?? '未知轮次';
      const ccGrayNum =
        ccGrayStage?.sub_stages?.find(
          s =>
            s.extra_data &&
            mrInfo &&
            (s.extra_data.releaseTime === -1 ? s.extra_data.packageTime : s.extra_data.releaseTime) >
              mrInfo?.merged_time,
        )?.display_name ?? '未知轮次';
      const mrMeego = meegoResult.find(v => v.task_type === 'issue');
      if (mrMeego) {
        await wait(100);
      }
      const meegoInfo = mrMeego
        ? await this.meego.requestTaskInfo(mrMeego.platform_project_name, mrMeego.task_type, {
            work_item_ids: [parseInt(mrMeego.task_id)],
            fields: [
              'priority',
              'field_df36d9',
              'field_828485',
              'field_62420e',
              'business',
              'bug_channel',
              'field_fbc20d',
              'issue_stage',
              'field_3d9501',
            ],
          })
        : null;
      const meegoItem = meegoInfo?.data[0];
      if (mrMeego && !meegoItem) {
        this.alarmService.reportError(
          `Meego信息获取失败 res:${JSON.stringify(meegoInfo)} req:${JSON.stringify(mrMeego)}`,
        );
      }
      const priorityValue = meegoItem?.fields.find(v => v.field_key === 'priority');
      const categoryValue = meegoItem?.fields.find(v => v.field_key === 'field_df36d9');
      const moduleName = categoryValue ? meegoUtils.getIssueModelName(categoryValue) : 'Unknown';
      const preDiscover = (meegoItem?.fields.find(v => v.field_key === 'field_828485')?.field_value as FieldValue)
        ?.label;
      const cantDiscoverReason = (
        meegoItem?.fields.find(v => v.field_key === 'field_62420e')?.field_value as FieldValue
      )?.label;
      const bugFindMethod = (meegoItem?.fields.find(v => v.field_key === 'bug_channel')?.field_value as FieldValue)
        ?.label;
      const bugStageIntroduction = (
        meegoItem?.fields.find(v => v.field_key === 'field_fbc20d')?.field_value as FieldValue
      )?.label;
      const bugStageFound = (meegoItem?.fields.find(v => v.field_key === 'issue_stage')?.field_value as FieldValue)
        ?.label;
      const bugType = (meegoItem?.fields.find(v => v.field_key === 'field_3d9501')?.field_value as FieldValue)?.label;

      const businessField = meegoItem?.fields?.find(value => value.field_key === 'business');
      const business_line_value = businessField?.field_value;
      let business = '';
      if (typeof business_line_value === 'string') {
        const result = this.versionFeatureService.getMeegoBusinessLineLabel(business_line_value);
        business = result.join('/');
      }
      mrInfos.push({
        mr_id: id,
        Bug标题: mrMeego?.task_title ?? '',
        Meego链接: mrMeego?.task_url ?? '',
        Meego业务线: business,
        Bug优先级: (priorityValue?.field_value as FieldValue)?.label ?? '',
        缺陷关联模块: moduleName,
        是否稳定性: moduleName.includes('Slardar稳定性') || moduleName.includes('基础性能自动化'),
        MR标题: mrInfo?.title,
        MR链接: mrInfo?.mr_detail_url,
        RD: mrInfo?.author,
        合入时间: dayjs
          .unix(mrInfo?.merged_time ?? 0)
          .utcOffset(8)
          .format(),
        合入目标分支: mrInfo?.target_branch,
        Bug提交时间: meegoItem
          ? dayjs
              .unix(meegoItem.created_at / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
        MR提交时间: dayjs
          .unix(mrInfo?.create_time ?? 0)
          .utcOffset(8)
          .format(),
        是否可前置发现: preDiscover,
        不可前置发现原因: cantDiscoverReason,
        MR提交人业务线: department,
        合入的剪映灰度轮次: lvGrayNum,
        合入的CapCut灰度轮次: ccGrayNum,
        BUG发现方式: bugFindMethod,
        引入阶段: bugStageIntroduction,
        发现阶段: bugStageFound,
        缺陷分类: bugType,
        拉出海外分支时间: overseaCheckoutTime
          ? dayjs
              .unix(overseaCheckoutTime / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
      });
    }
    mrInfos.sort((a, b) => Number(new Date(a['合入时间'].toString())) - Number(new Date(b['合入时间'].toString())));
    return mrInfos;
  }

  async getMrAfterSystemTestImplV2(appId: number, version: string) {
    const appInfo = versionUtils.appIdToAppInfo(appId.toString());
    if (!appInfo) {
      return [];
    }
    const versionInfo = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: appId,
      version,
    });
    const grayStageInfo = versionInfo?.version_stages?.find(
      v => v.stage_name.includes('gray') || v.stage_name.includes('testFlight') || v.stage_name.includes('testflight'),
    );
    const grayInfoList = grayStageInfo?.sub_stages
      ?.map(v => v.extra_data)
      ?.filter(it => it?.releaseTime && it?.releaseTime !== -1)
      ?.sort((a, b) => a.releaseTime - b.releaseTime);
    const integrationStageInfo = versionInfo?.version_stages?.find(v => v.stage_name.endsWith('integration'));
    const userStoryStageInfo = versionInfo?.version_stages?.find(
      v => v.stage_name.endsWith('user_story') || v.stage_name.endsWith('full_release_precheck'),
    );

    const getBranch = () => (isOverseas(appId) ? `overseas/release/${version}` : `release/${version}`);

    const getGroupName = (_platform: PlatformType) => {
      switch (_platform) {
        case PlatformType.Android:
          return isOverseas(appId) ? SpaceType.TTP_Android : SpaceType.Android;
        case PlatformType.iOS:
          return isOverseas(appId) ? SpaceType.TTP_iOS : SpaceType.iOS;
        default:
          return SpaceType.Android;
      }
    };

    const getProjectId = (_appId: number) => {
      switch (_appId) {
        case AppSettingId.LV_IOS:
          return appRepos.find(it => it.appId === AppSettingId.LV_IOS)?.projectId;
        case AppSettingId.LV_ANDROID:
          return appRepos.find(it => it.appId === AppSettingId.LV_ANDROID)?.projectId;
        case AppSettingId.CC_IOS:
          return appRepos.find(it => it.appId === AppSettingId.CC_IOS)?.projectId;
        case AppSettingId.CC_ANDROID:
          return appRepos.find(it => it.appId === AppSettingId.CC_ANDROID)?.projectId;
        default:
          return 39995;
      }
    };

    const mrList = new Map<PlatformType, number[]>();
    for (const _platform of [PlatformType.Android, PlatformType.iOS]) {
      mrList.set(_platform, [
        ...(mrList.get(_platform) ?? []),
        ...((await this.bits.searchAllMr({
          group_name: getGroupName(_platform),
          state: 'merged',
          target_branch: getBranch() ?? '',
          mr_type: 'bug',
          merged_time: JSON.stringify({
            gte: integrationStageInfo?.start_time,
            lte: dayjs().unix(),
          }),
        })) ?? []),
      ]);
    }

    const mrInfos: any[] = [];
    // 取另一仓库的MR，如果有关联，就塞到对应的platform
    for (const id of mrList.get(appInfo.platform === PlatformType.Android ? PlatformType.iOS : PlatformType.Android) ??
      []) {
      const relationInfo: MrRelationInfo[] | undefined = await this.bits.getMrRelationList(id);
      if (
        relationInfo?.find(
          it =>
            it?.project_id ===
              (appInfo.platform === PlatformType.Android
                ? getProjectId(isOverseas(appId) ? AppSettingId.CC_ANDROID : AppSettingId.LV_ANDROID)
                : getProjectId(isOverseas(appId) ? AppSettingId.CC_IOS : AppSettingId.LV_IOS)) &&
            it.target_branch === getBranch(),
        )
      ) {
        mrList.get(appInfo.platform)?.push(id);
      }
    }
    // 取正常的MR
    for (const id of Array.from(new Set(mrList.get(appInfo.platform) ?? []))) {
      const mrInfo = await this.bits.getMrInfo({ mrId: id });
      if (!mrInfo) {
        continue;
      }
      const meegoResult = await this.bits.getBindMeegoTaskInfo({
        mr_id: id,
      });
      const user = await this.lark.getUserIdByEmail(add_suffix_ne('@bytedance.com')(mrInfo?.author ?? ''));
      const department = await this.lark.getUserDepartment(user?.open_id ?? '').catch(() => '未知');
      let grayNum = '未知轮次';
      if (!grayInfoList || grayInfoList.length === 0) {
        grayNum = '未知轮次';
      } else {
        const _index = grayInfoList.findIndex(s => mrInfo && (s?.packageTime ?? s.releaseTime) > mrInfo.merged_time);
        grayNum = _index !== -1 ? `第${_index + 1}轮灰度` : `末灰后`;
      }
      const mrMeego = meegoResult.find(v => v.task_type === 'issue');
      if (mrMeego) {
        await wait(100);
      }
      const meegoInfo = mrMeego
        ? await this.meego.requestTaskInfo(mrMeego.platform_project_name, mrMeego.task_type, {
            work_item_ids: [parseInt(mrMeego.task_id, 10)],
            fields: [
              'priority',
              'field_df36d9',
              'field_828485',
              'field_62420e',
              'business',
              'bug_channel',
              'field_fbc20d',
              'issue_stage',
              'field_3d9501',
            ],
          })
        : null;
      const meegoItem = meegoInfo?.data[0];
      if (mrMeego && !meegoItem) {
        this.alarmService.reportError(
          `Meego信息获取失败 res:${JSON.stringify(meegoInfo)} req:${JSON.stringify(mrMeego)}`,
        );
      }
      const priorityValue = meegoItem?.fields.find(v => v.field_key === 'priority');
      const categoryValue = meegoItem?.fields.find(v => v.field_key === 'field_df36d9');
      const moduleName = categoryValue ? meegoUtils.getIssueModelName(categoryValue) : 'Unknown';
      const preDiscover = (meegoItem?.fields.find(v => v.field_key === 'field_828485')?.field_value as FieldValue)
        ?.label;
      const cantDiscoverReason = (
        meegoItem?.fields.find(v => v.field_key === 'field_62420e')?.field_value as FieldValue
      )?.label;
      const bugFindMethod = (meegoItem?.fields.find(v => v.field_key === 'bug_channel')?.field_value as FieldValue)
        ?.label;
      const bugStageIntroduction = (
        meegoItem?.fields.find(v => v.field_key === 'field_fbc20d')?.field_value as FieldValue
      )?.label;
      const bugStageFound = (meegoItem?.fields.find(v => v.field_key === 'issue_stage')?.field_value as FieldValue)
        ?.label;
      const bugType = (meegoItem?.fields.find(v => v.field_key === 'field_3d9501')?.field_value as FieldValue)?.label;

      const businessField = meegoItem?.fields?.find(value => value.field_key === 'business');
      const business_line_value = businessField?.field_value;
      let business = '';
      if (typeof business_line_value === 'string') {
        const result = this.versionFeatureService.getMeegoBusinessLineLabel(business_line_value);
        business = result.join('/');
      }
      mrInfos.push({
        mr_id: id,
        Bug标题: mrMeego?.task_title ?? '',
        Meego链接: mrMeego?.task_url ?? '',
        Meego业务线: business,
        Bug优先级: (priorityValue?.field_value as FieldValue)?.label ?? '',
        缺陷关联模块: moduleName,
        是否稳定性: moduleName.includes('Slardar稳定性') || moduleName.includes('基础性能自动化'),
        MR标题: mrInfo?.title,
        MR链接: mrInfo?.mr_detail_url,
        RD: mrInfo?.author,
        合入时间: dayjs
          .unix(mrInfo?.merged_time ?? 0)
          .utcOffset(8)
          .format(),
        合入目标分支: mrInfo?.target_branch,
        Bug提交时间: meegoItem
          ? dayjs
              .unix(meegoItem.created_at / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
        MR提交时间: dayjs
          .unix(mrInfo?.create_time ?? 0)
          .utcOffset(8)
          .format(),
        是否可前置发现: preDiscover,
        不可前置发现原因: cantDiscoverReason,
        MR提交人业务线: department,
        合入的灰度轮次: grayNum,
        BUG发现方式: bugFindMethod,
        引入阶段: bugStageIntroduction,
        发现阶段: bugStageFound,
        缺陷分类: bugType,
        // 拉出海外分支时间: overseaCheckoutTime ? dayjs.unix(overseaCheckoutTime).utcOffset(8).format() : 'Unknown',
        用户故事开始时间: userStoryStageInfo?.start_time
          ? dayjs.unix(userStoryStageInfo.start_time).utcOffset(8).format()
          : 'Unknown',
        是否用户故事后合入:
          userStoryStageInfo?.start_time && mrInfo?.merged_time
            ? mrInfo?.merged_time >= userStoryStageInfo.start_time
            : 'Unknown',
      });
    }
    mrInfos.sort((a, b) => Number(new Date(a['合入时间'].toString())) - Number(new Date(b['合入时间'].toString())));
    return mrInfos;
  }

  async getRetouchMrAfterSystemTestImpl(appId: number, version: string) {
    const appInfo = versionUtils.appIdToAppInfo(appId.toString());
    if (!appInfo) {
      return [];
    }
    const versionInfo = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: appId,
      version,
    });
    const grayStageInfo = versionInfo?.version_stages?.find(
      v => v.stage_name.includes('gray') || v.stage_name.includes('testFlight') || v.stage_name.includes('testflight'),
    );
    const grayInfoList = grayStageInfo?.sub_stages
      ?.map(v => v.extra_data)
      ?.filter(it => it?.releaseTime && it?.releaseTime !== -1)
      ?.sort((a, b) => a.releaseTime - b.releaseTime);
    const integrationStageInfo = versionInfo?.version_stages?.find(v => v.stage_name.endsWith('integration'));
    const userStoryStageInfo = versionInfo?.version_stages?.find(v => v.stage_name.endsWith('_user_story'));

    const getDomesticBranch = (_platform: PlatformType) =>
      _platform === PlatformType.Android
        ? `release/retouch/${isOverseas(appId) ? versionUtils.hypic2RetouchVersion(version) : version}`
        : `Release/${isOverseas(appId) ? versionUtils.hypic2RetouchVersion(version) : version}`;
    const getOverseasBranch = (_platform: PlatformType) =>
      _platform === PlatformType.Android
        ? `release/hypic/${isOverseas(appId) ? version : versionUtils.retouch2HypicVersion(version)}`
        : `Release/Oversea/${isOverseas(appId) ? version : versionUtils.retouch2HypicVersion(version)}`;

    // 获取海外分支拉出时间；醒H不通过纸飞机拉分支，无法获取到准确时间，通过分支首次提交时间替代
    const _mergeBranchInfos = await this.bits.searchBranch(
      appInfo.platform === PlatformType.Android ? 64902 : 64697,
      getOverseasBranch(appInfo.platform),
    );
    const overseaCheckoutTime = _mergeBranchInfos?.find(
      it => it?.branch === getOverseasBranch(appInfo.platform),
    )?.create_time;

    const mrList = new Map<PlatformType, number[]>();

    for (const _platform of [PlatformType.Android, PlatformType.iOS]) {
      if (isOverseas(appId)) {
        mrList.set(_platform, [
          ...(mrList.get(_platform) ?? []),
          ...((await this.bits.searchAllMr({
            group_name: _platform === PlatformType.Android ? SpaceType.Retouch_Android : SpaceType.Retouch_iOS,
            state: 'merged',
            target_branch: getDomesticBranch(_platform),
            mr_type: 'bug',
            merged_time: JSON.stringify({
              gte: integrationStageInfo?.start_time,
              lte: overseaCheckoutTime,
            }),
          })) ?? []),
          ...((await this.bits.searchAllMr({
            group_name: _platform === PlatformType.Android ? SpaceType.Retouch_Android : SpaceType.Retouch_iOS,
            state: 'merged',
            target_branch: getOverseasBranch(_platform),
            mr_type: 'bug',
            merged_time: JSON.stringify({
              gt: overseaCheckoutTime,
              lte: dayjs().unix(),
            }),
          })) ?? []),
        ]);
      } else {
        mrList.set(_platform, [
          ...(mrList.get(_platform) ?? []),
          ...((await this.bits.searchAllMr({
            group_name: _platform === PlatformType.Android ? SpaceType.Retouch_Android : SpaceType.Retouch_iOS,
            state: 'merged',
            target_branch: getDomesticBranch(_platform),
            mr_type: 'bug',
            merged_time: JSON.stringify({
              gte: integrationStageInfo?.start_time,
              lte: dayjs().unix(),
            }),
          })) ?? []),
        ]);
      }
    }
    const mrInfos: any[] = [];

    // 取另一仓库的MR
    for (const id of mrList.get(appInfo.platform === PlatformType.Android ? PlatformType.iOS : PlatformType.Android) ??
      []) {
      const relationInfo: MrRelationInfo[] | undefined = await this.bits.getMrRelationList(id);
      if (
        relationInfo?.find(
          it =>
            it?.project_id === (appInfo.platform === PlatformType.Android ? 64902 : 64697) &&
            (it.target_branch === getDomesticBranch(appInfo.platform) ||
              it.target_branch === getOverseasBranch(appInfo.platform)),
        )
      ) {
        mrList.get(appInfo.platform)?.push(id);
      }
    }
    // 取正常的MR
    for (const id of Array.from(new Set(mrList.get(appInfo.platform) ?? []))) {
      const mrInfo = await this.bits.getMrInfo({ mrId: id });
      if (!mrInfo) {
        continue;
      }
      const meegoResult = await this.bits.getBindMeegoTaskInfo({
        mr_id: id,
      });
      const user = await this.lark.getUserIdByEmail(add_suffix_ne('@bytedance.com')(mrInfo?.author ?? ''));
      const department = await this.lark.getUserDepartment(user?.open_id ?? '').catch(() => '未知');
      let grayNum = '未知轮次';
      if (!grayInfoList || grayInfoList.length === 0) {
        grayNum = '未知轮次';
      } else {
        const _index = grayInfoList.findIndex(s => mrInfo && (s?.packageTime ?? s.releaseTime) > mrInfo.merged_time);
        grayNum = _index !== -1 ? `第${_index + 1}轮灰度` : `末灰后`;
      }
      const mrMeego = meegoResult.find(v => v.task_type === 'issue');
      if (mrMeego) {
        await wait(200);
      }
      const meegoInfo = mrMeego
        ? await this.meego.requestTaskInfo(mrMeego.platform_project_name, mrMeego.task_type, {
            work_item_ids: [parseInt(mrMeego.task_id)],
            fields: [
              'priority',
              'field_df36d9',
              'field_828485',
              'field_62420e',
              'business',
              'bug_channel',
              'field_fbc20d',
              'issue_stage',
              'field_3d9501',
            ],
          })
        : null;
      const meegoItem = meegoInfo?.data[0];
      if (mrMeego && !meegoItem) {
        this.alarmService.reportError(
          `Meego信息获取失败 res:${JSON.stringify(meegoInfo)} req:${JSON.stringify(mrMeego)}`,
        );
      }
      const priorityValue = meegoItem?.fields.find(v => v.field_key === 'priority');
      const categoryValue = meegoItem?.fields.find(v => v.field_key === 'field_df36d9');
      const moduleName = categoryValue ? meegoUtils.getIssueModelName(categoryValue) : 'Unknown';
      const preDiscover = (meegoItem?.fields.find(v => v.field_key === 'field_828485')?.field_value as FieldValue)
        ?.label;
      const cantDiscoverReason = (
        meegoItem?.fields.find(v => v.field_key === 'field_62420e')?.field_value as FieldValue
      )?.label;
      const bugFindMethod = (meegoItem?.fields.find(v => v.field_key === 'bug_channel')?.field_value as FieldValue)
        ?.label;
      const bugStageIntroduction = (
        meegoItem?.fields.find(v => v.field_key === 'field_fbc20d')?.field_value as FieldValue
      )?.label;
      const bugStageFound = (meegoItem?.fields.find(v => v.field_key === 'issue_stage')?.field_value as FieldValue)
        ?.label;
      const bugType = (meegoItem?.fields.find(v => v.field_key === 'field_3d9501')?.field_value as FieldValue)?.label;

      const businessField = meegoItem?.fields?.find(value => value.field_key === 'business');
      const business_line_value = businessField?.field_value;
      let business = '';
      if (typeof business_line_value === 'string') {
        const result = this.versionFeatureService.getMeegoBusinessLineLabel(business_line_value);
        business = result.join('/');
      }
      mrInfos.push({
        mr_id: id,
        Bug标题: mrMeego?.task_title ?? '',
        Meego链接: mrMeego?.task_url ?? '',
        Meego业务线: business,
        Bug优先级: (priorityValue?.field_value as FieldValue)?.label ?? '',
        缺陷关联模块: moduleName,
        是否稳定性: moduleName.includes('Slardar稳定性') || moduleName.includes('基础性能自动化'),
        MR标题: mrInfo?.title,
        MR链接: mrInfo?.mr_detail_url,
        RD: mrInfo?.author,
        合入时间: dayjs
          .unix(mrInfo?.merged_time ?? 0)
          .utcOffset(8)
          .format(),
        合入目标分支: mrInfo?.target_branch,
        Bug提交时间: meegoItem
          ? dayjs
              .unix(meegoItem.created_at / 1000)
              .utcOffset(8)
              .format()
          : 'Unknown',
        MR提交时间: dayjs
          .unix(mrInfo?.create_time ?? 0)
          .utcOffset(8)
          .format(),
        是否可前置发现: preDiscover,
        不可前置发现原因: cantDiscoverReason,
        MR提交人业务线: department,
        合入的灰度轮次: grayNum,
        BUG发现方式: bugFindMethod,
        引入阶段: bugStageIntroduction,
        发现阶段: bugStageFound,
        缺陷分类: bugType,
        拉出海外分支时间: overseaCheckoutTime ? dayjs.unix(overseaCheckoutTime).utcOffset(8).format() : 'Unknown',
        用户故事开始时间: userStoryStageInfo?.start_time
          ? dayjs.unix(userStoryStageInfo.start_time).utcOffset(8).format()
          : 'Unknown',
        是否用户故事后合入:
          userStoryStageInfo?.start_time && mrInfo?.merged_time
            ? mrInfo?.merged_time >= userStoryStageInfo.start_time
            : 'Unknown',
      });
    }
    mrInfos.sort((a, b) => Number(new Date(a['合入时间'].toString())) - Number(new Date(b['合入时间'].toString())));
    return mrInfos;
  }
}
