import { VersionProcessInfo, VersionProcessStatus, VersionStageStatus } from '@shared/releasePlatform/versionStage';
import VersionProcessInfoDao from './dao/releasePlatform/VersionProcessInfoDao';
import { useInject } from '@edenx/runtime/bff';

export class VscodeService {
  /**
   * 获取应用版本信息
   * @param appId 应用ID
   * @returns 版本信息，包含正在运行的3个版本
   */
  async getAppVersionInfo(appId: number) {
    try {
      let appName = '';
      if (appId === 177501 || appId === 300601) {
        appName = appId === 177501 ? '剪映' : 'CapCut';
      }
      const allVersions = await useInject(VersionProcessInfoDao).findAllVersionInfos(appId);
      // 获取所有版本信息
      if (!allVersions || allVersions.length === 0) {
        return {
          code: 0,
          data: {
            appName,
            versions: [],
          },
        };
      }

      // 过滤正在运行的版本（有正在进行的阶段，可能多个）
      const runningVersions = allVersions.filter(
        (version: VersionProcessInfo) => version.status === VersionProcessStatus.OnProgress,
      );
      // 如果没有正在运行的版本，则返回最近的3个版本
      const versionsToProcess = runningVersions.length > 0 ? runningVersions : allVersions;

      // 按版本号排序并取前3个
      const sortedVersions = versionsToProcess
        .map(info => {
          info.version_stages = info.version_stages.sort((a, b) => a.start_time - b.start_time);
          return info;
        })
        .reverse()
        .slice(0, 3);

      // 处理版本信息，添加描述和消息
      const processedVersions = sortedVersions.map((version: VersionProcessInfo) => {
        const currentStage = this.getCurrentStageInfo(version);
        const nextStage = this.getNextStageInfo(version);
        const mrRule = {
          info: `合入规则:${this.getMrRule(version).info}`,
        };
        let len = -1;
        const infos: { id: number; info: string }[] = [];
        // 将当前阶段的所有进行中阶段消息作为单独的消息项
        currentStage.info.forEach((msg, index) => {
          if (msg && msg.length > 0) {
            len++;
            infos.push({
              id: len,
              info: msg,
            });
          }
        });

        // 添加下一阶段和MR规则消息

        if (nextStage.info.length > 0) {
          len++;
          infos.push({
            id: len,
            info: nextStage.info,
          });
        }
        if (infos.length > 0) {
          len++;
          infos.push({
            id: len,
            info: mrRule.info,
          });
        }
        return {
          version_name: version.version,
          version_description: currentStage.description,
          infos,
        };
      });

      return {
        code: 0,
        data: {
          appName,
          versions: processedVersions,
        },
      };
    } catch (error) {
      console.error('Failed to get app version info:', error);
      return {
        code: -1,
        error: error || '获取版本信息失败',
        data: {
          appName: '',
          versions: [],
        },
      };
    }
  }

  /**
   * 获取剪映版本信息（兼容现有接口）
   */
  async getLvVersionInfo() {
    return await this.getAppVersionInfo(177501); // 1 是剪映的appId
  }

  /**
   * 获取CapCut版本信息（兼容现有接口）
   */
  async getCapCutVersionInfo() {
    return await this.getAppVersionInfo(300601); // 2 是CapCut的appId
  }
  private formatDate(timestamp: number | null): string {
    if (!timestamp) {
      return '待定';
    }
    const date = new Date(timestamp * 1000);
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${month}月${day}日${hour}时${minute}分`;
  }
  /**
   * 获取当前阶段信息（返回进行中的所有阶段数组）
   */
  private getCurrentStageInfo(versionInfo: VersionProcessInfo) {
    const onProgressStages = versionInfo.version_stages.filter(stage => stage.status === VersionStageStatus.OnProgress);

    if (onProgressStages.length === 0) {
      const lastComplete = [...versionInfo.version_stages]
        .reverse()
        .find(stage => stage.status === VersionStageStatus.Complete);
      if (!lastComplete && versionInfo.version_stages[0].status === VersionStageStatus.NotStart) {
        const firstStage = versionInfo.version_stages[0];
        // 将时间戳转换为 2019/12/1 格式
        const formattedDate = this.formatDate(firstStage.start_time);
        return {
          description: `未开始,预计封板时间:${formattedDate}`,
          info: [],
        };
      }
      return {
        description: lastComplete ? `已完成:${lastComplete.display_name}阶段` : '未知阶段',
        info: [`已完成:${lastComplete?.display_name || '未知'}`],
      };
    }

    // 为每个阶段和子阶段创建单独的消息
    const stages: string[] = [];
    onProgressStages.forEach(stage => {
      // 先添加主阶段消息
      stages.push(`进行中:${stage.display_name}-${versionInfo.version}`);

      // 然后添加所有进行中的子阶段作为单独的消息
      if (stage.sub_stages && stage.sub_stages.length > 0) {
        const activeSubStages = stage.sub_stages.filter(sub => sub.status === VersionStageStatus.OnProgress);
        activeSubStages.forEach(subStage => {
          stages.push(`  └─ 子阶段:${subStage.display_name}-${versionInfo.version}`);
        });
      }
    });

    // 如果有多个进行中的阶段，描述显示"多阶段进行中"
    const lastComplete = [...versionInfo.version_stages]
      .reverse()
      .find(stage => stage.status === VersionStageStatus.OnProgress);
    const description = lastComplete ? `${lastComplete.display_name}-${this.getMrRule(versionInfo).info}` : '未知阶段';

    return {
      description,
      info: stages,
    };
  }

  private getNextStageInfo(versionInfo: VersionProcessInfo) {
    const onProgressStage = versionInfo.version_stages.find(stage => stage.status === VersionStageStatus.OnProgress);
    // 若存在 OnProgress 阶段且该阶段有子阶段，则在子阶段中查找第一个状态为 NotStart 的子阶段
    const nextStage =
      onProgressStage && onProgressStage.sub_stages
        ? onProgressStage.sub_stages.find(stage => stage.status === VersionStageStatus.NotStart) ??
          versionInfo.version_stages.find(stage => stage.status === VersionStageStatus.NotStart)
        : versionInfo.version_stages.find(stage => stage.status === VersionStageStatus.NotStart);
    if (!nextStage) {
      return {
        info: '',
      };
    }
    const startTime = nextStage.start_time ? this.formatDate(nextStage.start_time) : '待定';
    return {
      info: `下一阶段:${nextStage.display_name}(${startTime})`,
    };
  }

  private getMrRule(versionInfo: VersionProcessInfo) {
    const current = versionInfo.version_stages.find(stage => stage.status === VersionStageStatus.OnProgress);
    if (!current) {
      return {
        info: '不允许合入Mr',
      };
    }

    const onProgressSubStage: any = current.sub_stages?.find(sub => sub.status === VersionStageStatus.OnProgress);
    if (!onProgressSubStage) {
      return {
        info: '不允许合入Mr',
      };
    }
    const mrStandard = onProgressSubStage.mr_standard;
    let ruleDesc = '';

    switch (mrStandard) {
      case 0: // NoLimit
        ruleDesc = '允许合入所有级别Mr';
        break;
      case 1: // SP0P1P2p3
        ruleDesc = '允许合入S级/P0/P1/P2/P3 Bugfix';
        break;
      case 2: // SP0P1
        ruleDesc = '允许合入S级/P0/P1 Bugfix';
        break;
      case 3: // SP0
        ruleDesc = '允许合入S级/P0 Bugfix';
        break;
      case 4: // Prohibited
        ruleDesc = '不允许合入Mr';
        break;
      default:
        ruleDesc = '不允许合入Mr';
    }

    return {
      info: ruleDesc,
    };
  }
}
