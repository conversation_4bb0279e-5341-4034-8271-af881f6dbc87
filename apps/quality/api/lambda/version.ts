import { Api, Data, Get, Params, Patch, Post, Query, useInject } from '@edenx/runtime/bff';
import { queryPreviousVersionSchema, queryRecentVersionSchema } from '@shared/schema/alarm';
import AlarmVersionModel from '../model/AlarmVersionInfoTable';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { IosTypeOf, VersionType } from '@shared/utils/version_utils';
import { z } from 'zod';
import { pickBy } from 'lodash';
import SlardarRequestEngine from '../service/SlardarRequestEngine';
import dayjs from 'dayjs';
import AutoVersionStageDataManager from '../service/stage/autoVersionStageDataManager';
import { PlatformType } from '@pa/shared/dist/src/core';
import VersionProcessDao from '../dao/VersionProcessDao';
import { SelectedName } from '@shared/utils/offlineslardar';

export const queryPreviousVersion = Api(
  Post(`/versions/previous`),
  Data(queryPreviousVersionSchema),
  async ({ data }) => {
    const model = useInject(AlarmVersionModel);
    if (data.versionCode && data.versionCode.includes('.')) {
      return await model.findiOSLastVersion(
        data.aid,
        IosTypeOf(data.versionCode, data.aid === 3006) !== VersionType.TF_GRAY ? VersionType.FULL : VersionType.TF_GRAY,
        data.version,
      );
    }
    return model.findAndroidCompareVersion(data.aid, data.versionCode!);
  },
);
export const queryRecentVersions = Api(Post(`/versions`), Data(queryRecentVersionSchema), async ({ data }) => {
  const model = useInject(AlarmVersionModel);
  const filter = data.filter ? JSON.parse(data.filter) : {};
  const sort = data.sort ? JSON.parse(data.sort) : undefined;
  const d = await model.findRecentVersions(
    data.appId,
    data.platform,
    data.pageNum || 1,
    data.pageSize || 100,
    filter,
    sort,
  );
  const count = await model.countVersions({
    timestamp: {
      $gt: 1,
    },
    aid: data.appId,
    platform: {
      $in: data.platform ? [data.platform] : [SlardarPlatformType.Android, SlardarPlatformType.iOS],
    },
    ...filter,
  });
  return {
    data: d,
    count,
  };
});
export const queryReleaseVersions = Api(
  Get('/versions/release/:platform'),
  Params(
    z.object({
      platform: z.optional(z.string()),
    }),
  ),
  Query(z.object({ aid: z.string().optional() })),
  async ({ params, query }) => {
    const model = useInject(AlarmVersionModel);
    const p = params.platform;
    const platform = p ? SlardarPlatformType[p as keyof typeof SlardarPlatformType] : undefined;
    const { aid } = query;
    let queryParams: any = {
      version_type: {
        $in: [VersionType.FULL, VersionType.SMALL, VersionType.ONLINE],
      },
    };
    if (p) {
      queryParams = { ...queryParams, platform };
    }
    if (aid) {
      queryParams = { ...queryParams, aid };
    }
    return await model.genericFind(queryParams);
  },
);
export const patchVersion = Api(
  Patch('/versions/modify'),
  Data(
    z.object({
      aid: z.number(),
      timestamp: z.optional(z.number()),
      version_type: z.optional(z.nativeEnum(VersionType)),
      version_code: z.string(),
    }),
  ),
  async ({ data }) => {
    const model = useInject(AlarmVersionModel);
    const originalVer = (await model.findVersions(data.aid, [data.version_code])).shift();
    const ret = await model.patchVersion(
      pickBy<typeof data>(data, (v, k) => v && k !== 'version_code'),
      data.version_code,
      data.aid,
    );
    console.log(ret);

    // when iOS changes version, we should re-pull values.
    const engine = useInject(SlardarRequestEngine);
    if (
      // data.version_code.includes(".") &&
      data.timestamp &&
      originalVer &&
      dayjs.unix(data.timestamp).startOf('h').unix() !== dayjs.unix(originalVer.timestamp).startOf('h').unix()
    ) {
      console.log('re-pull value triggered');
      await engine.rePullValue(data.aid, [data.version_code], '/versions/modify');
    }
    return { status: 'OK' };
  },
);

export const getOver2TimeStamp = Api(
  Patch('/versions/getOver2TimeStamp'),
  Data(
    z.object({
      app_aid: z.number(),
      version_start_timeStamp: z.number(),
      platform: z.nativeEnum(SlardarPlatformType),
      version_code: z.string(),
      over_count: z.number(),
    }),
  ),
  async ({ data }) => {
    const dataManager = useInject(AutoVersionStageDataManager);
    return await dataManager.getOverTimeStamp(
      data.app_aid,
      data.version_code,
      data.version_start_timeStamp,
      data.platform,
      data.over_count,
    );
  },
);

export const getPreVersionCode = Api(
  Patch('/versions/getPreVersionCode'),
  Data(
    z.object({
      app_aid: z.number(),
      platform: z.string(),
      version: z.string(),
      version_code: z.string(),
    }),
  ),
  async ({ data }) => {
    const dataManager = useInject(AutoVersionStageDataManager);
    return await dataManager.getPreVersionCode(
      data.app_aid,
      data.platform as SlardarPlatformType,
      data.version,
      data.version_code,
    );
  },
);

export const getAppVersionInfo = Api(
  Post('/search/getAppVersionInfo'),
  Data(
    z.object({
      product: z.string(),
      platform: z.nativeEnum(PlatformType),
      size: z.number().optional(),
    }),
  ),
  async ({ data }) => {
    const versionProcess = useInject(VersionProcessDao);
    return await versionProcess.queryVersionList(data.product, data.platform, data.size);
  },
);

export const getVersionIntegrationTime = Api(
  Post('/search/version_time'),
  Data(
    z.object({
      aid: z.number(),
      version: z.string(),
      platform: z.nativeEnum(SlardarPlatformType),
      stage: z.nativeEnum(SelectedName),
    }),
  ),
  async ({ data }) => {
    const versionProcess = useInject(VersionProcessDao);

    const isAfterIntegration = data.stage === SelectedName.AfterIntegration;
    const IntegrationTime = isAfterIntegration
      ? await versionProcess.queryVersionIntegrationEndTime(data.aid, data.version, data.platform)
      : await versionProcess.queryVersionIntegrationTime(data.aid, data.version, data.platform);

    // const IntegrationTime = await versionProcess.queryVersionIntegrationTime(data.aid, data.version, data.platform);
    if (IntegrationTime === undefined) {
      return {
        code: 404,
        message: 'No data found for the given parameters',
        data: data.version,
      };
    }
    return {
      code: 200,
      message: 'Success',
      data: IntegrationTime,
    };
  },
);

export const getAppVersion = Api(
  Post('/search/getAppVersion'),
  Data(
    z.object({
      aid: z.number(),
      platform: z.nativeEnum(SlardarPlatformType),
    }),
  ),
  async ({ data }) => {
    const versionProcess = useInject(VersionProcessDao);
    const versions = await versionProcess.getPullOfflineVersion(data.aid, data.platform);
    if (!versions || versions.length === 0) {
      return {
        code: 404,
        message: 'No data found for the given parameters',
        data: [],
      };
    }
    // 按版本号降序排序
    versions.sort((a, b) => {
      const partsA = a.split('.').map(part => parseInt(part, 10));
      const partsB = b.split('.').map(part => parseInt(part, 10));
      const len = Math.max(partsA.length, partsB.length);
      for (let i = 0; i < len; i++) {
        const partA = partsA[i] || 0;
        const partB = partsB[i] || 0;
        if (partA !== partB) {
          return partB - partA; // 降序
        }
      }
      return 0;
    });

    return {
      code: 200,
      message: 'Success',
      data: versions.slice(0, 15), // 限制返回最多15条
    };
  },
);

export const getPullOfflineVersions = Api(
  Post('/search/getPullAppVersion'),
  Data(
    z.object({
      aid: z.number(),
      platform: z.nativeEnum(SlardarPlatformType),
    }),
  ),
  async ({ data }) => {
    const versionProcess = useInject(VersionProcessDao);
    const versions = await versionProcess.getPullOfflineVersionsByIntegrationTime(data.aid, data.platform);
    if (!versions || versions.length === 0) {
      return {
        code: 404,
        message: 'No data found for the given parameters',
        data: [],
      };
    }
    return {
      code: 200,
      message: 'Success',
      data: versions,
    };
  },
);
