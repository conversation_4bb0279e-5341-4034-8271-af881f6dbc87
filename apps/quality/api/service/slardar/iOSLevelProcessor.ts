import { autoLevelProcessor } from './autoLevelProcessor';
import { DBSlardarCrashIssueList } from '../../model/SlardarCrashIssueListTable';
import { CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import { DBSlardarCrashIssue } from '../../model/SlardarCrashIssueTable';
import SlardarUserCountModel from '../../model/SlardarUserCountTable';
import { useInject } from '@edenx/runtime/bff';
import { get_previous_version, versionCodeToVersion, VersionType } from '@shared/utils/version_utils';
import SlardarVersionMetricsModel, { MetricType } from '../../model/SlardarVersionMetricsTable';
import { current_region, Region } from '../../utils/region';

export class iOSLevelProcessor extends autoLevelProcessor {
  private slardarUserCountModel: SlardarUserCountModel;
  private slardarVersionMetricsModel: SlardarVersionMetricsModel;
  private userCountMap: { [versionCode: string]: number };
  private WatchDogCountMap: { [versionCode: string]: number };
  private LastVersionMap: { [version: string]: string };
  private lowUserMap: { [versionCode: string]: number };
  private aid: number;

  constructor(list: DBSlardarCrashIssueList[], issues: DBSlardarCrashIssue[]) {
    super(list, issues);
    this.slardarUserCountModel = useInject(SlardarUserCountModel);
    this.slardarVersionMetricsModel = useInject(SlardarVersionMetricsModel);
    this.userCountMap = {};
    this.LastVersionMap = {};
    this.WatchDogCountMap = {};
    this.lowUserMap = {};
  }

  async buildParams(aid: number) {
    for (const version_code in this.grouped_list) {
      if (!version_code.includes('.')) {
        continue;
      }
      this.aid = aid;
      const userCount = await this.slardarUserCountModel.lastTimestamp(aid, version_code);
      const compare_version = await this.get_compare_version_code(version_code);
      if (userCount) {
        this.userCountMap[version_code] = userCount.count;
        const lowUserCount = await this.slardarVersionMetricsModel.getValue(aid, version_code, MetricType.LOW_END_USER);
        if (lowUserCount) {
          this.lowUserMap[version_code] = lowUserCount / userCount.count;
        }
      }
      if (compare_version) {
        const lastUserCount = await this.slardarUserCountModel.lastTimestamp(aid, compare_version);
        if (lastUserCount) {
          this.userCountMap[compare_version] = lastUserCount.count;
          const lowUserCount = await this.slardarVersionMetricsModel.getValue(
            aid,
            compare_version,
            MetricType.LOW_END_USER,
          );
          if (lowUserCount) {
            this.lowUserMap[compare_version] = lowUserCount / lastUserCount.count;
          }
        }
      }
      const watchDogCount = await this.slardarVersionMetricsModel.getValue(
        aid,
        version_code,
        MetricType.WATCHDOG_USER_COUNT,
      );
      if (watchDogCount) {
        this.WatchDogCountMap[version_code] = watchDogCount;
      }
    }
  }

  async calculateLevel(d: DBSlardarCrashIssueList, isAggregated = false): Promise<(number | string)[]> {
    const curVersion = await this.alarmVersionModel.findVersions(this.aid, [d.version_code]);
    let versionType: VersionType | undefined;

    if (curVersion[0]) {
      versionType = curVersion[0].version_type;
    } else if (d.version_code.endsWith('.1')) {
      // 如果版本不存在，但以.1结尾，则认为是TF灰度版本
      versionType = VersionType.TF_GRAY;
    } else {
      return [3, '未找到版本号'];
    }
    switch (versionType) {
      // if (!curVersion[0]) {
      //   return [3, '未找到版本号'];
      // }
      // switch (curVersion[0].version_type) {
      case VersionType.TF_GRAY:
      case VersionType.SMALL_TF:
      case VersionType.FULL_TF:
        switch (d.crash_type) {
          case CrashType.OOMCrash:
            return await this.TFOOMCalculator(d);
          case CrashType.WatchDog:
            return await this.TFWatchDogCalculator(d);
          case CrashType.Crash:
            return await this.TFCrashCalCalculator(d);
          case CrashType.CpuMeTriket:
            return await this.TFCpuMeTriketCalculator(d);
          default:
            return [3, '非OOM|WATCH|CRASH类型|CPU类型'];
        }
      case VersionType.SMALL:
      case VersionType.FULL:
        switch (d.crash_type) {
          case CrashType.OOMCrash:
            return await this.DefaultOOMCalculator(d);
          case CrashType.WatchDog:
            return await this.DefaultWatchDogCalculator(d);
          case CrashType.Crash:
            return await this.DefaultCrashCalculator(d);
          default:
            return [3, '非OOM|WATCH|CRASH类型'];
        }
      default:
        return [3, '非定级版本'];
    }
  }

  protected async get_compare_version_code(version_code: string): Promise<string> {
    const version = versionCodeToVersion(version_code);
    if (version in this.LastVersionMap) {
      return this.LastVersionMap[version];
    }
    const lastVersion = await this.alarmVersionModel.findiOSLastVersion(
      this.aid,
      VersionType.FULL,
      versionCodeToVersion(version_code),
    );
    if (lastVersion) {
      this.LastVersionMap[version] = lastVersion.version_code;
      return lastVersion.version_code;
    } else {
      // 获取上上个版本的全量包
      const pre_version = get_previous_version(version);
      const pre_lastVersion = await this.alarmVersionModel.findiOSLastVersion(this.aid, VersionType.FULL, pre_version);
      if (pre_lastVersion) {
        this.LastVersionMap[version] = pre_lastVersion.version_code;
        return pre_lastVersion.version_code;
      }
    }
    return '';
  }

  protected async get_compare_users(version_code: string, d: DBSlardarCrashIssueList) {
    if (version_code in this.grouped_list) {
      const res = this.grouped_list[version_code].find(
        it => it.issue_id === d.issue_id && it.crash_type === d.crash_type && it.platform === d.platform,
      )?.users;
      if (res) {
        return res;
      }
    }
    return await this.slardarCrashIssueListModel.findPrevIssueListUsers(
      d.aid,
      d.issue_id,
      version_code,
      d.platform,
      d.crash_type,
    );
  }

  protected async get_start_version(d: DBSlardarCrashIssueList): Promise<string | undefined> {
    const start_version =
      this.issues.find(it => it.issue_id === d.issue_id && it.platform === d.platform)?.start_os_version ||
      (await this.slardarCrashIssueModel.getStartVersionByIssueId(d.aid, d.issue_id, d.platform));
    return start_version ? versionCodeToVersion(start_version) : undefined;
  }

  private async TFCpuMeTriketCalculator(d: DBSlardarCrashIssueList) {
    const start_version = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);
    const isNewIssue =
      start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1);
    // 新增问题
    if (d.users >= 100) {
      return [0, '新问题|用户崩溃数大于等于100'];
    } else if (d.users >= 50) {
      return [1, '新问题|用户崩溃数>=50'];
    }
    return [3, '未命中规则'];
  }

  // TF灰度
  private async TFCrashCalCalculator(d: DBSlardarCrashIssueList): Promise<[number, string]> {
    const VERSION_THRESHOLD_CC = 1440;
    const VERSION_THRESHOLD_DEFAULT = 1640;
    const MIN_USERS_FOR_ZERO_SCORE = 1;
    const MIN_USERS_FOR_HIGH_PRIORITY = 10;
    const MIN_USERS_PLUS_COUNT_FOR_MEDIUM_PRIORITY = 3;

    const startVersion = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);

    // 判断是否为新问题
    const isNewIssue = startVersion?.substring(0, startVersion.length - 1) === version.substring(0, version.length - 1);

    const intVersion = parseInt(version.replace(/\./g, ''), 10);

    if (!isNewIssue) {
      return [0, '非新问题,不自动开单'];
    }

    // 用户数≥10的直接返回
    if (d.users >= MIN_USERS_FOR_HIGH_PRIORITY) {
      return [0, '用户大于等于10'];
    }

    // 根据不同条件检查用户数≥1的情况
    const shouldCheckSingleUser = this.isCC()
      ? intVersion > VERSION_THRESHOLD_CC
      : intVersion > VERSION_THRESHOLD_DEFAULT;

    if (shouldCheckSingleUser && d.users >= MIN_USERS_FOR_ZERO_SCORE) {
      return [0, '用户大于等于1'];
    }

    // 新问题或用户数+数量≥3的情况
    if (isNewIssue || d.users + d.count >= MIN_USERS_PLUS_COUNT_FOR_MEDIUM_PRIORITY) {
      return [1, '新问题或用户数+数量大于等于3'];
    }

    // 默认情况
    return [3, '未命中规则'];
  }

  private async TFWatchDogCalculator(d: DBSlardarCrashIssueList) {
    const start_version = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);
    const isNewIssue =
      start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1);

    // 用户数大于等于10
    if (d.users >= 10) {
      return [0, '用户大于等于10'];
    }
    // 新增问题或者用户数大于等于5
    if (isNewIssue || d.users >= 5) {
      return [1, '新问题或用户数大于等于5'];
    }
    return [3, '未命中规则'];

    // // 出现问题人数/当前版本所有卡死人数 >=50%
    // if (d.version_code in this.WatchDogCountMap && d.users / this.WatchDogCountMap[d.version_code] > 0.5) {
    // 	return 0;
    // }
    // // 上个版本全量在Top100以外
    // const last_ranking = await this.get_compare_ranking(d);
    // if (!last_ranking || last_ranking > 100) {
    // 	return 0;
    // }
    // // 上个版本全量在Top50以外
    // if (last_ranking > 50) {
    // 	return 1;
    // }
    // // 问题出现人数/当前版本所有卡死人数>=50%
    // if (d.version_code in this.WatchDogCountMap && d.users / this.WatchDogCountMap[d.version_code] > 0.3) {
    // 	return 1;
    // }
  }

  private async TFOOMCalculator(d: DBSlardarCrashIssueList) {
    const start_version = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);
    const isNewIssue =
      start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1);
    // 新增问题
    if (isNewIssue && d.has_memory_graph) {
      if (d.count > 1) {
        return [0, `新问题|有内存图|数量大于1`];
      } else {
        return [1, `新问题|有内存图|数量等于1`];
      }
    }
    return [3, `新问题:${isNewIssue}|内存图:${d.has_memory_graph}`];
  }

  private async DefaultCrashCalculator(d: DBSlardarCrashIssueList) {
    // if (d.ranking > 20) {
    // 	return 3;
    // }
    // const start_version = await this.get_start_version(d);
    // const version = versionCodeToVersion(d.version_code);
    // const last_ranking = await this.get_compare_ranking(d);
    // // Top10且新增问题
    // if (
    // 	(start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1) ||
    // 		!last_ranking) &&
    // 	d.ranking <= 10
    // ) {
    // 	return 0;
    // }
    // const compare_version_code = await this.get_compare_version_code(d.version_code);
    // if (d.version_code in this.userCountMap && compare_version_code in this.userCountMap) {
    // 	const curUsers = await this.get_compare_users(compare_version_code, d);
    // 	if (curUsers) {
    // 		// 当前用户异常率
    // 		const curData = d.users / this.userCountMap[d.version_code];
    // 		// 对比版本用户异常率
    // 		const lastData = curUsers / this.userCountMap[compare_version_code];
    // 		// 劣化50%以上
    // 		if ((curData - lastData) / lastData >= 0.5) {
    // 			// Top10
    // 			if (d.ranking <= 10) {
    // 				return 0;
    // 			}
    // 			// Top20
    // 			if (d.ranking <= 20) {
    // 				return 1;
    // 			}
    // 		}
    // 	}
    // }
    // // Top20且新增
    // if (
    // 	(start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1) ||
    // 		!last_ranking) &&
    // 	d.ranking <= 20
    // ) {
    // 	return 1;
    // }
    // return 3;
    const start_version = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);
    const compare_version_code = await this.get_compare_version_code(d.version_code);
    const isNewIssue =
      start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1);

    const hasCount = d.version_code in this.userCountMap && compare_version_code in this.userCountMap;
    if (hasCount) {
      // 当前用户异常率
      const curData = d.users / this.userCountMap[d.version_code];
      if (curData > 0.00005) {
        return [0, '比例大于0.050‰'];
      } else if (curData > 0.00003) {
        return [1, '比例大于0.030‰'];
      }

      // - 不属于p0，p1的新增issue且影响用户数大于2且属于top30以内问题
      if (isNewIssue && d.ranking <= 30 && d.users > 2) {
        return [2, '新问题|top30|数量大于2'];
      } else {
        const last_ranking = await this.get_compare_ranking(d);
        // - 不属于p0，p1劣化比例大于800%的历史issue且属于top30以内问题
        let finalRate = 10;
        if (last_ranking) {
          const lastUsers = await this.get_compare_users(compare_version_code, d);
          if (lastUsers) {
            // 对比版本用户异常率
            const lastData = lastUsers / this.userCountMap[compare_version_code];
            finalRate = (curData - lastData) / lastData;
          }
        }
        if (finalRate >= 8 && d.ranking <= 30) {
          return [2, '劣化大于800%|top30'];
        }
      }
    }
    return [3, `未命中定级规则`];
  }

  private async DefaultWatchDogCalculator(d: DBSlardarCrashIssueList) {
    const start_version = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);

    const compare_version_code = await this.get_compare_version_code(d.version_code);
    const isNewIssue =
      start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1);

    if (d.version_code in this.userCountMap && compare_version_code in this.userCountMap) {
      // 当前用户异常率
      const curData = d.users / this.userCountMap[d.version_code];
      if (this.isCC()) {
        if (curData > 0.00009) {
          return [0, '比例大于0.09‰'];
        } else if (curData > 0.00007) {
          return [1, '比例大于0.07‰'];
        }
      } else {
        if (curData > 0.000055) {
          return [0, '比例大于0.055‰'];
        } else if (curData > 0.00004) {
          return [1, '比例大于0.04‰'];
        }
      }
      if (isNewIssue) {
        if (this.isCC()) {
          if (d.users > 5) {
            return [2, '新问题|用户大于5'];
          }
        } else {
          if (d.users > 3) {
            return [2, '新问题|用户大于3'];
          }
        }
      }
    }
    return [3, '未命中定级规则'];
  }

  private async DefaultOOMCalculator(d: DBSlardarCrashIssueList) {
    const compare_version_code = await this.get_compare_version_code(d.version_code);

    const last_ranking = await this.get_compare_ranking(d);
    // 上个版本不存在则劣化无限大
    if (!last_ranking && d.ranking <= 30 && d.count > 10) {
      return [0, '新问题|top30|数量大于10'];
    }

    if (
      d.version_code in this.userCountMap &&
      compare_version_code in this.userCountMap &&
      (d.count > 300 || d.users > 200)
    ) {
      // 当前用户异常率
      const curData = d.users / this.userCountMap[d.version_code];
      const lastUsers = await this.get_compare_users(compare_version_code, d);
      if (lastUsers) {
        // 对比版本用户异常率
        const lastData = lastUsers / this.userCountMap[compare_version_code];
        const finalRate = (curData - lastData) / lastData;
        if (finalRate >= 3) {
          return [0, '劣化大于300%'];
        } else if (finalRate >= 2.4) {
          return [1, '劣化大于240%'];
        } else if (finalRate >= 1.6) {
          return [2, '劣化大于160%'];
        }
      }
    }
    return [3, '未命中OOM规则'];
  }

  private isCC() {
    return current_region() === Region.SG;
  }
}

export class RetouchIOSLevelProcessor extends autoLevelProcessor {
  private slardarUserCountModel: SlardarUserCountModel;
  private slardarVersionMetricsModel: SlardarVersionMetricsModel;
  private userCountMap: { [versionCode: string]: number };
  private WatchDogCountMap: { [versionCode: string]: number };
  private LastVersionMap: { [version: string]: string };
  private lowUserMap: { [versionCode: string]: number };
  private aid: number;

  constructor(list: DBSlardarCrashIssueList[], issues: DBSlardarCrashIssue[]) {
    super(list, issues);
    this.slardarUserCountModel = useInject(SlardarUserCountModel);
    this.slardarVersionMetricsModel = useInject(SlardarVersionMetricsModel);
    this.userCountMap = {};
    this.LastVersionMap = {};
    this.WatchDogCountMap = {};
    this.lowUserMap = {};
  }

  async buildParams(aid: number) {
    for (const version_code in this.grouped_list) {
      if (!version_code.includes('.')) {
        continue;
      }
      this.aid = aid;
      const userCount = await this.slardarUserCountModel.lastTimestamp(aid, version_code);
      const compare_version = await this.get_compare_version_code(version_code);
      if (userCount) {
        this.userCountMap[version_code] = userCount.count;
        const lowUserCount = await this.slardarVersionMetricsModel.getValue(aid, version_code, MetricType.LOW_END_USER);
        if (lowUserCount) {
          this.lowUserMap[version_code] = lowUserCount / userCount.count;
        }
      }
      if (compare_version) {
        const lastUserCount = await this.slardarUserCountModel.lastTimestamp(aid, compare_version);
        if (lastUserCount) {
          this.userCountMap[compare_version] = lastUserCount.count;
          const lowUserCount = await this.slardarVersionMetricsModel.getValue(
            aid,
            compare_version,
            MetricType.LOW_END_USER,
          );
          if (lowUserCount) {
            this.lowUserMap[compare_version] = lowUserCount / lastUserCount.count;
          }
        }
      }
      const watchDogCount = await this.slardarVersionMetricsModel.getValue(
        aid,
        version_code,
        MetricType.WATCHDOG_USER_COUNT,
      );
      if (watchDogCount) {
        this.WatchDogCountMap[version_code] = watchDogCount;
      }
    }
  }

  async calculateLevel(d: DBSlardarCrashIssueList, isAggregated = false): Promise<(number | string)[]> {
    const curVersion = await this.alarmVersionModel.findVersions(this.aid, [d.version_code]);
    if (!curVersion[0]) {
      return [3, '无版本号'];
    }
    switch (curVersion[0].version_type) {
      case VersionType.TF_GRAY:
      case VersionType.SMALL_TF:
      case VersionType.FULL_TF:
        switch (d.crash_type) {
          case CrashType.OOMCrash:
            return await this.TFOOMCalculator(d);
          case CrashType.WatchDog:
            return await this.TFWatchDogCalculator(d);
          case CrashType.Crash:
            return await this.TFCrashCalCalculator(d);
          default:
            return [3, '非OOM|WATCH|CRASH类型'];
        }
      case VersionType.SMALL:
      case VersionType.FULL:
        switch (d.crash_type) {
          case CrashType.OOMCrash:
            return await this.DefaultOOMCalculator(d);
          case CrashType.WatchDog:
            return await this.DefaultWatchDogCalculator(d);
          case CrashType.Crash:
            return await this.DefaultCrashCalculator(d);
          default:
            return [3, '非OOM|WATCH|CRASH类型'];
        }
      default:
        return [3, '非定级版本'];
    }
  }

  protected async get_compare_version_code(version_code: string): Promise<string> {
    const version = versionCodeToVersion(version_code);
    if (version in this.LastVersionMap) {
      return this.LastVersionMap[version];
    }
    const lastVersion = await this.alarmVersionModel.findiOSLastVersion(
      this.aid,
      VersionType.FULL,
      versionCodeToVersion(version_code),
    );
    if (lastVersion) {
      this.LastVersionMap[version] = lastVersion.version_code;
      return lastVersion.version_code;
    } else {
      // 获取上上个版本的全量包
      const pre_version = get_previous_version(version);
      const pre_lastVersion = await this.alarmVersionModel.findiOSLastVersion(this.aid, VersionType.FULL, pre_version);
      if (pre_lastVersion) {
        this.LastVersionMap[version] = pre_lastVersion.version_code;
        return pre_lastVersion.version_code;
      }
    }
    return '';
  }

  protected async get_compare_users(version_code: string, d: DBSlardarCrashIssueList) {
    if (version_code in this.grouped_list) {
      const res = this.grouped_list[version_code].find(
        it => it.issue_id === d.issue_id && it.crash_type === d.crash_type && it.platform === d.platform,
      )?.users;
      if (res) {
        return res;
      }
    }
    return await this.slardarCrashIssueListModel.findPrevIssueListUsers(
      d.aid,
      d.issue_id,
      version_code,
      d.platform,
      d.crash_type,
    );
  }

  protected async get_start_version(d: DBSlardarCrashIssueList): Promise<string | undefined> {
    const start_version =
      this.issues.find(it => it.issue_id === d.issue_id && it.platform === d.platform)?.start_os_version ||
      (await this.slardarCrashIssueModel.getStartVersionByIssueId(d.aid, d.issue_id, d.platform));
    return start_version ? versionCodeToVersion(start_version) : undefined;
  }

  // TF灰度
  private async TFCrashCalCalculator(d: DBSlardarCrashIssueList) {
    return this.TFRetouchCalculator(d);
  }

  private async TFWatchDogCalculator(d: DBSlardarCrashIssueList) {
    return this.TFRetouchCalculator(d);
  }

  private async TFOOMCalculator(d: DBSlardarCrashIssueList) {
    return this.TFRetouchCalculator(d);
  }

  private async TFRetouchCalculator(d: DBSlardarCrashIssueList) {
    const start_version = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);
    const compare_version_code = await this.get_compare_version_code(d.version_code);
    const isNewIssue =
      start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1);

    // 不属于Top15或影响用户数小于2名的不处理
    if (d.ranking > 15 || d.users < 2) {
      return [3, '不属于Top15或影响用户数小于2'];
    }
    // 新增问题大于3例P1
    if (isNewIssue) {
      return d.users >= 3 ? [1, '新问题|用户大于3'] : [2, '新问题|用户小于3'];
    }
    // // 劣化问题超过50%为P1
    // if (!isNewIssue) {
    // 	if (d.version_code in this.userCountMap && compare_version_code in this.userCountMap) {
    // 		// 当前用户异常率
    // 		const curData = d.users / this.userCountMap[d.version_code];
    // 		const lastUsers = await this.get_compare_users(compare_version_code, d);
    // 		if (lastUsers) {
    // 			// 对比版本用户异常率
    // 			const lastData = lastUsers / this.userCountMap[compare_version_code];
    // 			const finalRate = (curData - lastData) / lastData;
    // 			if (finalRate >= 0.5) {
    // 				return 1;
    // 			} else if (finalRate >= 0.3) {
    // 				return 2;
    // 			}
    // 		}
    // 	}
    // }
    return [3, '未命中规则'];
  }

  private async DefaultCrashCalculator(d: DBSlardarCrashIssueList) {
    return this.DefaultRetouchOnlineCalculator(d);
  }

  private async DefaultWatchDogCalculator(d: DBSlardarCrashIssueList) {
    return this.DefaultRetouchOnlineCalculator(d);
  }

  private async DefaultOOMCalculator(d: DBSlardarCrashIssueList) {
    return this.DefaultRetouchOnlineCalculator(d);
  }

  // 醒图iOS提审版本定级
  private async DefaultRetouchOnlineCalculator(d: DBSlardarCrashIssueList) {
    const compare_version_code = await this.get_compare_version_code(d.version_code);
    const start_version = await this.get_start_version(d);
    const version = versionCodeToVersion(d.version_code);
    const isNewIssue =
      start_version?.substring(0, start_version?.length - 1) === version.substring(0, version.length - 1);

    // 不属于Top15或影响用户数小于2名的不处理
    if (d.ranking > 15 || d.users < 2) {
      return [3, '不属于Top15或影响用户数小于2'];
    }
    // 上个版本不存在则为新增问题，超过3例P2，超过5例P1
    if (isNewIssue) {
      if (d.users >= 5) {
        return [1, '新问题|用户大于5'];
      } else if (d.users >= 3) {
        return [2, '新问题|用户大于3'];
      }
    }
    // 劣化问题，超过50%为P1，30%到50%为P2
    if (!isNewIssue) {
      if (d.version_code in this.userCountMap && compare_version_code in this.userCountMap) {
        // 当前用户异常率
        const curData = d.users / this.userCountMap[d.version_code];
        const lastUsers = await this.get_compare_users(compare_version_code, d);
        if (lastUsers) {
          // 对比版本用户异常率
          const lastData = lastUsers / this.userCountMap[compare_version_code];
          const finalRate = (curData - lastData) / lastData;
          if (finalRate >= 0.5) {
            return [1, '劣化大于50%'];
          } else if (finalRate >= 0.3) {
            return [2, '劣化大于30%'];
          }
        }
      }
    }
    return [3, '未命中规则'];
  }
}
