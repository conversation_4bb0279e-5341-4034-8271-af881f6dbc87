import { Inject, Injectable } from '@gulux/gulux';
import LarkService from '@pa/backend/dist/src/third/lark';
import { CozeWorkFlowContextObject, CozeWorkFlowDebugInfo } from '@shared/coze/WorkFlow';
import { LarkBotMessageHookReceiveData } from '@pa/shared/dist/src/lark/chat';
import { MessageType } from '@pa/shared/dist/src/lark/larkCard';
import CozeService from '../service/third/coze';
import axios from 'axios';

interface ParsedSlardarUrlData {
  host: string;
  aid: number;
  os: string;
  region: string;
  subregion: string;
  type: string;
  lang: string;
  crash_type: string;
  event_id: string | undefined;
  issue_id: string | undefined;
  start_time: number | undefined;
  end_time: number | undefined;
  granularity: number | undefined;
  filters_conditions: any;
  filters_conditions_raw: string | undefined;
  order_by: any;
  pgno: any;
  pgsz: any;
  crash_time_type: any;
  anls_dim: any;
  token: any;
  token_type: any;
  IssueActiveTab: any;
  event_index: any;
}

function parseSlardarUrl2(url: string): ParsedSlardarUrlData | null {
  try {
    const u = new URL(url);

    const path = u.pathname;
    if (path !== '/node/app_detail/') {
      return null;
    }

    // 修复 URLSearchParams.entries() 错误
    const queryParams: Record<string, string> = {};
    u.searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });

    // 解析 hash 部分
    const [hashPathRaw, hashQueryRaw] = u.hash.slice(1).split('?');
    const hashPathList = hashPathRaw?.split('/') || [];

    let hashQueryParams: any = {};
    if (hashQueryRaw) {
      const queryObj = new URLSearchParams(hashQueryRaw);
      const rawParams = queryObj.get('params');
      if (rawParams) {
        hashQueryParams = JSON.parse(rawParams);
      }
    }

    let crash_type = hashPathList.length === 3 ? hashPathList[2] : hashPathList[3];
    if (crash_type === 'other_system_kill_unknown') {
      crash_type = 'other_system_kill';
    }

    const issue_id = hashPathList.length >= 5 ? hashPathList[4] : undefined;
    const event_id = hashPathList.length >= 6 ? hashPathList[5] : undefined;

    return {
      host: u.host, // 修复 host 属性错误
      aid: Number(queryParams.aid),
      os: queryParams.os,
      region: queryParams.region,
      subregion: queryParams.subregion,
      type: queryParams.type,
      lang: queryParams.lang,
      crash_type,
      event_id,
      issue_id,
      start_time: hashQueryParams?.start_time ? Number(hashQueryParams.start_time) : undefined,
      end_time: hashQueryParams?.end_time ? Number(hashQueryParams.end_time) : undefined,
      granularity: hashQueryParams?.granularity ? Number(hashQueryParams.granularity) : undefined,
      filters_conditions: hashQueryParams?.filters_conditions,
      filters_conditions_raw: hashQueryParams?.filters_conditions
        ? JSON.stringify(hashQueryParams.filters_conditions)
        : undefined,
      order_by: hashQueryParams?.order_by,
      pgno: hashQueryParams?.pgno,
      pgsz: hashQueryParams?.pgsz,
      crash_time_type: hashQueryParams?.crash_time_type,
      anls_dim: hashQueryParams?.anls_dim,
      token: hashQueryParams?.token,
      token_type: hashQueryParams?.token_type,
      IssueActiveTab: hashQueryParams?.IssueActiveTab,
      event_index: hashQueryParams?.event_index,
    };
  } catch (e) {
    return null;
  }
}

async function sendSlardarRequest(parsedData: ParsedSlardarUrlData | null, token: string): Promise<any> {
  if (!parsedData) {
    throw new Error('解析后的 URL 数据为空');
  }

  const requestBody = {
    aid: parsedData.aid,
    os: parsedData.os,
    start_time: parsedData.start_time,
    end_time: parsedData.end_time,
    crash_type: parsedData.crash_type,
    issue_id: parsedData.issue_id,
    pgsz: 1,
    pgno: parsedData.event_index,
    sdk: false,
  };

  const baseHost = parsedData.region === 'maliva' ? 'slardar-sg.tiktok-row.net' : 'slardar.bytedance.net';
  const url = `https://${baseHost}/api_v2/app/crash/event/list`;

  try {
    const response = await axios.post(url, requestBody, {
      headers: {
        'X-Jwt-Token': token,
      },
    });
    if (response.data && response.data.data && Array.isArray(response.data.data.result)) {
      return response.data.data.result.map((item: any) => ({
        crash_time: item.crash_time,
        device_id: item.device_id,
        event_id: item.event_id,
      }));
    }

    throw new Error('响应数据格式不符合预期');
  } catch (error) {
    console.error('请求失败', error);
    throw error;
  }
}

@Injectable()
export default class StackTraceService {
  @Inject()
  private lark: LarkService;
  @Inject()
  private coze: CozeService;

  getSlardarToken = (reqRegion: number) => {
    // todo： 得去数据库或者redis中获取token
    switch (reqRegion) {
      case 0:
        return '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      case 1:
        return '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      default:
        return '';
    }
  };
  async runCozeWorkflow(input: string): Promise<string | null> {
    const token = await this.coze.generateJWTToken();
    const url = 'https://api.coze.cn/v1/workflow/run';
    const requestBody = {
      workflow_id: '7532763316088209454',
      parameters: {
        input,
      },
    };

    try {
      const response = await axios.post(url, requestBody, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      if (response.data && response.data.code === 0 && response.data.data) {
        const parsedData = JSON.parse(response.data.data);
        return parsedData.output;
      }
      return 'Error Occur When analyzing stack trace!';
    } catch (error) {
      return 'Error Occur When analyzing stack trace!';
    }
  }
  async scheduleRequest(url: string): Promise<string | null> {
    // 解析 URL
    const parsedData = parseSlardarUrl2(url);

    // 根据 region 设置 token 参数：maliva 传 1，其他情况传 0
    const tokenParam = parsedData?.region === 'maliva' ? 1 : 0;
    const token = this.getSlardarToken(tokenParam);
    try {
      // 发送 Slardar 请求
      const responseData = await sendSlardarRequest(parsedData, token);
      if (responseData && responseData.length > 0) {
        const { event_id, device_id, crash_time } = responseData[0];
        if (parsedData && event_id && device_id && crash_time) {
          const baseHost = parsedData.region === 'maliva' ? 'slardar-sg.tiktok-row.net' : 'slardar.bytedance.net';
          const downloadUrl = `https://${baseHost}/api_v2/app/crash/event/log/download?symbolicate=true&event_id=${event_id}&device_id=${device_id}&crash_time=${crash_time}&aid=${parsedData.aid}&os=${parsedData.os}&lang=${parsedData.lang}&region=${parsedData.region}&type=${parsedData.type}`;
          const logResponse = await axios.get(downloadUrl, {
            headers: {
              'X-Jwt-Token': token,
            },
            responseType: 'text', // 设置响应类型为文本
          });
          return logResponse.data;
        }
      }
      return 'Error Occur!';
    } catch (error) {
      return 'Error Occur!';
    }
  }
  async sendStackTraceMessage(
    url: string,
    cozeResponse: CozeWorkFlowContextObject,
    cozeDebugInfo: CozeWorkFlowDebugInfo | undefined,
    receiveData: LarkBotMessageHookReceiveData,
  ): Promise<void> {
    const stackTrace = await this.scheduleRequest(url);
    if (stackTrace) {
      // 正则表达式匹配头部信息
      const headerRegex = /^([\s\S]*?)(?=Thread \d+ name:)/;
      const headerMatch = stackTrace.match(headerRegex);
      const header = headerMatch ? headerMatch[1].trim() : '';

      // 正则表达式匹配第一个崩溃线程的堆栈信息
      const firstThreadRegex = /(Thread \d+ name:[\s\S]*?)(?=Thread \d+ name:|$)/;
      const firstThreadMatch = stackTrace.match(firstThreadRegex);
      const firstThread = firstThreadMatch ? firstThreadMatch[1].trim() : '';

      // 合并头部信息和第一个崩溃线程的堆栈信息
      let messageText = `${header}\n\n${firstThread}`;

      // 飞书消息有长度限制（文本消息约15KB），我们在此进行安全截断
      const maxLength = 15000; // 设置一个安全的最大长度
      if (messageText.length > maxLength) {
        messageText = `${messageText.substring(0, maxLength)}\n... (日志过长，已被截断)`;
      }
      await this.runCozeWorkflow(messageText)
        .then(cozeAnalysis => {
          if (cozeAnalysis) {
            const cozeContent = JSON.stringify({ text: cozeAnalysis });
            return this.lark.replyMessage(cozeContent, receiveData.message.message_id, MessageType.text);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  }
}
