import { Api, Data, Get, Headers, Post, Query, useInject } from '@edenx/runtime/bff';
import LibraInfoModel from '../model/LibraInfoTable';
import { z } from 'zod';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { LibraControlService } from '../service/libra/LibraControlService';
import {
  ControlStatus,
  ExemptStatus,
  LibraControlReq,
  PaControlRecord,
  PaControlType,
} from '@shared/libra/libraControl';
import LibraService from '../service/third/libra';
import { getLibraVersionPlatformFromFilterRule } from '../utils/libraUtil';
import { LibraControlRecordModel } from '../model/LibraControlRecordTable';
import LibraAPIService from '../service/libra/LibraAPIService';
import LibraNewInfoUserSettingService from '../service/libra/LibraNewInfoUserSettingService';
import { LibraNewInfoListService } from '../service/libra/LibraNewInfoListService';
import { StoryRevenueTableColumnGroup } from '@shared/storyRevenueReviewPlatform/StoryRevenueUserSettings/StoryRevenueTableColumnUserSetting';
import FlightConclusionReportService from '../service/libra/FlightConclusionReportService';
import { LibraNotifyService } from '../service/libra/LibraNotifyService';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import { ExperimentErrorInfo } from '@shared/libra/common';
import { isSGLibraByLibraAppId, LibraRegion } from '@shared/libra/commonLibra';
import LibraNewInfoMemberService from '../service/libra/LibraNewInfoMemberService';
import { LibraBenefitsHiveService } from '../service/libra/LibraBenefitsHiveService';
import LibraChangeEventDao from '../dao/libra/LibraChangeEventDao';
import { calculateDayDiffResult, getAppNameByLibraAppId } from '@shared/libra/libraManageUtils';
import { getCNToken, getCNTokenAsync, getSGToken, getSGTokenAsync, GrayOrRelease } from '@shared/libra/LibraCreate';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { Flight, FlightReportRequest } from '@shared/libra/flight';
import { LibraHitResult } from '@pa/shared/dist/src/libra/LibraAttributionModel';
import { reportTeaEvent } from '@api/index';
import { LibraPaControlRecordService } from '../service/libra/LibraPaControlRecordService';
import { ManageType, Token } from '@shared/libra/NonOpen';
import { LibraCreateTemplateService } from '../service/libra/LibraCreateTemplateService';
import { PaExtraInfo } from '@shared/libra/LibraNewInfo';
import LibraControlRecordDetail from '@/component/LibraControlRecordDetail';
import LibraPaControlRecordDao from '../dao/libra/LibraPaControlRecordDao';
import { AbnormalFlightReportInfoService } from '../service/libra/AbnormalFlightReportInfoService';
import { LibraEventType } from '@shared/libra/libraInfo';
import SettingsService from '../service/third/settings';
import MeegoRawService from '../service/third/meego';
import LarkService from '@pa/backend/dist/src/third/lark';
import { UserData, UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { LibraPatrolService } from '../service/libra/LibraPatrol/LibraPatrolService';
import { FlightMetricConclusionParams } from '@shared/libra/FlightMetricConclusion';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { LibraDesignDocService } from '../service/libra/LibraDesignDocService';
import LibraNewInfoListDao from '../dao/libra/LibraNewInfoListDao';
import LibraPatrolConfigDao from '../dao/libra/LibraPatrolConfigDao';
import { LibraPatrolConfig } from '@shared/libra/LibraPatrolInfo';
import { libra_history } from '@api/libraNonOpen';

export const queryLibraInfoSchema = z.object({
  product: z.string(),
  platform: z.string(),
  version: z.string(),
});

export const libraInfoStatusSchema = z.object({
  id: z.string(),
  product: z.string(),
  version: z.string(),
  platform: z.string(),
  status: z.string(),
});

export const experimentTemplateSchema = z.object({
  name: z.string(),
  libraAppId: z.number(),
  manageType: z.nativeEnum(ManageType),
  grayOrRelease: z.string(),
  business: z.any().optional(),
  businessName: z.string(),
  owners: z.array(z.any()),
  basicParams: z.any(),
  psm: z.array(z.string()),
  creatorEmail: z.string(),
});
export const searchLibraInfoList = Api(Post(`/libra/infoList`), Data(queryLibraInfoSchema), async ({ data }) => {
  const libraModel = useInject(LibraInfoModel);
  return await libraModel.query(data.product, data.platform, data.version);
});

export const updateLibraInfo = Api(Post('/update_libra_info'), Data(libraInfoStatusSchema), async ({ data }) => {
  const libraModel = useInject(LibraInfoModel);
  const result = await libraModel.updateStatus(data.id, data.product, data.platform, data.version, data.status);
  if (result) {
    return { code: NetworkCode.Success, message: '更新成功' };
  } else {
    return {
      code: NetworkCode.Error,
      message: '找不到目标项，请刷新界面后重试',
    };
  }
});

export const libraControlWebhook = Api(
  Post('/control_webhook'),
  Data(z.any()),
  Headers(z.any()),
  async ({ data, headers }) => await useInject(LibraControlService).handleControl(data as LibraControlReq),
);

export const isThirdGray = Api(
  Post('/is_third_gray'),
  Data(
    z.object({
      version: z.string(),
      appId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(RpcProxyManager);
    const isAfter = await service.getHost().isAfterThirdGray(data.version, data.appId);
    return isAfter;
  },
);

export const isFullRelease = Api(
  Post('/is_full_release'),
  Data(
    z.object({
      version: z.string(),
      appId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(RpcProxyManager);
    const isAfter = await service.getHost().isFullRelease(data.version, data.appId);
    return isAfter;
  },
);

export const searchControlRecord = Api(
  Post('/search_control_record'),
  Data(
    z.object({
      create_ts: z.array(z.string()).optional(),
      event_type: z.array(z.string()).optional(),
      flight_id: z.string().optional(),
      current: z.number().optional(),
      pageSize: z.number().optional(),
      title: z.string().optional(),
      user: z.string().optional(),
      onlyChange: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const model = useInject(LibraControlService);
    return model.queryControlRecord(data);
  },
);

export const reCheckControl = Api(
  Post('/recheck_control'),
  Data(
    z.object({
      _id: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraControlService);
    return await service.reCheck(data._id);
  },
);

export const getControlDetail = Api(
  Post('/get_control_detail'),
  Data(
    z.object({
      _id: z.string(),
    }),
  ),
  async ({ data }) => {
    const model = useInject(LibraControlRecordModel);
    return await model.queryById(data._id);
  },
);

export const approveSkip = Api(
  Post('/approve_skip'),
  Data(
    z.object({
      _id: z.string(),
      loginEmail: z.string(),
      reason: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraControlService);
    return await service.approveSkip(data._id, data.loginEmail, data.reason);
  },
);

export const joinGroupChat = Api(
  Post('/join_group_chat'),
  Data(
    z.object({
      _id: z.string(),
      loginEmail: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraControlService);
    return await service.joinGroupChat(data._id, data.loginEmail);
  },
);

export const queryFlightIssueList = Api(
  Post('/query_flight_issue_list'),
  Data(
    z.object({
      flight_id: z.string(),
    }),
  ),
  async ({ data }) => await useInject(LibraControlService).queryFlightIssueList(data.flight_id),
);

export const queryFlightDetail = Api(
  Post('/query_flight_detail'),
  Data(z.object({ flight_id: z.string() })),
  async ({ data }) => {
    const flight = await useInject(LibraService).queryFlightDetailById(data.flight_id);
    const records = await useInject(LibraInfoModel).getActualTrafficRecord(data.flight_id);
    const isSG = isSGLibraByLibraAppId(flight?.app_id ?? 0);
    return {
      flight,
      platform: getLibraVersionPlatformFromFilterRule(
        flight?.filter_rule ?? [],
        isSG ? LibraRegion.SG : LibraRegion.CN,
        flight?.name ?? '',
        flight?.description ?? '',
      ),
      records,
    };
  },
);

export const cancelOperation = Api(
  Post('/cancel_operation'),
  Data(
    z.object({
      _id: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraControlService);
    return await service.cancelOperation(data._id);
  },
);

export const queryFlightErrorList = Api(
  Post('/query_flight_error_list'),
  Data(
    z.object({
      flight_id: z.string(),
    }),
  ),
  async ({ data }) =>
    (await useInject(RpcProxyManager).getHost().fetchExperimentErrorList(data.flight_id)) as ExperimentErrorInfo[],
);

export const testQueryLVAllFlightList = Api(Post('/test_query_lv_all_flight_list'), Data(z.any()), async ({ data }) => {
  const service = useInject(LibraAPIService);
  return await service.queryLVAllFlightList();
});

/**
 * ===========================================
 * Section: 实验列表-Table column 相关
 * ===========================================
 */

export const fetchFlightTableColumnSetting = Api(
  Post('/flight_manager_platform/fetch_flight_table_column_setting'),
  Data(
    z.object({
      userEmail: z.string(),
    }),
  ),
  async ({ data }) => {
    const userSettingService = useInject(LibraNewInfoUserSettingService);
    const res = await userSettingService.fetchTableColumnSetting(data.userEmail);
    if (!res) {
      return {
        code: 1,
        groups: [],
      };
    } else {
      return {
        code: 0,
        groups: res,
      };
    }
  },
);

export const updateFlightTableColumnSetting = Api(
  Post('/flight_manager_platform/update_flight_table_column_setting'),
  Data(
    z.object({
      userEmail: z.string(),
      newSetting: z.any(),
    }),
  ),
  async ({ data }) => {
    await useInject(LibraNewInfoUserSettingService).updateColumnSetting(
      data.userEmail,
      data.newSetting as StoryRevenueTableColumnGroup[],
    );
    return {};
  },
);

/**
 * ===========================================
 * Section: Libra数据拉取\更新相关
 * ===========================================
 */

export const fetchFlightList = Api(
  Post('/flight_manager_platform/fetch_flight_list'),
  Data(
    z.object({
      region: z.number().optional(),
      args: z.any(),
      page: z.number(),
      pageSize: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    const res = await libraService.list(data.args, data.page, data.pageSize);
    const total = await libraService.count(data.args);
    return {
      code: 0,
      data: res,
      total,
    };
  },
);

export const fetchOneFlight = Api(
  Post('/flight_manager_platform/fetch_one_flight'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    const res = await libraService.findOne({ 'flightInfo.id': data.flightId });
    return {
      code: 0,
      data: res,
    };
  },
);
export const fetchFlightBusinessById = Api(
  Post('/flight_manager_platform/fetch_flight_business_by_id'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const logger = useInject(BytedLogger);
    try {
      const id = data.flightId;
      const newInfo = await useInject(LibraNewInfoListDao).findOne({ 'flightInfo.id': id });
      logger.info('----------------字段修复-----------------');
      logger.info(String(id));
      logger.info(JSON.stringify(newInfo));
      logger.info(newInfo?.meegoInfo?.[0]?.businessLine?.[0] ?? '');
      logger.info('----------------字段修复-----------------');
      let allBusinessLines: string[] = [];
      if (newInfo && newInfo.meegoInfo && Array.isArray(newInfo.meegoInfo)) {
        newInfo.meegoInfo.forEach(item => {
          if (item && Array.isArray(item.businessLine)) {
            // 将每个 businessLine 数组合并到 allBusinessLines 中
            allBusinessLines = allBusinessLines.concat(item.businessLine);
          }
        });
      }
      return allBusinessLines;
    } catch (error) {
      logger.info('----------------字段修复-----------------');
      logger.error(error as Error);
      logger.error('-----------------------------');
      return [];
    }
  },
);
export const fetchFlightListAll = Api(
  Post('/flight_manager_platform/fetch_flight_list_all'),
  Data(
    z.object({
      args: z.any(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    const res = await libraService.listAll(data.args);
    return {
      code: 0,
      data: res,
    };
  },
);

export const fetchVersionFlightList = Api(
  Post('/flight_manager_platform/fetch_version_flight_list'),
  Data(
    z.object({
      libraAppId: z.number(),
      platform: z.string(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const appName = getAppNameByLibraAppId(data.libraAppId);
    const meegoVersion = `${appName === 'CapCut' ? 'CC' : appName}-${data.platform}-${data.version}`;
    const libraService = useInject(LibraNewInfoListService);
    const filter = {
      libraAppId: data.libraAppId,
      'meegoInfo.releaseVersion': {
        $regex: meegoVersion,
      },
    };
    const res = await libraService.list(filter, 0, Number.MAX_SAFE_INTEGER);
    const total = await libraService.count(filter);
    return {
      code: 0,
      data: res,
      total,
    };
  },
);

export const createLibraNewInfo = Api(
  Post('/flight_manager_platform/create_libra_new_info'),
  Data(
    z.object({
      region: z.number(),
      app: z.number(),
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    await libraService.createLibraNewInfo(data.region, data.app, data.flightId);
    return {
      code: 0,
      message: 'success',
    };
  },
);
export const createLibraReleaseByGray = Api(
  Post('/flight_manager_platform/create_libra_release_by_gray'),
  Data(
    z.object({
      tokens: z.any(),
      appId: z.number(),
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(LibraNotifyService);
    return await libraService.createReleaseFromGray(data.flightId, data.tokens, data.appId);
  },
);
export const textCloseLibraCard = Api(
  Post('/flight_manager_platform/text_close_libra_card'),
  Data(
    z.object({
      appId: z.number(),
      fullVersionDesc: z.string(),
      startTime: z.number(),
    }),
  ),
  async ({ data }) =>
    await useInject(LibraNotifyService).closeGrayNotify(
      data.appId,
      '',
      data.fullVersionDesc,
      data.fullVersionDesc,
      data.startTime,
    ),
);
export const updateLibraNewInfo = Api(
  Post('/flight_manager_platform/update_libra_new_info'),
  Data(
    z.object({
      libraInfo: z.any(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    return await libraService.update(data.libraInfo);
  },
);

export const batchCreateLibraNewInfo = Api(
  Post('/flight_manager_platform/batch_update_libra_new_info'),
  Data(z.any()),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    await libraService.updateExistedLibraNewInfo();
    await libraService.batchCreateLibraNewInfo(LibraRegion.CN, 147);
    await libraService.batchCreateLibraNewInfo(LibraRegion.SG, 305);
    await libraService.batchCreateLibraNewInfo(LibraRegion.SG, 295);
  },
);

export const backTrackLibraNewInfo = Api(
  Post('/flight_manager_platform/libra_new_info_backtrack'),
  Data(z.any()),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    await libraService.backTrackHistoryLibraInfo();
  },
);

export const updateLibraNewInfoVersion = Api(
  Post('/flight_manager_platform/update_libra_new_info_version'),
  Data(z.any()),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    await libraService.updateLibraInfoVersions();
  },
);

export const clearAllLibraNewInfo = Api(
  Post('/flight_manager_platform/clear_all_info'),
  Data(z.any()),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    await libraService.clearLibraNewInfo();
  },
);

/**
 * ===========================================
 * Section: Libra 实验通知&提醒相关
 * ===========================================
 */
// 提醒开启灰度实验
export const launchGrayNotify = Api(
  Post('/flight_manager_platform/launch_gray_notify'),
  Data(
    z.object({
      appId: z.number(),
      grayVersion: z.string(),
      fullVersionDesc: z.string(),
      versionStageName: z.string(),
      startTime: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraNotifyService = useInject(LibraNotifyService);
    await libraNotifyService.launchGrayNotify(
      data.appId,
      data.grayVersion,
      data.fullVersionDesc,
      data.versionStageName,
      data.startTime,
    );
    return {
      code: 0,
      message: 'success',
    };
  },
);

// 提醒开启正式实验
export const launchReleaseNotify = Api(
  Post('/flight_manager_platform/launch_release_notify'),
  Data(
    z.object({
      appId: z.number(),
      releaseVersion: z.string(),
      fullVersionDesc: z.string(),
      versionStageName: z.string(),
      startTime: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraNotifyService = useInject(LibraNotifyService);
    await libraNotifyService.launchReleaseNotify(
      data.appId,
      data.releaseVersion,
      data.fullVersionDesc,
      data.versionStageName,
      data.startTime,
    );
    return {
      code: 0,
      message: 'success',
    };
  },
);

// 提醒定时 Review 实验数据
export const remindDataReviewNotify = Api(
  Post('/flight_manager_platform/remind_data_review_notify'),
  Data(z.any()),
  async ({ data }) => {
    const libraNotifyService = useInject(LibraNotifyService);
    await libraNotifyService.remindDataReviewNotify();
    return {
      code: 0,
      message: 'success',
    };
  },
);

// “不再提醒”开启灰度实验
export const forbidLaunchGrayNotify = Api(
  Post('/flight_manager_platform/forbid_launch_gray_notify'),
  Data(
    z.object({
      libraInfo: z.any(),
    }),
  ),
  async ({ data }) => {
    const libraNotifyService = useInject(LibraNotifyService);
    await libraNotifyService.forbidLaunchGrayNotify(data.libraInfo);
    return {
      code: 0,
      message: 'success',
    };
  },
);

// “不再提醒”开启正式实验
export const forbidLaunchReleaseNotify = Api(
  Post('/flight_manager_platform/forbid_launch_release_notify'),
  Data(
    z.object({
      libraInfo: z.any(),
    }),
  ),
  async ({ data }) => {
    const libraNotifyService = useInject(LibraNotifyService);
    await libraNotifyService.forbidLaunchReleaseNotify(data.libraInfo);
    return {
      code: 0,
      message: 'success',
    };
  },
);

/**
 * ===========================================
 * Section: Libra数据更新
 * ===========================================
 */

export const editFlightInfo = Api(
  Post('/flight_manager_platform/edit_flight_info'),
  Data(
    z.object({
      flightId: z.number(),
      libraAppId: z.number(),
      keyPath: z.string(),
      newValue: z.any(),
      tmpValue: z.any(),
      editUserEmail: z.string(),
      editConfig: z.any().optional(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(LibraNewInfoListService);
    return await libraService.editFlightInfo(
      data.flightId,
      data.libraAppId,
      data.keyPath,
      data.newValue,
      data.tmpValue,
      data.editUserEmail,
      data.editConfig,
    );
  },
);

/**
 * ===========================================
 * Section: 实验报告相关
 * ===========================================
 */
export const generateFlightReport = Api(
  Post('/flight_manager_platform/generate_flight_report'),
  Data(
    z.object({
      flightId: z.number(),
      region: z.number(),
      ownerEmail: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const reportService = useInject(FlightConclusionReportService);
    const docUrl = await reportService.generateFlightConclusionReportDoc(data.region, data.flightId, data.ownerEmail);
    const libraNewInfoService = useInject(LibraNewInfoListService);
    const libraInfo = await libraNewInfoService.findOne({ 'flightInfo.id': data.flightId });
    return {
      code: 0,
      message: 'success',
      docUrl,
      libraInfo,
    };
  },
);

/**
 * ===========================================
 * Section: Libra 收益 Hive 表相关
 * ===========================================
 */
export const createLarkSpreadsheetForQueryHive = Api(
  Post('/libra/create_lark_spreadsheet_for_query_hive'),
  Data(
    z.object({
      title: z.string(),
      folderToken: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraBenefitsHiveService);
    return await service.createLarkSpreadsheet(data.title, data.folderToken);
  },
);

export const updateLarkSpreadsheetForQueryHive = Api(
  Post('/libra/update_lark_spreadsheet_for_query_hive'),
  Data(
    z.object({
      spreadsheet_token: z.string(),
      queryHiveInfoList: z.array(
        z.object({
          flight_id: z.string(),
          search_date: z.string(),
          region: z.string(),
        }),
      ),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraBenefitsHiveService);
    return await service.updateLarkSpreadsheetWithQueryHiveInfoList(
      data.spreadsheet_token,
      data.queryHiveInfoList,
      true,
      true,
    );
  },
);

export const createLibraBenefitsInfo = Api(
  Post('/libra/create_libra_benefits_info'),
  Data(
    z.object({
      app_id: z.number(),
      app: z.string(),
      token: z.string(),
      flight_id: z.number(),
      display_name: z.string(),
      flight_end_date: z.string(),
      flight_start_date: z.string(),
      flight_owner: z.string(),
      latest_flight_status: z.number(),
      flight_layer: z.string(),
      flight_version_resource: z.number(),
      flight_hash_strategy: z.string(),
      flight_tags: z.string(),
      vid_infos: z.array(
        z.object({
          vid: z.number(),
          benefits: z.array(
            z.object({
              metric_id: z.number(),
              metric_name: z.string(),
              metric_group_id: z.number(),
              metric_group_name: z.string(),
              significance: z.number(),
              relative_diff: z.number(),
              absolute_diff: z.number(),
              p_value: z.number(),
              margin: z.number(),
              mde: z.number(),
              metric_value: z.number(),
            }),
          ),
        }),
      ),
      search_date: z.string(),
      region: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraBenefitsHiveService);
    return await service.createLibraBenefitsInfo(data);
  },
);

export const replaceLibraBenefitsInfo = Api(
  Post('/libra/replace_libra_benefits_info'),
  Data(
    z.object({
      app_id: z.number(),
      app: z.string(),
      token: z.string(),
      flight_id: z.number(),
      display_name: z.string(),
      flight_end_date: z.string(),
      flight_start_date: z.string(),
      flight_owner: z.string(),
      latest_flight_status: z.number(),
      flight_layer: z.string(),
      flight_version_resource: z.number(),
      flight_hash_strategy: z.string(),
      flight_tags: z.string(),
      vid_infos: z.array(
        z.object({
          vid: z.number(),
          benefits: z.array(
            z.object({
              metric_id: z.number(),
              metric_name: z.string(),
              metric_group_id: z.number(),
              metric_group_name: z.string(),
              significance: z.number(),
              relative_diff: z.number(),
              absolute_diff: z.number(),
              p_value: z.number(),
              margin: z.number(),
              mde: z.number(),
              metric_value: z.number(),
            }),
          ),
        }),
      ),
      search_date: z.string(),
      region: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraBenefitsHiveService);
    return await service.replaceLibraBenefitsInfo(data);
  },
);

/**
 * ===========================================
 * Section: 风神看板接口
 * ===========================================
 */
export const allLibraDashboardData = Api(
  Post('/libra/all_libra_dashboard_data'),
  Query(
    z.object({
      page: z.string(),
    }),
  ),
  Data(z.any()),
  async ({ data, query: { page } }) => {
    const intPage = parseInt(page, 10);
    if (intPage < 1) {
      return {
        code: -1,
        data: [],
        message: '[page] should not be less than 1',
      };
    }
    const pageSize = 50;
    const service = useInject(LibraBenefitsHiveService);
    const list = await service.queryLibraBenefitsInfoList(data, intPage, pageSize);
    const libraBenefitsInfoList = list.map(item => item.toObject()) ?? [];
    const flattenList = await service.flattenLibraBenefitsInfoListByMetricId(libraBenefitsInfoList);
    const total = await service.queryLibraBenefitsInfoListCount(data);
    const totalPages = Math.ceil(total / pageSize);
    const hasMore = intPage < totalPages;
    return {
      code: 0,
      hasMore,
      data: flattenList,
    };
  },
);

export const allLibraDashboardDataByFlightId = Api(
  Post('/libra/all_libra_dashboard_data_by_flight_id'),
  Query(
    z.object({
      page: z.string(),
    }),
  ),
  Data(z.any()),
  async ({ data, query: { page } }) => {
    const intPage = parseInt(page, 10);
    if (intPage < 1) {
      return {
        code: -1,
        data: [],
        message: '[page] should not be less than 1',
      };
    }
    const pageSize = 100;
    const service = useInject(LibraBenefitsHiveService);
    const list = await service.queryLibraBenefitsInfoList(data, intPage, pageSize);
    const libraBenefitsInfoList = list.map(item => item.toObject()) ?? [];
    const flattenList = await service.flattenLibraBenefitsInfoListByFlightId(libraBenefitsInfoList);
    const total = await service.queryLibraBenefitsInfoListCount(data);
    const totalPages = Math.ceil(total / pageSize);
    const hasMore = intPage < totalPages;
    return {
      code: 0,
      hasMore,
      data: flattenList,
    };
  },
);

// 实验列表信息
export const allLibraListForDashboard = Api(
  Post('/libra/all_libra_list_for_dashboard'),
  Query(
    z.object({
      page: z.string(),
    }),
  ),
  Data(z.any()),
  async ({ data, query: { page } }) => {
    const intPage = parseInt(page, 10);
    if (intPage < 1) {
      return {
        code: -1,
        data: [],
        message: '[page] should not be less than 1',
      };
    }
    const pageSize = 100;
    const service = useInject(LibraNewInfoListService);
    const list = await service.listNoSort(data, intPage, pageSize);
    const listForDashboard = await service.convertLibraInfoListForAeolusDashboard(list);
    const total = await service.count(data);
    const totalPages = Math.ceil(total / pageSize);
    const hasMore = intPage < totalPages;
    return {
      code: 0,
      hasMore,
      data: listForDashboard,
    };
  },
);

/**
 * ===========================================
 * Section: 获取变更记录
 * ===========================================
 */
export const fetchFlightEventList = Api(
  Post('/flight_manager_platform/fetch_flight_event_list'),
  Data(
    z.object({
      args: z.any(),
      page: z.number(),
      pageSize: z.number(),
    }),
  ),
  async ({ data }) => {
    const eventDao = useInject(LibraChangeEventDao);
    const res = await eventDao.list(data.args, data.page, data.pageSize);
    const total = await eventDao.count(data.args);
    return {
      code: 0,
      data: res,
      total,
    };
  },
);
export const testLibraCloseAttributionNotFinishedBatchNotifyByMeegoTeam = Api(
  Post('/flight_manager_platform/test_Libra_close_attribution_not_finished_batch_notify_by_meego_team'),
  Data(
    z.object({
      sendPrivate: z.boolean(),
    }),
  ),
  async ({ data }) => {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    const startTimeStr = '2025-04-01 00:00:00';

    // 计算昨日23:59:59时间戳
    const endTimeStr = dayjs()
      .tz('Asia/Shanghai')
      .subtract(1, 'day')
      .endOf('day')
      .subtract(1, 'millisecond')
      .format('YYYY-MM-DD HH:mm:ss');
    await useInject(LibraNewInfoListService).libraCloseAttributionNotFinishedBatchNotifyByMeegoTeam(
      startTimeStr,
      endTimeStr,
      false,
      data.sendPrivate,
      undefined,
      true,
    );
  },
);
export const fetchFlightEventListByTimeRange = Api(
  Post('/flight_manager_platform/fetch_flight_events_by_time_range'),
  Data(
    z.object({
      // 必选参数：时间范围数组，每个元素为包含 startTime 和 endTime 的对象
      timeRanges: z.array(
        z.object({
          startTime: z.number(),
          endTime: z.number(),
        }),
      ),
      // 可选参数：skip
      page: z.number().optional(),
      // 可选参数：limit
      pageSize: z.number().optional(),
      // 可选参数：字符串数组业务线
      businessLines: z.array(z.string()).optional(),
      // 可选参数：字符串数组变更类型
      eventTypes: z.array(z.string()).optional(),
      // 可选参数：实验 ID 数组
      flightIds: z.array(z.number()).optional(),
      // 可选参数：实验名称数组
      flightNames: z.array(z.string()).optional(),
      // 可选参数：操作人数组
      eventOperators: z.array(z.string()).optional(),
    }),
  ),
  async ({ data }) => {
    const eventDao = useInject(LibraChangeEventDao);
    // 构建查询条件
    const query: any = {};
    const timeRangeConditions = data.timeRanges.map(range => ({
      ts: { $gte: range.startTime, $lte: range.endTime },
    }));
    query.$and = [{ $or: timeRangeConditions }];

    if (data.businessLines && data.businessLines.length > 0) {
      query['libraNewInfo.meegoInfo.businessLine'] = { $in: data.businessLines };
    }
    if (data.eventTypes && data.eventTypes.length > 0) {
      const validEventTypes = Object.values(LibraEventType);
      const invalidTypes = data.eventTypes.filter(type => {
        // 尝试将 string 转换为 LibraEventType
        const eventType = type as LibraEventType;
        return !validEventTypes.includes(eventType);
      });
      if (invalidTypes.length > 0) {
        return {
          code: 1, // 自定义错误码
          message: `无效的 eventTypes: ${invalidTypes.join(', ')}`,
        };
      }
      query.eventType = { $in: data.eventTypes };
    }
    if (data.flightIds && data.flightIds.length > 0) {
      query.flightId = { $in: data.flightIds };
    }
    if (data.flightNames && data.flightNames.length > 0) {
      query.flightName = { $in: data.flightNames };
    }
    if (data.eventOperators && data.eventOperators.length > 0) {
      query.eventOperator = { $in: data.eventOperators };
    }

    let result: any[] = [];
    const page = data.page || 1;
    const pageSize = data.pageSize || 100;
    const total = await eventDao.count(query);
    if (data.page !== undefined && data.pageSize !== undefined) {
      result = await eventDao.list(query, data.page, data.pageSize);
    } else {
      result = await eventDao.listAll(query);
    }
    const totalPages = Math.ceil(total / pageSize);
    const hasMore = page < totalPages;

    return {
      code: 0,
      count: result.length,
      data: result,
      page,
      pageSize,
      total,
      totalPages,
      hasMore,
    };
  },
);
// 更新变更记录
export const updateFlightOneEvent = Api(
  Post('/flight_manager_platform/fetch_flight_save_one_event'),
  Data(
    z.object({
      data: z.any(),
    }),
  ),
  async ({ data }) => {
    const eventDao = useInject(LibraChangeEventDao);
    return await eventDao.update(data.data);
  },
);
// 获取昨日变更的所有实验，并完成话题群播报
export const flightChangeNotifyOfYesterday = Api(
  Post('/flight_manager_platform/flight_change_notify_of_yesterday'),
  Data(
    z.object({
      sendPrivate: z.boolean().optional(),
      customQuery: z.any().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const flightList = await service.libraChangeNotifyOfYesterday(data.sendPrivate, data.customQuery);
    return {
      code: 0,
      message: 'success',
      data: flightList,
    };
  },
);

// 获取昨日填写了实验关闭归因的实验列表，并完成话题群播报
export const flightCloseAttributionFinishedNotifyOfYesterday = Api(
  Post('/flight_manager_platform/flight_close_attribution_finished_notify_of_yesterday'),
  Data(
    z.object({
      sendPrivate: z.boolean().optional(),
      customQuery: z.any().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const flightList = await service.libraCloseAttributionFinishedNotifyOfYesterday(data.sendPrivate, data.customQuery);
    return {
      code: 0,
      message: 'success',
      data: flightList,
    };
  },
);

// 获取本周尚未填写实验关闭归因的实验列表，并完成话题群播报
export const flightCloseAttributionNotFinishedNotifyOfThisWeek = Api(
  Post('/flight_manager_platform/flight_close_attribution_not_finished_notify_of_this_week'),
  Data(
    z.object({
      sendPrivate: z.boolean().optional(),
      customQuery: z.any().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const flightList = await service.libraCloseAttributionNotFinishedNotifyOfThisWeek(
      data.sendPrivate,
      data.customQuery,
    );
    return {
      code: 0,
      message: 'success',
      data: flightList,
    };
  },
);

// 获取上周尚未填写实验关闭归因的实验列表，并完成话题群播报
export const flightCloseAttributionNotFinishedNotifyOfLastWeek = Api(
  Post('/flight_manager_platform/flight_close_attribution_not_finished_notify_of_last_week'),
  Data(
    z.object({
      sendPrivate: z.boolean().optional(),
      customQuery: z.any().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const flightList = await service.libraCloseAttributionNotFinishedNotifyOfLastWeek(
      data.sendPrivate,
      data.customQuery,
    );
    return {
      code: 0,
      message: 'success',
      data: flightList,
    };
  },
);

// 尚未填写实验关闭归因的实验列表通知，并且自定义时间段、自定义标题、自定义 Meego 虚拟团队、自定义 chatId
export const flightCloseAttributionNotFinishedNotifyOfCustomDaysDuration = Api(
  Post('/flight_manager_platform/flight_close_attribution_not_finished_notify_of_custom_days_duration'),
  Data(
    z.object({
      startTimeStr: z.string(),
      endTimeStr: z.string(),
      customTitle: z.string(),
      meegoTeamIds: z.array(z.number()),
      chatId: z.string(),
      sendPrivate: z.boolean().optional(),
      customQuery: z.any().optional(),
      inviteLibraOperatorsJoinGroup: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    await service.libraCloseAttributionNotFinishedNotifyOfCustomDaysDuration(
      data.startTimeStr,
      data.endTimeStr,
      data.customTitle,
      data.meegoTeamIds,
      data.chatId,
      data.sendPrivate,
      data.customQuery,
      data.inviteLibraOperatorsJoinGroup,
    );
    return {
      code: 0,
      msg: 'succes',
    };
  },
);

//
export const flightCloseAttributionNotFinishedBatchNotifyOfCustomDaysDurationByMeegoTeam = Api(
  Post('/flight_manager_platform/flight_close_attribution_not_finished_batch_notify_by_meego_team'),
  Data(
    z.object({
      startTimeStr: z.string(),
      endTimeStr: z.string(),
      shouldCreateLarkGroup: z.boolean().optional(),
      sendPrivate: z.boolean().optional(),
      customQuery: z.any().optional(),
      inviteLibraOperatorsJoinGroup: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    await service.libraCloseAttributionNotFinishedBatchNotifyByMeegoTeam(
      data.startTimeStr,
      data.endTimeStr,
      data.shouldCreateLarkGroup,
      data.sendPrivate,
      data.customQuery,
      data.inviteLibraOperatorsJoinGroup,
    );
    return {
      code: 0,
      msg: 'success',
    };
  },
);

// 批量推送：尚未填写实验关闭归因的实验列表通知，并且自定义时间段、自定义标题、自定义 Meego 虚拟团队、自定义 chatId
export const flightCloseAttributionNotFinishedBatchNotifyOfCustomDaysDuration = Api(
  Post('/flight_manager_platform/flight_close_attribution_not_finished_batch_notify'),
  Data(
    z.object({
      startTimeStr: z.string(),
      endTimeStr: z.string(),
      chatId: z.string().optional(),
      sendPrivate: z.boolean().optional(),
      customQuery: z.any().optional(),
      inviteLibraOperatorsJoinGroup: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    await service.libraCloseAttributionNotFinishedBatchNotifyOfCustomDaysDuration(
      data.startTimeStr,
      data.endTimeStr,
      data.chatId,
      data.sendPrivate,
      data.customQuery,
      data.inviteLibraOperatorsJoinGroup,
    );
    return {
      code: 0,
      msg: 'success',
    };
  },
);

/**
 * ===========================================
 * Section: 获取豁免记录
 * ===========================================
 */
export const fetchExemptEventList = Api(
  Post('/flight_manager_platform/fetch_exempt_event_list'),
  Data(
    z.object({
      args: z.any(),
      page: z.number(),
      pageSize: z.number(),
    }),
  ),
  async ({ data }) => {
    const eventDao = useInject(LibraPaControlRecordDao);
    const res = await eventDao.list(data.args, data.page, data.pageSize);
    const total = await eventDao.count(data.args);
    return {
      code: 0,
      data: res,
      total,
    };
  },
);

/**
 * ===========================================
 * Section: 实验周报生成
 * ===========================================
 */
export const genWeeklyReportsByBusinessLine = Api(
  Post('/flight_manager_platform/gen_weekly_reports_by_business_line'),
  Data(
    z.object({
      businessLine: z.string(),
    }),
  ),
  async ({ data }) => {
    // @TODO 考虑扩充，将来不一定是分业务线，可能分小组
    const service = useInject(LibraBenefitsHiveService);
    const libraList = await service.genWeeklyReportsByBusinessLine(data.businessLine);
    return {
      code: 0,
      data: libraList,
    };
  },
);

/**
 * ===========================================
 * Section: 权限相关
 * ===========================================
 */
export const fetchUserMemberList = Api(
  Post('/flight_manager_platform/permission/user/fetch'),
  Data(
    z.object({
      query: z.any(),
    }),
  ),
  async ({ data }) => {
    const memberService = useInject(LibraNewInfoMemberService);
    const userMemberList = await memberService.getUserMemberList(data.query);
    return {
      code: 0,
      message: 'success',
      userMemberList: userMemberList ?? [],
    };
  },
);

export const fetchBusinessMemberList = Api(
  Post('/flight_manager_platform/permission/business/fetch'),
  Data(
    z.object({
      query: z.any(),
    }),
  ),
  async ({ data }) => {
    const memberService = useInject(LibraNewInfoMemberService);
    const businessMemberList = await memberService.getBusinessMemberList(data.query);
    return {
      code: 0,
      message: 'success',
      businessMemberList: businessMemberList ?? [],
    };
  },
);

export const updateLibraUserMember = Api(
  Post('/flight_manager_platform/permission/user/update'),
  Data(
    z.object({
      userEmail: z.string(),
      roleType: z.number(),
    }),
  ),
  async ({ data }) => {
    const memberService = useInject(LibraNewInfoMemberService);
    await memberService.saveOrUpdateUserMember(data.userEmail, data.roleType);
    return {
      code: 0,
      message: 'success',
    };
  },
);

export const updateLibraBusinessMember = Api(
  Post('/flight_manager_platform/permission/business/update'),
  Data(
    z.object({
      businessLine: z.string(),
      roleType: z.number(),
    }),
  ),
  async ({ data }) => {
    const memberService = useInject(LibraNewInfoMemberService);
    await memberService.saveOrUpdateBusinessMember(data.businessLine, data.roleType);
    return {
      code: 0,
      message: 'success',
    };
  },
);
export const deleteUserMember = Api(
  Post('/flight_manager_platform/permission/user/delete'),
  Data(
    z.object({
      userEmail: z.string(),
    }),
  ),
  async ({ data }) => {
    const memberService = useInject(LibraNewInfoMemberService);
    await memberService.deleteUserMember(data.userEmail);
    return {
      code: 0,
      message: 'success',
    };
  },
);
export const deleteBusinessMember = Api(
  Post('/flight_manager_platform/permission/business/delete'),
  Data(
    z.object({
      businessLine: z.string(),
    }),
  ),
  async ({ data }) => {
    const memberService = useInject(LibraNewInfoMemberService);
    await memberService.deleteBusinessMember(data.businessLine);
    return {
      code: 0,
      message: 'success',
    };
  },
);

export const getRoleTypeByEmail = Api(
  Post('/flight_manager_platform/permission/role_type'),
  Data(
    z.object({
      email: z.string(),
    }),
  ),
  async ({ data }) => {
    const memberService = useInject(LibraNewInfoMemberService);
    const roleType = await memberService.getRoleTypeByEmail(data.email);
    return {
      code: 0,
      message: 'success',
      roleType,
    };
  },
);

export const createLibraSuccess = Api(
  Post('/flight_manager_platform/create_libra_success'),
  Data(
    z.object({
      region: z.nativeEnum(LibraRegion),
      appId: z.number(),
      flightId: z.number(),
      extraInfo: z.object({
        grayOrRelease: z.string(),
        isReopen: z.boolean(),
        reopenReason: z.array(z.string()),
        dataRecoveryRemind: z.array(z.number()),
      }),
    }),
  ),
  async ({ data }) => {
    useInject(BytedLogger).info('createLibraSuccess', data);
    const service = useInject(LibraNewInfoListService);
    const extraInfo: PaExtraInfo = {
      grayOrRelease: data.extraInfo.grayOrRelease as GrayOrRelease,
      isReopen: data.extraInfo.isReopen,
      reopenReason: data.extraInfo.reopenReason,
      dataRecoveryRemind: data.extraInfo.dataRecoveryRemind,
    };
    const info = await service.createLibraNewInfo(data.region, data.appId, data.flightId, extraInfo);
    if (info) {
      await useInject(LibraNotifyService).notifyWhenLibraCreate(info);
    }
    return {
      code: 0,
      message: 'success',
      data: info,
    };
  },
);

export const getFlightList = Api(
  Post('/flight_manager_platform/get_flight_list'),
  Data(
    z.object({
      args: z.any(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const res = await service.listAll(data.args);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);
// 获取 Meego 团队（推荐，通过纸飞机数据库查询）
// 说明：数据查询更快，但数据可能不实时
export const getMeegoTeamList = Api(
  Post('/flight_manager_platform/get_meego_team_list'),
  Data(
    z.object({
      teamIds: z.array(z.number()),
      projectKey: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.getMeegoTeamList(data.teamIds, data.projectKey);
  },
);

// 获取 Meego 团队（通过 Meego API）
// 说明：数据查询较慢，但数据是实时的
export const getMeegoTeamByMeegoAPI = Api(
  Post('/flight_manager_platform/get_meego_team_list_by_meego_api'),
  Data(
    z.object({
      teamIds: z.array(z.number()),
      projectKey: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.getMeegoTeamByMeegoAPI(data.teamIds, data.projectKey);
  },
);

// 更新 Meego 团队（需要先通过 Meego API 查询数据，然后更新到纸飞机数据库）
export const updateMeegoTeamList = Api(
  Post('/flight_manager_platform/update_meego_team_list'),
  Data(
    z.object({
      teamIds: z.array(z.number()),
      projectKey: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateMeegoTeamList(data.teamIds, data.projectKey);
  },
);

// 更新单个 Meego 团队的飞书群 Id
export const updateChatIdOfMeegoTeamInfo = Api(
  Post('/flight_manager_platform/update_chat_id_of_meego_team_info'),
  Data(
    z.object({
      teamId: z.number(),
      chatId: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateChatIdOfMeegoTeamInfo(data.teamId, data.chatId);
  },
);

// 为某个 LibraInfo 更新主责团队 MainTeamId
export const updateMainTeamOfLibraInfo = Api(
  Post('/flight_manager_platform/update_main_team_of_libra_info'),
  Data(
    z.object({
      flightId: z.number(),
      libraAppId: z.number(),
      mainTeamId: z.number(),
      shouldClear: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateMainTeamOfLibraInfo(data.flightId, data.libraAppId, data.mainTeamId, data.shouldClear);
  },
);

// 获取 Meego 团队数据源（用于 TreeSelect）
export const getMeegoTeamTreeSelectDataSource = Api(
  Post('/flight_manager_platform/get_meego_team_tree_select_data_source'),
  Data(z.object({})),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.getMeegoTeamTreeSelectDataSource();
  },
);

// 给实验列表添加 Meego 团队信息（主要用于刷新存量数据）
export const addMeegoTeamInfoToLibraList = Api(
  Post('/flight_manager_platform/add_meego_team_info_to_libra_list'),
  Data(
    z.object({
      teamIds: z.array(z.number()),
      projectKey: z.string(),
      query: z.any().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.addMeegoTeamInfoToLibraInfo(data.teamIds, data.projectKey, data.query);
  },
);

export const createExperimentTemplate = Api(
  Post('/flight_manager_platform/create_experiment_template'),
  Data(experimentTemplateSchema),
  async ({ data }) => {
    const templateService = useInject(LibraCreateTemplateService);
    const convertedData = {
      ...data,
      grayOrRelease: data.grayOrRelease as GrayOrRelease,
    };
    const res = await templateService.createTemplate(convertedData);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

export const submitPaControlExemption = Api(
  Post('/flight_manager_platform/submit_pa_control_exemption'),
  Data(
    z.object({
      flight_name: z.string(),
      meego_info: z.any(),
      owner_email: z.string(),
      influence: z.string(),
      exempt_reason: z.string(),
      improve_measure: z.string(),
      libra_app_id: z.array(z.number()),
      control_type: z.string(),
      version: z.array(z.string()),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraPaControlRecordService);
    const appIdArr = [];
    const record: PaControlRecord = {
      libra_app_id: data.libra_app_id,
      flight_name: data.flight_name,
      owner_email: data.owner_email,
      influence: data.influence,
      exempt_reason: data.exempt_reason,
      improve_measure: data.improve_measure,
      meego_info: data.meego_info,
      exempt_status: ExemptStatus.Exempt,
      app_id: [0],
      control_type: data.control_type as PaControlType,
      status: ControlStatus.Pass,
      version: data.version,
    };
    const res = await service.saveRecord(record);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);
export const updateExperimentTemplate = Api(
  Post('/flight_manager_platform/update_experiment_template'),
  Data(experimentTemplateSchema),
  async ({ data }) => {
    const templateService = useInject(LibraCreateTemplateService);
    const convertedData = {
      ...data,
      grayOrRelease: data.grayOrRelease as GrayOrRelease,
    };
    const res = await templateService.updateTemplate(convertedData);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);
export const deleteExperimentTemplate = Api(
  Post('/flight_manager_platform/delete_experiment_template'),
  Data(
    z.object({
      name: z.string(),
      businessName: z.string(),
      libraAppId: z.number(),
    }),
  ),
  async ({ data }) => {
    const templateService = useInject(LibraCreateTemplateService);
    const res = await templateService.deleteTemplate({
      name: data.name,
      businessName: data.businessName,
      libraAppId: data.libraAppId,
    });
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

export const hasExempted = Api(
  Post('/flight_manager_platform/has_exempted'),
  Data(
    z.object({
      meegoId: z.number(),
      owner_email: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraPaControlRecordService);
    const res = await service.findOneByMeegoId(data.meegoId, data.owner_email);
    if (res && res.exempt_status === ExemptStatus.Exempt) {
      return {
        code: 0,
        message: 'success',
      };
    }
    return {
      code: -1,
      message: 'fail',
    };
  },
);
export const listExperimentTemplate = Api(
  Post('/flight_manager_platform/list_experiment_template'),
  Data(
    z.object({
      query: z.any(),
      page: z.number(),
      pageSize: z.number(),
    }),
  ),
  async ({ data }) => {
    const templateService = useInject(LibraCreateTemplateService);
    const res = await templateService.listTemplate(data.query, data.page, data.pageSize);
    const total = await templateService.count(data.query);
    return {
      code: 0,
      data: res,
      total,
    };
  },
);

export const recordAbnormalFlightReportInfo = Api(
  Post('/flight_manager_platform/record_abnormal_flight_report_info'),
  Data(
    z.object({
      meegoId: z.number(),
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(AbnormalFlightReportInfoService);
    const res = await service.saveAfterThirdGrayOrFullReleaseFlight(data.meegoId, data.flightId);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

export const libraAttribution = Api(
  Post('/libra_attribution'),
  Data(
    z.object({
      dids: z.string().optional(),
      appid: z.number().optional(),
      isOverseas: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    reportTeaEvent({
      data: {
        eventName: 'Quality_Click',
        params: {
          toolName: '实验归因',
        },
      },
    });
    const libraHitResults: LibraHitResult[] = [];
    if (data.dids && data.dids.split(',').length <= 5) {
      const insufficientResult: LibraHitResult = { error: 'did过少，为了确保归因准确性，请输入至少10个did' };
      libraHitResults.push(insufficientResult);
      return libraHitResults;
    }
    const dids: string = data.dids ?? '';
    const appid: number = data.appid ?? 147;
    const isOverseas: boolean = data.isOverseas ?? false;
    const result = await useInject(LibraAPIService).queryLibraInfoByDid(dids, appid, isOverseas);
    if (result === undefined) {
      const noResult: LibraHitResult = { error: '无归因结果' };
      libraHitResults.push(noResult);
      return libraHitResults;
    }
    if (typeof result === 'string') {
      const noResult: LibraHitResult = { error: result };
      libraHitResults.push(noResult);
      return libraHitResults;
    }
    // 存储每个 vid 命中的 did 数量
    const vidHitCount: Record<number, Set<number>> = {};

    result.forEach(item => {
      item.version_ids.forEach(vid => {
        if (!vidHitCount[vid]) {
          vidHitCount[vid] = new Set();
        }
        vidHitCount[vid].add(item.did);
      });
    });
    // 计算总 did 数量
    const totalDidCount = new Set(result.map(item => item.did)).size;
    // 计算每个 vid 的命中 did 数量
    const vidHitCounts = Object.entries(vidHitCount).map(([vid, didSet]) => ({
      vid,
      hitCount: didSet.size / totalDidCount,
    }));

    // 按照命中 did 数量由多到少排序
    vidHitCounts.sort((a, b) => b.hitCount - a.hitCount);
    console.log('vidHitCounts', vidHitCounts);
    // 筛选出命中 did 数量大于总 did 数量 50%的 vid
    const filteredVids = vidHitCounts.filter(item => item.hitCount > 0);
    console.log('filteredVids', filteredVids);
    const allVersionIds = filteredVids.reduce((acc: string[], item) => acc.concat(item.vid), []);
    const uniqueVersionIds = [...new Set(allVersionIds)];
    const groupedFlights: Flight[] = [];
    // 接口限制，一次请求超过300个vid会报错
    for (let i = 0; i < uniqueVersionIds.length; i += 300) {
      const versionids = uniqueVersionIds.slice(i, i + 300).join(',');
      const flights = await useInject(LibraAPIService).queryLibraInfoByVids(versionids, isOverseas, 1);
      if (flights !== undefined) {
        groupedFlights.push(...flights);
      }
    }
    const filterResult = groupedFlights.filter(flight => flight.versions.length >= 2);
    const hitResult: Flight[] = [];
    const NumberToPercentage = (rate: number) => {
      if (rate === undefined) {
        return '0';
      }
      const percentage = `${(rate * 100).toFixed(2)}%`;
      return percentage;
    };
    const TimestampToDate = (timestamp: number) => {
      const date = new Date(timestamp * 1000);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const secondsValue = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${secondsValue}`;
    };
    function libraStatus(status: number) {
      if (status === 0) {
        return '已结束';
      } else if (status === 1) {
        return '进行中';
      } else if (status === 2) {
        return '待调度';
      } else if (status === 3) {
        return '测试中';
      } else if (status === 4) {
        return '已暂停';
      } else {
        return '已结束';
      }
    }
    const uniqueFlights = Array.from(new Set(filterResult.map(flight => flight.id))).map(id =>
      filterResult.find(flight => flight.id === id),
    );
    uniqueFlights.forEach(filterFlight => {
      let experimentalGroupTraffic = 0;
      let experimentalGroupHitRate = 0;
      const hitVid: number[] = [];
      if (filterFlight === undefined) {
        return;
      }
      filterFlight.versions.forEach(version => {
        if (version.type === 1 && vidHitCounts.some(item => item.vid === version.id.toString())) {
          experimentalGroupTraffic += version.real_traffic;
          hitVid.push(version.id);
          const targetHitCount = vidHitCounts.find(item => item.vid === version.id.toString())?.hitCount;
          if (targetHitCount !== undefined) {
            experimentalGroupHitRate += targetHitCount;
          }
        }
      });

      /**
       * 算法规则，实验组命中率减去实验放量流量大于20%跟实验关联性较大
       * 具体原因：实验流量小，命中率高，大概率是相关，
       * 实验流量高，命中率接近100%，也基本都是实验相关
       */
      const hitDiff = 0.2; // 实验组命中率 - 实验组流量大于20%
      if (experimentalGroupHitRate - experimentalGroupTraffic > hitDiff) {
        const foundVersionForType0 = filterFlight.versions.find(version => version.type === 0);
        let hitCount: number;
        if (foundVersionForType0) {
          const found = vidHitCounts.find(item => item.vid === foundVersionForType0.id.toString());
          if (found) {
            hitCount = found.hitCount;
          } else {
            hitCount = 0;
          }
        } else {
          hitCount = 0;
        }
        if (foundVersionForType0 && experimentalGroupHitRate - hitCount > hitDiff && hitCount < 0.05) {
          hitResult.push(filterFlight);
          const cnUrl = 'https://data.bytedance.net/libra/flight/';
          const sgUrl = 'https://libra-sg.tiktok-row.net/libra/flight/';
          const baseUrl = isOverseas ? sgUrl : cnUrl;
          const libraHit: LibraHitResult = {
            experimental_group_hit_rate: NumberToPercentage(experimentalGroupHitRate),
            experimental_group_real_traffic: NumberToPercentage(experimentalGroupTraffic),
            controll_group_hit_rate: NumberToPercentage(hitCount),
            controll_group_real_traffic: NumberToPercentage(foundVersionForType0.real_traffic),
            link: `${baseUrl}${filterFlight.id}`,
            name: filterFlight.name,
            owner: filterFlight.owner[0],
            start_time: filterFlight.start_time,
            status: libraStatus(filterFlight.status),
            modify_time: TimestampToDate(filterFlight.modify_time),
            hit_vid: hitVid,
            type: filterFlight.type,
          };
          libraHitResults.push(libraHit);
        }
      }
    });
    console.log('libraHitResults', libraHitResults);
    if (libraHitResults.length === 0) {
      const noResult: LibraHitResult = {};
      if (filteredVids.length > 0) {
        noResult.error = '无关联实验';
      } else {
        noResult.error = '无关联实验(请检查app是否选对)';
      }
      libraHitResults.push(noResult);
      return libraHitResults;
    }
    return libraHitResults;
  },
);

export const getFlightFullPeriod = Api(
  Post('/flight_manager_platform/get_flight_full_period'),
  Data(
    z.object({
      libraStartTimestamp: z.number(),
      libraStopTimestamp: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const res = await service.getFlightFullPeriod(data.libraStartTimestamp, data.libraStopTimestamp);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

// 获取正在进行中的 flightId 列表（根据时间戳范围）
export const getInProgressFlightIds = Api(
  Post('/flight_manager_platform/get_in_progress_flight_ids'),
  Data(
    z.object({
      libraStartTimestampMin: z.number().optional(), // 实验开始时间的起始 timestamp
      libraStartTimestampMax: z.number().optional(), // 实验开始时间的结束 timestamp
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const res = await service.getInProgressFlightIds(
      data.libraStartTimestampMin ?? 0,
      data.libraStartTimestampMax ?? 0,
    );
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

// 获取可能是“全量”的 flightId 列表（根据时间戳范围）
export const getPossibleFullFlightIds = Api(
  Post('/flight_manager_platform/get_possible_full_flight_ids'),
  Data(
    z.object({
      libraStartTimestampMin: z.number().optional(), // 实验开始时间的起始 timestamp
      libraStartTimestampMax: z.number().optional(), // 实验开始时间的结束 timestamp
      libraStopTimestampMin: z.number().optional(), // 实验结束时间的起始 timestamp
      libraStopTimestampMax: z.number().optional(), // 实验结束时间的结束 timestamp
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const res = await service.getPossibleFullFlightIds(
      data.libraStartTimestampMin ?? 0,
      data.libraStartTimestampMax ?? 0,
      data.libraStopTimestampMin ?? 0,
      data.libraStopTimestampMax ?? 0,
    );
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

// @FIXME: 接口数据不置信，不要使用，后续考虑删掉
export const getFlightIdsForPossibleFullFlight = Api(
  Post('/flight_manager_platform/get_flight_ids_for_possible_full_flight'),
  Data(
    z.object({
      libraStartTimestamp: z.number(),
      libraStopTimestamp: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const res = await service.getFlightIdsForPossibleFullFlight(data.libraStartTimestamp, data.libraStopTimestamp);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

// @FIXME: 接口数据不置信，不要使用，后续考虑删掉
export const getSingleFlightFullPeriod = Api(
  Post('/flight_manager_platform/get_single_flight_full_period'),
  Data(
    z.object({
      eventType: z.string(),
      flightId: z.number(),
      libraStartTimestamp: z.number(),
      libraStopTimestamp: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const res = await service.getSingleFlightFullPeriod(
      data.eventType as LibraEventType,
      data.flightId,
      data.libraStartTimestamp,
      data.libraStopTimestamp,
    );
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

export const getMeegoFullPeriod = Api(
  Post('/flight_manager_platform/get_meego_full_period'),
  Data(
    z.object({
      meegoId: z.string(),
      reviewPeriodId: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    const res = service.queryMeegoFullPeriodByStoryRevenueTask(data.meegoId, data.reviewPeriodId);
    return {
      code: 0,
      message: 'success',
      data: res,
    };
  },
);

export const getFlightInfoByLibraApi = Api(
  Post('/flight_manager_platform/get_flight_info_by_libra_api'),
  Data(
    z.object({
      flightId: z.number(),
      region: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraAPIService);
    const res = await service.queryFlight(data.region as LibraRegion, data.flightId);
    return {
      code: 0,
      message: 'success',
      data: res ?? {},
    };
  },
);

export const closeFlightById = Api(
  Post('/flight_manager_platform/close_flight_by_flightId'),
  Data(
    z.object({
      flightId: z.string(),
      region: z.number(),
      operator: z.string(),
    }),
  ),
  async ({ data }) => {
    console.log('data', data);
    const service = useInject(LibraAPIService);
    const res = await service.stopFlight(data.region as LibraRegion, data.flightId, data.operator);
    console.log('res', res);
    return res;
  },
);

export const getSecToken = Api(Post('/open/flight_manager_platform/get_sec_token'), Data(z.any()), async ({ data }) => {
  const tokenString: string | undefined = process.env.SEC_TOKEN_STRING;
  return {
    code: 0,
    message: 'success',
    token: tokenString,
  };
});

export const getFlightMetricInfo = Api(
  Post('/open/flight_manager_platform/get_flight_metric_info'),
  Data(
    z.object({
      region: z.number(),
      flight_id: z.number(),
      start_date: z.string(),
      end_date: z.string(),
      metric_group_id: z.number(),
      dim_id: z.number(),
      dim_value_ids: z.array(z.number()),
      app_id: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraPatrolService);
    if (data.dim_id === undefined || data.dim_id === 0) {
      const req: FlightReportRequest = {
        start_date: data.start_date,
        end_date: data.end_date,
        view_type: 'merge',
        merge_type: 'total',
        mult_cmp_corr: 1,
        metric_group: data.metric_group_id,
      };
      const res = await service.getLibraMetricData(data.region as LibraRegion, data.flight_id, data.app_id, req);
      return {
        code: 0,
        message: 'success',
        metadata: res?.metadata,
        report_page_metrics: res?.report_page_metrics,
      };
    }
    const req: FlightMetricConclusionParams = {
      start_date: data.start_date,
      end_date: data.end_date,
      view_type: 'merge',
      merge_type: 'total',
      mult_cmp_corr: 1,
      metric_group: data.metric_group_id,
      dim: data.dim_id,
      dim_vals: data.dim_value_ids.join(','),
      selected_metric_ids: '',
    };
    const res = await service.getLibraMetricDataWithDim(data.region as LibraRegion, data.flight_id, data.app_id, req);
    return {
      code: 0,
      message: 'success',
      metadata: res?.metadata,
      report_page_metrics: res?.report_page_metrics,
    };
  },
);

export const getSettingsKeyInfo = Api(
  Post('/flight_manager_platform/get_settings_key_info'),
  Data(
    z.object({
      itemId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(SettingsService);
    return {
      setting_detail: (await service.queryItemDetail(data.itemId)) ?? {},
      ab_list: (await service.querySettingsItemABConfigs(data.itemId)) ?? [],
    };
  },
);

export const getLibraChangeEventListWithNoAggregate = Api(
  Post('/flight_manager_platform/get_libra_change_event_list_with_no_aggregate'),
  Data(z.any()),
  async ({ data }) => {
    const service = useInject(LibraChangeEventDao);
    const res = await service.listAllWithNoAggregate(data);
    return {
      code: 0,
      message: 'success',
      data: res ?? [],
    };
  },
);

export const getMeegoInfoByMeegoApi = Api(
  Post('/flight_manager_platform/get_meego_info_by_meego_api'),
  Data(
    z.object({
      meegoId: z.number(),
      workType: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(MeegoRawService);
    const res = await service.requestWorkItem('faceu', data.workType, [data.meegoId]);
    return {
      code: 0,
      message: 'success',
      data: res ?? {},
    };
  },
);

export const calDayDiffResult = Api(
  Post('/flight_manager_platform/calculate_day_diff'),
  Data(
    z.object({
      startTimestamp: z.number(),
      stopTimestamp: z.number(),
    }),
  ),
  async ({ data }) => {
    const { startTimestamp, stopTimestamp } = data;
    return calculateDayDiffResult(startTimestamp, stopTimestamp);
  },
);

export const updateMeegoTypeOfLibraInfo = Api(
  Post('/flight_manager_platform/update_meego_type_of_libra_info'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateMeegoTypeOfLibraInfo(data.flightId);
  },
);

export const updateCloseAttributionOfLibraInfoByLibraReason = Api(
  Post('/flight_manager_platform/update_close_attribution_of_libra_info_by_libra_reason'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateCloseAttributionOfLibraInfoByLibraReason(data.flightId);
  },
);

export const libraCloseAttributionFinishedNotify = Api(
  Post('/flight_manager_platform/libra_close_attribution_finished_notify'),
  Data(
    z.object({
      flightId: z.number(),
      sendPrivate: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNotifyService);
    return await service.libraCloseAttributionFinishedNotify(data.flightId, data.sendPrivate);
  },
);

export const updateLibraMeegoInfo = Api(
  Post('/flight_manager_platform/update_libra_meego_info'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateMeegoInfo(data.flightId);
  },
);

export const updateLibraFlightInfo = Api(
  Post('/flight_manager_platform/update_libra_flight_info'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateFlightInfo(data.flightId);
  },
);

// 更新实验类型（正式/灰度/反转等），一般用于刷新历史存量数据（因为增量数据已经 cover）
export const updateLibraFlightType = Api(
  Post('/flight_manager_platform/update_libra_flight_type'),
  Data(
    z.object({
      flightId: z.number(),
      forceUpdate: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.updateFlightType(data.flightId, data.forceUpdate);
  },
);

// 删除实验类型（正式/灰度/反转等），一般用于校正数据（因为目前实验类型只适用于剪映和CapCut App，其他应用不适用）
// 比如：若 PC、Web 也被标记上了实验类型，这种是不符合预期的，应该删除掉被错误标记的实验类型。所以提供此 api 接口
export const deleteLibraFlightType = Api(
  Post('/flight_manager_platform/delete_libra_flight_type'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.deleteFlightType(data.flightId);
  },
);

// 修正全量实验关闭归因
export const fixPossibleFullReleasedFlightCloseAttribution = Api(
  Post('/flight_manager_platform/fix_possible_full_released_flight_close_attribution'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.fixPossibleFullReleasedFlightCloseAttribution(data.flightId);
  },
);

export const queryLibraStartTimeAndEndTimeByAPI = Api(
  Post('/flight_manager_platform/query_libra_start_time_and_end_time_by_api'),
  Data(
    z.object({
      flightId: z.number(),
      region: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.queryLibraStartTimeAndEndTimeByAPI(data.flightId, data.region);
  },
);

export const fixLibraStartTimeAndEndTime = Api(
  Post('/flight_manager_platform/fix_libra_start_time_and_end_time'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.fixLibraStartTimeAndEndTime(data.flightId);
  },
);

/**
 * ===========================================
 * Section: 实验设计相关
 * ===========================================
 */
export const createLibraDesignDoc = Api(
  Post('/libra_design/create_libra_design_doc'),
  Data(
    z.object({
      docTitle: z.string(),
      templateDocToken: z.string(),
      newOwnerEmail: z.string(),
      otherCollaboratorEmails: z.array(z.string()),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraDesignDocService);
    return await service.createLibraDesignDoc(
      data.docTitle,
      data.templateDocToken,
      data.newOwnerEmail,
      data.otherCollaboratorEmails,
    );
  },
);
/**
 * ===========================================
 * Section: 实验巡检指标相关
 * ===========================================
 */
export const deleteLibraPatrolConfigById = Api(
  Post('/libra_patrol/delete_libra_patrol_config_by_id'),
  Data(
    z.object({
      isTest: z.number(),
      metricGroupId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraPatrolConfigDao);
    return await service.clear({ metric_group_id: data.metricGroupId, is_test: data.isTest });
  },
);
export const clearAllLibraPatrolConfig = Api(Post('/libra_patrol_config/clear_all'), async () => {
  const service = useInject(LibraPatrolConfigDao);
  return await service.clearAll();
});
/**
 * ===========================================
 * Section: Libra 非官方 API 相关
 * ===========================================
 */
export const getLibraHistory = Api(
  Post('/libra_api_no_open/get_libra_history'),
  Data(
    z.object({
      flightId: z.number(),
      libraAppId: z.number(),
      region: z.number(),
    }),
  ),
  async ({ data }) => {
    // 固定使用 longguoyang 的 token（注意点：使用前，需要登录一下 Libra 平台）
    const email = '<EMAIL>';
    const libraRegion = data.region as LibraRegion;
    if (!(libraRegion === LibraRegion.CN || libraRegion === LibraRegion.SG)) {
      return {
        code: -1,
        message: 'region is invalid',
      };
    }
    let token: Token | undefined;
    if (libraRegion === LibraRegion.CN) {
      token = await getCNTokenAsync(email);
      // CN 区实验
      if (!token || !token?.token) {
        return {
          code: -1,
          message: 'get CN token failed',
        };
      }
    } else {
      token = await getSGTokenAsync(email);
      // SG 区实验
      if (!token || !token?.token) {
        return {
          code: -1,
          message: 'get SG token failed',
        };
      }
    }

    if (!token) {
      return {
        code: -1,
        message: 'get token failed',
      };
    }

    const result = await libra_history({
      // @FIXME: 这里外部的 header-origin 传入 localhost:8080，以便于在 sg 的情况下获得 https://libra-sg.tiktok-row.net 前缀
      headers: libraRegion === LibraRegion.SG ? { origin: 'localhost:8080' } : {},
      data: {
        flight_id: data.flightId,
        libra_app_id: data.libraAppId,
        tokens: [token],
      },
    });
    return result;
  },
);
