import React, { ReactNode, useEffect, useState } from 'react';
import {
  ReleasePlatformUrlSearchParams,
  versionManager,
  VersionProcessInfo,
  VersionStageInfo,
  VersionStageStatus,
} from '@shared/releasePlatform/versionStage';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import VersionStageSettingModule from '@/model/versionStageSettingModel';
import {
  CheckItemStatus,
  ItemType,
  VersionStageCheckItem,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { VersionFeedback } from '@shared/releasePlatform/feedbackFollwUp';
import {
  batchNotifyChecklist,
  findChecklist,
  findChecklistForIndependentGrey,
  findChecklistForIndependentGreyByInstance,
  forceStartVersionStage,
  pcBuildPackage,
  turnVersionStage,
} from '@api/releasePlatform';
import { FunctionalBugItemInfo, StabilityMetricItemInfo } from '@shared/releasePlatform/versionStageCheckItemInfo';
import BusCodeFreezeList from '@/component/BusCodeFreezeList';
import VersionFeatureList from '@/component/BusCodeFreezeList/VersionFeatureList';
import {
  BMOutputDataCheckList,
  BugResolveRatioCheckItemList,
  CircuitBreakerCheckList,
  FeedbackCheckItemList,
  LibraGrayCheckItemList,
  ManualCheckItemList,
  MeegoCheckItemList,
  QATestProgressCheckItemList,
  SlardarCheckItemList,
  TimeLineAnalysisCheckList,
} from '@/component/VersionStageChecklistView/checkItemList';
import TemporaryCheckItemList from '@/component/VersionStageChecklistView/temporaryCheckItemList';
import VersionMetricCheckList from '@/component/VersionMetricCheckList';
import { Card, Col, Divider, Empty, Row, Space, Spin, Switch, Tag, Typography } from '@douyinfe/semi-ui';
import { IllustrationNoContent, IllustrationNoContentDark } from '@douyinfe/semi-illustrations';
import { useSearchParams } from '@edenx/runtime/router';
import AppSettingModule from '@/model/appSettingModel';
import { logger } from '@/pages/approval/utils/Logger';
import { requestCreateRequirementApprovalOrder } from '@/pages/approval/utils/ApprovalRequestUtils';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { IconClear, IconTickCircle } from '@douyinfe/semi-icons';
import { VersionLibraSideSheet } from '@/component/VersionLibraSideSheet';
import OperatorComfirmButton from '@/component/OperatorComfirmButton';
import AddCheckItemButton from '@/component/VersionStageChecklistView/addCheckItemButton';
import IndependentGreyStageProcess from '@/component/IndependentGreyStageProcess';
import { IndependentGreyStageInfo } from '@shared/approval/IndependentGreyStageInfo';
import IndependentGreyStageSettingModule from '@/model/independentGreyStageModel';

const { Title } = Typography;

interface CheckItemGroup {
  itemType: ItemType;
  checkItems: VersionStageCheckItem[];
  pass: boolean;
}

const ChecklistTitle: React.FC<{
  checklist?: VersionStageCheckList;
  versionInfo?: VersionProcessInfo;
  stageInfo?: VersionStageInfo;
  updateVersionInfo: (newInfo: VersionProcessInfo) => void;
  updateChecklist: (newChecklist: VersionStageCheckList) => void;
  filterSwitchChanged: (value: boolean) => void;
}> = ({ checklist, stageInfo, versionInfo, updateChecklist, updateVersionInfo, filterSwitchChanged }) => {
  const [checklistResult, setChecklistResult] = useState(false);
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [versionOperator, setVersionOperator] = useState<string[]>([]);
  const [checked, setChecked] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const [appSettingState] = useModel(AppSettingModule);
  const [userSettingState] = useModel(UserSettingModule);

  useEffect(() => {
    if (!checklist) {
      setChecklistResult(true);
      return;
    }
    const blockCheckItems = checklist.check_items.filter(it => it.status !== CheckItemStatus.Exempt);
    setChecklistResult(blockCheckItems.length <= 0);
    const focusCheckItem = searchParams.get(ReleasePlatformUrlSearchParams.FocusCheckItem);
    if (focusCheckItem === 'true') {
      setChecked(true);
    } else {
      setChecked(false);
    }
  }, [checklist]);

  useEffect(() => {
    if (checked) {
      searchParams.set(ReleasePlatformUrlSearchParams.FocusCheckItem, 'true');
    } else {
      searchParams.delete(ReleasePlatformUrlSearchParams.FocusCheckItem);
    }
    setSearchParams(searchParams);
    filterSwitchChanged(checked);
  }, [checked]);

  useEffect(() => {
    if (!versionInfo) {
      return;
    }
    const operator = versionManager(versionInfo);
    setVersionOperator(operator);
  }, [versionInfo]);

  const handleRequirementApprovalSubmit = async (values: any) => {
    logger.debug(`handleSubmit =>${JSON.stringify(values)}`);
    const rsp = await requestCreateRequirementApprovalOrder(values, values?.appIds, versionInfo?.version);
    return rsp?.code === NetworkCode.Success;
  };

  return (
    <>
      <Row gutter={8}>
        <Col span={10}>
          <Space>
            <Title heading={5} style={{ margin: '8px 0' }}>
              阶段准出结论
            </Title>
            {checklistResult ? (
              <Tag color="light-green" prefixIcon={<IconTickCircle />} size="large" type="light">
                通过
              </Tag>
            ) : (
              <Tag color="pink" prefixIcon={<IconClear />} size="large" type="light">
                不通过
              </Tag>
            )}
            <Divider layout={'vertical'} margin={3} />
            <Switch defaultChecked={false} checked={checked} onChange={(v, e) => setChecked(v)} />
            <Title heading={6} style={{ margin: '8px 0' }}>
              只看我的
            </Title>
          </Space>
        </Col>
        <Col span={14}>
          <div style={{ float: 'right' }}>
            <Space>
              <VersionLibraSideSheet versionInfo={versionInfo} stageName={stageInfo?.display_name ?? ''} />
              <OperatorComfirmButton
                operatorEmails={versionOperator}
                title={'强制流转'}
                theme={'solid'}
                type={'primary'}
                confirmTitle={'将强制完成此阶段（如此阶段已完成，点击确定可完成父阶段），并尝试开始后续阶段'}
                onclick={async () => {
                  const newVersionInfo = await turnVersionStage({
                    data: {
                      appId: versionInfo?.app_id ?? 0,
                      version: versionInfo?.version ?? '',
                      stageName:
                        (stageInfo?.status === VersionStageStatus.Complete
                          ? stageInfo?.parent_stage_name
                          : stageInfo?.stage_name) ?? '',
                      userEmail: userSettingState.info.email ?? '',
                    },
                  });
                  if (newVersionInfo) {
                    updateVersionInfo(newVersionInfo);
                  }
                }}
              />
              {stageInfo?.status === VersionStageStatus.NotStart && (
                <OperatorComfirmButton
                  operatorEmails={versionOperator}
                  title={'直接开始'}
                  theme={'solid'}
                  type={'primary'}
                  confirmTitle={'直接开始此版本阶段'}
                  onclick={async () => {
                    const newVersionInfo = await forceStartVersionStage({
                      data: {
                        appId: versionInfo?.app_id ?? 0,
                        version: versionInfo?.version ?? '',
                        stage: stageInfo.stage_name ?? '',
                        userEmail: userSettingState.info.email ?? '',
                      },
                    });
                    if (newVersionInfo) {
                      updateVersionInfo(newVersionInfo);
                    }
                  }}
                />
              )}
              <AddCheckItemButton
                versionInfo={versionInfo}
                stageInfo={stageInfo}
                checkItemsDidUpdate={async () => {
                  setRefreshLoading(true);
                  await findChecklist({
                    data: {
                      appId: versionInfo?.app_id ?? 0,
                      version: versionInfo?.version ?? '',
                      stage: stageInfo?.stage_name ?? '',
                      needLatest: false,
                    },
                  }).then(res => {
                    if (res) {
                      updateChecklist(res);
                    }
                    setRefreshLoading(false);
                  });
                }}
              />
              <OperatorComfirmButton
                operatorEmails={versionOperator}
                title={'一键提醒'}
                theme={'solid'}
                type={'primary'}
                confirmTitle={'将在对应群提醒所有未通过的准出项'}
                onclick={async () => {
                  const blockCheckItems = checklist?.check_items.filter(it => it.status !== CheckItemStatus.Exempt);
                  const itemTypes: ItemType[] = [];
                  blockCheckItems?.forEach(it => {
                    if (itemTypes.includes(it.item_type)) {
                      return;
                    }
                    itemTypes.push(it.item_type);
                  });
                  for (const itemType of itemTypes) {
                    await batchNotifyChecklist({
                      data: {
                        appId: versionInfo?.app_id ?? 0,
                        version: versionInfo?.version ?? '',
                        itemType,
                        stageName: stageInfo?.stage_name ?? '',
                      },
                    });
                  }
                }}
                disable={stageInfo?.status !== VersionStageStatus.OnProgress || checklistResult}
              />
              <OperatorComfirmButton
                operatorEmails={versionOperator}
                title={'更新结论'}
                theme={'solid'}
                type={'primary'}
                confirmTitle={'将拉取最新的准出检查项状态'}
                confirmContent={'耗时操作，请勿频繁触发'}
                onclick={async () => {
                  setRefreshLoading(true);
                  await findChecklist({
                    data: {
                      appId: versionInfo?.app_id ?? 0,
                      version: versionInfo?.version ?? '',
                      stage: stageInfo?.stage_name ?? '',
                      needLatest: true,
                    },
                  }).then(res => {
                    if (res) {
                      updateChecklist(res);
                    }
                    setRefreshLoading(false);
                  });
                }}
                // disable={stageInfo?.status === VersionStageStatus.Complete}
              />
              {versionInfo?.app_id !== 2020092383 && versionInfo?.app_id !== 35928901 ? (
                <></>
              ) : (
                <OperatorComfirmButton
                  operatorEmails={versionOperator}
                  title={'PC手动打包'}
                  theme={'solid'}
                  type={'primary'}
                  confirmTitle={'将触发PC打包逻辑'}
                  onclick={async () => {
                    await pcBuildPackage({
                      data: {
                        appId: versionInfo?.app_id ?? 0,
                        version: versionInfo?.version ?? '',
                        stageName: stageInfo?.stage_name ?? '',
                      },
                    });
                  }}
                  disable={stageInfo?.status !== VersionStageStatus.OnProgress}
                />
              )}
            </Space>
          </div>
        </Col>
      </Row>
      <Divider margin="3px" />
    </>
  );
};

const IndependentStageCheckListView: React.FC<{
  independentGreyStageInfos: IndependentGreyStageInfo[];
  versionInfo: VersionProcessInfo;
  updateVersionInfo: (newInfo: VersionProcessInfo) => void;
}> = ({ independentGreyStageInfos, versionInfo, updateVersionInfo }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [independentGreyStageInfo, independentGreyStageInfoAction] = useModel(IndependentGreyStageSettingModule);
  const [stageSettingState, stageSettingAction] = useModel(VersionStageSettingModule);
  const [requestChecklist, setRequestChecklist] = useState(false);
  const [checklist, setChecklist] = useState<VersionStageCheckList | undefined>();
  const [displayCheckItems, setDisplayCheckItems] = useState<VersionStageCheckItem[]>([]);
  const [filterOpen, setFilterOpen] = useState(false);
  const [checkTableList, setCheckTableList] = useState<ReactNode[]>([]);

  useEffect(() => {
    if (!independentGreyStageInfos) {
      return;
    }
    let selectedSubStage = independentGreyStageInfos.find(
      subStage => subStage.status === VersionStageStatus.OnProgress,
    );
    if (!selectedSubStage) {
      selectedSubStage = independentGreyStageInfos.findLast(
        subStage => subStage.status === VersionStageStatus.Complete,
      );
    }
    if (!selectedSubStage) {
      selectedSubStage = independentGreyStageInfos.find(subStage => subStage.status === VersionStageStatus.NotStart);
    }
    if (selectedSubStage) {
      independentGreyStageInfoAction.updateCurrentStage(selectedSubStage);
    }
  }, [independentGreyStageInfos]);

  useEffect(() => {
    setRequestChecklist(true);
    const stage = independentGreyStageInfo.info;
    // findChecklistForIndependentGreyByInstance({
    //   data: { instanceCode: independentGreyStageInfos[0].instance_code },
    // }).then(curChecklist => {
    //   if (curChecklist) {
    //     setChecklist(curChecklist);
    //   }
    //   setRequestChecklist(false);
    // });
    findChecklistForIndependentGrey({
      data: {
        appId: stage.app_id,
        version: stage?.version ?? '',
        stage: stage.stage_name ?? '',
        // 'single_independent_grey_stage'
      },
    }).then(curChecklist => {
      if (curChecklist) {
        setChecklist(curChecklist);
      }
      setRequestChecklist(false);
    });
  }, [independentGreyStageInfo.info, versionInfo]);

  useEffect(() => {
    if (!checklist) {
      return;
    }
    if (filterOpen) {
      const newCheckItems = checklist.check_items.filter(it => {
        if (it.item_type === ItemType.BugResolveRatio) {
          return true;
        } else if (it.item_type === ItemType.FunctionalBug) {
          const bugInfo = it.item_info as FunctionalBugItemInfo;
          return (
            bugInfo.operator.email === userSettingState.info.email ||
            bugInfo.reporter.email === userSettingState.info.email
          );
        } else if (it.item_type === ItemType.Slardar) {
          const metrixInfo = it.item_info as StabilityMetricItemInfo;
          return metrixInfo.owners.map(owner => owner.email).includes(userSettingState.info.email);
        } else {
          return it.owner?.email === userSettingState.info.email;
        }
      });
      setDisplayCheckItems(newCheckItems);
    } else {
      setDisplayCheckItems(checklist.check_items);
    }
  }, [checklist, filterOpen]);

  const updateCheckItem: (item: VersionStageCheckItem) => void = (item: VersionStageCheckItem) => {
    if (!checklist) {
      return;
    }
    const newCheckItems = [...checklist.check_items]; // 创建check_items的新副本
    const index = newCheckItems.findIndex(it => it.check_item_id === item.check_item_id);
    if (index !== -1) {
      newCheckItems[index] = item; // 更新具体的项
    }

    const newChecklist = { ...checklist, check_items: newCheckItems }; // 创建包含更新后的check_items的checklist新副本
    setChecklist(newChecklist);
  };

  // useEffect(() => {
  //   const fetchFeedbackFollow = async () => {
  //     const rsp = await getAllFeedbackFollow({
  //       data: {
  //         appId: versionInfo?.app_id ?? 0,
  //         version: versionInfo?.version ?? '',
  //         stage: 'feedback_follow',
  //       },
  //     });
  //     setFeedbackFollow(rsp);
  //   };
  // }, [versionInfo]);

  useEffect(() => {
    const groupedChecklist: CheckItemGroup[] = [];
    const itemTypes = Object.values(ItemType);
    itemTypes.forEach(type => {
      const items = displayCheckItems.filter(it => it.item_type === type);
      if ((items?.length ?? 0) > 0) {
        const blockedItems = items?.filter(it => it.status !== CheckItemStatus.Exempt);
        groupedChecklist.push({
          itemType: type,
          checkItems: items,
          pass: (blockedItems?.length ?? 0) === 0,
        } as CheckItemGroup);
      }
    });
    const tableList: ReactNode[] = [];
    const manualCheckItems: VersionStageCheckItem[] = [];
    const stage = stageSettingState.info.subStage ?? stageSettingState.info.mainStage;
    const stageName = stage?.stage_name ?? '';
    for (const group of groupedChecklist) {
      if (!versionInfo) {
        continue;
      }
      if (group.itemType === ItemType.CodeFreeze) {
        const itemContainer = <BusCodeFreezeList versionInfo={versionInfo} />;
        tableList.push(itemContainer);
        tableList.push(<br />);
        const featureContainer = <VersionFeatureList versionInfo={versionInfo} />;
        tableList.push(featureContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.Slardar) {
        const itemContainer = (
          <SlardarCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            stageInfo={stage}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.CircuitBreaker) {
        const itemContainer = (
          <CircuitBreakerCheckList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            title="熔断指标"
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.FunctionalBug) {
        const itemContainer = (
          <MeegoCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            updateCheckItem={updateCheckItem}
            stageInfo={stage}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.Business) {
        const itemContainer = (
          <ManualCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            title="业务指标"
            stageName={stageName}
            stageInfo={stage}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.QABM) {
        manualCheckItems.push(...group.checkItems);
      } else if (group.itemType === ItemType.RDBM) {
        manualCheckItems.push(...group.checkItems);
      } else if (group.itemType === ItemType.QATestProgress) {
        const itemContainer = (
          <QATestProgressCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            updateCheckItem={updateCheckItem}
            filterOpen={filterOpen}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.BugResolveRatio) {
        const itemContainer = (
          <BugResolveRatioCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            updateCheckItem={updateCheckItem}
            filterOpen={filterOpen}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.Temporary) {
        const itemContainer = (
          <TemporaryCheckItemList versionInfo={versionInfo} checkItems={group.checkItems} stageInfo={stage} />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.MetricData) {
        const itemContainer = (
          <VersionMetricCheckList
            title="业务指标"
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            updateCheckItem={updateCheckItem}
            filterOpen={filterOpen}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.BMOutputData) {
        const itemContainer = (
          <BMOutputDataCheckList
            updateVersionInfo={updateVersionInfo}
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            title="人员项"
            stageName={stageName}
            stageInfo={stage}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.TimelineAnalysis) {
        const itemContainer = (
          <TimeLineAnalysisCheckList
            updateVersionInfo={updateVersionInfo}
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            title="人员项"
            stageName={stageName}
            stageInfo={stage}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.FeedBackFollow) {
        const itemContainer = (
          <FeedbackCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            stageInfo={stage}
            updateCheckItem={updateCheckItem}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
        // const itemContainer = (
        //   <FeedBackFollowCheckList
        //     updateVersionInfo={updateVersionInfo}
        //     versionInfo={versionInfo}
        //     checkItems={group.checkItems}
        //     title="反馈跟进情况"
        //     stageName={stageName}
        //     stageInfo={stage}
        //   />
        // );
        // tableList.push(itemContainer);
        // tableList.push(<br />);
      } else if (group.itemType === ItemType.LibraGrayCheckItem) {
        const itemContainer = (
          <LibraGrayCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            stageInfo={stage}
            updateCheckItem={updateCheckItem}
            title={'跟版需求未开启100%灰度实验'}
            libraItemType={ItemType.LibraGrayCheckItem}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      } else if (group.itemType === ItemType.LibraNoTestCheckItem) {
        const itemContainer = (
          <LibraGrayCheckItemList
            versionInfo={versionInfo}
            checkItems={group.checkItems}
            stageName={stageName}
            stageInfo={stage}
            updateCheckItem={updateCheckItem}
            title={'免测需求未开启100%灰度实验'}
            libraItemType={ItemType.LibraNoTestCheckItem}
          />
        );
        tableList.push(itemContainer);
        tableList.push(<br />);
      }
    }
    if (manualCheckItems.length > 0 && versionInfo !== undefined) {
      const itemContainer = (
        <ManualCheckItemList
          versionInfo={versionInfo}
          checkItems={manualCheckItems}
          title="人工确认项"
          stageName={stageName}
          stageInfo={stage}
        />
      );
      tableList.push(itemContainer);
      tableList.push(<br />);
    }
    if (tableList.length <= 0) {
      tableList.push(
        <Empty
          image={<IllustrationNoContent style={{ width: 200, height: 300 }} />}
          darkModeImage={<IllustrationNoContentDark style={{ width: 150, height: 150 }} />}
          title={'暂无相关配置'}
        />,
      );
    }
    setCheckTableList(tableList);
  }, [displayCheckItems]);

  const checkItemTableList = () => {
    if ((checklist?.check_items.length ?? 0) <= 0) {
      return (
        <Empty
          image={<IllustrationNoContent style={{ width: 200, height: 300 }} />}
          darkModeImage={<IllustrationNoContentDark style={{ width: 150, height: 150 }} />}
          title={'暂无相关配置'}
        />
      );
    }
    if (displayCheckItems.length <= 0) {
      return (
        <Empty
          image={<IllustrationNoContent style={{ width: 200, height: 300 }} />}
          darkModeImage={<IllustrationNoContentDark style={{ width: 150, height: 150 }} />}
          title={'没有待办准出项'}
        />
      );
    }

    return checkTableList;
  };

  if (!independentGreyStageInfos || independentGreyStageInfos.length === 0) {
    return <></>;
  }

  return (
    <div style={{ width: '100%', paddingLeft: 10, paddingTop: 10 }}>
      <Spin spinning={requestChecklist}>
        <IndependentGreyStageProcess stages={independentGreyStageInfos} />

        <Card style={{ width: '100%', paddingLeft: 10, paddingRight: 10 }}>
          {stageSettingState.info.mainStage?.stage_name === 'integration' ? (
            <></>
          ) : (
            <>
              <ChecklistTitle
                updateVersionInfo={updateVersionInfo}
                updateChecklist={newChecklist => setChecklist(newChecklist)}
                filterSwitchChanged={value => setFilterOpen(value)}
                versionInfo={versionInfo}
                checklist={checklist}
                stageInfo={stageSettingState.info.subStage ?? stageSettingState.info.mainStage}
              />
            </>
          )}
          <br />
          {checkItemTableList()}
          {stageSettingState.info.mainStage?.stage_name !== 'version_review' ||
          !versionInfo?.versionReviewDoc ||
          versionInfo?.versionReviewDoc === '' ? (
            <></>
          ) : (
            <iframe style={{ width: '100%', height: '500px' }} src={versionInfo.versionReviewDoc} />
          )}{' '}
        </Card>
      </Spin>
      <br />
    </div>
  );
};

export default IndependentStageCheckListView;
