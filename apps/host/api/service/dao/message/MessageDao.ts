import { Inject, Injectable } from '@gulux/gulux';
import { ModelType } from '@gulux/gulux/typegoose';
import { MessageTable } from '../../../model/message/MessageModel';
import { VscodeMessageInfo } from '@shared/message/VscodeMessageInfo';
import { QueryOptions } from '@byted/bytedmongoose';

@Injectable()
export default class MessageDao {
  @Inject(MessageTable)
  private messageModel: ModelType<MessageTable>;

  // 保存消息
  async saveMessage(data: VscodeMessageInfo): Promise<VscodeMessageInfo> {
    const message = new this.messageModel(data);
    return message.save();
  }

  // 根据ID查找消息
  async findById(id: string): Promise<VscodeMessageInfo | null> {
    return this.messageModel.findById(id).exec();
  }

  // 查找未过期的消息
  async findUnexpiredMessages(sinceTimestamp?: number): Promise<VscodeMessageInfo[]> {
    const now = Date.now();
    const query: any = {
      expireAt: { $gt: now },
    };

    if (sinceTimestamp) {
      query.createdAt = { $gt: sinceTimestamp };
    }

    return this.messageModel.find(query).sort({ createdAt: 1 }).exec();
  }

  // 查找未发送的个人消息
  async findUnsentPersonalMessages(email: string): Promise<VscodeMessageInfo[]> {
    const now = Date.now();
    return this.messageModel
      .find({
        email,
        isSent: false,
        expireAt: { $gt: now },
      })
      .sort({ createdAt: 1 })
      .exec();
  }

  // 查找未发送的群发消息
  async findUnsentBroadcastMessages(): Promise<VscodeMessageInfo[]> {
    const now = Date.now();
    return this.messageModel
      .find({
        email: { $exists: false },
        isSent: false,
        expireAt: { $gt: now },
      })
      .sort({ createdAt: 1 })
      .exec();
  }

  // 更新消息发送次数
  async addSendMac(id: string, mac: string): Promise<void> {
    await this.messageModel
      .findByIdAndUpdate(id, {
        $inc: { sendCount: 1 },
        $addToSet: { 'sendInfo.sendMac': mac },
      })
      .exec();
  }
  async addSendEmail(id: string, email: string): Promise<void> {
    await this.messageModel
      .findByIdAndUpdate(id, {
        $inc: { sendCount: 1 },
        $addToSet: { 'sendInfo.sendEmail': email },
      })
      .exec();
  }

  // 删除过期消息
  async deleteExpiredMessages(): Promise<void> {
    const now = Date.now();
    await this.messageModel
      .deleteMany({
        expireAt: { $lte: now },
      })
      .exec();
  }
}
