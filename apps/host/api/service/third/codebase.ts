import { Inject, Injectable } from '@gulux/gulux';
import TccService from './tcc';
import { NetworkX } from '../../utils/NetworkX';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { ChangeModel, CommentsModel } from '@shared/gitlab/codebaseChange';

@Injectable()
export default class CodebaseService {
  TAG = 'CodebaseService';
  @Inject()
  private tcc: TccService;
  @Inject()
  private logger: BytedLogger;
  createRequest(): NetworkX {
    return new NetworkX('https://codebase-api.byted.org', {
      'Content-Type': 'application/json',
      Authorization: `Codebase-Service-JWT ${this.tcc.getCiTTAndroidCodebaseKey()}`,
    });
  }

  // getChangeFromCodeBase 获取 repo_id 和 change_id
  async getChangeFromCodeBase(projectId: number, iid: number): Promise<ChangeModel | undefined> {
    const request = this.createRequest();
    const result = await request.get<ChangeModel>(`/unstable/repos/gitlab~${projectId}/changes/gitlab~${iid}`);
    this.logger.info(`${this.TAG} ${JSON.stringify(result)}`);
    return result;
  }

  // getCommentByRepoChangeFromCodebase 根据 repo_id 和 changeId 来获取评论
  async getCommentByRepoChangeFromCodebase(repoId: number, changeId: number): Promise<CommentsModel | undefined> {
    const request = this.createRequest();
    const result = await request.get<CommentsModel>(`/unstable/repos/${repoId}/changes/${changeId}/comments`);
    this.logger.info(`${this.TAG} ${JSON.stringify(result)}`);
    return result;
  }

  async batchGetChangeDetails(changes: { change_id: number; repo_id: number }[]) {
    const request = new NetworkX('https://codebase-api.byted.org', {
      'Content-Type': 'application/json',
      Authorization: `Codebase-Service-JWT ${this.tcc.getMultiLanguageCodebaseKey()}`,
    });
    const result = await request.post<{ changes: ChangeModel[]; unavailable_changes: ChangeModel[] }>(
      `/unstable/batch_get_change_details?o=all_patchsets&o=comments&o=drafts&o=review_actions&o=reviewed_files&o=current_files&o=track_comments&o=comments_award_emojis&o=current_commits`,
      { changes },
    );
    this.logger.info(`${this.TAG} ${JSON.stringify(result)}`);
    return result;
  }

  async getChangeContents(repoId: number, filePath: string, revision: string) {
    const request = new NetworkX('https://codebase-api.byted.org', {
      'Content-Type': 'application/json',
      Authorization: `Codebase-Service-JWT ${this.tcc.getMultiLanguageCodebaseKey()}`,
    });
    const result = await request.get<any>(`/unstable/repos/${repoId}/contents/${filePath}?revision=${revision}`);
    // this.logger.info(`${this.TAG} ${JSON.stringify(result)}`);
    return result;
  }
}
