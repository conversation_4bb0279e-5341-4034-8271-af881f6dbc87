import React, { useEffect, useState } from 'react';
import { Modal, Input, Button, Space, Typography, Toast } from '@douyinfe/semi-ui';
import { CopyOutlined, ReloadOutlined } from '@ant-design/icons';
import { getMcpToken, refreshMcpToken } from '@api/index';
import { NetworkCode } from '@pa/shared/dist/src/core';

const { Text } = Typography;

interface McpTokenModalProps {
  visible: boolean;
  email: string;
  onClose: () => void;
}

const McpTokenModal: React.FC<McpTokenModalProps> = ({ visible, email, onClose }) => {
  const [token, setToken] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const fetchToken = async () => {
    setLoading(true);
    try {
      const response = await getMcpToken({ data: { email } });
      if (response.code === NetworkCode.Success && response.data) {
        setToken(response.data);
      } else {
        Toast.error(response.message || '获取 MCP Token 失败');
      }
    } catch (error) {
      Toast.error('获取 MCP Token 失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchToken();
    }
  }, [visible]);

  const handleRefreshToken = async () => {
    setRefreshing(true);
    try {
      const response = await refreshMcpToken({ data: { email } });
      if (response.code === NetworkCode.Success && response.data) {
        setToken(response.data);
        Toast.success('MCP Token 刷新成功');
      } else {
        Toast.error(response.message || '刷新 MCP Token 失败');
      }
    } catch (error) {
      Toast.error('刷新 MCP Token 失败');
      console.error('刷新 MCP Token 失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCopyToken = () => {
    navigator.clipboard.writeText(token).then(
      () => {
        Toast.success('MCP Token 已复制到剪贴板');
      },
      () => {
        Toast.error('复制失败，请手动复制');
      },
    );
  };

  return (
    <Modal
      title="我的 MCP Token"
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
      width={600}
    >
      <Space vertical style={{ width: '100%' }}>
        <Text>您可以使用此 Token 访问 MCP 服务。Token 已加密，可以安全使用。</Text>
        <Input value={token} readOnly placeholder="加载中..." />
        <Space>
          <Button type="primary" icon={<CopyOutlined />} onClick={handleCopyToken} disabled={!token}>
            复制 Token
          </Button>
          <Button icon={<ReloadOutlined />} onClick={handleRefreshToken} loading={refreshing} disabled={loading}>
            刷新 Token
          </Button>
        </Space>
      </Space>
    </Modal>
  );
};

export default McpTokenModal;
