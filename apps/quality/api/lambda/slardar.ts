import { Api, Data, Post, useInject } from '@edenx/runtime/bff';
import {
  addSlardarCommentsSchema,
  checkAndUpdateBugSchema,
  createBugIssueSchema,
  fetchSceneListParam,
  findGrayVersionTimeSchema,
  findJOBInfoByIssueSchema,
  getIssuePercentParamsSchema,
  queryCrashListWithIssueIdSchema,
  queryCrashRateMetricSchema,
  QueryGetCrashListRequestSchema,
  queryGetSlardarCommentsSchema,
  queryHistoryCrashSchema,
  queryNewCrashSchema,
  querySlardarCrashIssueSchema,
  querySlardarCrashSchema,
  querySlardarInfoSchema,
  querySlardarValueSchema,
  querySlardarVersionQualityValueSchema,
  queryVersionDetail,
  sendWarningIssueSchema,
  top10CrashListSchema,
  updateIssueLevelSchema,
  updateSceneOwnerParam,
  updateSlardarCrashIssueSchema,
  updateSlardarWarnSchema,
} from '@shared/schema/alarm';
import SlardarValueModel, { DBSlardarValue } from '../model/SlardarValueTable';
import { ResultCode } from '@shared/typings/tea/metric';
import SlardarInfoModel from '../model/SlardarInfoTable';
import SlardarVersionQualityValueModel from '../model/SlardarVersionQualityValueTable';
import SlardarCrashIssueListModel from '../model/SlardarCrashIssueListTable';
import SlardarCrashIssueModel, { DBSlardarCrashIssue } from '../model/SlardarCrashIssueTable';
import AlarmVersionModel, { DBAlarmVersion } from '../model/AlarmVersionInfoTable';
import {
  CrashHistoryListInfo,
  CrashListInfo,
  CrashNewListInfo,
  CrashType,
  GetCrashFieldPercentResponseData,
  GetCrashFiledPercentParams,
  GetCrashListRequest,
} from '@shared/typings/slardar/crash/issueListSearch';
import {
  CrashType2Url,
  getOfflineSlardarIssueListParams,
  getOfflineSlardarIssueParams,
  getSlardarIssueListParams,
  getSlardarUrl,
} from '@shared/typings/slardar/common';
import SlardarVersionQualityIssueModel, {
  DBSlardarVersionQualityIssue,
} from '../model/SlardarVersionQualityIssueTable';

import { RedisClient } from '@gulux/gulux/redis';
import MeegoService from '../service/meego';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';

import SlardarService from 'api/service/slardar/slardar';
import BitsService from 'api/service/bits';
import LimitProcessService from 'api/service/LimitProcess';
import SlardarJOBModel from 'api/model/SlardarJobTable';
import { current_region, Region, utcOffset } from '../utils/region';
import dayjs from 'dayjs';
import SlardarUserCountModel from '../model/SlardarUserCountTable';
import {
  CompareVersionCode,
  filterHighestVersions,
  versionCodeToMeegoVersion,
  versionCodeToVersion,
  VersionType,
} from '@shared/utils/version_utils';
import utc from 'dayjs/plugin/utc';
import { extractor } from '@shared/utils/tools';
import SlardarAutoWarnService from '../service/slardar/autoWarn';
import LarkCardService from '../service/larkCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { z } from 'zod';
import SlardarVersionQualityRateModel from '../model/SlardarVersionQualityRateTable';
import { groupBy } from 'lodash';
import SlardarDAU3IssueListModel from '../model/SlardarDAU3IssueListTable';
import SlardarVersionNewIssueRateModel from '../model/SlardarVersionNewIssueRateTable';
import SlardarVersionNewIssueListModel from '../model/SlardarVersionNewIssueListTable';
import { UserCountType } from '@shared/typings/chart';
import SlardarRequestEngine from '../service/SlardarRequestEngine';
import { parseSlardarLink } from '../utils/slardarLink';
import { HYPIC_SLARDAR_APP_ID, SLARDAR_APP_ID } from '../service/slardar/shared';
import iOSVersionQualityService from 'api/service/slardar/iOSVersionQuality';
import { PlatformType } from '@pa/shared/dist/src/core';
import { DeviceLevel } from '@shared/common';
import { DebugAssignVersionTest } from '@api/debug';
import { GrayScaleCreateBug } from '../service/slardar/GrayscaleCreateBug';
import SlardarIssueBugItemModel from '../model/SlardarIssueBugItem';

export const querySlardarValue = Api(Post('/slardar/value'), Data(querySlardarValueSchema), async ({ data }) => {
  const slardarValueModel = useInject(SlardarValueModel);
  const alarmVersionModel = useInject(AlarmVersionModel);
  const userCountModel = useInject(SlardarUserCountModel);

  // base timestamp only for "current" version
  const { slardarInfoId, versionCodes, baseTimestamp, baseVersion, deviceLevel } = data;

  const resp: DBSlardarValue[] = [];
  const userCountResp: UserCountType[] = [];
  // filter duplicate versions
  const vc_unique = extractor(...new Set(versionCodes));
  for (const vc of vc_unique) {
    const base_ts =
      vc === baseVersion && baseTimestamp
        ? baseTimestamp
        : (await alarmVersionModel.findVersions(data.aid, [vc])).shift()?.timestamp;
    if (!base_ts) {
      // no such version
      continue;
    }
    const start_ts = dayjs.unix(base_ts).startOf('h').add(1, 'h').unix();
    const end_ts = start_ts + 72 * 3600;
    const cur_resp = await slardarValueModel.getValueForVersion(
      data.aid,
      slardarInfoId,
      vc,
      start_ts,
      end_ts,
      deviceLevel,
    );
    resp.push(
      ...cur_resp
        .map((it): DBSlardarValue => {
          it.HourFromVersion = Math.floor((it.Timestamp - start_ts) / 3600 + 1);
          return it;
        })
        .filter(it => it.HourFromVersion <= 72),
    );

    const cur_count = await userCountModel.findInRange(data.aid, vc, start_ts, end_ts, deviceLevel);
    userCountResp.push(
      ...cur_count
        .map(it => ({
          hourFromVersion: Math.floor((it.timestamp - start_ts) / 3600 + 1),
          version: vc,
          count: it.count,
        }))
        .filter(it => it.hourFromVersion <= 72),
    );
  }
  return {
    Code: ResultCode.success,
    Message: 'OK',
    ValueList: resp.sort((a, b) =>
      a.HourFromVersion !== b.HourFromVersion
        ? a.HourFromVersion - b.HourFromVersion
        : b.VersionCode.localeCompare(a.VersionCode),
    ),
    CountList: userCountResp.sort((a, b) =>
      a.hourFromVersion !== b.hourFromVersion
        ? a.hourFromVersion - b.hourFromVersion
        : b.version.localeCompare(a.version),
    ),
  };
});

export const querySlardarInfo = Api(Post('/slardar/info'), Data(querySlardarInfoSchema), async ({ data }) => {
  const slardarInfoModel = useInject(SlardarInfoModel);
  return {
    Code: ResultCode.success,
    Message: 'OK',
    InfoList: await slardarInfoModel.findEnabled(data.aid, [data.platform as SlardarPlatformType]),
  };
});

export const queryVersionQualityValue = Api(
  Post('/slardar/quality/value'),
  Data(querySlardarVersionQualityValueSchema),
  async ({ data }) => {
    const model = useInject(SlardarVersionQualityValueModel);
    return {
      Code: ResultCode.success,
      Message: 'OK',
      Data: await model.findVersionsIn(data.versions, data.deviceLevel),
    };
  },
);

export const queryVersionInfo = Api(
  Post('/version/detail'),
  Data(queryVersionDetail),
  async ({ data }) => await useInject(AlarmVersionModel).findVersions(data.aid, data.version_code),
);

export const fetchSceneList = Api(
  Post('/metric/scenelist'),
  Data(fetchSceneListParam),
  async ({ data }) => await useInject(SlardarRequestEngine).getSceneList(data.scene, data.owner),
);

export const iosQualityUpdate = Api(
  Post('/version/iosupdatequality'),
  Data(
    z.object({
      funcname: z.string().optional(),
      issueid: z.string().optional(),
      crashtype: z.string().optional(),
      count: z.number().optional(),
      needresignuser: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const xxxxx = useInject(iOSVersionQualityService);
    return await xxxxx.entry(data);
  },
);

export const updateSceneOwner = Api(
  Post('/metric/updatesceneowner'),
  Data(updateSceneOwnerParam),
  async ({ data }) =>
    await useInject(SlardarRequestEngine).updateSceneOwner(data.aid, data.platform, data.scene, data.owner),
);

export const fetchTransparentData = Api(
  Post('/slardar/fetchTransparentData'),
  Data(QueryGetCrashListRequestSchema),
  async ({ data }) => {
    const service = useInject(SlardarService);
    // @ts-ignore
    const queryData: GetCrashListRequest = { ...data };
    return service.crashIssueListSearch(queryData);
  },
);

export const getCrashInfoDetailById = Api(
  Post('/slardar/getCrashInfoDetailById'),
  Data(
    z.object({
      aid: z.number(),
      issue_id: z.string(),
      platform: z.nativeEnum(SlardarPlatformType),
      crash_type: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(SlardarService);
    return service.getCrashInfoById(data.aid, data.issue_id, data.platform, data.crash_type);
  },
);

export const getIssuePercent = Api(
  Post('/slardar/getIssuePercent'),
  Data(getIssuePercentParamsSchema),
  async ({ data }) => {
    const service = useInject(SlardarService);
    // @ts-ignore
    const queryData: GetCrashFiledPercentParams = { ...data };
    return service.getCrashFiledPercent(queryData);
  },
);

export const getCommentListData = Api(
  Post('/slardar/getCommentListData'),
  Data(queryGetSlardarCommentsSchema),
  async ({ data }) => {
    const service = useInject(SlardarService);
    return service.getCommentList(data.aid, data.issue_id, data.platform);
  },
);

export const addIssueComment = Api(
  Post('/slardar/addIssueComment'),
  Data(addSlardarCommentsSchema),
  async ({ data }) => {
    const service = useInject(SlardarService);
    return service.addIssueComment(data.aid, data.issue_id, data.platform, data.comment);
  },
);

export const querySlardarCrashList = Api(Post('/slardar/crash'), Data(querySlardarCrashSchema), async ({ data }) => {
  if (!data.version && !data.versionCode) {
    return {
      data: [],
      total: 0,
      totalDone: 0,
      totalSum: 0,
    };
  }
  const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
  const slardarCrashIssueModel = useInject(SlardarCrashIssueModel);
  const listInfos = await slardarCrashIssueListModel.querySlardarCrashIssueList(
    data.aid,
    data.platform,
    data.crashType,
    data.pageSize,
    data.current,
    data.filters,
    data.filterOOM,
    data.isNew,
    data.isMemoryGraph,
    data.versionCode,
    data.version,
    data.deviceLevel,
    data.sorter,
  );
  const issueIds = listInfos.data.map(item => item.issue_id);
  const issueInfos = await slardarCrashIssueModel.querySlardarCrashIssue(data.aid, issueIds);
  const version = data.version ? data.version : versionCodeToMeegoVersion(data.versionCode!);
  // const all_gray_version = await useInject(AlarmVersionModel).findGrayVersionCodes(data.aid, version, data.platform);
  // const last_gray_version = all_gray_version[all_gray_version.length - 1].version_code;
  const crashListInfos = listInfos.data
    .map(listItem => {
      const issueItem: DBSlardarCrashIssue | undefined = issueInfos.find(
        (item: DBSlardarCrashIssue) => item.issue_id === listItem.issue_id,
      );
      if (issueItem) {
        const crashListInfo: CrashListInfo = { ...issueItem, ...listItem } as CrashListInfo;
        crashListInfo.meego_url = '';
        if (issueItem.meego_map && version in issueItem.meego_map) {
          crashListInfo.meego_url = issueItem.meego_map[version];
        }
        crashListInfo.slardar_url = getSlardarUrl(
          data.aid,
          CrashType2Url[data.crashType],
          data.platform,
          [CrashType.JavaMemLeak, CrashType.JavaSmallInstance].includes(data.crashType)
            ? `perf_v2/memory/detail/${data.crashType === CrashType.JavaMemLeak ? 'activity' : 'instance'}/${crashListInfo.issue_id}`
            : `abnormal/detail/${CrashType2Url[data.crashType]}/${crashListInfo.issue_id}`,
          getSlardarIssueListParams(
            data.platform,
            data.versionCode ? data.versionCode : '',
            data.start_time,
            data.end_time,
            data.version,
            data.crashType,
          ),
        );
        return crashListInfo;
      }
      return undefined;
    })
    .filter(item => item !== undefined) as CrashListInfo[];
  if (data.platform === SlardarPlatformType.Android && data.versionCode && data.deviceLevel === DeviceLevel.ALL) {
    await slardarCrashIssueListModel.addPrevRanking(
      data.aid,
      crashListInfos,
      data.versionCode,
      data.platform,
      issueIds,
      data.crashType,
      data.deviceLevel,
    );
  }
  if (data.platform === SlardarPlatformType.Android && data.version && data.deviceLevel === DeviceLevel.ALL) {
    await slardarCrashIssueListModel.addAndroidPrevVersionRanking(
      data.aid,
      crashListInfos,
      data.version,
      data.platform,
      issueIds,
      data.crashType,
      data.deviceLevel,
    );
  }
  if (data.platform === SlardarPlatformType.iOS && data.deviceLevel === DeviceLevel.ALL) {
    await slardarCrashIssueListModel.addiOSPrevInfo(
      data.aid,
      crashListInfos,
      data.versionCode!,
      data.platform,
      issueIds,
      data.crashType,
      data.deviceLevel,
    );
  }
  await slardarCrashIssueListModel.addMeegoInfo(crashListInfos);
  if (data.isDeteriorate) {
    crashListInfos.sort((a, b) => {
      if (a.baseRanking && b.baseRanking && a.user_rate && b.user_rate && a.base_user_rate && b.base_user_rate) {
        return (
          (parseFloat(b.user_rate) - parseFloat(b.base_user_rate)) / parseFloat(b.base_user_rate) -
          (parseFloat(a.user_rate) - parseFloat(a.base_user_rate)) / parseFloat(a.base_user_rate)
        );
      }
      if (a.baseRanking) {
        return 1;
      }
      if (b.baseRanking) {
        return -1;
      }
      if (a.user_rate && b.user_rate) {
        return parseFloat(b.user_rate) - parseFloat(a.user_rate);
      }
      return 0;
    });
  }
  return {
    data: crashListInfos,
    total: listInfos.total,
    totalDone: listInfos.totalDone,
    totalSum: listInfos.totalOwner,
  };
});

export const queryOfflineSlardarCrashList = Api(
  Post('/slardar/offline_crash'),
  Data(querySlardarCrashSchema),
  async ({ data }) => {
    if (!data.version && !data.versionCode) {
      return {
        data: [],
        total: 0,
        totalDone: 0,
        totalSum: 0,
      };
    }
    const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
    const slardarCrashIssueModel = useInject(SlardarCrashIssueModel);
    const slardarIssueBugItemModel = useInject(SlardarIssueBugItemModel);
    const listInfos = await slardarCrashIssueListModel.queryOfflineSlardarCrashIssueList(
      data.aid,
      data.platform,
      data.crashType,
      data.pageSize,
      data.current,
      data.filters,
      data.filterOOM,
      data.isNew,
      data.isMemoryGraph,
      data.versionCode,
      data.version,
      data.deviceLevel,
      data.stage,
    );
    const issueIds = listInfos.data.map(item => item.issue_id);
    const issueInfos = await slardarCrashIssueModel.querySlardarCrashIssue(data.aid, issueIds);
    const version = data.version ? data.version : versionCodeToMeegoVersion(data.versionCode!);
    // const all_gray_version = await useInject(AlarmVersionModel).findGrayVersionCodes(data.aid, version, data.platform);
    // const last_gray_version = all_gray_version[all_gray_version.length - 1].version_code;
    const crashListInfos = listInfos.data
      .map(listItem => {
        const issueItem: DBSlardarCrashIssue | undefined = issueInfos.find(
          (item: DBSlardarCrashIssue) => item.issue_id === listItem.issue_id,
        );
        if (issueItem) {
          const crashListInfo: CrashListInfo = { ...issueItem, ...listItem } as CrashListInfo;
          crashListInfo.meego_url = '';
          if (issueItem.meego_map && version in issueItem.meego_map) {
            crashListInfo.meego_url = issueItem.meego_map[version];
          }
          crashListInfo.slardar_url = getSlardarUrl(
            data.aid,
            CrashType2Url[data.crashType],
            data.platform,
            [CrashType.JavaMemLeak, CrashType.JavaSmallInstance].includes(data.crashType)
              ? `perf_v2/memory/detail/${data.crashType === CrashType.JavaMemLeak ? 'activity' : 'instance'}/${crashListInfo.issue_id}`
              : `abnormal/detail/${CrashType2Url[data.crashType]}/${crashListInfo.issue_id}`,
            getOfflineSlardarIssueListParams(
              data.platform,
              data.aid,
              listItem.version_code, // data.versionCode ? data.versionCode : '',
              data.start_time,
              data.end_time,
              data.version,
              data.crashType,
            ),
          );
          return crashListInfo;
        }
        return undefined;
      })
      .filter(item => item !== undefined) as CrashListInfo[];
    if (data.platform === SlardarPlatformType.Android && data.version && data.deviceLevel === DeviceLevel.ALL) {
      await slardarCrashIssueListModel.addPrevRanking(
        data.aid,
        crashListInfos,
        `${data.version.replaceAll('.', '')}0000`,
        data.platform,
        issueIds,
        data.crashType,
        data.deviceLevel,
      );
    }
    if (data.platform === SlardarPlatformType.Android && data.version && data.deviceLevel === DeviceLevel.ALL) {
      await slardarCrashIssueListModel.addAndroidPrevVersionRanking(
        data.aid,
        crashListInfos,
        data.version,
        data.platform,
        issueIds,
        data.crashType,
        data.deviceLevel,
      );
    }
    if (data.platform === SlardarPlatformType.iOS && data.deviceLevel === DeviceLevel.ALL) {
      await slardarCrashIssueListModel.addiOSPrevInfo(
        data.aid,
        crashListInfos,
        data.version!,
        data.platform,
        issueIds,
        data.crashType,
        data.deviceLevel,
      );
    }
    await slardarCrashIssueListModel.addMeegoInfo(crashListInfos);
    if (data.isDeteriorate) {
      crashListInfos.sort((a, b) => {
        if (a.baseRanking && b.baseRanking && a.user_rate && b.user_rate && a.base_user_rate && b.base_user_rate) {
          return (
            (parseFloat(b.user_rate) - parseFloat(b.base_user_rate)) / parseFloat(b.base_user_rate) -
            (parseFloat(a.user_rate) - parseFloat(a.base_user_rate)) / parseFloat(a.base_user_rate)
          );
        }
        if (a.baseRanking) {
          return 1;
        }
        if (b.baseRanking) {
          return -1;
        }
        if (a.user_rate && b.user_rate) {
          return parseFloat(b.user_rate) - parseFloat(a.user_rate);
        }
        return 0;
      });
    }
    return {
      data: crashListInfos,
      total: listInfos.total,
      totalDone: listInfos.totalDone,
      totalSum: listInfos.totalOwner,
    };
  },
);

export const checkAndUpdateBug = Api(
  Post('/issue/check/update/bug'),
  Data(checkAndUpdateBugSchema),
  async ({ data }) => {
    const pattern = /detail\/(\d+)/;
    const match = data.url.match(pattern);
    const meegoId = match ? match[1] : 0;
    // const app = current_region() === Region.SG ? "CC" : "剪映";
    const meegoService = useInject(MeegoService);
    // const versionId = await meegoService.queryVersionId(
    // 	"faceu",
    // 	`${app}-${data.platform}-${versionCodeToVersion(data.version_code)}`
    // );
    // 找到所有issue_id相同中issue_level最小的issue_level,比较
    const temp = await meegoService.getIssuePriorityByUrl(data.url);
    const { cur_level } = temp;
    let BMVersion = data.version ? data.version : versionCodeToMeegoVersion(data.version_code);
    if (current_region() === Region.SG) {
      const [major, minor, patch] = BMVersion.split('.').map(Number);
      BMVersion = `${major + 2}.${minor}.${patch}`;
    }
    if (cur_level > data.priority) {
      await meegoService.updateIssue(Number(meegoId), data.priority, BMVersion, data.platform);
    }
  },
);

export const updateSlardarCrashIssue = Api(
  Post('/slardar/update/crash/issue'),
  Data(updateSlardarCrashIssueSchema),
  async ({ data }) => {
    console.log(data);
    const slardarCrashIssueModel = useInject(SlardarCrashIssueModel);
    const version = data.version ? data.version : versionCodeToMeegoVersion(data.version_code);
    await slardarCrashIssueModel.updateSlardarMeegoMap(
      data.aid,
      data.issue_id,
      data.meego_url,
      version, // versionCodeToMeegoVersion(data.version_code),
    );
    return { message: 'success' };
  },
);

export const updateSlardarWarn = Api(
  Post('/slardar/crash/warn/update'),
  Data(updateSlardarWarnSchema),
  async ({ data }) => {
    const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
    await slardarCrashIssueListModel.updateSlardarCrashWarn(
      data.aid,
      data.platform,
      data.versionCode,
      data.issue_id,
      data.crashType,
      data.is_warning,
    );
    return {
      msg: 'success',
    };
  },
);

export const queryTop10SlardarCrashList = Api(
  Post('/slardar/crash/top10'),
  Data(top10CrashListSchema),
  async ({ data }) => {
    const model = useInject<SlardarVersionQualityIssueModel>(SlardarVersionQualityIssueModel);
    return await model.findTop10(data.version_code, data.platform, data.crash_type, data.deviceLevel, data.aid);
  },
);

export const queryNewCrashList = Api(Post('/slardar/crash/new/list'), Data(queryNewCrashSchema), async ({ data }) => {
  const model = useInject(SlardarVersionNewIssueListModel);
  const res: CrashNewListInfo[] = [];
  const list = await model.query({
    platform: data.platform,
    crash_type: data.crashType,
    version_code: data.version_code,
  });
  const issueInfo = await useInject(SlardarCrashIssueModel).querySlardarCrashIssue(
    data.aid,
    list.map(v => v.issue_id),
  );
  for (const d of list) {
    const issue = issueInfo.find(v => v.issue_id === d.issue_id);
    if (issue) {
      res.push({
        crash_clazz: issue.crash_clazz,
        crash_exception: issue.crash_exception,
        crash_file: issue.crash_file,
        crash_line_number: issue.crash_line_number,
        crash_method: issue.crash_method,
        crash_reason: issue.crash_reason,
        crash_type: d.crash_type,
        end_os_version: issue.end_os_version,
        issue_id: issue.issue_id,
        managers: issue.managers,
        platform: issue.platform,
        ranking: d.ranking,
        rate: d.rate,
        start_os_version: issue.start_os_version,
        user: d.user,
        slardar_url: getSlardarUrl(
          issue.aid,
          CrashType2Url[data.crashType],
          data.platform,
          CrashType.JavaMemLeak === data.crashType
            ? `perf_v2/memory/detail/activity/${issue.issue_id}`
            : `abnormal/detail/${CrashType2Url[data.crashType]}/${issue.issue_id}`,
          getSlardarIssueListParams(
            data.platform,
            data.version_code,
            data.start_time,
            data.end_time,
            '',
            data.crashType,
          ),
        ),
      });
    }
  }
  return {
    data: res,
  };
});

export const queryHistoryCrashList = Api(
  Post('/slardar/crash/history/list'),
  Data(queryHistoryCrashSchema),
  async ({ data }) => {
    const model = useInject(SlardarDAU3IssueListModel);
    const lastTimestamp = data.timeStamp - 7 * 24 * 60 * 60;
    const lastData = new Date(lastTimestamp * 1000);
    const lastFormattedDate = `${lastData.getFullYear()}.${(lastData.getMonth() + 1)
      .toString()
      .padStart(2, '0')}.${lastData.getDate().toString().padStart(2, '0')}`;
    const res: CrashHistoryListInfo[] = [];
    const list = await model.query({
      aid: data.aid,
      platform: data.platform,
      crash_type: data.crashType,
      date: data.date,
      issue_id: { $in: data.issueIds },
    });
    const last_list = await model.query({
      platform: data.platform,
      crash_type: data.crashType,
      date: lastFormattedDate,
      issue_id: { $in: data.issueIds },
    });
    const issueInfo = await useInject(SlardarCrashIssueModel).querySlardarCrashIssue(data.aid, data.issueIds);
    for (const d of list) {
      const issue = issueInfo.find(v => v.issue_id === d.issue_id);
      const last = last_list.find(v => v.issue_id === d.issue_id);
      if (issue && last) {
        res.push({
          crash_clazz: issue.crash_clazz,
          crash_exception: issue.crash_exception,
          crash_file: issue.crash_file,
          crash_line_number: issue.crash_line_number,
          crash_method: issue.crash_method,
          crash_reason: issue.crash_reason,
          crash_type: d.crash_type,
          end_os_version: issue.end_os_version,
          issue_id: issue.issue_id,
          last_ranking: last.ranking,
          last_rate: last.rate,
          last_user: last.user,
          managers: issue.managers,
          platform: issue.platform,
          ranking: d.ranking,
          rate: d.rate,
          start_os_version: issue.start_os_version,
          user: d.user,
          slardar_url: getSlardarUrl(
            d.aid,
            CrashType2Url[data.crashType],
            data.platform,
            CrashType.JavaMemLeak === data.crashType
              ? `perf_v2/memory/detail/activity/${issue.issue_id}`
              : `abnormal/detail/${CrashType2Url[data.crashType]}/${issue.issue_id}`,
            getSlardarIssueListParams(
              data.platform,
              'DAU TOP3 version',
              data.timeStamp - 7 * 24 * 3600,
              data.timeStamp,
              '',
              data.crashType,
            ),
          ),
        });
      }
    }
    return {
      data: res,
    };
  },
);

export const queryNewCrashRateMetric = Api(
  Post('/slardar/crash/new/rate/metric'),
  Data(queryCrashRateMetricSchema),
  async ({ data }) => {
    const model = useInject(SlardarVersionNewIssueRateModel);
    const raw = await model.query({
      platform: data.platform,
      start_time: { $gte: data.start_time, $lte: data.end_time },
    });
    const res = groupBy(raw, d => d.crash_type);
    return {
      data: res,
    };
  },
);

export const queryCrashRateMetric = Api(
  Post('/slardar/crash/rate/metric'),
  Data(queryCrashRateMetricSchema),
  async ({ data }) => {
    const model = useInject(SlardarVersionQualityRateModel);
    const raw = await model.query({
      platform: data.platform,
      Timestamp: { $gte: data.start_time, $lte: data.end_time },
    });
    const res = groupBy(raw, d => d.crash_type);
    return {
      data: res,
    };
  },
);

export const queryCrashListWithIssueId = Api(
  Post('/slardar/crash/issue_id'),
  Data(queryCrashListWithIssueIdSchema),
  async ({ data }) => {
    const model = useInject<SlardarVersionQualityIssueModel>(SlardarVersionQualityIssueModel);
    return await model.findIssueIdIn(
      data.version_code,
      data.platform,
      data.crash_type,
      data.issue_list,
      data.deviceLevel,
      data.aid,
    );
  },
);

export type DBSlardarVersionQualityIssueAndReason = DBSlardarVersionQualityIssue & {
  reason: string;
};
export type CrashListInfoAndReason = CrashListInfo & {
  reason: string | undefined;
};
export const queryVersionDegradationIssues = Api(
  Post('/slardar/crash/version_degradation_issues'),
  Data(z.object({ product: z.string() })),
  async ({ data }) => {
    const redis = useInject(RedisClient);
    const aid = data.product === '剪映' ? 1775 : 3006;
    // const cacheDataStr = await redis.get(`slardar:degradation:${productKey}`);
    // const redisDegradation = cacheDataStr ? JSON.parse(cacheDataStr) : undefined;
    // if (redisDegradation) {
    // 	return redisDegradation;
    // }
    const qualityModel = useInject<SlardarVersionQualityIssueModel>(SlardarVersionQualityIssueModel);
    const versionModel = useInject<AlarmVersionModel>(AlarmVersionModel);
    const slardarCrashIssueModel = useInject(SlardarCrashIssueModel);
    const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
    const platform = SlardarPlatformType.Android;
    const constraint = {
      version_type: {
        $in: [VersionType.FULL, VersionType.ONLINE],
      },
    };
    // 获取所有大版本，todo 可以加个一年内的限制
    let versions: DBAlarmVersion[] = await versionModel.genericFind({ ...constraint, platform });
    // 去除临时版本
    const filterVersions = filterHighestVersions(versions.map(version => version.version));
    versions = versions.filter(version => filterVersions.indexOf(version.version) !== -1);
    const sortedVersions: DBAlarmVersion[] = versions
      .sort((a, b) => CompareVersionCode(a.version_code, b.version_code))
      .slice(-20);

    let lastJavaCrashTop500: DBSlardarVersionQualityIssue[] | undefined,
      lastNativeCrashTop500: DBSlardarVersionQualityIssue[] | undefined;

    function rateFormat(rate: number): string {
      const result = Number(rate * 1000).toFixed(4);
      return `${result}‰`;
    }

    function filterDegradetionIssues(
      crashTop50: DBSlardarVersionQualityIssue[],
      lasVersionIssues: DBSlardarVersionQualityIssue[] | undefined,
    ): DBSlardarVersionQualityIssueAndReason[] {
      const degradationIssues = new Set<DBSlardarVersionQualityIssueAndReason>();
      if (!lasVersionIssues) {
        return [];
      }
      for (const issue of crashTop50) {
        const lastVersionIssue = lasVersionIssues.find(value => value.issue_id === issue.issue_id);
        if (lastVersionIssue) {
          const base_rate = rateFormat(lastVersionIssue.user_rate);
          const rate = rateFormat(issue.user_rate);
          const final_rate = ((issue.user_rate - lastVersionIssue.user_rate) / lastVersionIssue.user_rate) * 100;
          const final_show_rate = final_rate > 1000 ? `1000+%` : `${final_rate.toFixed(0)}%`;

          if (issue.ranking <= 5) {
            if (lastVersionIssue.ranking >= 200) {
              const issueAndReason = issue as DBSlardarVersionQualityIssueAndReason;
              issueAndReason.reason = `top${lastVersionIssue.ranking} -> top${issue.ranking}`;
              degradationIssues.add(issueAndReason);
            } else if (final_rate >= 100) {
              const issueAndReason = issue as DBSlardarVersionQualityIssueAndReason;
              issueAndReason.reason = `top${issue.ranking}, ${base_rate} -> ${rate}(${final_show_rate})`;
              degradationIssues.add(issueAndReason);
            }
          } else if (issue.ranking <= 20) {
            if (lastVersionIssue.ranking >= 200) {
              const issueAndReason = issue as DBSlardarVersionQualityIssueAndReason;
              issueAndReason.reason = `top${lastVersionIssue.ranking} -> top${issue.ranking}`;
              degradationIssues.add(issueAndReason);
            } else if (final_rate >= 300) {
              const issueAndReason = issue as DBSlardarVersionQualityIssueAndReason;
              issueAndReason.reason = `top${issue.ranking}, ${base_rate} -> ${rate}(${final_show_rate})`;
              degradationIssues.add(issueAndReason);
            }
          } else if (issue.ranking <= 50) {
            if (lastVersionIssue.ranking >= 200) {
              const issueAndReason = issue as DBSlardarVersionQualityIssueAndReason;
              issueAndReason.reason = `top${lastVersionIssue.ranking} -> top${issue.ranking}`;
              degradationIssues.add(issueAndReason);
            } else if (final_rate >= 500) {
              const issueAndReason = issue as DBSlardarVersionQualityIssueAndReason;
              issueAndReason.reason = `top${issue.ranking}, ${base_rate} -> ${rate}(${final_show_rate})`;
              degradationIssues.add(issueAndReason);
            }
          }
        }
      }
      return [...degradationIssues];
    }

    const degradationData = [];
    for (const version of sortedVersions) {
      // 获取当前版本Top 500
      const javaCrashTop500 = await qualityModel.findIssueByVersionCode(
        version.version_code,
        SlardarPlatformType.Android,
        CrashType.JavaCrash,
        500,
      );
      const nativeCrashTop500 = await qualityModel.findIssueByVersionCode(
        version.version_code,
        SlardarPlatformType.Android,
        CrashType.NativeCrash,
        500,
      );

      const oomCrashIssueIds = await slardarCrashIssueListModel.queryOOMIds(
        aid,
        SlardarPlatformType.Android,
        version.version_code,
      );
      const javaCrashTop50 = javaCrashTop500
        .slice(0, 50)
        .filter(value => oomCrashIssueIds.indexOf(value.issue_id) === -1);
      const nativeCrashTop50 = nativeCrashTop500
        .slice(0, 50)
        .filter(value => oomCrashIssueIds.indexOf(value.issue_id) === -1);

      const degradationJavaIssues = filterDegradetionIssues(javaCrashTop50, lastJavaCrashTop500);
      const degradationNativeIssues = filterDegradetionIssues(nativeCrashTop50, lastNativeCrashTop500);
      lastJavaCrashTop500 = javaCrashTop500;
      lastNativeCrashTop500 = nativeCrashTop500;
      console.log(`${version.version_code}: ${degradationJavaIssues.length}, ${degradationNativeIssues.length}`);
      degradationJavaIssues.forEach(value => console.log(value.issue_id));
      degradationNativeIssues.forEach(value => console.log(value.issue_id));
      const javaIssuesIds = degradationJavaIssues.map(item => item.issue_id);
      const nativeIssuesIds = degradationNativeIssues.map(item => item.issue_id);
      const javaIssues = await slardarCrashIssueModel.querySlardarCrashIssue(aid, javaIssuesIds);
      const nativeIssues = await slardarCrashIssueModel.querySlardarCrashIssue(aid, nativeIssuesIds);
      console.log(`querySlardarCrashIssue: ${javaIssues.length}, ${nativeIssues.length}`);

      const issueLists = await slardarCrashIssueListModel.querySlardarCrashIssueListByIssueIdAndVersionCode(
        aid,
        platform,
        [...javaIssuesIds, ...nativeIssuesIds],
        version.version_code,
      );

      const issueInfos = [...javaIssuesIds, ...nativeIssuesIds].map(issueId => {
        const issueList = issueLists.find(crashIssueList => crashIssueList.issue_id === issueId);
        const issue = [...javaIssues, ...nativeIssues].find(crashIssue => crashIssue.issue_id === issueId);
        const crashListInfo: CrashListInfoAndReason = { ...issue, ...issueList } as CrashListInfoAndReason;
        if (!crashListInfo.crash_type) {
          crashListInfo.crash_type =
            javaIssuesIds.indexOf(issueId) !== -1 ? CrashType.JavaCrash : CrashType.NativeCrash;
        }
        crashListInfo.slardar_url = getSlardarUrl(
          aid,
          CrashType2Url[crashListInfo.crash_type],
          PlatformType.Android,
          [CrashType.JavaMemLeak, CrashType.JavaSmallInstance].includes(crashListInfo.crash_type)
            ? `perf_v2/memory/detail/${crashListInfo.crash_type === CrashType.JavaMemLeak ? 'activity' : 'instance'}/${crashListInfo.issue_id}`
            : `abnormal/detail/${CrashType2Url[crashListInfo.crash_type]}/${issueId}`,
        );
        crashListInfo.reason = [...degradationJavaIssues, ...degradationNativeIssues].find(
          value => value.issue_id === issueId,
        )?.reason;
        // issue id 可能不存在
        crashListInfo.issue_id = issueId;
        console.log(`reason:${crashListInfo.reason}`);
        return crashListInfo;
      });

      degradationData.push({
        version: version.version,
        javaDegradationIssues: degradationJavaIssues,
        nativeDegradationIssues: degradationNativeIssues,
        issueInfos,
      });
    }
    // redis.set(`slardar:degradation:${productKey}`, JSON.stringify(degradationData));
    // redis.expire(`slardar:degradation:${productKey}`, 28800);
    return degradationData;
  },
);

export const querySlardarCrashIssue = Api(
  Post('/slardar/crash/issue'),
  Data(querySlardarCrashIssueSchema),
  async ({ data }) => {
    const model = useInject(SlardarCrashIssueModel);
    return model.querySlardarCrashIssue(data.aid, data.issue_list);
  },
);

export const findJOBInfoByIssue = Api(
  Post('/slardar/issue/location'),
  Data(findJOBInfoByIssueSchema),
  async ({ data }) => {
    const service = useInject(SlardarService);
    const bitsService = useInject(BitsService);
    const jobModel = useInject(SlardarJOBModel);
    const limitProcess = useInject(LimitProcessService);
    const { os, crashType, issueId } = parseSlardarLink(data.issue_link);
    if (!(os && crashType && issueId)) {
      return { errmsg: '输入的 issue 链接解析失败' };
    }

    const resp = await jobModel.getValueByIssueId(os, issueId);
    if (resp.length > 0) {
      const job_url = `https://bits.bytedance.net/space/legacy/build/logs?jobId=${resp[0].jobId}`;
      const mr_url =
        resp[0].mrId !== '' ? `https://bits.bytedance.net/bytebus/devops/code/detail/${resp[0].mrId}` : undefined;
      return { errmsg: undefined, data: { job_url, mr_url } };
    }

    const canProcess = await limitProcess.dailyRateLimit('findJOBInfoByIssue', 500);

    if (!canProcess) {
      return { errmsg: '今天查询次数已超出' };
    }

    const end_time = Date.parse(new Date().toString()) / 1000;
    const start_time = end_time - 2592000;

    const lastEventResult = await service.getLastEventFilteredByJobInfo(
      SLARDAR_APP_ID(),
      start_time,
      end_time,
      crashType,
      issueId,
      os,
      data.is_oversea,
    );

    if (lastEventResult.errmsg !== 'success') {
      return { errmsg: lastEventResult.errmsg };
    }
    const lastEvent = lastEventResult.data;
    if (lastEvent) {
      const eventDetail = await service.queryEventDetail(crashType, os, data.is_oversea, lastEvent);
      let job_url: string | undefined, mr_url: string | undefined;
      if (eventDetail.filters.lv_job_info) {
        job_url = `https://bits.bytedance.net/space/legacy/build/logs?jobId=${eventDetail.filters.lv_job_info}`;
        const bitRes = await bitsService.getMRIdFromJobDetail(eventDetail.filters.lv_job_info);
        if (bitRes.mr_id) {
          mr_url = `https://bits.bytedance.net/bytebus/devops/code/detail/${bitRes.mr_id}`;
        }
        await jobModel.save({
          issueId: lastEvent.issue_id,
          jobId: eventDetail.filters.lv_job_info,
          mrId: bitRes.mr_id ?? '',
          platform: os,
        });
      }
      return { data: { job_url, mr_url } };
    } else {
      return { errmsg: '当前 issue 链接没有筛选到带 lv_job_info 的 event' };
    }
  },
);
export const querySmallGrayTime = Api(
  Post('/slardar/gray/version/time'),
  Data(findGrayVersionTimeSchema),
  async ({ data }) => {
    const alarmVersionModel = useInject(AlarmVersionModel);
    return await alarmVersionModel.findGrayingTime(data.version);
  },
);

export const getQuota = Api(Post('/slardar/quota'), Data(z.object({ aid: z.number() })), async ({ data: { aid } }) => {
  dayjs.extend(utc);
  const today = dayjs().utcOffset(utcOffset()).format('YYYYMMDD');
  const redis = useInject(RedisClient);
  return await redis.hgetall(`slardar:quota:${aid}:${today}`);
});

export const autoCreatBug = Api(Post('/auto/createBug'), Data(createBugIssueSchema), async ({ data }) => {
  const slardarService = useInject(SlardarService);
  const slardarCrashIssueListModel = useInject(SlardarCrashIssueListModel);
  try {
    const issueList = data.version
      ? await slardarCrashIssueListModel.queryCreateBugIssueListByVersion(data.aid, data.version, data.platform)
      : await slardarCrashIssueListModel.queryCreateBugIssueList(data.aid, data.versionCode, data.platform);
    console.log(`以下issue触发提单${issueList}`);
    // if (data.aid === HYPIC_SLARDAR_APP_ID() && data.platform === SlardarPlatformType.Android) {
    //   console.log('醒图Android暂时不进行自动提单');
    //   return { code: 1 };
    // }
    await slardarService.autoCreateBugProcessor(issueList, true, true, data.creator);
    return {
      code: 1,
    };
  } catch (e) {
    console.log(`自动提单失败，失败原因${e}`);
    return {
      code: -1,
      msg: `${e}`,
    };
  }
});

export const autoUpdateLevel = Api(Post('/auto/update/level'), Data(updateIssueLevelSchema), async ({ data }) => {
  try {
    const metrics = await useInject(SlardarInfoModel).find({ Id: data.crash_type });
    const versions = await useInject(AlarmVersionModel).genericFind({
      aid: data.aid,
      version_code: data.version_code,
      platform: data.platform,
    });
    await useInject(SlardarRequestEngine).slardarDataCollectForVersionMetrics(
      data.aid,
      versions,
      metrics,
      data.device_level,
      '2',
      data.platform,
      versions,
    );
    return {
      code: 0,
    };
  } catch (e) {
    console.log(`自动更新定级失败，失败原因${e}`);
    return {
      code: -1,
      err: `${e}`,
    };
  }
});
export const sendWarningMessage = Api(Post('/send/warning'), Data(sendWarningIssueSchema), async ({ data }) => {
  const alarmService = useInject(SlardarAutoWarnService);
  const autoWarn = useInject(SlardarAutoWarnService);
  const larkCard = useInject(LarkCardService);
  const bitsService = useInject(BitsService);
  const { bm } = await bitsService.getBMInfo(data.platform, data.versionCode, data.aid);
  let warningInfos = [];
  let tips;
  if (data.platform === SlardarPlatformType.Android) {
    const resp = await autoWarn.queryWarningIssueList(
      data.aid,
      data.versionCode,
      data.platform,
      data.start_time,
      data.end_time,
    );
    warningInfos = resp.data;
    tips = resp.tips;
  } else {
    warningInfos = await alarmService.queryiOSWarningIssueList(
      data.aid,
      data.versionCode,
      data.platform,
      data.start_time,
      data.end_time,
    );
  }
  const version = versionCodeToVersion(data.versionCode);
  const card = await larkCard.SlardarCrashIssueWarnCard(
    warningInfos,
    data.platform,
    data.versionCode,
    version,
    bm,
    tips,
    data.aid,
  );
  const { chatId } =
    data.aid === SLARDAR_APP_ID()
      ? await bitsService.getVersionBMGroup(versionCodeToMeegoVersion(data.versionCode), data.platform)
      : { chatId: 'oc_86bc29c0dd143bd02b9b0ad78949ff7c' }; // 醒图先推送测试群组
  const larkService = useInject(LarkService);
  try {
    /*
		const Users: string[] = [];
		for (const info of warningInfos) {
			for (const user of info.managers) {
				if (!Users.includes(user)) {
					Users.push(user);
				}
			}
		}
		if (chatId) {
			try {
				const UserIds = await larkService.getUserIdByEmails(Users);
				if (UserIds) {
					await larkService.inviteUsers2Group(
						UserIds.map((v) => v.user_id),
						chatId
					);
				}
			} catch (e) {}
		} */
    await larkService.sendCardMessage(UserIdType.chatId, chatId ? chatId : 'oc_421725f66bd9b296fff97e71a9e50466', card);
  } catch (err) {}
  if (data.aid === HYPIC_SLARDAR_APP_ID()) {
    try {
      await larkService.sendCardMessage(UserIdType.openId, 'ou_10921f450212535eff605673272012c7', card);
    } catch (err) {}
  }
  return { bm, chatId, data: 'success' };
});

export const queryUserCount = Api(
  Post('/slardar/userCount'),
  Data(
    z.object({
      aid: z.number(),
      versions: z.array(z.string()),
    }),
  ),
  async ({ data: { aid, versions } }) => {
    const model = useInject(SlardarUserCountModel);
    return await model.find(aid, versions);
  },
);

export const updateIssueLabels = Api(
  Post('/slardar/updateIssueLabels'),
  Data(
    z.object({
      aid: z.number(),
      issue_id: z.string(),
      platform: z.nativeEnum(SlardarPlatformType),
      labels: z.any().optional(),
    }),
  ),
  async ({ data: { aid, issue_id, platform, labels } }) => {
    const slardarCrashIssueModel = useInject(SlardarCrashIssueModel);
    return await slardarCrashIssueModel.updateLabels(aid, issue_id, platform, labels);
  },
);

export const GrayNotificationTest = Api(
  Post('/slardar/GrayNotificationTest'),
  Data(
    z.object({
      aid: z.number(),
      version: z.string().optional(),
      version_code: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    if (!data.version && !data.version_code) {
      return {
        code: -1,
        message: 'version and version_code are both empty',
      };
    }
    const versionModel = useInject(AlarmVersionModel);
    const currentTimestampInSeconds = Math.floor(Date.now() / 1000);
    if (data.version) {
      const version = await versionModel.findVersionCodes(data.aid, data.version, SlardarPlatformType.Android);
      await new GrayScaleCreateBug(
        SlardarPlatformType.Android,
        currentTimestampInSeconds,
        true,
        true,
      ).StatisticsAutoTestBug(version[0]);
    } else {
      const version = await versionModel.findGrayVersionCode(data.aid, data.version_code, SlardarPlatformType.Android);
      await new GrayScaleCreateBug(
        SlardarPlatformType.Android,
        currentTimestampInSeconds,
        false,
        true,
      ).StatisticsAutoTestBug(version[0]);
    }
    return {
      code: 0,
      message: 'ok',
    };
  },
);
/**
 * BFF for getCrashFiledPercent
 */
export const queryDimensions = Api(
  Post('/slardar/queryDimensions'),
  Data(
    z.object({
      aid: z.number(),
      platform: z.nativeEnum(SlardarPlatformType),
      start_time: z.number(),
      end_time: z.number(),
      filters_conditions: z.string().optional(),
      crash_type: z.string(),
      crash_time_type: z.string().optional(),
      issue_id: z.string().optional(),
      update_version_code: z.string().or(z.array(z.string())).optional(),
      version: z.string().or(z.array(z.string())).optional(),
      granularity: z.number().optional(),
      field: z.array(z.string()).optional(),
      map_key: z.array(z.string()).optional(),
    }),
  ),
  async ({ data }) => {
    const slardarService = useInject(SlardarService);
    const results = {
      code: [] as number[],
      message: [] as string[],
      field: {} as { [key: string]: GetCrashFieldPercentResponseData },
      map_key: {} as { [key: string]: GetCrashFieldPercentResponseData },
    };
    if (data.field) {
      for (const field of data.field) {
        const result = await slardarService.getCrashFiledPercent({
          aid: data.aid,
          platform: data.platform,
          start_time: data.start_time,
          end_time: data.end_time,
          filters_conditions: data.filters_conditions ? JSON.parse(data.filters_conditions) : undefined,
          granularity: data.granularity,
          crash_type: data.crash_type,
          crash_time_type: data.crash_time_type,
          issue_id: data.issue_id,
          update_version_code: data.update_version_code,
          version: data.version,
          field,
        });
        results.code.push(result.errno);
        results.message.push(result.errmsg);
        results.field[field] = result.data;
      }
    }
    if (data.map_key) {
      for (const map_key of data.map_key) {
        const result = await slardarService.getCrashFiledPercent({
          aid: data.aid,
          platform: data.platform,
          start_time: data.start_time,
          end_time: data.end_time,
          filters_conditions: data.filters_conditions ? JSON.parse(data.filters_conditions) : undefined,
          granularity: data.granularity,
          crash_type: data.crash_type,
          crash_time_type: data.crash_time_type,
          issue_id: data.issue_id,
          update_version_code: data.update_version_code,
          version: data.version,
          map_key,
        });
        results.code.push(result.errno);
        results.message.push(result.errmsg);
        results.map_key[map_key] = result.data;
      }
    }
    return results;
  },
);
