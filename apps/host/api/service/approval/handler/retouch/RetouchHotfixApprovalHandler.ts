import { useInject } from '@edenx/runtime/bff';
import { Logger } from '@gulux/gulux';
import dayjs from 'dayjs';
import { LarkClient } from '@gulux/gulux/lark';
import * as process from 'process';
import { BaseApprovalHandler } from '../BaseApprovalHandler';
import { ApprovalInfoTable } from '../../../../model/approval/ApprovalInfoModel';
import { teaCollect, TeaEvent } from '../../../../tea';
import {
  ApprovalDetail,
  ApprovalHistoryEvent,
  ApprovalOrderStatus,
  ApprovalType,
  HotfixTaskApproval,
} from '@shared/approval/ApprovalOrder';
import { ApprovalDBService } from '../../ApprovalDBService';
import ApprovalService from '../../ApprovalService';
import { ApprovalTaskEvent, ApprovalTaskStatus } from '@pa/shared/dist/src/lark/approval';
import {
  RequiredTrasactionCreateParams,
  VersionTransactionBinding,
  VersionTransactionType,
} from '@shared/releasePlatform/versionTransaction';

export class RetouchHotfixApprovalHandler extends BaseApprovalHandler {
  name = 'RetouchHotfixApprovalHandler';
  approvalCode = '52F85F89-EC63-44CA-BCB9-7214E5911FD5';

  override async handleApproved(approval?: ApprovalInfoTable): Promise<void> {
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.InsertProductRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_APPROVED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const logger = useInject(Logger);
    // 审批单落库
    const approvalDBService = useInject(ApprovalDBService);
    const approvalService = useInject(ApprovalService);
    const instanceCode = approval?.instanceCode;
    logger.info(
      `[${this.name}] handleApproved handleApproved approvalCode: ${approval?.approvalCode} instanceCode: ${instanceCode}`,
    );
    if (instanceCode) {
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Completed);
      await approvalService.hotfixTaskApproved(approval);
    }
    return;
  }

  override async handleRejected(approval?: ApprovalInfoTable): Promise<void> {
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.InsertProductRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_REJECTED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const logger = useInject(Logger);
    const approvalService = useInject(ApprovalService);
    // logger.info(`[HotfixApprovalHandler] handleRejected result:${JSON.stringify(result)} `);
    // 审批单落库
    const approvalDBService = useInject(ApprovalDBService);
    const instanceCode = approval?.instanceCode;
    logger.info(
      `[${this.name}] handleApproved handleRejected approvalCode: ${approval?.approvalCode} instanceCode: ${instanceCode}`,
    );
    if (instanceCode) {
      await approvalService.hotfixTaskRejected(approval);
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Rejected);
    }
    return;
  }

  async handleTask(event: ApprovalTaskEvent): Promise<boolean> {
    const approvalDBService = useInject(ApprovalDBService);
    const logger = useInject(Logger);
    logger.info(`[${this.name}] handleTask => ${JSON.stringify(event)}`);
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: event.instance_code,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }
    const instance = rsp?.data;
    const approval = await approvalDBService.findOneByTask(event);
    const approvalService = useInject(ApprovalService);

    // 查找自测节点任务
    const selfCheckTask = instance?.task_list?.find(it => it?.node_id === '9d8f794e3ecd2935b9a993464066119b'); // 自检节点
    const firstAssessmentTaskKeyName = '1c52bb0cce89474d44e3bedb6610082b'; // 一级审批节点名称，SOP标准判断
    const secondAssessmentTaskKeyName = '54e8ce3880b03f8f5f4ed03c19ec4673'; // 二级审批节点名称，热修质量影响面判断
    const thirdAssessmentTaskKeyName = '8026fb829ed76273da779749334c0e0c'; // 三级审批节点名称，受影响的各方判断
    const finalAssessmentTaskKeyName = '543c58330cb4a5b73eae53098be0cc2a'; // 最终审批节点名称，必要性判断
    const firstAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === firstAssessmentTaskKeyName);
    const secondAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === secondAssessmentTaskKeyName);
    const thirdAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === thirdAssessmentTaskKeyName);
    const finalAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === finalAssessmentTaskKeyName);
    if (approval) {
      if (approval.approvalStatus === ApprovalOrderStatus.STOP) {
        logger.info(`[${this.name}] approval => ${JSON.stringify(approval)}, do not to handle.`);
        return false;
      }
      if (event.status === ApprovalTaskStatus.APPROVED) {
        // 自测通过，发一级节点通知
        if (event.task_id === selfCheckTask?.id) {
          await approvalService.retouchHotfixTaskCommonApproved(approval, firstAssessmentTaskKeyName);
          await approvalService.retouchApprovalInvolveBusinessLineAssessment(approval);
        }
        if (
          firstAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          firstAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchHotfixTaskCommonApproved(approval, secondAssessmentTaskKeyName);
          await approvalService.retouchApprovalInvolveBusinessLineAssessment(approval);
        }
        if (
          secondAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          secondAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchHotfixTaskCommonApproved(approval, thirdAssessmentTaskKeyName);
          // 受影响业务方加签
          await approvalService.retouchApprovalInvolveBusinessLineAction(
            approval,
            thirdAssessmentTaskList?.[0]?.id ?? '',
          );
        }
        if (
          thirdAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          thirdAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchHotfixTaskCommonApproved(approval, finalAssessmentTaskKeyName);
          // 发送之前阶段的信息汇总
          const commentSummary = await approvalService.buildRetouchCommonSummary(
            event.instance_code,
            new Map<string, string[]>([
              [firstAssessmentTaskKeyName, firstAssessmentTaskList?.map(it => it.id) ?? []],
              [secondAssessmentTaskKeyName, secondAssessmentTaskList?.map(it => it.id) ?? []],
              [thirdAssessmentTaskKeyName, thirdAssessmentTaskList?.map(it => it.id) ?? []],
            ]),
          );
          if (commentSummary) {
            await approvalService.retouchHotfixSummary(approval, commentSummary);
          }
        }
      } else if (event.status === ApprovalTaskStatus.REJECTED) {
        // 审批拒绝
      }
      return true;
    } else {
      logger.info(`[${this.name}] handle not find approval => ${JSON.stringify(event)}`);
    }
    return false;
  }

  override constructVersionTrasactionInfo(approval?: ApprovalInfoTable): RequiredTrasactionCreateParams | undefined {
    const hotfixInfo = approval?.hotfixTask as HotfixTaskApproval;
    if (!hotfixInfo) {
      return undefined;
    }
    const bindings: VersionTransactionBinding[] = hotfixInfo.versions.map(it => ({
      app_id: hotfixInfo.appId,
      version: it,
    }));
    const transactionInfo = {
      type: VersionTransactionType.Hotfix,
      bindings,
      extra: {
        approvalDetail: approval as ApprovalDetail,
      },
    } as RequiredTrasactionCreateParams;
    return transactionInfo;
  }
}
