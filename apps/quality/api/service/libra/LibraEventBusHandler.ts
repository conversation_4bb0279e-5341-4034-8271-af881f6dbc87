import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { LibraBaseInfo, LibraEvent, LibraEventType, OperationReason } from '@shared/libra/libraInfo';
import { LibraAppIds } from '@shared/libra/commonLibra';
import { AppId2Name } from '@shared/libra/common';
import LarkService from '@pa/backend/dist/src/third/lark';
import {
  MsgLibraFullV2,
  MsgLibraOfflineV2,
  MsgLibraStartV2,
  MsgLibraTrafficChangeV2,
  MsgSettingSolidify,
} from './LibraCard';
import commonUtils from '@shared/utils/commonUtils';
import { isCN } from '@pa/backend/dist/src/utils/region';
import FlightConclusionReportService from './FlightConclusionReportService';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import LibraNewInfoListDao from '../../dao/libra/LibraNewInfoListDao';
import { PaAlarmService } from '@pa/backend/dist/src/utils/alarm';
import { LibraNotifyService } from './LibraNotifyService';
import { FlightType, LibraFlightType, LibraNotifyType, LibraStopType } from '@shared/libra/LibraNewInfo';
import { LibraNewInfoListService } from './LibraNewInfoListService';
import { getRealStopReason, LibraChangeEvent } from '@shared/libra/LibraChangeEvent';
import LibraChangeEventDao from '../../dao/libra/LibraChangeEventDao';
import LibraAPIService from './LibraAPIService';
import SettingsService from '../third/settings';
import { PublishStatus } from '@shared/settings/SettingsABTestConfig';
import { SettingsDetail } from '@shared/settings/SettingsDetail';
import { inPeakTime, isBigFlow } from '../../utils/libraUtil';
import { NetworkX } from '@pa/backend/dist/src/utils/network';
import { GrayOrRelease } from '@shared/libra/LibraCreate';
import {
  flightTypeDisplayName,
  LIBRA_APP_ID,
  MeegoProjectKeyFaceU,
  MeegoTeamNameByTeamId,
} from '@shared/libra/libraManageUtils';
import { add_suffix_ne } from '@shared/utils/tools';

/**
 * Note：由于这个类中的所有方法是在lifecycle中回调的，不是在一次请求中，所有调用链不可使用useInject，否则会出现useContext为空发生崩溃
 */
@Injectable({ scopeEscape: true })
export class LibraEventBusHandler {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private reportService: FlightConclusionReportService;
  @Inject()
  private libraNewInfoListDao: LibraNewInfoListDao;
  @Inject()
  private libraNewInfoListService: LibraNewInfoListService;
  @Inject()
  private alarmService: PaAlarmService;
  @Inject()
  private libraNotifyService: LibraNotifyService;
  @Inject()
  private libraChangeEventDao: LibraChangeEventDao;
  @Inject()
  private libraAPI: LibraAPIService;
  @Inject()
  private settingsService: SettingsService;
  @Inject()
  private rpcProxy: RpcProxyManager;

  // Libra 事件中心接入文档：https://bytedance.larkoffice.com/docx/AjAtdENVIoci9gxjqY4cSHJFnRf
  async didReceiveEvent(libraEvent: LibraEvent, eventbusId: string) {
    if (eventbusId.length === 0) {
      this.logger.error(`[LibraEventBusHandler] didReceiveEvent, eventbusId is empty`);
      return;
    }

    if (
      !(
        [
          LibraAppIds.lv,
          LibraAppIds.lv_pc,
          LibraAppIds.lv_web,
          LibraAppIds.cc,
          LibraAppIds.cc_pc,
          LibraAppIds.cc_web,
          LibraAppIds.retouch,
          LibraAppIds.hypic,
        ] as string[]
      ).includes(libraEvent.app_info.app_ids)
    ) {
      return;
    }

    this.logger.info(`[LibraEventBusHandler] didReceiveEvent: ${JSON.stringify(libraEvent)}`);
    const isOverseasTT = process.env.RUNTIME_IDC_NAME === 'sg1' || process.env.RUNTIME_IDC_NAME === 'my';
    if (isCN() || isOverseasTT) {
      // 由于实验管理所有数据库都在海外，国内的eventbus事件需要转发到海外处理（国内 + 非合规海外 均转发到合规海外）
      this.rpcProxy.getQuality(true).forwardCNLibraEvent(libraEvent, eventbusId).then();
      return;
    }

    // 根据 eventbusId 判断是否需要去重
    const existedEvent = await this.libraChangeEventDao.findOneWithNoAggregate({
      eventId: eventbusId,
    });
    if (existedEvent) {
      // 消息去重，已经有相关 id 的 eventbus 消息记录
      this.logger.info(`[LibraEventBusHandler] didReceiveEvent(repeated), eventId: ${eventbusId}`);
      return;
    }

    if (process.env.RUNTIME_IDC_NAME === 'useast9a' || process.env.RUNTIME_IDC_NAME === 'mya') {
      // 转发给 Cuba
      const network = new NetworkX('https://mycuba.fn.bytedance.net/api/libra_status_changed_hook', {});
      network.post('', libraEvent as any).catch(e => {
        this.logger.error(`[LibraEventBusHandler] forwardLibraEvent to CUBA failed: ${JSON.stringify(e)}`);
      });
    }

    // 转发给 CCIS
    const network = new NetworkX('https://37y0i3u3.fn.bytedance.net/api/Libra/SaveLibraEvent', {});
    network
      .post('', {
        libra_event: libraEvent as any,
      })
      .catch(e => {
        this.logger.error(`[LibraEventBusHandler] forwardLibraEvent to CCIS failed: ${JSON.stringify(e)}`);
      });

    // 触发LibraNewInfo表刷新
    await this.libraNewInfoListService.handleLibraEventReceived(libraEvent);
    // 在此注册所有事件处理方法
    const eventActionMap: Record<string, ((event: LibraEvent, eventbusId: string) => void)[]> = {
      [LibraEventType.FlightStart]: [this.recordChangeEvent, this.handleFlightStart],
      [LibraEventType.FlightStop]: [this.recordChangeEvent, this.handleFlightStop],
      [LibraEventType.FlightFull]: [this.recordChangeEvent, this.handleFlightFull],
      [LibraEventType.FlightChangeTraffic]: [this.recordChangeEvent, this.handleChangeTraffic],
      [LibraEventType.FlightSuspend]: [this.recordChangeEvent],
      [LibraEventType.FlightContinue]: [this.recordChangeEvent],
      [LibraEventType.FlightForceRestart]: [this.recordChangeEvent],
    };

    const actions = eventActionMap[libraEvent.event.event_type];
    if (actions && actions.length > 0) {
      await Promise.all(actions.map(async action => action.bind(this)(libraEvent, eventbusId)));
    }
  }

  private async getLibraBaseInfo(libraEvent: LibraEvent) {
    const appIds = libraEvent.app_info.app_ids; // 产品注册appid，若有多个以逗号分隔
    const appIdsArray = appIds.split(',').map(item => item.trim());
    const appNames = appIdsArray.map(item => AppId2Name[item] ?? '');
    const flightId = libraEvent.flight_info.flight_id;
    const isSG =
      appIds.includes(LibraAppIds.cc) ||
      appIds.includes(LibraAppIds.cc_web) ||
      appIds.includes(LibraAppIds.cc_pc) ||
      appIds.includes(LibraAppIds.hypic);
    const flightUrl = commonUtils.getLibraFlightUrl(flightId, isSG);
    const flightDisplayName = libraEvent.flight_info.flight_display_name;
    const flightOwners = libraEvent.flight_info.owner; // 实验owner，多个以逗号分隔
    const flightOwnersArray = flightOwners.split(',').map(item => item.trim());
    let owners = await this.larkService.getUserInfoByEmails(flightOwnersArray.map(owner => `${owner}@bytedance.com`));
    if (owners === undefined) {
      owners = [];
    }
    // 实验类型
    const flightType = flightTypeDisplayName(libraEvent.flight_info.flight_type);
    return {
      appIds: appIdsArray,
      appNames,
      flightId,
      flightName: flightDisplayName,
      flightUrl,
      flightOwners: owners,
      flightType,
      operator: libraEvent.event.event_operator,
    } as LibraBaseInfo;
  }

  // 处理实验流量变更
  async handleChangeTraffic(libraEvent: LibraEvent, eventbusId: string) {
    const baseInfo = await this.getLibraBaseInfo(libraEvent);
    const { traffic } = libraEvent.flight_info;
    const trafficChangeType = libraEvent.traffic_change_type;
    const flightInfo = await this.libraNewInfoListDao.findById(baseInfo.flightId);
    let startTraffic = '';
    let endTraffic = '';
    if (libraEvent.traffic_change_strategy.length > 0) {
      startTraffic = libraEvent.traffic_change_strategy[0].start_traffic;
      endTraffic = libraEvent.traffic_change_strategy[0].end_traffic;
    }
    const msg = new MsgLibraTrafficChangeV2(
      baseInfo,
      traffic,
      trafficChangeType,
      startTraffic,
      endTraffic,
      flightInfo,
      libraEvent,
    );
    await this.libraNotifyService.sendLibraMsg(LibraNotifyType.FlightTrafficChanged, msg, flightInfo);
  }

  // 处理实验全量
  async handleFlightFull(libraEvent: LibraEvent, eventbusId: string) {
    const baseInfo = await this.getLibraBaseInfo(libraEvent);
    const flightInfo = await this.libraNewInfoListDao.findById(baseInfo.flightId);
    let reportUrl: string | undefined;
    if (!flightInfo) {
      this.alarmService.reportError(
        `[LibraEventBusHandler] handleFlightFull: flight not found, flightId: ${baseInfo.flightId}`,
      );
    } else {
      // 更新一下 stopReason 和 stopType
      // flightInfo.flightInfo.stopReasonType = LibraStopType.FullRelease;
      // flightInfo.flightInfo.stopReasonDetail = libraEvent.event.operation_reason;
      // flightInfo.flightInfo.stopTime = Number(libraEvent.ts);
      // await this.libraNewInfoListService.tryToAddCloseAttributionOfLibraInfoByLibraReason(flightInfo, libraEvent); // 尝试自动填充实验关闭归因(根据 Libra 关闭原因)
      // await this.libraNewInfoListDao.update(flightInfo);
      reportUrl = await this.reportService.generateFlightConclusionReportDoc(
        flightInfo.flightInfo.region,
        flightInfo.flightInfo.id,
      );
    }
    const msg = new MsgLibraFullV2(
      baseInfo,
      reportUrl ?? '',
      flightInfo?.meegoInfo?.[0]?.daOwners ?? [],
      flightInfo,
      libraEvent,
    );
    await this.libraNotifyService.sendLibraMsg(LibraNotifyType.FlightFullyLaunched, msg, flightInfo);
  }

  private getFlightStopType(operationReason: string) {
    if (OperationReason[operationReason] && OperationReason[operationReason].includes('下线')) {
      return LibraStopType.Offline;
    } else if (OperationReason[operationReason] && OperationReason[operationReason].includes('重开')) {
      return LibraStopType.Reopen;
    }

    // 会存在“实验下线-其他”、“实验重开-其他”的情况，一般 operationReason.length > 0。这种情况不能归为“实验全量”
    // TODO: 已与 libra 反馈，补充“实验下线-其他”、“实验重开-其他”两种情况下的枚举值，以方便判定。待 Libra 后续支持。
    if (operationReason.length > 0) {
      return LibraStopType.Reopen; // 标记为重开，让其补充填写归因
    }

    return LibraStopType.FullRelease; // 全量的情况下，operationReason 一般都是空字符串
  }

  // 处理实验开启
  async handleFlightStart(libraEvent: LibraEvent, eventbusId: string) {
    const baseInfo = await this.getLibraBaseInfo(libraEvent);
    const flightInfo = await this.libraNewInfoListDao.findById(baseInfo.flightId);
    // 获取初始流量
    const startTraffic = libraEvent.flight_info.traffic;
    const msg = new MsgLibraStartV2(baseInfo, startTraffic, flightInfo, libraEvent);
    await this.libraNotifyService.sendLibraMsg(LibraNotifyType.FlightStart, msg, flightInfo);
  }

  // 处理实验停止
  async handleFlightStop(libraEvent: LibraEvent, eventbusId: string) {
    const baseInfo = await this.getLibraBaseInfo(libraEvent);
    const flightInfo = await this.libraNewInfoListDao.findById(baseInfo.flightId);
    const realReason = getRealStopReason(libraEvent.event.operation_reason);
    const isFullReason = [OperationReason.FullRelease, OperationReason.CollectData].includes(realReason);
    let reportUrl: string | undefined;
    if (isFullReason && flightInfo) {
      // 全量类型的关闭也需要生成报告
      reportUrl = await this.reportService.generateFlightConclusionReportDoc(
        flightInfo.flightInfo.region,
        flightInfo.flightInfo.id,
      );
    }
    // 存储实验停止事件到LibraNewInfo
    if (flightInfo) {
      flightInfo.flightInfo.stopReasonType = this.getFlightStopType(libraEvent.event.operation_reason);
      flightInfo.flightInfo.stopReasonDetail = libraEvent.event.operation_reason;
      const flightCurrentTraffic = flightInfo.flightInfo.trafficInfo.currentTrafficValue;
      // 如果是关闭的正式实验，并且是因为到期自动关闭、流量未到 100%，则不能标记为全量实验，需要进行校正
      if (
        flightInfo.flightInfo.type === LibraFlightType.Release &&
        flightInfo.flightInfo.stopReasonType === LibraStopType.FullRelease &&
        flightInfo.flightInfo.isAutoClosedByExpired &&
        flightCurrentTraffic < 1
      ) {
        // 修正关闭归因，标记为实验下线
        flightInfo.flightInfo.stopReasonType = LibraStopType.Offline;
        flightInfo.flightInfo.stopReasonDetail = '到期自动结束实验';
      }
      // 修正一些其他情况：
      // 情况 1：stopReasonDetail 不为空（一般 stopReasonDetail 为空，才是全量），并且 stopReasonDetail 不包含“推全”、“全量”等关键字
      // 情况 2：libra 关闭归因确实是全量，但实际却不是全量（和 libra 沟通，有些 libra api 创建的实验/或者未发起 review 就直接关闭的实验，没有 stopReasonDetail 或者 api 中的 close_reason 为空，这种情况暂时没办法判断是否是全量实验）
      // 针对情况 2，目前主要做如下限定(目前只针对剪映App和CapCut App)：
      // - 如果不是服务端实验（服务端实验看起来很容易全量，可能小时级别就到 100% 量级了），且开启实验间~关闭时间的间隔 < 7d、进组人数 < 1w，这种情况不应该判定为全量实验
      if (
        flightInfo.flightInfo.stopReasonType === LibraStopType.FullRelease &&
        flightInfo.flightInfo.type === LibraFlightType.Release // 只校正正式实验
      ) {
        if (
          flightInfo.flightInfo.stopReasonDetail &&
          flightInfo.flightInfo.stopReasonDetail.length > 0 &&
          !flightInfo.flightInfo.stopReasonDetail.includes('推全') &&
          !flightInfo.flightInfo.stopReasonDetail.includes('全量')
        ) {
          // 修正关闭归因，标记为实验下线
          flightInfo.flightInfo.stopReasonType = LibraStopType.Offline;
        } else if (
          flightInfo.flightInfo.libraType !== 'strategy' &&
          (flightInfo.libraAppId === LIBRA_APP_ID.JianYingApp || flightInfo.libraAppId === LIBRA_APP_ID.CapCutApp) &&
          flightInfo.flightInfo.startTime &&
          flightInfo.flightInfo.endTime
        ) {
          const less7days = flightInfo.flightInfo.endTime - flightInfo.flightInfo.startTime < 7 * 24 * 60 * 60;
          if (
            less7days &&
            flightInfo.flightInfo.userNumber < 10000 &&
            flightInfo.flightInfo.libraCloseReason !== undefined &&
            flightInfo.flightInfo.libraCloseReason !== 'FlightComplete'
          ) {
            // 修正关闭归因，标记为实验下线
            flightInfo.flightInfo.stopReasonType = LibraStopType.Offline;
            // flightInfo.flightInfo.stopReasonDetail = '实验开启时间小于 7d 并且进组人数小于 1w';
          }
        }
      }

      flightInfo.flightInfo.stopTime = Number(libraEvent.ts);
      await this.libraNewInfoListService.tryToAddCloseAttributionOfLibraInfoByLibraReason(flightInfo, libraEvent); // 尝试自动填充实验关闭归因(根据 Libra 关闭原因)
      if (flightInfo.extraInfo) {
        flightInfo.extraInfo.stopDetailReason = libraEvent.event.operation_reason;
        flightInfo.extraInfo.stopType = this.getFlightStopType(libraEvent.event.operation_reason);
      } else {
        flightInfo.extraInfo = {
          dataRecoveryRemind: [],
          grayOrRelease: GrayOrRelease.gray,
          isReopen: false,
          reopenReason: [],
          stopDetailReason: libraEvent.event.operation_reason,
          stopType: this.getFlightStopType(libraEvent.event.operation_reason),
        };
      }
      await this.libraNewInfoListDao.update(flightInfo);
    }
    const msg = new MsgLibraOfflineV2(baseInfo, realReason, flightInfo, libraEvent, reportUrl);
    await this.libraNotifyService.sendLibraMsg(LibraNotifyType.FlightOffline, msg, flightInfo);
    // settings实验需要固化提示
    const needSettingFullTip =
      isFullReason &&
      ([FlightType.SettingsClientSDK, FlightType.SettingsClientNormal] as string[]).includes(
        flightInfo?.flightInfo?.libraType ?? '',
      );
    this.logger.info(`handleFlightStop:${libraEvent.flight_info.flight_id} needSettingFullTip: ${needSettingFullTip}`);
    if (needSettingFullTip && flightInfo?.flightInfo) {
      const flightDetail = await this.libraAPI.queryFlight(flightInfo.flightInfo.region, flightInfo.flightInfo.id);
      this.logger.info(
        `handleFlightStop:${libraEvent.flight_info.flight_id} settings:${flightDetail?.settings_item_ids}`,
      );
      const notifySettings: SettingsDetail[] = [];
      if (flightDetail?.settings_item_ids && flightDetail?.settings_item_ids.length > 0) {
        for (const itemId of flightDetail.settings_item_ids) {
          const abConfigs = await this.settingsService.querySettingsItemABConfigs(itemId);
          const curFlight = abConfigs?.find(config => config?.flight_id === flightInfo.flightInfo.id);
          if (curFlight?.publish_status !== PublishStatus.Published) {
            const item = await this.settingsService.queryItemDetail(itemId);
            if (item) {
              notifySettings.push(item);
            }
          }
        }
      }
      if (notifySettings.length > 0) {
        const msg2 = new MsgSettingSolidify(flightInfo, realReason, notifySettings);
        await this.libraNotifyService.sendLibraMsg(LibraNotifyType.SettingSolidify, msg2, flightInfo);
      }
    }
  }

  // 发起review
  async handleReviewStart(libraEvent: LibraEvent, eventbusId: string) {}

  /**
   * libra变更事件入库
   * @param libraEvent
   * @param eventbusId
   */
  async recordChangeEvent(libraEvent: LibraEvent, eventbusId: string) {
    // 记录事件
    const changeEvent = {
      eventType: libraEvent.event.event_type as LibraEventType,
      eventInfluence: libraEvent.event.event_influence,
      eventOperator: libraEvent.event.event_operator,
      operationReason: libraEvent.event.operation_reason,
      flightId: Number(libraEvent.flight_info.flight_id),
      flightName: libraEvent.flight_info.flight_display_name,
      flightUrl: libraEvent.flight_info.flight_url,
      ts: Number(libraEvent.ts),
      trafficChangeType: libraEvent.traffic_change_type,
      trafficChangeStrategy: libraEvent.traffic_change_strategy,
      inPeakTime: inPeakTime(Number(libraEvent.ts)),
      isBigFlow: isBigFlow(libraEvent),
      traffic: libraEvent.flight_info.traffic,
      eventId: eventbusId,
    } as LibraChangeEvent;
    await this.libraChangeEventDao.create(changeEvent);
  }
}
