import { BaseApprovalHandler } from '../BaseApprovalHandler';
import { ApprovalInfoTable } from '../../../../model/approval/ApprovalInfoModel';
import { Promise } from '@byted/bytedmongoose';
import { teaCollect, TeaEvent } from '../../../../tea';
import {
  ApprovalDetail,
  ApprovalHistoryEvent,
  ApprovalOrderStatus,
  ApprovalType,
} from '@shared/approval/ApprovalOrder';
import dayjs from 'dayjs';
import process from 'process';
import { useInject } from '@edenx/runtime/bff';
import { Logger } from '@gulux/gulux';
import { ApprovalDBService } from '../../ApprovalDBService';
import ApprovalService from '../../ApprovalService';
import { LarkClient } from '@gulux/gulux/lark';
import { ApprovalTaskEvent, ApprovalTaskStatus } from '@pa/shared/dist/src/lark/approval';
import {
  RequiredTrasactionCreateParams,
  VersionTransactionBinding,
  VersionTransactionType,
} from '@shared/releasePlatform/versionTransaction';

export class RetouchFixedVersionApprovalHandler extends BaseApprovalHandler {
  approvalCode = '46728CD2-78E0-449E-9AF4-6B7D49A41D2D';
  name = 'RetouchFixedVersionApprovalHandler';

  async handleApproved(approval: ApprovalInfoTable): Promise<void> {
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.FixedVersionRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_APPROVED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const logger = useInject(Logger);
    // 审批单落库
    const approvalDBService = useInject(ApprovalDBService);
    const approvalService = useInject(ApprovalService);
    const instanceCode = approval?.instanceCode;
    logger.info(
      `[RetouchFixedVersionApprovalHandler] handleApproved handleApproved approvalCode: ${approval?.approvalCode} instanceCode: ${instanceCode}`,
    );
    if (instanceCode) {
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Completed);
      await approvalService.fixVersionTaskApproved(approval);
      await approvalService.onFixVersionTaskApproved(approval);
      await this.updateFixVersion(instanceCode, approval);
    }
    return;
  }

  async updateFixVersion(instanceCode: string, approval: ApprovalInfoTable) {
    // 获取实例
    const larkClient = useInject(LarkClient);
    const logger = useInject(Logger);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: instanceCode,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return;
    }
    const instance = rsp?.data;
    logger.info(`[updateFixVersion] instance: ${JSON.stringify(instance)}`);
    if (!instance?.form) {
      return;
    }
    const approvalForm: { id: string; value: string }[] = JSON.parse(instance?.form);
    logger.info(`[updateFixVersion] approvalForm: ${JSON.stringify(approvalForm)}`);
    const fixVersion = approvalForm?.find(it => it?.id === 'widget17520690004520001')?.value;
    const timeStr = approvalForm?.find(it => it?.id === 'widget17520691507270001')?.value;
    logger.info(
      `[updateFixVersion] approvalForm: ${JSON.stringify(approvalForm)}, fixVersion: ${fixVersion}, timeStr: ${timeStr}`,
    );
    const time = timeStr ? dayjs(timeStr).unix() : dayjs().unix();
    // 更新一下小版本
    if (approval && approval?.fixVersionTask) {
      approval.fixVersionTask.expectedIntegrationTime = time;
      approval.fixVersionTask.desireVersion = fixVersion;
      const approvalDBService = useInject(ApprovalDBService);
      await approvalDBService.updateFixVersionTask(instanceCode, approval.fixVersionTask);
      return approval;
    }
  }

  async handleRejected(approval: ApprovalInfoTable): Promise<void> {
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.InsertProductRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_REJECTED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const logger = useInject(Logger);

    // logger.info(`[HotfixApprovalHandler] handleRejected result:${JSON.stringify(result)} `);
    // 审批单落库
    const approvalDBService = useInject(ApprovalDBService);
    const approvalService = useInject(ApprovalService);
    const instanceCode = approval?.instanceCode;
    logger.info(
      `[RetouchFixedVersionApprovalHandler] handleApproved handleRejected approvalCode: ${approval?.approvalCode} instanceCode: ${instanceCode}`,
    );
    if (instanceCode) {
      await approvalService.fixVersionTaskRejected(approval);
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Rejected);
    }
    return;
  }

  async handleTask(event: ApprovalTaskEvent): Promise<boolean> {
    const approvalDBService = useInject(ApprovalDBService);
    const logger = useInject(Logger);
    logger.info(`[${this.name}] handleTask => ${JSON.stringify(event)}`);
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: event.instance_code,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }
    const instance = rsp?.data;
    // 查找任务节点
    const selfCheckTask = instance?.task_list?.find(it => it?.node_id === 'e8c67f707ae021ab4c88b3ba04e9ed73'); // 自检节点
    const firstAssessmentTaskKeyName = 'cb76074e7498823b2ce069e42e811aa7'; // 一级审批节点名称，SOP标准判断
    const secondAssessmentTaskKeyName = '1d2e4ad2e88041e2a1876454484c8842'; // 二级审批节点名称，小版本质量影响面判断
    const thirdAssessmentTaskKeyName = 'df7d292744af0054b079dcc8f2349809'; // 三级审批节点名称，版本节奏影响判断
    const fourthAssessmentTaskKeyName = '08f790110c946298c6e12b5355c71433'; // 四级审批节点名称，受影响业务线判断
    const finalAssessmentTaskKeyName = 'bf0f15b8ff0d785e96551167143104e8'; // 最终审批节点名称，必要性判断
    // taskId各不相同
    const firstAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === firstAssessmentTaskKeyName);
    const secondAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === secondAssessmentTaskKeyName);
    const thirdAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === thirdAssessmentTaskKeyName);
    const fourthAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === fourthAssessmentTaskKeyName);
    const finalAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === finalAssessmentTaskKeyName);

    const approval = await approvalDBService.findOneByTask(event);
    const approvalService = useInject(ApprovalService);
    if (approval) {
      if (approval.approvalStatus === ApprovalOrderStatus.STOP) {
        logger.info(`[${this.name}] approval => ${JSON.stringify(approval)}, do not to handle.`);
        return false;
      }

      if (event.status === ApprovalTaskStatus.APPROVED) {
        // 审批通过
        if (event.task_id === selfCheckTask?.id) {
          await approvalService.retouchFixVersionTaskCommonApproved(approval, firstAssessmentTaskKeyName);
          await approvalService.retouchApprovalInvolveBusinessLineAssessment(approval);
        }
        if (
          firstAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          firstAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchFixVersionTaskCommonApproved(approval, secondAssessmentTaskKeyName);
          await approvalService.retouchApprovalInvolveBusinessLineAssessment(approval);
        }
        if (
          secondAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          secondAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchFixVersionTaskCommonApproved(approval, thirdAssessmentTaskKeyName);
          await approvalService.retouchApprovalInvolveBusinessLineAssessment(approval);
        }
        if (
          thirdAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          thirdAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchFixVersionTaskCommonApproved(approval, fourthAssessmentTaskKeyName);
          // 受影响业务方加签
          await approvalService.retouchApprovalInvolveBusinessLineAction(
            approval,
            fourthAssessmentTaskList?.[0]?.id ?? '',
          );
        }
        if (
          fourthAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          fourthAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchFixVersionTaskCommonApproved(approval, finalAssessmentTaskKeyName);
          // 发送之前阶段的信息汇总
          const commentSummary = await approvalService.buildRetouchCommonSummary(
            event.instance_code,
            new Map<string, string[]>([
              [firstAssessmentTaskKeyName, firstAssessmentTaskList?.map(it => it.id) ?? []],
              [secondAssessmentTaskKeyName, secondAssessmentTaskList?.map(it => it.id) ?? []],
              [thirdAssessmentTaskKeyName, thirdAssessmentTaskList?.map(it => it.id) ?? []],
              [fourthAssessmentTaskKeyName, fourthAssessmentTaskList?.map(it => it.id) ?? []],
            ]),
          );
          if (commentSummary) {
            await approvalService.retouchFixVersionSummary(approval, commentSummary);
          }
        }
      } else if (event.status === ApprovalTaskStatus.REJECTED) {
        // 审批拒绝
      }
      return true;
    } else {
      logger.info(`[HotfixApprovalHandler] handle not find approval => ${JSON.stringify(event)}`);
    }
    return false;
  }

  override constructVersionTrasactionInfo(approval?: ApprovalInfoTable): RequiredTrasactionCreateParams | undefined {
    const fixVersionInfo = approval?.fixVersionTask;
    if (!fixVersionInfo) {
      return undefined;
    }
    const bindings: VersionTransactionBinding[] = [
      {
        app_id: fixVersionInfo.appId,
        version: fixVersionInfo.desireVersion,
      },
    ];
    const transactionInfo = {
      type: VersionTransactionType.AddSmallSync,
      bindings,
      extra: {
        approvalDetail: approval as ApprovalDetail,
      },
    } as RequiredTrasactionCreateParams;
    return transactionInfo;
  }
}
