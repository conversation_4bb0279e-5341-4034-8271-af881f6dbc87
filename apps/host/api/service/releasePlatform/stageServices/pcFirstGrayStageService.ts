import { Inject, Injectable } from '@gulux/gulux';
import BaseStageService from './baseStageService';
import VersionProcessInfoDao from '../../dao/releasePlatform/VersionProcessInfoDao';
import { VersionStageCheckListService } from '../versionStageCheckListService';
import LarkService from '@pa/backend/dist/src/third/lark';
import VersionReleaseCardService from '../VersionReleaseCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import {
  VersionMrStandard,
  VersionPhase,
  VersionProcessInfo,
  VersionStageInfo,
  VersionStageStatus,
} from '@shared/releasePlatform/versionStage';
import { PCSubGrayExtraData, VersionStageCheckList } from '@shared/releasePlatform/versionStageInfoCheckList';
import { SegmentsItem } from '@shared/bits/calendar';
import timeUtils from '../../../utils/timeUtil';
import { getStageInfo } from '@shared/releasePlatform/releasePlatformUtils';
import { Temporal } from '@js-temporal/polyfill';
import commonUtils from '../../../utils/commonUtils';
import { PCPackageData } from '@shared/bits/webHook';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import { ReleasePlatformMessageGroupType } from '../releasePlatformMessageService';
import versionUtils from '../../../utils/versionUtils';

@Injectable()
export default class PCFirstGrayStageService extends BaseStageService {
  @Inject()
  private versionProcessDao: VersionProcessInfoDao;
  @Inject()
  private stageChecklistService: VersionStageCheckListService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private versionReleaseCard: VersionReleaseCardService;
  @Inject()
  private logger: BytedLogger;

  stageName(): string {
    return 'first_gray_win';
  }

  canProcess(stage: string) {
    return (
      stage === 'first_gray_win' ||
      stage.split('@')[0] === 'first_sub_gray_win' ||
      stage.split('@')[0] === 'first_final_gray_win'
    );
  }

  async createStageInfo(
    calendars: SegmentsItem[],
    timelineConfig: VersionPhase,
  ): Promise<VersionStageInfo | undefined> {
    const mainStageCalendar = calendars.find(segment => segment.name.includes(timelineConfig.bits_calendar_segment));
    if (!mainStageCalendar && timelineConfig.bits_calendar_segment && timelineConfig.bits_calendar_segment.length > 0) {
      return undefined;
    }
    const startTime = mainStageCalendar
      ? timeUtils.keyNode2Hour(mainStageCalendar.calc_start_date, timelineConfig.start_hour ?? '00').epochSeconds +
        (timelineConfig.start_time_offset ?? 0)
      : 0;
    const endCalendar = calendars?.find(segment =>
      segment.name.includes(timelineConfig.end_calendar_segment ?? 'empty'),
    );
    const endTime = endCalendar
      ? timeUtils.keyNode2Hour(endCalendar.calc_end_date, timelineConfig.end_hour ?? '00').epochSeconds
      : startTime > 0
        ? startTime + (timelineConfig?.duration ?? 0)
        : 0;
    const versionStageInfo: VersionStageInfo = {
      stage_name: timelineConfig.stage_name,
      display_name: timelineConfig.display_name,
      start_time: startTime,
      real_start_time: startTime,
      end_time: endTime,
      real_end_time: 0,
      status: VersionStageStatus.NotStart,
      sub_stages: [],
      message_count_map: {},
      is_delay: false,
      mr_standard: timelineConfig.mr_standard,
      version_phase: timelineConfig,
      extra_data: timelineConfig.extra_data,
    };
    for (const subStage of timelineConfig.sub_stages) {
      const subStageCalendar = calendars.find(segment => segment.name.includes(subStage.bits_calendar_segment));
      if (!subStageCalendar && subStage.bits_calendar_segment && subStage.bits_calendar_segment.length > 0) {
        return undefined;
      }
      let subStageStartTime = subStageCalendar
        ? timeUtils.keyNode2Hour(subStageCalendar.calc_start_date, subStage.start_hour ?? '00').epochSeconds +
          (subStage.start_time_offset ?? 0)
        : 0;
      const subEndCalendar = calendars?.find(segment =>
        segment.name.includes(subStage.end_calendar_segment ?? 'empty'),
      );
      let subEndTime = subEndCalendar
        ? timeUtils.keyNode2Hour(subEndCalendar.calc_start_date, subStage.end_hour ?? '00').epochSeconds
        : subStageStartTime > 0
          ? subStageStartTime + (subStage?.duration ?? 0)
          : 0;
      if (subStageCalendar) {
        let originStartTime =
          timeUtils.keyNode2Hour(subStageCalendar.calc_start_date, subStage.start_hour ?? '00').epochSeconds +
          (subStage.start_time_offset ?? 0);
        let originEndTime = subEndCalendar
          ? timeUtils.keyNode2Hour(subEndCalendar.calc_start_date, subStage.end_hour ?? '00').epochSeconds
          : originStartTime > 0
            ? originStartTime + (subStage?.duration ?? 0)
            : 0;
        let offDay = await this.oncallService.isOffDay(originStartTime * 1000);
        let i = 0;
        while (offDay) {
          originStartTime += 86400;
          originEndTime += 86400;
          offDay = await this.oncallService.isOffDay(originStartTime * 1000);
          ++i;
          // 最多往后找7天，避免死循环
          if (i > 7) {
            break;
          }
        }
        subStageStartTime = originStartTime;
        subEndTime = originEndTime;
      }
      const subStageInfo: VersionStageInfo = {
        stage_name: subStage.stage_name,
        display_name: subStage.display_name,
        start_time: subStageStartTime,
        real_start_time: subStageStartTime,
        end_time: subEndTime,
        real_end_time: 0,
        status: VersionStageStatus.NotStart,
        sub_stages: [],
        message_count_map: {},
        is_delay: false,
        parent_stage_name: versionStageInfo.stage_name,
        mr_standard: timelineConfig.mr_standard,
        version_phase: subStage,
        extra_data: subStage.extra_data,
      };
      versionStageInfo.sub_stages.push(subStageInfo);
    }
    return versionStageInfo;
  }

  async updateStageInfo(versionInfo: VersionProcessInfo): Promise<void> {
    const firstGrayStage = getStageInfo(versionInfo.version_stages, 'first_gray_win');
    if (!firstGrayStage || !firstGrayStage.sub_stages || firstGrayStage.sub_stages.length === 0) {
      return;
    }
    // pc版本号格式是：7.3.0, 判断第二位是否偶数
    const version = versionInfo.version.split('.');
    if (version.length < 2) {
      return;
    }
    const isEven = Number(version[1]) % 2 === 0;
    // 如果是偶数，需要再添加两轮灰度，开始时间取已有最后一轮灰度的开始时间加一天，以此类推，注意节假日
    if (isEven) {
      const lastGraySubstage = firstGrayStage.sub_stages[firstGrayStage.sub_stages.length - 1];
      const actualGrayCount =
        versionInfo.app_id === AppSettingId.LV_WIN || versionInfo.app_id === AppSettingId.LV_MAC
          ? firstGrayStage.sub_stages.length
          : firstGrayStage.sub_stages.length;
      const startTime = lastGraySubstage.start_time;
      for (let j = 1; j < 2; ++j) {
        const originStartTime = firstGrayStage.sub_stages[firstGrayStage.sub_stages.length - 1].start_time + 86400;
        const originEndTime = originStartTime + 86300;
        let tempTime = originStartTime;
        let offDay = await this.oncallService.isOffDay(originStartTime * 1000);
        let i = 0;
        while (offDay) {
          ++i;
          tempTime += 86400;
          offDay = await this.oncallService.isOffDay(tempTime * 1000);
          // 最多往后找7天，避免死循环
          if (i > 7) {
            break;
          }
        }
        const subStageStartTime = originStartTime + i * 86400;
        const subEndTime = originEndTime + i * 86300;
        const subStageInfo: VersionStageInfo = {
          stage_name: `first_sub_gray_win@${j + actualGrayCount}`,
          display_name: `第${j + actualGrayCount}轮灰度`,
          start_time: subStageStartTime,
          real_start_time: subStageStartTime,
          end_time: subEndTime,
          real_end_time: 0,
          status: VersionStageStatus.NotStart,
          sub_stages: [],
          message_count_map: {},
          is_delay: false,
          parent_stage_name: firstGrayStage?.stage_name,
          mr_standard: lastGraySubstage.mr_standard,
          version_phase: lastGraySubstage.version_phase,
          extra_data: lastGraySubstage.extra_data,
        };
        firstGrayStage?.sub_stages.push(subStageInfo);
      }
    }
  }

  async stageSummary(
    versionInfo: VersionProcessInfo,
    stageInfo: VersionStageInfo,
    subStageInfo?: VersionStageInfo,
  ): Promise<string> {
    const extraData = subStageInfo?.extra_data as PCSubGrayExtraData;
    if (!subStageInfo || (extraData?.releaseTime ?? -1) < 0) {
      if (subStageInfo?.status === VersionStageStatus.Complete) {
        return '本轮灰度已跳过';
      } else {
        return '灰度打包中，如有严重问题需合入请联系BM';
      }
    }
    return `小版本号：${extraData.versionCode}, 放量时间：${timeUtils.now2Timeformat(extraData.releaseTime * 1000)}, 下载链接：${extraData.download_url}， 放量比例：${extraData.did_ratio}, 静默区间：${extraData.silence_range ?? ''}`;
  }

  getDesiredTimestamp(timestamp: number) {
    const now = new Date();
    const today19PM = new Date(now);
    today19PM.setHours(11, 0, 0, 0);

    if (timestamp > today19PM.getTime()) {
      return today19PM.getTime();
    } else {
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterday19PM = new Date(yesterday);
      yesterday19PM.setHours(11, 0, 0, 0);
      return yesterday19PM.getTime();
    }
  }

  async canComplete(
    versionInfo: VersionProcessInfo,
    stage: VersionStageInfo,
    checklist?: VersionStageCheckList,
  ): Promise<boolean> {
    const fullStage = getStageInfo(versionInfo.version_stages, 'second_gray_win');
    if (!fullStage) {
      return super.canComplete(versionInfo, stage, checklist);
    }
    const curTime = Date.now() / 1000;
    if (curTime - fullStage.start_time > 0) {
      return super.canComplete(versionInfo, stage, checklist);
    } else {
      return false;
    }
  }

  async pcGrayReleased(pcPackage: PCPackageData) {
    let appID = 2020092383;
    if (pcPackage.is_oversea) {
      appID = pcPackage.platform === 'Mac' ? 35928902 : 35928901;
    } else {
      appID = pcPackage.platform === 'Mac' ? 2020092892 : 2020092383;
    }
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appID, version: pcPackage.version });
    if (!versionInfo) {
      this.logger.error('[pcGrayReleased] versionInfo is undefined!');
      return {
        code: -1,
        message: 'versionInfo is undefined!',
      };
    }
    const grayStage = getStageInfo(versionInfo.version_stages, this.stageName());
    if (!grayStage) {
      this.logger.error('[pcGrayReleased] grayStage is undefined!');
      return {
        code: -1,
        message: 'grayStage is undefined!',
      };
    }
    if (grayStage.status === VersionStageStatus.NotStart) {
      grayStage.status = VersionStageStatus.OnProgress;
    }

    // const currentTime = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    // 改成接收发布单的时间
    const currentTime = pcPackage.deploy_time;

    let firstIndex: number;
    // TTP项目灰度调整
    if (versionUtils.CompareVersionCode(versionInfo.version, '6.4.5') > 0) {
      firstIndex = 0;
    } else {
      firstIndex = appID === AppSettingId.CC_WIN || appID === AppSettingId.CC_MAC ? 1 : 0;

      this.logger.info(`[pc_version_auto] pre input ${grayStage.sub_stages[0].status}, ${grayStage.sub_stages[0].display_name}`)
      if (firstIndex === 1 && grayStage.sub_stages.length > 0) {
        // cc的第一个国内观察阶段转为完成
        this.logger.info(`[pc_version_auto] handle cc pc ${grayStage.sub_stages[0].status}, ${grayStage.sub_stages[0].display_name}`)
        grayStage.sub_stages[0].status = VersionStageStatus.Complete;
      }
    }

    // 灰度列表索引，先从首轮开始
    let betaIndex = 1;
    for (let i = firstIndex; i < grayStage.sub_stages.length; i++) {
      this.logger.info(`[pc_version_auto] start ${i} ${betaIndex}`)
      if (grayStage.sub_stages[i].status !== VersionStageStatus.Complete) {
        const extraData = grayStage.sub_stages[i].extra_data
          ? (grayStage.sub_stages[i].extra_data as PCSubGrayExtraData)
          : undefined;
        // 加一个保护，如果是加量，灰度版本号不变，不用添加新的灰度，直接返回
        if (
          extraData?.versionCode &&
          extraData?.versionCode === pcPackage.version_code &&
          extraData?.releaseTime &&
          extraData?.releaseTime > 0
        ) {
          if (extraData && extraData.versionCode === pcPackage.version_code) {
            extraData.download_url = pcPackage.download_url;
            extraData.did_ratio = pcPackage.did_ratio;
          }
          await this.versionProcessDao.updateByCriteria(
            { version: pcPackage.version, app_id: appID },
            versionInfo,
            false,
          );
          const card = await this.versionReleaseCard.buildPCGrayCard(versionInfo, betaIndex, grayStage, pcPackage);
          await this.messageDao.sendVersionMessage(
            ReleasePlatformMessageGroupType.BmReleaseGroup,
            card,
            versionInfo,
          );
          this.logger.info(`[pc_version_auto] 重复版本，已更新放量，第${betaIndex}轮`)
          return {
            code: 0,
            message: '重复版本，已更新放量',
          };
        }
        if ((extraData?.releaseTime ?? 0) > 0) {
          grayStage.sub_stages[i].status = VersionStageStatus.Complete;
          grayStage.sub_stages[i].real_end_time = Temporal.Now.zonedDateTimeISO(
            commonUtils.defaultTimeZone,
          ).epochSeconds;

          betaIndex += 1;
          if (i + 1 < grayStage.sub_stages.length) {
            if (grayStage.sub_stages[i + 1].start_time > currentTime) {
              // 换包加灰
              const subGrayStageInfo: VersionStageInfo = {
                is_delay: false,
                mr_standard: 0,
                stage_name: `first_final_gray_win@${currentTime}`,
                display_name: `加灰`,
                start_time: currentTime,
                real_start_time: currentTime,
                end_time: 0,
                real_end_time: 0,
                status: VersionStageStatus.OnProgress,
                sub_stages: [],
                status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
                message_count_map: {},
                extra_data: {
                  releaseTime: currentTime,
                  versionCode: pcPackage.version_code,
                  packageTime: this.getDesiredTimestamp(currentTime) / 1000,
                  download_url: pcPackage.download_url,
                  did_ratio: pcPackage.did_ratio,
                  silence_range: pcPackage.silence_range,
                },
              };
              // 插入到当前的下一个
              grayStage.sub_stages.splice(i + 1, 0, subGrayStageInfo);
              await this.versionProcessDao.updateByCriteria(
                { version: pcPackage.version, app_id: appID },
                versionInfo,
                false,
              );
              const card = await this.versionReleaseCard.buildPCGrayCard(versionInfo, betaIndex, grayStage, pcPackage);
              await this.messageDao.sendVersionMessage(
                ReleasePlatformMessageGroupType.BmReleaseGroup,
                card,
                versionInfo,
              );
              this.logger.info(`[pc_version_auto] 当天加灰第${betaIndex}轮`)
              return {
                code: 0,
                message: '当天加灰',
              };
            }
            // 正常进入下一轮灰度
            grayStage.sub_stages[i + 1].status = VersionStageStatus.OnProgress;
            grayStage.sub_stages[i + 1].real_start_time = currentTime,
            grayStage.sub_stages[i + 1].extra_data = {
                versionCode: pcPackage.version_code,
                releaseTime: currentTime,
                download_url: pcPackage.download_url,
                did_ratio: pcPackage.did_ratio,
                silence_range: pcPackage.silence_range,
              } as PCSubGrayExtraData;
          } else {
            let grayCount = i + 2;
            if (versionInfo.app_id === AppSettingId.CC_MAC || versionInfo.app_id === AppSettingId.CC_WIN) {
              grayCount = i + 1;
            }
            const subGrayStageInfo: VersionStageInfo = {
              stage_name: `first_final_gray_win@${grayCount}`,
              display_name: `加灰-第${grayCount}轮灰度`,
              start_time: 0,
              real_start_time: currentTime,
              end_time: 0,
              real_end_time: 0,
              status: VersionStageStatus.OnProgress,
              sub_stages: [],
              status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
              message_count_map: {},
              extra_data: {
                releaseTime: currentTime,
                versionCode: pcPackage.version_code,
                packageTime: this.getDesiredTimestamp(currentTime) / 1000,
                download_url: pcPackage.download_url,
                did_ratio: pcPackage.did_ratio,
                silence_range: pcPackage.silence_range,
              } as PCSubGrayExtraData,
              is_delay: false,
              mr_standard: VersionMrStandard.SP0P1,
              parent_stage_name: this.stageName(),
            };
            grayStage.sub_stages.push(subGrayStageInfo);
          }
          await this.versionProcessDao.updateByCriteria({ version: pcPackage.version, app_id: appID }, versionInfo);
          const card = await this.versionReleaseCard.buildPCGrayCard(versionInfo, betaIndex, grayStage, pcPackage);
          await this.messageDao.sendVersionMessage(
            ReleasePlatformMessageGroupType.BmReleaseGroup,
            card,
            versionInfo,
          );
          this.logger.info(`[pc_version_auto] 延期加灰第${betaIndex}轮`)
          return {
            code: 0,
            message: '延期加灰',
          };
        } else {
          grayStage.sub_stages[i].extra_data = {
            versionCode: pcPackage.version_code,
            releaseTime: currentTime,
            download_url: pcPackage.download_url,
            did_ratio: pcPackage.did_ratio,
            silence_range: pcPackage.silence_range,
          } as PCSubGrayExtraData;
        }
        if (grayStage.sub_stages[i].status === VersionStageStatus.NotStart) {
          grayStage.sub_stages[i].status = VersionStageStatus.OnProgress;
          grayStage.sub_stages[i].real_start_time = currentTime;
        }
        await this.versionProcessDao.updateByCriteria(
          { version: pcPackage.version, app_id: appID },
          versionInfo,
          false,
        );
        const card = await this.versionReleaseCard.buildPCGrayCard(versionInfo, betaIndex, grayStage, pcPackage);
        await this.messageDao.sendVersionMessage(ReleasePlatformMessageGroupType.BmReleaseGroup, card, versionInfo);
        break;
      } else {
        // 已完成轮次递增
        if (grayStage.sub_stages[i].extra_data?.releaseTime ?? -1 > 0) {
          betaIndex += 1;
          this.logger.info(`[pc_version_auto] 已完成轮次递增至${betaIndex}轮`)
        }
      }
    }
    this.logger.info(`[pc_version_auto] 发灰成功 ${betaIndex}`)
    this.logger.info(`[adrGrayReleased] 第${grayStage.sub_stages.length}轮灰度`);
    return {
      code: 0,
      message: '发灰成功',
    };
  }

  async grayInfoUpdate(versionInfo: VersionProcessInfo, versionCode: string, time: number, subStageName: string) {
    const tfStage = versionInfo.version_stages.find(it => it.stage_name === this.stageName());
    if (!tfStage) {
      return;
    }
    const subStage = tfStage?.sub_stages.find(it => it.stage_name === subStageName);
    if (subStage) {
      (subStage.extra_data as PCSubGrayExtraData)
        ? ((subStage.extra_data as PCSubGrayExtraData).releaseTime = time)
        : {};
      (subStage.extra_data as PCSubGrayExtraData)
        ? ((subStage.extra_data as PCSubGrayExtraData).versionCode = versionCode)
        : {};
    } else {
      const curTime = new Date();
      const subGrayStageInfo: VersionStageInfo = {
        stage_name: `final_gray@${tfStage.sub_stages.length + 1}`,
        display_name: `加灰-第${tfStage.sub_stages.length + 1}轮灰度`,
        start_time: 0,
        real_start_time: 0,
        end_time: 0,
        real_end_time: 0,
        status: VersionStageStatus.NotStart,
        sub_stages: [],
        status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
        message_count_map: {},
        extra_data: {
          releaseTime: time > 0 ? time : -1,
          versionCode,
          packageTime: this.getDesiredTimestamp(curTime.getTime()) / 1000,
        } as PCSubGrayExtraData,
        is_delay: false,
        mr_standard: VersionMrStandard.SP0P1,
        parent_stage_name: this.stageName(),
      };
      tfStage.sub_stages.push(subGrayStageInfo);
    }
    await this.versionProcessDao.updateByCriteria(
      { version: versionInfo.version, app_id: versionInfo.app_id },
      versionInfo,
      false,
    );
  }
}
