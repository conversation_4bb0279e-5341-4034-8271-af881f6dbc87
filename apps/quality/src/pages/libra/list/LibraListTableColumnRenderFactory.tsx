import {
  LibraNewInfoTableColumnConfig,
  LibraNewInfoTablePageStatus,
} from '@shared/libra/LibraNewInfoTableColumnSetting';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import { LibraNewInfo, LibraNewInfoEditaResultType } from '@shared/libra/LibraNewInfo';
import { Space, Tag, TagGroup, Toast, Typography } from '@douyinfe/semi-ui';
import UserAvatarGroup from '@/component/UserAvatarGroup';
import {
  flightLayerTypeDisplayName,
  flightStatusColorMap,
  flightStatusDisplayName,
  flightTagColorArray,
  flightTypeDisplayName,
  formatNumber,
  formatTimeDifference,
  libraStopDetailReason2DisplayNameMap,
  libraStopType2DisplayNameMap,
  libraType2DisplayNameMap,
  MeegoTeamMapForUISelect,
  MeegoTeamNameByTeamId,
} from '@shared/libra/libraManageUtils';
import React from 'react';
import FlightVersionCard from '@/pages/libra/list/component/FlightVersionCard';
import { LibraRegion } from '@shared/libra/commonLibra';
import FlightTagSelectior from '@/pages/libra/list/component/FlightTagSelectior';
import { editFlightInfo, updateMainTeamOfLibraInfo } from '@api/libra';
import StabilityPatrolInfoCell from '@/pages/libra/list/component/StabilityPatrolInfoCell';
import FlightReportButton from '@/pages/libra/list/component/FlightReportButton';
import FlightChangeLogButton from '@/pages/libra/list/component/FlightChangeLogButton';
import { LibraPlatformPermission, LibraPlatformRoleType } from '@shared/libra/LibraPlatformUserMemberInfo';
import FlightCloseAttributionEditPanelButton from '@/pages/libra/list/component/FlightCloseAttributionEditPanelButton';
import { EventResponseTagSelector } from '@/pages/storyRevenueReviewPlatform/reviewTable/ReviewTableColumnObjectFactory';
import { StoryRevenueTaskInfo } from '@shared/storyRevenueReviewPlatform/StoryRevenueTaskInfo';
import LibraListMoreOptionMenu from '@/pages/libra/list/component/LibraListMoreOptionMenu';

const { Text, Title, Paragraph } = Typography;

const processEditResult = (res: { result: LibraNewInfoEditaResultType; newInfo?: LibraNewInfo }) => {
  if (res.result === LibraNewInfoEditaResultType.Success) {
    Toast.success('修改成功');
  }
  if (res.result === LibraNewInfoEditaResultType.Conflict) {
    Toast.error('修改失败，数据冲突，请刷新页面重试');
  }
  if (res.result === LibraNewInfoEditaResultType.UnknownError) {
    Toast.error('修改失败，未知错误');
  }
};
export const getLibraListTableColumnRender = (
  config: LibraNewInfoTableColumnConfig,
  pageStatus: LibraNewInfoTablePageStatus,
  userEmail: string,
  updateLibraInfo: (newInfo: LibraNewInfo) => void,
  onFlightDetailOpen: (flightDetail: LibraNewInfo) => void,
): ColumnProps<LibraNewInfo> | undefined => {
  const columnRenderMap: { [key: string]: ColumnProps<LibraNewInfo> } = {
    flight_id: {
      title: '实验ID',
      dataIndex: 'flightInfo.id',
      width: 110,
      fixed: true,
      render: (text, record) => (
        <Space vertical={true} align={'start'}>
          <Text>{record.flightInfo.id}</Text>
          <Text>{flightTypeDisplayName(record.flightInfo.libraType)}</Text>
        </Space>
      ),
    },
    flight_name: {
      title: '实验名称/描述/业务线/标签',
      dataIndex: 'flightInfo.name',
      width: 250,
      fixed: true,
      render: (text, record) => {
        let flightUrl = `https://data.bytedance.net/libra/flight/${record.flightInfo.id}/report/main`;
        if (record.flightInfo.region !== LibraRegion.CN) {
          flightUrl = `https://libra-sg.tiktok-row.net/libra/flight/${record.flightInfo.id}/report/main`;
        }
        return (
          <Space vertical={true} align={'start'} style={{ maxWidth: '100%' }}>
            <Text
              link
              ellipsis={true}
              onClick={() => {
                onFlightDetailOpen(record);
              }}
            >
              {record.flightInfo.name}
            </Text>
            <Paragraph
              type={'secondary'}
              ellipsis={{ rows: 3, showTooltip: { type: 'popover', opts: { style: { maxWidth: '80%' } } } }}
              style={{ width: '100%' }}
            >
              {record.flightInfo.description}
            </Paragraph>
            {record.meegoInfo !== undefined && (
              <TagGroup
                tagList={record.meegoInfo?.map((it, index) => {
                  const bussinessNames: string[] = [];
                  it.businessLine.forEach(it2 => {
                    if (it2.length) {
                      bussinessNames.push(it2);
                    }
                  });
                  return {
                    children: bussinessNames.join('/'),
                    color: flightTagColorArray[((index + 1) * 3) % flightTagColorArray.length],
                  };
                })}
              />
            )}
            {record.flightInfo.tags.length > 0 && (
              <TagGroup
                tagList={record.flightInfo.tags.map((it, index) => ({
                  children: it,
                  color: flightTagColorArray[index % flightTagColorArray.length],
                }))}
              />
            )}
          </Space>
        );
      },
    },
    flight_status: {
      title: '实验状态',
      dataIndex: 'flightInfo.status',
      width: 100,
      render: (text, record) => (
        <Tag color={flightStatusColorMap[record.flightInfo.status]}>
          {flightStatusDisplayName(record.flightInfo.status)}
        </Tag>
      ),
    },
    flight_versions: {
      title: '实验组',
      dataIndex: 'flightInfo.versions.vid',
      width: 200,
      render: (text, record) => (
        <Space vertical={true} align={'start'}>
          {record.flightInfo.versions.map(it => (
            <FlightVersionCard key={it.vid} libraVersionInfo={it} />
          ))}
        </Space>
      ),
    },
    flight_owner: {
      title: '实验Owner',
      dataIndex: 'flightInfo.owners',
      width: 200,
      render: (text, record) => <UserAvatarGroup users={record.flightInfo.owners} triggerType={'hover'} />,
    },
    flight_start_time: {
      title: '时间',
      dataIndex: 'flightInfo.startTime',
      width: 200,
      render: (text, record) => (
        <Space vertical={true} align={'start'}>
          <Tag>{formatTimeDifference(record.flightInfo.startTime * 1000, record.flightInfo.endTime * 1000)}</Tag>
          <Text size={'small'} style={{ color: 'gray' }}>
            {new Date(record.flightInfo.startTime * 1000).toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              timeZone: 'Asia/Shanghai',
            })}
          </Text>
          <Text size={'small'} style={{ color: 'gray' }}>
            {new Date(record.flightInfo.endTime * 1000).toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              timeZone: 'Asia/Shanghai',
            })}
          </Text>
        </Space>
      ),
    },
    flight_type: {
      title: '实验类型',
      dataIndex: 'flightInfo.type',
      width: 150,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record) => {
        const optionList = Object.keys(libraType2DisplayNameMap).map(it => ({
          value: it.toString(),
          label: libraType2DisplayNameMap[Number(it)],
        }));
        return (
          <FlightTagSelectior
            initValue={
              record.flightInfo.type || record.flightInfo.type === 0 ? record.flightInfo.type.toString() : undefined
            }
            options={optionList}
            onChange={async value => {
              await editFlightInfo({
                data: {
                  flightId: record.flightInfo.id,
                  libraAppId: record.libraAppId,
                  keyPath: 'flightInfo.type',
                  newValue: Number(value),
                  tmpValue: record.flightInfo.type,
                  editUserEmail: userEmail,
                },
              }).then(res => {
                if (res.newInfo) {
                  updateLibraInfo(res.newInfo);
                }
                processEditResult(res);
              });
            }}
            pageStatus={pageStatus}
            editPermission={LibraPlatformPermission.Edit}
          />
        );
      },
    },
    flight_quality_problem: {
      title: '质量问题实验',
      dataIndex: 'flightInfo.isQualityProblem',
      width: 150,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record) => {
        const optionList = [
          { value: '1', label: '是' },
          { value: '0', label: '否' },
        ];

        // 优先使用手动标记的值，如果没有则使用自动计算的值
        const currentValue = record.flightInfo.isQualityProblem;

        return (
          <FlightTagSelectior
            initValue={currentValue === undefined || currentValue === null ? undefined : currentValue ? '1' : '0'}
            options={optionList}
            onChange={async value => {
              const newQualityProblem = value !== undefined ? value === '1' : undefined;
              await editFlightInfo({
                data: {
                  flightId: record.flightInfo.id,
                  libraAppId: record.libraAppId,
                  keyPath: 'flightInfo.isQualityProblem',
                  newValue: newQualityProblem,
                  tmpValue: record.flightInfo.isQualityProblem,
                  editUserEmail: userEmail,
                },
              }).then(res => {
                if (res.newInfo) {
                  updateLibraInfo(res.newInfo);
                }
                processEditResult(res);
              });
            }}
            pageStatus={pageStatus}
            editPermission={LibraPlatformPermission.Edit}
            displayManuallyEditFlag={record.flightInfo.isManualMarkedQualityProblem}
          />
        );
      },
    },
    flight_traffic_info: {
      title: '流量信息',
      dataIndex: 'flightInfo.trafficInfo',
      width: 200,
      render: (text, record) => (
        <Space vertical={true} align={'start'}>
          <Tag>{`流量层：${record.flightInfo.trafficInfo.trafficLayer}`}</Tag>
          {record.flightInfo.trafficInfo.startTrafficValue && (
            <Text>{`开始流量：${record.flightInfo.trafficInfo.startTrafficValue * 100}%`}</Text>
          )}
          {record.flightInfo.trafficInfo.endTrafficValue && (
            <Text>{`结束流量：${record.flightInfo.trafficInfo.endTrafficValue * 100}%`}</Text>
          )}
          <Text>{`当前生效流量：${record.flightInfo.trafficInfo.currentTrafficValue * 100}%`}</Text>
        </Space>
      ),
    },
    flight_layer_type: {
      title: '分流类型',
      dataIndex: 'flightInfo.layerType',
      width: 100,
      render: (text, record) => <Tag>{flightLayerTypeDisplayName(record.flightInfo.layerType)}</Tag>,
    },
    // flight_reopen_type: {
    //   title: '重开归因',
    //   dataIndex: 'flightInfo.reopenReasonType',
    //   width: 200,
    //   onCell: (record, rowIndex) => ({
    //     style: { width: '100%', height: 50, padding: 0 },
    //   }),
    //   render: (text, record) => {
    //     const optionList = Object.keys(libraReopenType2DisplayNameMap).map(it => ({
    //       value: it.toString(),
    //       label: libraReopenType2DisplayNameMap[Number(it)],
    //     }));
    //     return (
    //       <FlightTagSelectior
    //         initValue={
    //           record.flightInfo.reopenReasonType || record.flightInfo.reopenReasonType === 0
    //             ? record.flightInfo.reopenReasonType.toString()
    //             : undefined
    //         }
    //         options={optionList}
    //         onChange={async value => {
    //           await editFlightInfo({
    //             data: {
    //               flightId: record.flightInfo.id,
    //               libraAppId: record.libraAppId,
    //               keyPath: 'flightInfo.reopenReasonType',
    //               newValue: Number(value),
    //               tmpValue: record.flightInfo.reopenReasonType,
    //               editUserEmail: userEmail,
    //             },
    //           }).then(res => {
    //             if (res.newInfo) {
    //               updateLibraInfo(res.newInfo);
    //             }
    //             processEditResult(res);
    //           });
    //         }}
    //         pageStatus={pageStatus}
    //         editPermission={LibraPlatformPermission.Edit}
    //       />
    //     );
    //   },
    // },
    // flight_reopen_detail: {
    //   title: '重开详细原因',
    //   dataIndex: 'flightInfo.reopenReasonDetail',
    //   width: 200,
    //   onCell: (record, rowIndex) => ({
    //     style: { width: '100%', height: 50, padding: 0 },
    //   }),
    //   render: (text, record) => (
    //     <LibraListEditableTextCell
    //       editPermission={LibraPlatformPermission.Edit}
    //       pageStatus={pageStatus}
    //       cellTitle={'编辑重开原因'}
    //       initText={record.flightInfo.reopenReasonDetail}
    //       onTextUpdate={async newText => {
    //         await editFlightInfo({
    //           data: {
    //             flightId: record.flightInfo.id,
    //             libraAppId: record.libraAppId,
    //             keyPath: 'flightInfo.reopenReasonDetail',
    //             newValue: newText,
    //             tmpValue: record.flightInfo.reopenReasonDetail,
    //             editUserEmail: userEmail,
    //           },
    //         }).then(res => {
    //           if (res.newInfo) {
    //             updateLibraInfo(res.newInfo);
    //           }
    //           processEditResult(res);
    //         });
    //       }}
    //     />
    //   ),
    // },
    // flight_stop_type: {
    //   title: '关闭原因',
    //   dataIndex: 'flightInfo.stopReasonType',
    //   width: 200,
    //   onCell: (record, rowIndex) => ({
    //     style: { width: '100%', height: 50, padding: 0 },
    //   }),
    //   render: (text, record) => {
    //     const optionList = Object.keys(libraStopType2DisplayNameMap).map(it => ({
    //       value: it.toString(),
    //       label: libraStopType2DisplayNameMap[Number(it)],
    //     }));
    //     return (
    //       <FlightTagSelectior
    //         initValue={record.flightInfo.stopReasonType ? record.flightInfo.stopReasonType?.toString() : undefined}
    //         options={optionList}
    //         onChange={async value => {
    //           await editFlightInfo({
    //             data: {
    //               flightId: record.flightInfo.id,
    //               libraAppId: record.libraAppId,
    //               keyPath: 'flightInfo.stopReasonType',
    //               newValue: Number(value),
    //               tmpValue: record.flightInfo.stopReasonType,
    //               editUserEmail: userEmail,
    //             },
    //           }).then(res => {
    //             if (res.newInfo) {
    //               updateLibraInfo(res.newInfo);
    //             }
    //             processEditResult(res);
    //           });
    //         }}
    //         pageStatus={pageStatus}
    //         editPermission={LibraPlatformPermission.Edit}
    //       />
    //     );
    //   },
    // },
    flight_close_attribution: {
      title: '实验关闭归因',
      width: 200,
      onCell: (record, rowIndex) => ({
        style: { width: '100%', height: 50, padding: 0 },
      }),
      render: (text, record) => (
        <FlightCloseAttributionEditPanelButton
          libraInfo={record}
          pageStatus={pageStatus}
          editPermission={LibraPlatformPermission.Edit}
          onEditSuccess={updatedLibraInfo => {
            updateLibraInfo(updatedLibraInfo);
          }}
        />
      ),
    },
    flight_close_attribution_from_libra: {
      title: 'Libra 关闭原因(仅参考)',
      width: 200,
      render: (text, record) => {
        if (record.flightInfo.stopReasonType !== undefined && record.flightInfo.stopReasonDetail !== undefined) {
          return (
            <Space vertical={true} align={'start'}>
              <Tag>{libraStopType2DisplayNameMap[record.flightInfo.stopReasonType]}</Tag>
              {record.flightInfo.stopReasonDetail.length > 0 && (
                <Tag>
                  {libraStopDetailReason2DisplayNameMap[record.flightInfo.stopReasonDetail] ??
                    `其他-${record.flightInfo.stopReasonDetail}`}
                </Tag>
              )}
            </Space>
          );
        }
        return <></>;
      },
    },
    // flight_stop_detail: {
    //   title: '关闭详细归因',
    //   dataIndex: 'flightInfo.stopReasonDetail',
    //   width: 200,
    //   onCell: (record, rowIndex) => ({
    //     style: { width: '100%', height: 50, padding: 0 },
    //   }),
    //   render: (text, record) => {
    //     const optionList = Object.keys(libraStopDetailReason2DisplayNameMap).map(it => ({
    //       value: it,
    //       label: libraStopDetailReason2DisplayNameMap[it],
    //     }));
    //     return (
    //       <FlightTagSelectior
    //         initValue={record.flightInfo.stopReasonDetail ? record.flightInfo.stopReasonDetail : undefined}
    //         options={optionList}
    //         onChange={async value => {
    //           await editFlightInfo({
    //             data: {
    //               flightId: record.flightInfo.id,
    //               libraAppId: record.libraAppId,
    //               keyPath: 'flightInfo.stopReasonDetail',
    //               newValue: value,
    //               tmpValue: record.flightInfo.stopReasonDetail,
    //               editUserEmail: userEmail,
    //             },
    //           }).then(res => {
    //             if (res.newInfo) {
    //               updateLibraInfo(res.newInfo);
    //             }
    //             processEditResult(res);
    //           });
    //         }}
    //         pageStatus={pageStatus}
    //         editPermission={LibraPlatformPermission.Edit}
    //       />
    //     );
    //   },
    // },
    // flight_stop_remark: {
    //   title: '关闭备注',
    //   dataIndex: 'flightInfo.stopRemark',
    //   width: 200,
    //   onCell: (record, rowIndex) => ({
    //     style: { width: '100%', height: 50, padding: 0 },
    //   }),
    //   render: (text, record) => (
    //     <LibraListEditableTextCell
    //       editPermission={LibraPlatformPermission.Edit}
    //       pageStatus={pageStatus}
    //       cellTitle={'编辑关闭备注'}
    //       initText={record.flightInfo.stopRemark}
    //       onTextUpdate={async newText => {
    //         await editFlightInfo({
    //           data: {
    //             flightId: record.flightInfo.id,
    //             libraAppId: record.libraAppId,
    //             keyPath: 'flightInfo.stopRemark',
    //             newValue: newText,
    //             tmpValue: record.flightInfo.stopRemark,
    //             editUserEmail: userEmail,
    //           },
    //         }).then(res => {
    //           if (res.newInfo) {
    //             updateLibraInfo(res.newInfo);
    //           }
    //           processEditResult(res);
    //         });
    //       }}
    //     />
    //   ),
    // },
    flight_user_count: {
      title: '进组人数',
      dataIndex: 'flightInfo.userCount',
      width: 130,
      render: (text, record) => {
        const { versions } = record.flightInfo;
        return (
          <Space vertical={true} align={'start'}>
            <Tag>{`总人数：${formatNumber(record.flightInfo.userNumber)}`}</Tag>
            {versions.map(
              it =>
                it.userNumber > 0 && (
                  <Tag key={it.vid} type={'ghost'}>{`${it.vname}：${formatNumber(it.userNumber)}`}</Tag>
                ),
            )}
          </Space>
        );
      },
    },
    feature_name: {
      title: '需求名称',
      dataIndex: 'meegoInfo.name',
      width: 200,
      render: (text, record) => (
        <Space vertical={true} align={'start'} style={{ maxWidth: '100%' }}>
          {record.meegoInfo?.map(it => (
            <Text
              link={{
                href: it.url,
                target: '_blank',
              }}
              ellipsis={true}
            >
              {it.name}
            </Text>
          )) ?? []}
        </Space>
      ),
    },
    business_line: {
      title: '业务线',
      dataIndex: 'meegoInfo.businessLine',
      width: 200,
      render: (text, record) => {
        const businessLines: string[] = [];
        record.meegoInfo?.forEach(it => {
          it.businessLine?.forEach(it2 => {
            if (it2.length && !businessLines.includes(it2)) {
              businessLines.push(it2);
            }
          });
        });
        return (
          <TagGroup
            tagList={businessLines.map((it, index) => ({
              children: it,
              color: flightTagColorArray[index % flightTagColorArray.length],
            }))}
            maxTagCount={2}
            style={{ maxWidth: '100%' }}
          />
        );
      },
    },
    business_team: {
      title: '业务团队',
      dataIndex: 'meegoTeamInfo.teamName',
      width: 250,
      render: (text, record) => {
        const businessTeams: string[] = [];
        let mainTeamName = '';
        record.meegoTeamInfo?.forEach(it => {
          if (it.teamName.length && !businessTeams.includes(it.teamName)) {
            businessTeams.push(it.teamName);
            if (it.isMainTeam) {
              mainTeamName = it.teamName;
            }
          }
        });
        return (
          <Space vertical={true} align={'start'}>
            {businessTeams.map((it, index) => (
              <Tag key={it} color={flightTagColorArray[index % flightTagColorArray.length]}>
                {mainTeamName.length > 0 && it === mainTeamName && businessTeams.length > 1 ? `${it}[主]` : `${it}`}
              </Tag>
            ))}
          </Space>
        );
      },
    },
    main_business_team: {
      title: '主责业务团队',
      dataIndex: 'meegoTeamInfo.teamId',
      width: 240,
      render: (text, record) => {
        const teamMap: Record<string, string> = MeegoTeamMapForUISelect;
        let optionList = Object.keys(teamMap).map(it => ({
          value: it,
          label: teamMap[it],
        }));
        let mainTeamId = 0;
        if (record.meegoTeamInfo && record.meegoTeamInfo.length > 0) {
          const teamIds = record.meegoTeamInfo.map(it => it.teamId);
          optionList = optionList.filter(it => teamIds.includes(Number(it.value)));
          for (const teamInfo of record.meegoTeamInfo) {
            if (teamInfo.isMainTeam) {
              mainTeamId = teamInfo.teamId;
              break;
            }
          }
        }
        const fakeTaskInfo = {
          terminated: true,
          status: 0,
          reviewPeriodId: '',
          reviewDuration: 0,
          fillInCompleted: false,
          meegoInfo: {
            id: '',
            url: '',
            name: '',
            owners: [],
            creator: {
              name: '',
            },
            type: '',
            status: '',
            primaryBusiness: '',
            secondaryBusiness: '',
            submodules: [],
            publishedRegions: [],
            publishedApps: [],
            prdUrl: '',
            rdWorkload: 0,
            relatedRDTeams: [],
            techOwners: [],
            testFinishedTime: 0,
            internalReviewFinishedTime: 0,
            priority: '',
          },
        } as StoryRevenueTaskInfo;

        let isLibraOwner = false;
        for (const owner of record.flightInfo.owners) {
          if (owner.email && owner.email === userEmail) {
            isLibraOwner = true;
            break;
          } else if (owner.name === userEmail.replace('@bytedance.com', '')) {
            isLibraOwner = true;
            break;
          }
        }
        const canEdit =
          record.meegoTeamInfo &&
          record.meegoTeamInfo.length > 0 &&
          ((pageStatus.userRoleType && Boolean(pageStatus.userRoleType & LibraPlatformPermission.Edit)) ||
            isLibraOwner);
        return (
          <div style={{ width: '100%', height: '100%' }}>
            <EventResponseTagSelector
              taskInfo={fakeTaskInfo}
              editable={canEdit !== undefined ? canEdit : false}
              options={optionList}
              defaultValue={mainTeamId > 0 ? mainTeamId.toString() : undefined}
              saveValue={async newValue => {
                const newValueIntValue = Number(newValue[0]);
                let selectedTeamId = 0;
                if (isNaN(newValueIntValue)) {
                  selectedTeamId = 0;
                } else {
                  selectedTeamId = newValueIntValue;
                }

                const result = await updateMainTeamOfLibraInfo({
                  data: {
                    flightId: record.flightInfo.id,
                    libraAppId: record.libraAppId,
                    mainTeamId: selectedTeamId,
                    shouldClear: selectedTeamId === 0,
                  },
                });
                if (result.code !== 0) {
                  Toast.error(`更新主业务团队失败，错误信息：${result.message}`);
                  return;
                }
                updateLibraInfo(result.data as LibraNewInfo);
                if (selectedTeamId === 0) {
                  Toast.success(`重置主责业务团队成功！`);
                } else {
                  Toast.success(`更新主责业务团队成功：${MeegoTeamNameByTeamId(selectedTeamId)}！`);
                }
              }}
            />
          </div>
        );
      },
    },
    published_app: {
      title: '上线应用',
      dataIndex: 'meegoInfo.releaseApps',
      width: 200,
      render: (text, record) => {
        const appNames: string[] = [];
        record.meegoInfo?.forEach(it => {
          it.releaseApps?.forEach(it2 => {
            if (it2.length && !appNames.includes(it2)) {
              appNames.push(it2);
            }
          });
        });
        return (
          <TagGroup
            tagList={appNames.map((it, index) => ({
              children: it,
              color: flightTagColorArray[index % flightTagColorArray.length],
            }))}
            maxTagCount={2}
            style={{ maxWidth: '100%' }}
          />
        );
      },
    },
    published_version: {
      title: '跟车版本',
      dataIndex: 'meegoInfo.releaseVersion',
      width: 200,
      render: (text, record) => {
        const versionNames: string[] = [];
        record.meegoInfo?.forEach(it => {
          it.releaseVersion?.forEach(it2 => {
            if (it2.length && !versionNames.includes(it2)) {
              versionNames.push(it2);
            }
          });
        });
        // 对 versionNames 进行排序（按照 A-Z，中文开头的则排在前面）
        versionNames.sort((a, b) => {
          const reg = /^[\u4e00-\u9fa5]/; // 匹配中文开头的正则表达式
          if (reg.test(a) && !reg.test(b)) {
            return -1; // a 是中文开头，b 不是，a 排在前面
          } else if (!reg.test(a) && reg.test(b)) {
            return 1; // b 是中文开头，a 不是，b 排在前面
          } else {
            return a.localeCompare(b); // 都不是中文开头，按照字母顺序排序
          }
        });
        return (
          <Space vertical={true} align={'start'}>
            {versionNames.map((it, index) => (
              <Tag key={it} color={flightTagColorArray[index % flightTagColorArray.length]}>
                {it}
              </Tag>
            ))}
          </Space>
        );
      },
    },
    stability_metrix: {
      title: '稳定性指标',
      dataIndex: 'patrolInfo.stabilityMetrics',
      width: 200,
      render: (text, record) => <StabilityPatrolInfoCell libraNewInfo={record} />,
    },
    business_metrix: {
      title: '业务核心指标',
      dataIndex: 'patrolInfo.businessCoreMetrics',
      width: 200,
      render: (text, record) => <Text />,
    },
    feedback_metrix: {
      title: '反馈指标',
      dataIndex: 'patrolInfo.feedbackMetrics',
      width: 200,
      render: (text, record) => <Text />,
    },
    operations: {
      title: '操作',
      dataIndex: 'operations',
      // width: 300,
      width: 150,
      render: (text, record) => (
        <Space align={'start'} style={{ width: '100%' }}>
          <FlightChangeLogButton libraInfo={record} />
          {/* <FlightPatrolLogButton libraInfo={record} /> */}
          <FlightReportButton libraInfo={record} updateLibraInfo={updateLibraInfo} />
          <LibraListMoreOptionMenu libraInfo={record} onLibraInfoUpdate={updateLibraInfo} />
        </Space>
      ),
    },
  };

  return columnRenderMap[config.column_id];
};
