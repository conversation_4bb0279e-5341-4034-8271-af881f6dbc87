import { Inject, Injectable } from '@gulux/gulux';
import { ModelType } from '@gulux/gulux/typegoose';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import CryptoJS from 'crypto-js';
import { PAMcpTokenTable } from '../../model/PAMcpTokenModel';
import { McpToken, McpTokenOperationResult } from '@shared/mcp/mcp-info';

/**
 * MCP Token 服务，用于生成、加密、解密和存储 token
 */
@Injectable()
export default class McpTokenService {
  @Inject(PAMcpTokenTable)
  private paMcpTokenModel: ModelType<PAMcpTokenTable>;

  @Inject()
  logger: BytedLogger;

  private readonly SECRET_KEY = 'mcp-token-secret-key';

  /**
   * 根据 email 生成 token
   * @param email 用户邮箱
   * @returns 加密后的 token
   */
  async generateToken(email: string): Promise<string> {
    if (!email) {
      throw new Error('Email is required');
    }

    // 生成 token（简单示例：时间戳 + email）
    const timestamp = Date.now();
    const rawToken = `${timestamp}:${email}`;

    // 加密 token
    const encryptedToken = this.encryptToken(rawToken);

    // 保存到数据库
    await this.saveToken(email, encryptedToken);

    return encryptedToken;
  }

  /**
   * 加密 token
   * @param token 原始 token
   * @returns 加密后的 token
   */
  encryptToken(token: string): string {
    return CryptoJS.AES.encrypt(token, this.SECRET_KEY).toString();
  }

  /**
   * 解密 token
   * @param encryptedToken 加密后的 token
   * @returns 解密后的 token
   */
  decryptToken(encryptedToken: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedToken, this.SECRET_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * 从 token 中获取 email
   * @param encryptedToken 加密后的 token
   * @returns email
   */
  getEmailFromToken(encryptedToken: string): string {
    try {
      const decryptedToken = this.decryptToken(encryptedToken);
      const [, email] = decryptedToken.split(':');
      return email;
    } catch (error) {
      this.logger.error('Failed to get email from token', error);
      throw new Error('Invalid token');
    }
  }

  /**
   * 保存 token 到数据库
   * @param email 用户邮箱
   * @param token 加密后的 token
   */
  async saveToken(email: string, token: string): Promise<void> {
    const query = { email };
    const update: Partial<McpToken> = {
      email,
      token,
      updatedAt: new Date(),
    };
    const options = { upsert: true, new: true };

    await this.paMcpTokenModel.findOneAndUpdate(query, update, options);
  }

  /**
   * 根据 email 获取 token
   * @param email 用户邮箱
   * @returns 加密后的 token，如果不存在则返回 null
   */
  async getTokenByEmail(email: string): Promise<string> {
    const tokenRecord = await this.paMcpTokenModel.findOne({ email }).exec();
    if (!tokenRecord) {
      return await this.generateToken(email);
    }
    return tokenRecord.token;
  }

  /**
   * 根据 email 获取完整的 token 记录
   * @param email 用户邮箱
   * @returns 完整的 token 记录，如果不存在则返回 null
   */
  async getTokenRecordByEmail(email: string): Promise<McpToken | null> {
    const tokenRecord = await this.paMcpTokenModel.findOne({ email }).exec();
    if (!tokenRecord) {
      return null;
    }

    return {
      email: tokenRecord.email,
      token: tokenRecord.token,
      createdAt: tokenRecord.createdAt,
      updatedAt: tokenRecord.updatedAt,
    };
  }
}
