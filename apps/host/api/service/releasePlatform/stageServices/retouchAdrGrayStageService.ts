import { Inject, Injectable } from '@gulux/gulux';
import BaseStageService from './baseStageService';
import VersionProcessInfoDao from '../../dao/releasePlatform/VersionProcessInfoDao';
import { VersionStageCheckListService } from '../versionStageCheckListService';
import LarkService from '@pa/backend/dist/src/third/lark';
import VersionReleaseCardService from '../VersionReleaseCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import {
  currentStage,
  getOnProgressStages,
  VersionMrStandard,
  VersionPhase,
  VersionProcessInfo,
  VersionStageInfo,
  VersionStageStatus,
} from '@shared/releasePlatform/versionStage';
import { Temporal } from '@js-temporal/polyfill';
import commonUtils from '../../../utils/commonUtils';
import {
  SubGrayExtraData,
  SubTestFlightExtraData,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { LVProductType, ProductType, RetouchProductType } from '@shared/process/versionProcess';
import { MAIN_HOST_HTTPS, PlatformType, User } from '@pa/shared/dist/src/core';
import { AppId2Name } from '@shared/experiment/experimentInfo';
import { BmType } from '@shared/bits/bmInfo';
import timeUtils from '../../../utils/timeUtil';
import { CardTemplate } from '@pa/shared/dist/src/lark/larkCard';
import { ReleasePlatformMessageGroupType } from '../releasePlatformMessageService';
import { SegmentsItem } from '@shared/bits/calendar';
import { getStageInfo, getUrlStageParams } from '@shared/releasePlatform/releasePlatformUtils';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { JobStepInfo } from '@shared/bits/jobInfo';

@Injectable()
export default class RetouchAdrGrayStageService extends BaseStageService {
  @Inject()
  private versionProcessDao: VersionProcessInfoDao;
  @Inject()
  private stageChecklistService: VersionStageCheckListService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private versionReleaseCard: VersionReleaseCardService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private businessConfig: BusinessConfigService;

  stageName(): string {
    return 'retouch_adr_gray';
  }

  canProcess(stage: string) {
    return (
      stage === 'retouch_adr_gray' ||
      stage.split('@')[0] === 'retouch_adr_sub_gray' ||
      stage === 'retouch_adr_gray_pre_check' ||
      stage === 'retouch_adr_final_gray'
    );
  }

  async createStageInfo(
    calendars: SegmentsItem[],
    timelineConfig: VersionPhase,
  ): Promise<VersionStageInfo | undefined> {
    const info = await super.createStageInfo(calendars, timelineConfig);
    info?.sub_stages?.forEach(it => {
      if (it.extra_data) {
        it.extra_data.packageTime = it.start_time;
      }
    });
    return info;
  }

  async stageSummary(
    versionInfo: VersionProcessInfo,
    stageInfo: VersionStageInfo,
    subStageInfo?: VersionStageInfo,
  ): Promise<string> {
    const extraData = subStageInfo?.extra_data as SubGrayExtraData;
    if (!subStageInfo || (extraData?.releaseTime ?? -1) < 0) {
      if (subStageInfo?.status === VersionStageStatus.Complete) {
        return '本轮灰度已跳过';
      } else {
        return '灰度打包中，如有严重问题需合入请联系BM';
      }
    }
    return `小版本号：${extraData.versionCode}, 放量时间：${timeUtils.now2Timeformat(extraData.releaseTime * 1000)}`;
  }

  getDesiredTimestamp(timestamp: number) {
    const now = new Date();
    const today19PM = new Date(now);
    today19PM.setHours(11, 0, 0, 0);

    if (timestamp > today19PM.getTime()) {
      return today19PM.getTime();
    } else {
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterday19PM = new Date(yesterday);
      yesterday19PM.setHours(11, 0, 0, 0);
      return yesterday19PM.getTime();
    }
  }

  async canComplete(
    versionInfo: VersionProcessInfo,
    stage: VersionStageInfo,
    checklist?: VersionStageCheckList,
  ): Promise<boolean> {
    return super.canComplete(versionInfo, stage, checklist);
  }

  async grayInfoUpdate(versionInfo: VersionProcessInfo, versionCode: string, time: number, subStageName: string) {
    const tfStage = versionInfo.version_stages.find(it => it.stage_name === this.stageName());
    if (!tfStage) {
      return;
    }
    const subStage = tfStage?.sub_stages.find(it => it.stage_name === subStageName);
    if (subStage) {
      (subStage.extra_data as SubGrayExtraData) ? ((subStage.extra_data as SubGrayExtraData).releaseTime = time) : {};
      (subStage.extra_data as SubGrayExtraData)
        ? ((subStage.extra_data as SubGrayExtraData).versionCode = versionCode)
        : {};
    } else {
      const curTime = new Date();
      const subGrayStageInfo: VersionStageInfo = {
        stage_name: `retouch_adr_final_gray@${tfStage.sub_stages.length + 1}`,
        display_name: `加灰-第${tfStage.sub_stages.length + 1}轮灰度`,
        start_time: 0,
        real_start_time: 0,
        end_time: 0,
        real_end_time: 0,
        status: VersionStageStatus.NotStart,
        sub_stages: [],
        status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
        message_count_map: {},
        extra_data: {
          releaseTime: time > 0 ? time : -1,
          versionCode,
          packageTime: this.getDesiredTimestamp(curTime.getTime()) / 1000,
        } as SubGrayExtraData,
        is_delay: false,
        mr_standard: VersionMrStandard.SP0P1,
        parent_stage_name: this.stageName(),
      };
      tfStage.sub_stages.push(subGrayStageInfo);
    }
    await this.versionProcessDao.updateByCriteria(
      { version: versionInfo.version, app_id: versionInfo.app_id },
      versionInfo,
    );
  }

  // 加量通知
  async adrGrayReleaseMore(version: string, platform: PlatformType, product: ProductType, versionCode: string) {
    if (platform !== PlatformType.Android) {
      return;
    }
    const appID =
      product === RetouchProductType.retouch ? 251502 : product === RetouchProductType.hypic ? 2020093924 : undefined;
    if (appID === undefined) {
      this.logger.error('[adrGrayReleaseMore] appID is undefined!');
      return;
    }
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appID, version });
    if (!versionInfo) {
      this.logger.error('[adrGrayReleaseMore] versionInfo is undefined!');
      return;
    }
    const title = `[${AppId2Name[versionInfo.app_id]}-${versionInfo.version}] 灰度包加量提醒`;
    let description = `**${AppId2Name[versionInfo.app_id]}-${versionInfo.version}开始加量**\n\n**版本号：${versionCode}**\n`;
    description += `<at email="${versionInfo.bmInfo[BmType.rd].email}"></at> <at email="${versionInfo.bmInfo[BmType.qa].email}"></at>\n\n`;
    const qualityBmTypeList = [
      BmType.anr_crash,
      BmType.oom_crash,
      BmType.java_crash,
      BmType.native_crash,
      BmType.iosoom,
      BmType.crash,
      BmType.ioswatchdog,
    ];
    const qualityBmUsers: User[] = [];
    let atQualityBm = '';
    for (const atQualityBmElement of qualityBmTypeList) {
      if (versionInfo.bmInfo[atQualityBmElement]?.email !== undefined) {
        atQualityBm += `<at email="${versionInfo.bmInfo[atQualityBmElement].email}"></at>`;
        const userInfo = await this.lark.getUserInfoByEmail(versionInfo.bmInfo[atQualityBmElement].email);
        if (userInfo) {
          qualityBmUsers.push(userInfo);
        }
      }
    }
    const metricUrl = `${MAIN_HOST_HTTPS}/quality/version/slardar?appid=${versionInfo.app_id}&selected=${versionCode}`;
    description += `请质量BM：${atQualityBm}关注稳定性数据\n**[点击查看全部稳定性指标](${metricUrl})**`;
    const card = this.versionReleaseCard.buildDescriptionCardWithJumpUrlButton(
      title,
      CardTemplate.yellow,
      description,
      '纸飞机发版平台',
      `https://paper-airplane.bytedance.net/versionReleaseCenter?show_detail=true&appid=${versionInfo.app_id}&version=${versionInfo.version}`,
    );
    await this.messageDao.sendVersionMessage(
      ReleasePlatformMessageGroupType.RetouchRDQAGroup,
      card,
      versionInfo,
      qualityBmUsers,
      this.msgSource,
      this.getMsgSourceName(),
    );
  }

  async adrGrayReleased(
    version: string,
    platform: PlatformType,
    product: ProductType,
    versionCode: string,
    jobInfo?: JobStepInfo,
  ) {
    if (platform !== PlatformType.Android) {
      return;
    }
    const appID =
      product === RetouchProductType.retouch ? 251502 : product === RetouchProductType.hypic ? 2020093924 : undefined;
    if (appID === undefined) {
      this.logger.error('[adrGrayReleased] appID is undefined!');
      return;
    }
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appID, version });
    if (!versionInfo) {
      this.logger.error('[adrGrayReleased] versionInfo is undefined!');
      return;
    }
    const grayStageInfo = getStageInfo(versionInfo.version_stages, this.stageName());
    if (!grayStageInfo) {
      this.logger.error('[adrGrayReleased] grayStageInfo is undefined!');
      return;
    }
    grayStageInfo.status = VersionStageStatus.OnProgress;
    const currentTime = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    if (versionInfo.app_id === AppSettingId.HYPIC_ANDROID) {
      const releaseStage = getStageInfo(versionInfo.version_stages, 'retouch_adr_release');
      if (releaseStage) {
        releaseStage.extra_data = {
          versionCode,
          releaseTime: currentTime,
          packageTime: jobInfo?.started ?? currentTime,
        } as SubGrayExtraData;
      }
    }
    for (let i = 0; i < grayStageInfo.sub_stages.length; i++) {
      if (grayStageInfo.sub_stages[i].status !== VersionStageStatus.Complete) {
        const extraData = grayStageInfo.sub_stages[i].extra_data
          ? (grayStageInfo.sub_stages[i].extra_data as SubGrayExtraData)
          : undefined;
        // 加一个保护，如果是加量，灰度版本号不变，不用添加新的灰度，直接返回
        if (
          extraData?.versionCode &&
          extraData?.versionCode === versionCode &&
          extraData?.releaseTime &&
          extraData?.releaseTime > 0
        ) {
          return;
        }
        let notifyStageInfo;
        if ((extraData?.releaseTime ?? 0) > 0) {
          grayStageInfo.sub_stages[i].status = VersionStageStatus.Complete;
          if (i + 1 < grayStageInfo.sub_stages.length) {
            grayStageInfo.sub_stages[i + 1].status = VersionStageStatus.OnProgress;
            grayStageInfo.sub_stages[i + 1].extra_data = {
              versionCode,
              releaseTime: currentTime,
              packageTime: jobInfo?.started ?? currentTime,
            } as SubGrayExtraData;
          } else {
            const subGrayStageInfo: VersionStageInfo = {
              stage_name: `retouch_adr_final_gray@${i + 2}`,
              display_name: `加灰-第${i + 2}轮灰度`,
              start_time: 0,
              real_start_time: 0,
              end_time: 0,
              real_end_time: 0,
              status: VersionStageStatus.OnProgress,
              sub_stages: [],
              status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
              message_count_map: {},
              extra_data: {
                releaseTime: currentTime,
                versionCode,
                packageTime: jobInfo?.started ?? currentTime,
              } as SubGrayExtraData,
              is_delay: false,
              mr_standard: VersionMrStandard.SP0P1,
              parent_stage_name: this.stageName(),
            };
            grayStageInfo.sub_stages.push(subGrayStageInfo);
            notifyStageInfo = subGrayStageInfo;
          }
        } else {
          grayStageInfo.sub_stages[i].extra_data = {
            versionCode,
            releaseTime: currentTime,
            packageTime: jobInfo?.started ?? currentTime,
          } as SubGrayExtraData;
          notifyStageInfo = grayStageInfo.sub_stages[i];
        }
        await this.versionProcessDao.updateVersionProcessInfo(versionInfo);
        // await this.versionProcessDao.updateByCriteria({ version, app_id: appID }, versionInfo, false);
        this.logger.info(`[adrGrayReleased] 第${i + 1}轮灰度: notifyStageInfo: ${JSON.stringify(notifyStageInfo)}`);
        if (notifyStageInfo) {
          await this.grayReleaseNotify(versionInfo, notifyStageInfo);
        }
        break;
      } else if (i + 1 === grayStageInfo.sub_stages.length) {
        const subGrayStageInfo: VersionStageInfo = {
          stage_name: `retouch_adr_final_gray@${i + 2}`,
          display_name: `加灰-第${i + 2}轮灰度`,
          start_time: 0,
          real_start_time: 0,
          end_time: 0,
          real_end_time: 0,
          status: VersionStageStatus.OnProgress,
          sub_stages: [],
          status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
          message_count_map: {},
          extra_data: {
            releaseTime: currentTime,
            versionCode,
            packageTime: jobInfo?.started ?? currentTime,
          } as SubGrayExtraData,
          is_delay: false,
          mr_standard: VersionMrStandard.SP0P1,
          parent_stage_name: this.stageName(),
        };
        grayStageInfo.sub_stages.push(subGrayStageInfo);
        this.logger.info(`[adrGrayReleased] 加灰 第${grayStageInfo.sub_stages.length}轮灰度`);
        await this.versionProcessDao.updateByCriteria({ version, app_id: appID }, versionInfo, false);
        await this.grayReleaseNotify(versionInfo, subGrayStageInfo);
        break;
      }
    }
    this.logger.info(`[adrGrayReleased] 第${grayStageInfo.sub_stages.length}轮灰度`);
  }

  // 灰度放量通知
  async grayReleaseNotify(versionInfo: VersionProcessInfo, stageInfo: VersionStageInfo) {
    this.logger.info(
      `[grayReleaseNotify] stageInfo: ${JSON.stringify(stageInfo)}, versionInfo: ${JSON.stringify(versionInfo)}`,
    );
    const appInfo = await this.businessConfig.appID2AppInfo(versionInfo.app_id);
    if (!appInfo) {
      this.logger.error('[grayReleaseNotify] appInfo is undefined!');
      return false;
    }
    const title = `[${AppId2Name[versionInfo.app_id]}-${versionInfo.version}] ${appInfo.platform === PlatformType.Android ? '灰度包放量通知' : 'TF包放量通知'}`;
    if (!stageInfo.extra_data) {
      this.logger.error('[grayReleaseNotify] stageInfo.extra_data is undefined!');
      return false;
    }
    const releaseExtraData = stageInfo.extra_data as SubTestFlightExtraData;
    const releaseContent = `**${AppId2Name[versionInfo.app_id]}-${versionInfo.version}-${stageInfo.display_name}开始放量**\n\n**版本号：${releaseExtraData.versionCode}**\n`;
    let description = releaseContent;
    description += `<at email="${versionInfo.bmInfo[BmType.rd].email}"></at> <at email="${versionInfo.bmInfo[BmType.qa].email}"></at>\n\n`;
    const qualityBmTypeList = [
      BmType.anr_crash,
      BmType.oom_crash,
      BmType.java_crash,
      BmType.native_crash,
      BmType.iosoom,
      BmType.crash,
      BmType.ioswatchdog,
    ];
    const qualityBmUsers: User[] = [];
    let atQualityBm = '';
    for (const atQualityBmElement of qualityBmTypeList) {
      if (versionInfo.bmInfo[atQualityBmElement]?.email !== undefined) {
        atQualityBm += `<at email="${versionInfo.bmInfo[atQualityBmElement].email}"></at>`;
        const userInfo = await this.lark.getUserInfoByEmail(versionInfo.bmInfo[atQualityBmElement].email);
        if (userInfo) {
          qualityBmUsers.push(userInfo);
        }
      }
    }
    const metricUrl = `${MAIN_HOST_HTTPS}/quality/version/slardar?appid=${versionInfo.app_id}&selected=${releaseExtraData.versionCode}`;
    description += `请质量BM：${atQualityBm}关注稳定性数据\n**[点击查看全部稳定性指标](${metricUrl})**`;
    const stageUrl = getUrlStageParams(stageInfo.stage_name, versionInfo);
    const card = this.versionReleaseCard.buildDescriptionCardWithJumpUrlButton(
      title,
      CardTemplate.yellow,
      description,
      '纸飞机发版平台',
      `https://paper-airplane.bytedance.net/release/list?show_detail=true&appid=${versionInfo.app_id}&version=${versionInfo.version}&${stageUrl}`,
    );
    if (
      versionInfo.app_id === AppSettingId.RETOUCH_IOS ||
      versionInfo.app_id === AppSettingId.RETOUCH_ANDROID ||
      versionInfo.app_id === AppSettingId.HYPIC_ANDROID
    ) {
      await this.messageDao.sendVersionMessage(
        ReleasePlatformMessageGroupType.RetouchRDQAGroup,
        card,
        versionInfo,
        qualityBmUsers,
        this.msgSource,
        this.getMsgSourceName(),
      );
    }
  }
}
