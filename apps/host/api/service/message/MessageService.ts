import { Inject, Injectable } from '@gulux/gulux';
import MessageDao from '../dao/message/MessageDao';
import { VscodeMessageInfo } from '@shared/message/VscodeMessageInfo';
import { useInject } from '@edenx/runtime/bff';

export interface SaveMessageRequest {
  messages: string[];
  email?: string[];
  expireInHours: number; // 过期时间（小时）
}

export interface GetMessagesRequest {
  latestGetMessageCreateTime?: number;
  email?: string;
  mac?: string;
}

export interface GetMessagesResponse {
  messages: string[];
  latestCreateTime?: number;
}

@Injectable()
export class MessageService {
  private messageDao: MessageDao;
  constructor() {
    this.messageDao = useInject(MessageDao);
  }
  // 保存消息
  async saveMessage(request: SaveMessageRequest): Promise<VscodeMessageInfo> {
    const { messages, email, expireInHours } = request;
    const messageData: VscodeMessageInfo = {
      messages,
      email: email || undefined,
      expireAt: Date.now() + expireInHours * 60 * 60 * 1000,
      sendInfo: {
        sendCount: 0,
        sendEmail: [],
        sendMac: [],
      },
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isBroadcast: email === undefined || email.length === 0,
    };

    return this.messageDao.saveMessage(messageData);
  }

  // 获取消息
  async getMessages(request: GetMessagesRequest): Promise<GetMessagesResponse> {
    const { latestGetMessageCreateTime, email, mac } = request;
    // 获取所有未过期的消息
    const allMessages = await this.messageDao.findUnexpiredMessages(latestGetMessageCreateTime);
    // 根据是否有email来过滤消息
    const filteredMessages: VscodeMessageInfo[] = [];
    if (email) {
      // 有email的情况：获取个人消息
      const personalMessages = allMessages.filter(msg => msg.email?.includes(email!));
      // 只获取未发送的个人消息
      for (const message of personalMessages) {
        if (!message.sendInfo.sendEmail.includes(email)) {
          await this.messageDao.addSendEmail(message._id!, email);
          filteredMessages.push(message);
        }
      }
    } else if (mac) {
      // 无email的情况：获取群发消息
      const broadcastMessages = allMessages.filter(msg => msg.isBroadcast);
      for (const message of broadcastMessages) {
        if (!message.sendInfo.sendMac.includes(mac)) {
          await this.messageDao.addSendMac(message._id!, mac);
          filteredMessages.push(message);
        }
      }
    }
    // 获取最新的创建时间
    let latestCreateTime = 0;
    if (filteredMessages.length > 0) {
      latestCreateTime = Math.max(...filteredMessages.map(msg => msg.createdAt));
    }
    return {
      messages: filteredMessages.flatMap(msg => msg.messages),
      latestCreateTime,
    };
  }

  // 清理过期消息
  async cleanupExpiredMessages(): Promise<void> {
    await this.messageDao.deleteExpiredMessages();
  }
}
