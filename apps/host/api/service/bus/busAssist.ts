import { NetworkCode, PlatformType, User } from '@pa/shared/dist/src/core';
import { BuildMasterInfo, LVProductType } from '@shared/process/versionProcess';
import { compact, fromPairs, merge, noop, pick, sample, some, uniq, values, without } from 'lodash';
import { BmType } from '@shared/bits/bmInfo';
import { add_suffix_ne, trim_suffix, wait } from '@pa/shared/dist/src/utils/tools';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { MrInfo, MrState, MrType } from '@shared/bits/mrInfo';
import { BusType, GetOffReason, GetOnReason, SpaceType } from '@shared/bus/busType';
import { getBitsSpacesById, silentBusMode } from '@shared/bus/config';
import { BusMeta, BusStatus } from '@shared/bus/busMeta';
import { BusMr, BusMrStatus } from '@shared/bus/busMr';
import utc from 'dayjs/plugin/utc';
import dayjs from 'dayjs';
import { Inject, Injectable } from '@gulux/gulux';
import VersionProcessDao from '../dao/VersionProcessDao';
import BitsService from '../third/bits';
import { BusMetaTable } from '../../model/BusMeta';
import { ModelType } from '@gulux/gulux/typegoose';
import { BusMrTable } from '../../model/BusMr';
import LarkService from '@pa/backend/dist/src/third/lark';
import BusCardService, { MsgBusStart, MsgPCOrRetouchBusStart } from './busCard';
import BusDao from '../dao/BusDao';
import AirplaneConfigService from '../AirplaneConfigService';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import FastMergeService from './fastMerge';
import { BusFastMergeInfoTable } from '../../model/BusFastMergeInfo';
import BatchMrService from './BatchMr';
import BusMrConflictCheckService from './BusMrConflictCheck';
import { useInject } from '@edenx/runtime/bff';
import MergeRequestService from '../mergeRequest';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import MrApprovalService from '../mrAporoval/MrApprovalService';
import CollectAbnormalMultiMrService from '../tasks/collectAbnormalMultiMr';
import StoryReadyNotifyService from '../handler/consul/storyReadyNotify';
import MessageService from '@pa/backend/dist/src/service/message';
import { AppSettingId, BusinessType } from '@pa/shared/dist/src/appSettings/appSettings';
import { VersionProcessInfo } from '@shared/releasePlatform/versionStage';
import { AppId2Name } from '@shared/experiment/experimentInfo';
import MeegoOldService from '../meegoOld';
import { MrStatus } from '@shared/meego/CustomData';
import CodeFreezeMrService from '../CodeFreezeMrService';
import { MrEfficiencyModelService } from '../mr/MrEfficiencyModelService';
import { BusCodeReviewService } from './BusCodeReviewService';
import { BoardingMrDao } from '../dao/mr/BoardingMrDao';
import versionUtils from '../../utils/versionUtils';
import DevOpsCheckPodsTTPStatus from '../devops/devopsCheckPodsTTPStatus';
import { NetworkX } from '../../utils/NetworkX';

export function mrHasTicket(mr: MrInfo, version: string): boolean {
  const tagName = `ticket:integration:${version}`;
  return some(mr.tags, t => t.name === tagName);
}

export function mrHasTicketNoVersion(mr: MrInfo): boolean {
  const tagName = `ticket:integration:`;
  return some(mr.tags, t => t.name.includes(tagName));
}

export function mrHasOnBus(mr: MrInfo): boolean {
  const tagName = `bus`;
  return some(mr.tags, t => t.name.includes(tagName));
}

@Injectable()
export default class BusAssistService {
  @Inject()
  private versionProcessDao: VersionProcessDao;

  @Inject()
  private bitsService: BitsService;

  @Inject(BusMetaTable)
  private busMetaModel: ModelType<BusMetaTable>;

  @Inject(BusMrTable)
  private busMrModel: ModelType<BusMrTable>;

  @Inject(BusFastMergeInfoTable)
  private busFastMergeModel: ModelType<BusFastMergeInfoTable>;

  @Inject()
  private lark: LarkService;

  @Inject()
  private busCard: BusCardService;

  @Inject()
  private busDao: BusDao;

  @Inject()
  private configService: AirplaneConfigService;

  @Inject()
  private fastMergeService: FastMergeService;

  @Inject()
  private batchMrService: BatchMrService;

  @Inject()
  private conflictCheckService: BusMrConflictCheckService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private mrApproval: MrApprovalService;

  @Inject()
  private messageService: MessageService;

  @Inject()
  private meegoOld: MeegoOldService;

  @Inject()
  private codeFreezeMrService: CodeFreezeMrService;

  @Inject()
  private mrEfficiencyModelService: MrEfficiencyModelService;

  @Inject()
  private busCodeReviewService: BusCodeReviewService;

  @Inject()
  private boardingMrDao: BoardingMrDao;

  @Inject()
  private checkPodsTTPStatus: DevOpsCheckPodsTTPStatus;

  /**
   * 查询双端 BM
   * @param version 版本，如 12.4.0
   * @return key 是 PlatformType, value 是 BM 列表
   */
  async queryBothBM(version: string, isCC: boolean): Promise<Record<string, BuildMasterInfo[]>> {
    return Promise.all(
      [PlatformType.Android, PlatformType.iOS, PlatformType.PC].map(
        async (p): Promise<[PlatformType, BuildMasterInfo[]]> => [
          p,
          await this.versionProcessDao
            .queryVersionProcessInfo(isCC ? LVProductType.cc : LVProductType.lv, p, version)
            .then(it => values(pick(it?.bmInfo, [BmType.rd, BmType.qa])))
            .then(it => it.filter(c => c).map(c => c!)),
        ],
      ),
    ).then(it => fromPairs(it));
  }

  /**
   * 初始化PC和醒图封版车
   * @param version 封版的版本，如 12.4.0
   */
  async initPCOrRetouchBus(versionInfo: VersionProcessInfo) {
    // 先创建跟车群
    this.logger.info('[Bus] [initPCOrRetouchBus] 正在创建跟车群');
    const chatId = await this.createPCOrRetouchBusGroup(versionInfo);
    // const chatId = 'oc_33ce1a8d0ab578e54914960ecc7c9dd6';
    this.logger.info(`[Bus] [initPCOrRetouchBus] 创建跟车群 ${chatId} 完成，正在保存车次`);

    // 保存 bus
    const bus = await this.busMetaModel.create({
      version: versionInfo.version,
      name: AppId2Name[versionInfo.app_id],
      type: BusType.integration,
      status: BusStatus.OPEN,
      chatId,
      appId: versionInfo.app_id,
    });
    // const bus = await this.busDao.queryCurrentIntegrationBusByAppId(versionInfo.app_id);
    // if (!bus) {
    //   this.logger.error('[Bus] [initPCOrRetouchBus] 未找到对应的集成车');
    //   return;
    // }
    this.logger.info(`[Bus] [initPCOrRetouchBus] 保存车次 ${bus._id} 完成，搜索所有符合要求的 MR`);

    // 搜索 MR
    let allMrIds: number[] = [];
    if (versionInfo.app_id === AppSettingId.RETOUCH_IOS) {
      allMrIds = await this.potentialRetouchMrSearch(SpaceType.Retouch_iOS);
    } else if (versionInfo.app_id === AppSettingId.RETOUCH_ANDROID) {
      allMrIds = await this.potentialRetouchMrSearch(SpaceType.Retouch_Android);
    } else {
      allMrIds = await this.potentialPCMrSearch(versionInfo.version);
    }
    this.logger.info(`[Bus] [initPCOrRetouchBus] 搜索所有符合要求的 MR 完成，共计 ${allMrIds.length} 个，正在获取信息`);
    const needNotifyMr: MrInfo[] = [];
    for (const mrId of allMrIds) {
      const mrInfo = await this.bitsService.getMrInfo({
        mrId,
      });
      if (!mrInfo) {
        continue;
      }
      needNotifyMr.push(mrInfo);
    }
    this.logger.info('[Bus] [initPCOrRetouchBus] MR 信息获取完成，正在检索 MR 作者信息');

    // 给不合要求的 MR 发消息
    // 检索这些 MR 的 owner 的 open id
    const mrAuthorIds = await this.getMrAuthorIds(needNotifyMr.map(it => it.author));
    this.logger.info('[Bus] [initPCOrRetouchBus] 检索 MR 作者信息完成，正在保存 MR 信息');
    // const realNeedNotifyMr: MrInfo[] = [];
    // for (const mr of needNotifyMr) {
    //   const busMr = await this.busMrModel.findOne({
    //     mrId: mr.id,
    //   });
    //   if (busMr && busMr.status !== BusMrStatus.NOT_REPLIED) {
    //     continue;
    //   }
    //   await this.busMrModel.create({
    //     busId: bus._id,
    //     mrId: mr.id,
    //     status: BusMrStatus.NOT_REPLIED,
    //     messageId: '',
    //     merged: false,
    //   });
    //   realNeedNotifyMr.push(mr);
    //   this.logger.info(`[Bus] [initIntegrationBus] 保存 MR 信息完成，mrId: ${mr.id}`);
    // }
    // const realMrAuthorIds = await this.getMrAuthorIds(realNeedNotifyMr.map(it => it.author));
    const appId = versionInfo.app_id;
    const preBoardedMrIds = await this.boardingMrDao.getMrIdsByAppId(appId.toString());
    this.logger.info(`[Bus] [initPCOrRetouchBus] ${versionInfo.app_id}本次上车有${preBoardedMrIds.length}条预上车mr`);
    const preBoardedMrIdSet = new Set(preBoardedMrIds);
    // 保存这些 MR
    await this.busMrModel.create(
      needNotifyMr.map(mrInfo => ({
        busId: bus._id,
        mrId: mrInfo.id,
        status: preBoardedMrIdSet.has(mrInfo.id) ? BusMrStatus.ON_BUS : BusMrStatus.NOT_REPLIED,
        messageId: '',
        merged: false,
      })),
    );
    this.logger.info('[Bus] [initPCOrRetouchBus] 保存 MR 完成，正在给不符合上车要求的 MR 作者私发消息');

    // 发消息
    await this.remindGetOnBus(bus, needNotifyMr, mrAuthorIds);
    this.logger.info('[Bus] [initPCOrRetouchBus] 给不符合上车要求的 MR 作者私发消息完成，正在拉自动上车 MR 作者入群');

    let needNotifyChat = [
      'oc_8684a703990c6c2d16237ab689bbff8e', // Android RD 同步群
      'oc_269ae6e4553e44f315cf8a133632f789', // iOS RD 同步群
    ];
    if (versionInfo.app_id === AppSettingId.RETOUCH_IOS) {
      needNotifyChat = ['oc_7d9eda316a806f13a9a7549bd37dcd04']; // Test Api
    } else if (versionInfo.app_id === AppSettingId.RETOUCH_ANDROID) {
      needNotifyChat = ['oc_7d9eda316a806f13a9a7549bd37dcd04']; // Test Api
    } else if (versionInfo.app_id === AppSettingId.LV_WIN) {
      needNotifyChat = ['oc_83bdad3e1650e16c3bad8dbf763a0da7']; // PC RD 同步群
    }
    // 发大群通知封版开始
    (silentBusMode
      ? ['oc_aa209429502d19fddaced628766a2dc7'] // Test Api
      : needNotifyChat
    ).map(async id => {
      await this.lark.sendCardMessage(
        UserIdType.chatId,
        id,
        this.busCard.buildBusStartCard(bus, AppId2Name[versionInfo.app_id]),
      );
    });

    // 完事了先发一遍通知
    this.logger.info('[Bus] [initPCOrRetouchBus] 已通知大群，车次初始化流程结束');
    await this.notifyBusMrList(bus, false);

    // 清空预上车数据库里对应app的条目，对遗留的发送一条通知
    const preBoardedMrIdLeft = await this.boardingMrDao.popMrIdsByAppId(versionInfo.app_id.toString());
    this.logger.info(`[Bus] [initPCOrRetouchBus] 遗留${preBoardedMrIdLeft.length}条预上车mr未通知/未消费`);
    await this.informLeftPreOnboards(preBoardedMrIdLeft);

    // 将当期RDBM拉入跳过cr群
    const bms = versionInfo.bmInfo[BmType.rd];
    const skipCrChat = 'oc_7e51a96b9b548e0e71694545ce2d7d26';
    await this.lark.addUserToChatGroup(skipCrChat, UserIdType.openId, [bms.openId]);
    await this.lark.sendTextMessage(
      UserIdType.chatId,
      skipCrChat,
      `今天是${versionInfo.version}封板日，本期RDBM已自动进群，本群会推送请求延后Code Review的MR信息，请在今天内关注消息，为达标MR手动合码。`,
    );
  }

  /**
   * 创建跟车群
   * @param fixedUsers 固定进入封版群用户的邮箱
   * @param version 封版的版本
   * @return 创建群聊的 chatId
   */
  async createPCOrRetouchBusGroup(versionInfo: VersionProcessInfo): Promise<string> {
    let defaultUser: User[] = [];
    if (versionInfo.app_id === AppSettingId.LV_WIN) {
      defaultUser = (await this.configService.queryConfigItem(
        BusinessType.PC.toString(),
        VersionConfigKeys.releaseDefaultGroupUsers,
      )) as User[];
    } else if (versionInfo.app_id === AppSettingId.RETOUCH_IOS) {
      defaultUser = (await this.configService.queryConfigItem(
        BusinessType.Retouch.toString(),
        VersionConfigKeys.releaseDefaultGroupUsers,
        PlatformType.iOS,
      )) as User[];
    } else if (versionInfo.app_id === AppSettingId.RETOUCH_ANDROID) {
      defaultUser = (await this.configService.queryConfigItem(
        BusinessType.Retouch.toString(),
        VersionConfigKeys.releaseDefaultGroupUsers,
        PlatformType.Android,
      )) as User[];
    }
    if (!defaultUser) {
      defaultUser = [];
    }
    const testUser = await this.lark.getUserInfoByEmail('<EMAIL>');
    if (testUser) {
      defaultUser.push(testUser);
    }
    const bms = versionInfo.bmInfo[BmType.rd];
    const bmIds = [bms.openId];
    if (silentBusMode) {
      bmIds.length = 0;
    }
    // test code
    bmIds.length = 0;
    const chat = await this.lark.createLarkGroup(
      {
        user_id_type: UserIdType.openId,
        set_bot_manager: false,
      },
      {
        description: '',
        name: `${AppId2Name[versionInfo.app_id]} ${versionInfo.version} 封版跟车群`,
        owner_id: defaultUser[0]?.open_id ?? sample(bmIds),
        user_id_list: defaultUser.map(it => it.open_id).concat(bmIds),
      },
    );
    await this.messageService.sendNormalMsg(
      new MsgPCOrRetouchBusStart(versionInfo.version, bms),
      UserIdType.chatId,
      chat.chat_id,
    );
    return chat.chat_id;
  }
  /**
   * 在发车前一天15:00通知PC/醒图MR作者选择是否上车
   * @param versionInfo 版本信息
   */
  async notifyPCRetouchMrOwnerToChooseOnBus(versionInfo: VersionProcessInfo) {
    this.logger.info('[Bus] [notifyPCRetouchMrOwnerToChooseOnBus] 开始处理PC/醒图MR提前通知');
    const { app_id: appId, version } = versionInfo;

    // 根据产品线选择对应的MR搜索方法
    let allMrIds: number[] = [];
    if (appId === AppSettingId.RETOUCH_IOS) {
      allMrIds = await this.potentialRetouchMrSearch(SpaceType.Retouch_iOS);
    } else if (appId === AppSettingId.RETOUCH_ANDROID) {
      allMrIds = await this.potentialRetouchMrSearch(SpaceType.Retouch_Android);
    } else if (appId === AppSettingId.LV_WIN) {
      allMrIds = await this.potentialPCMrSearch(version);
    } else {
      this.logger.error(`[Bus] [notifyPCRetouchMrOwnerToChooseOnBus] 不支持的产品线: ${appId}`);
      return;
    }
    this.logger.info(`[Bus] [notifyPCRetouchMrOwnerToChooseOnBus] 搜索符合要求的MR完成，共计 ${allMrIds.length} 个`);

    // 获取MR详细信息（PC/醒图版本不做主仓校验）
    const targetMr: MrInfo[] = [];
    for (const mrId of allMrIds) {
      const mrInfo = await this.bitsService.getMrInfo({ mrId });
      if (mrInfo) {
        targetMr.push(mrInfo);
      }
    }
    this.logger.info(`[Bus] [notifyPCRetouchMrOwnerToChooseOnBus] 获取MR信息完成，有效MR数量：${targetMr.length}`);

    // 获取MR作者的open ID
    const mrAuthorIds = await this.getMrAuthorIds(targetMr.map(it => it.author));
    this.logger.info(`[Bus] [notifyPCRetouchMrOwnerToChooseOnBus] 检索MR作者信息完成`);

    // 发送选择是否上车的提醒卡片
    await this.remindMarkOnBus(appId, version, targetMr, mrAuthorIds);
    this.logger.info(`[Bus] [notifyPCRetouchMrOwnerToChooseOnBus] PC/醒图MR作者通知完成`);
  }
  /**
   * 自动上车处理逻辑
   */
  private async processAutoGetOnBus(
    bus: BusMeta,
    appId: AppSettingId | null | undefined,
    mrId: number,
    userId: string,
  ): Promise<string | undefined> {
    if (!appId) {
      this.logger.error(`[Bus][processGetOnBus] no app id in mr: ${mrId}, busid: ${bus._id}`);
      return;
    }
    const mrInfo = await this.bitsService.getMrInfo({ mrId });
    if (!mrInfo) {
      this.logger.error(`[Bus][processGetOnBus] MR not found: ${mrId}`);
      return `[Bus][processGetOnBus]MR不存在: ${mrId}`;
    }
    const replyUser = async (msg: string) => {
      await this.lark.sendTextMessage(UserIdType.userId, userId, msg);
    };
    if (bus && mrInfo) {
      this.getOnMr(bus, mrInfo, GetOnReason.PreConfirmed)
        .then(async err => {
          if (err) {
            await replyUser(`自动上车操作失败, 请尝试手动点击上车按钮或联系bm${err}`);
          } else {
            await replyUser(`已将你的mr自动上车！${mrInfo.mr_detail_url}`);
          }
        })
        .catch(async e => {
          await replyUser(`自动上车操作失败, 请尝试手动点击上车按钮或联系bm${JSON.stringify(e)}`);
        });
    }
    try {
      await this.boardingMrDao.deleteByMrIdAndAppId(mrId, appId.toString());

      // 通知CR创建
      if (bus.appId === AppSettingId.LV_ANDROID) {
        await this.busCodeReviewService.notifyMrOwnerToCreateCodeReview(mrId);
      }

      return;
    } catch (e) {
      const errorMsg = `自动处理上车失败: ${JSON.stringify(e)}`;
      this.logger.error(`[Bus][processGetOnBus] ${errorMsg}`);
      return errorMsg;
    }
  }

  /**
   * 给未被消费的mr发一条信息
   */
  async informLeftPreOnboards(mrIds: number[]) {
    if (!mrIds || mrIds.length === 0) {
      this.logger.info('[Bus] [informLeftPreOnboards] No leftover pre-onboard MRs to inform.');
      return;
    }

    this.logger.info(`[Bus] [informLeftPreOnboards] Informing ${mrIds.length} leftover pre-onboard MRs.`);

    const mrInfosToInform: MrInfo[] = [];
    for (const mrId of mrIds) {
      try {
        const mrInfo = await this.bitsService.getMrInfo({ mrId });
        if (mrInfo) {
          mrInfosToInform.push(mrInfo);
        } else {
          this.logger.warn(`[Bus] [informLeftPreOnboards] Failed to get info for MR: ${mrId}`);
        }
      } catch (error) {
        this.logger.error(`[Bus] [informLeftPreOnboards] Error fetching info for MR: ${mrId}`, error);
      }
    }

    if (mrInfosToInform.length === 0) {
      this.logger.info('[Bus] [informLeftPreOnboards] No valid MR info found for leftover MRs.');
      return;
    }

    const authorUsernames = uniq(mrInfosToInform.map(info => info.author));
    const mrAuthorIdsMap = await this.getMrAuthorIds(authorUsernames);

    for (const mrInfo of mrInfosToInform) {
      const authorOpenId = mrAuthorIdsMap[mrInfo.author];
      if (authorOpenId) {
        try {
          const message = `您之前标记上车的 MR ${mrInfo.id} (${mrInfo.title}) 因故未跟随今日封板自动上车，可能因为您已完成或取消流程\n如有疑问请联系BM。MR链接: ${mrInfo.mr_detail_url}`;
          await this.lark.sendTextMessage(UserIdType.openId, authorOpenId, message);
          this.logger.info(
            `[Bus] [informLeftPreOnboards] Sent message to author ${mrInfo.author} for MR: ${mrInfo.id}`,
          );
        } catch (error) {
          this.logger.error(
            `[Bus] [informLeftPreOnboards] Failed to send message to author ${mrInfo.author} for MR: ${mrInfo.id}`,
            error,
          );
        }
      } else {
        this.logger.warn(
          `[Bus] [informLeftPreOnboards] Could not find OpenID for author: ${mrInfo.author} of MR: ${mrInfo.id}`,
        );
      }
    }
    this.logger.info('[Bus] [informLeftPreOnboards] Finished informing leftover pre-onboard MRs.');
  }

  /**
   * 在发车前一天15:00通知每个mr，选择是否上车
   * @param appSettingId
   * @param version
   */
  async notifyMrOwnerToChooseOnBus(appSettingId: AppSettingId, version: string) {
    // 获取当前app对应的空间配置
    const spaces = getBitsSpacesById(appSettingId);
    // 搜索可能跟车的MR ID列表
    const allMrIds = await this.potentialMrSearch(spaces);
    this.logger.info(`[Bus] [notifyMrOwnerToChooseOnBus] 搜索符合要求的MR完成，共计 ${allMrIds.length} 个`);
    const isCC = appSettingId === AppSettingId.CC_ANDROID;
    // 过滤需要通知的MR
    // 以下目前只处理了剪映和CC的情况
    const targetMr: MrInfo[] = [];
    for (const mrId of allMrIds) {
      const mrInfo = await this.bitsService.getMrInfo({
        mrId,
      });
      if (!mrInfo) {
        continue;
      }
      // 有移动端主仓的MR
      if (isCC) {
        // 只保留仅限cc的MR，防止重复通知
        if (
          (await this.bitsService.checkMrContainsCCRepo(mrInfo)) &&
          !(await this.bitsService.checkMrContainsLvRepo(mrInfo))
        ) {
          targetMr.push(mrInfo);
        }
      } else {
        if (await this.bitsService.checkMrContainsLvRepo(mrInfo)) {
          targetMr.push(mrInfo);
        }
      }
    }
    this.logger.info(`[Bus] [notifyMrOwnerToChooseOnBus] 获取MR信息完成，有效MR数量：${targetMr.length}`);

    // 获取MR作者的open ID，这里键是名字，值是ou开头的id
    const mrAuthorIds = await this.getMrAuthorIds(targetMr.map(it => it.author));
    this.logger.info(`[Bus] [notifyMrOwnerToChooseOnBus] 检索MR作者信息完成`);

    // 发送选择是否上车的提醒卡片
    await this.remindMarkOnBus(appSettingId, version, targetMr, mrAuthorIds);
    this.logger.info(`[Bus] [notifyMrOwnerToChooseOnBus] MR作者ID: ${JSON.stringify(mrAuthorIds)}`);
  }
  /**
   * 初始化封版车(适用于剪映/Capcut(TTP)
   * @param appSettingId 产品线
   * @param version 封版的版本，如 12.4.0
   */
  async initIntegrationBus(appSettingId: AppSettingId, version: string) {
    // 先创建跟车群
    this.logger.info('[Bus] [initIntegrationBus] 正在创建跟车群');
    const isCC = appSettingId === AppSettingId.CC_ANDROID;
    const busName = isCC ? 'Capcut' : '剪映';
    const chatId = await this.createBusGroup(busName, version, isCC);
    this.logger.info(`[Bus] [initIntegrationBus] 创建跟车群 ${chatId} 完成，正在保存车次`);

    // 保存 bus，已恢复原来的代码
    const bus = await this.busMetaModel.create({
      appId: appSettingId,
      name: busName,
      version,
      type: BusType.integration,
      status: BusStatus.OPEN,
      chatId,
    });
    this.logger.info(
      `[Bus] [initIntegrationBus] 保存车次 ${bus._id} 完成，该车次为${bus.name} ${bus.version}，搜索所有符合要求的 MR`,
    );
    if (!isCC) {
      await this.busFastMergeModel.create({
        version,
        busId: bus._id,
        lastMergeTime: 0,
      });
    }

    // 搜索 MR
    const spaces = getBitsSpacesById(appSettingId);
    const allMrIds = await this.potentialMrSearch(spaces);
    this.logger.info(`[Bus] [initIntegrationBus] 搜索所有符合要求的 MR 完成，共计 ${allMrIds.length} 个，正在获取信息`);
    const badMr: MrInfo[] = [];
    for (const mrId of allMrIds) {
      const mrInfo = await this.bitsService.getMrInfo({
        mrId,
      });
      if (!mrInfo) {
        continue;
      }
      // 必须要有移动端主仓的MR才检测
      if (isCC) {
        if (await this.bitsService.checkMrContainsCCRepo(mrInfo)) {
          badMr.push(mrInfo);
        }
      } else {
        if (await this.bitsService.checkMrContainsLvRepo(mrInfo)) {
          badMr.push(mrInfo);
        }
      }
    }
    this.logger.info('[Bus] [initIntegrationBus] MR 信息获取完成，正在检索 MR 作者信息');

    // 给不合要求的 MR 发消息
    // 检索这些 MR 的 owner 的 open id
    const mrAuthorIds = await this.getMrAuthorIds(badMr.map(it => it.author));
    this.logger.info('[Bus] [initIntegrationBus] 检索 MR 作者信息完成，正在保存 MR 信息');

    // 获取双端的预上车 MR（可能没必要）
    let preBoardedMrIdsForBadMr = await this.boardingMrDao.getMrIdsByAppId(appSettingId.toString());
    const iosAppId = appSettingId === AppSettingId.LV_ANDROID ? AppSettingId.LV_IOS : AppSettingId.CC_IOS;
    const preBoardedIos = await this.boardingMrDao.getMrIdsByAppId(iosAppId.toString());
    preBoardedMrIdsForBadMr = [...preBoardedMrIdsForBadMr, ...preBoardedIos];

    this.logger.info(`[Bus] [initIntegrationBus] ${appSettingId}本次上车有${preBoardedMrIdsForBadMr.length}条预上车mr`);
    const preBoardedMrIdSetForBadMr = new Set(preBoardedMrIdsForBadMr);
    // 保存这些 MR
    await this.busMrModel.create(
      badMr.map(mrInfo => ({
        busId: bus._id,
        mrId: mrInfo.id,
        status: preBoardedMrIdSetForBadMr.has(mrInfo.id) ? BusMrStatus.ON_BUS : BusMrStatus.NOT_REPLIED,
        messageId: '',
        merged: false,
      })),
    );
    this.logger.info('[Bus] [initIntegrationBus] 保存 MR 完成，正在给不符合上车要求的 MR 作者私发消息');

    // 发消息提醒上车
    if (silentBusMode) {
      await this.remindGetOnBus(
        bus,
        badMr.filter(v => v.author.includes('zhanglinwei')),
        mrAuthorIds,
      );
    } else {
      await this.remindGetOnBus(bus, badMr, mrAuthorIds);
    }
    this.logger.info('[Bus] [initIntegrationBus] 给不符合上车要求的 MR 作者私发消息完成，正在拉自动上车 MR 作者入群');

    // 发大群通知封版开始
    (silentBusMode
      ? ['oc_421725f66bd9b296fff97e71a9e50466'] // Test Api
      : [
          'oc_8684a703990c6c2d16237ab689bbff8e', // Android RD 同步群
          'oc_269ae6e4553e44f315cf8a133632f789', // iOS RD 同步群
        ]
    ).map(async id => {
      await this.lark.sendCardMessage(UserIdType.chatId, id, this.busCard.buildBusStartCard(bus));
    });

    // 创建占位MR
    const res = await useInject(MergeRequestService).createPlaceholderMR(version, isCC);
    this.logger.info('[Bus] [initIntegrationBus] 创建占位MR', res);

    // 清空预上车数据库里对应app的条目，对遗留的发送一条通知
    const preBoardedMrIdLeft = await this.boardingMrDao.popMrIdsByAppId(appSettingId.toString());
    this.logger.info(`[Bus] [initIntegrationBus] 遗留${preBoardedMrIdLeft.length}条预上车mr未通知/未消费`);
    await this.informLeftPreOnboards(preBoardedMrIdLeft);

    // 将当期RDBM拉入跳过cr群
    const bms = await this.queryBothBM(version, isCC);
    await this.addRDBMInGroup(bms, version);
  }

  /**
   * 创建跟车群
   * @param fixedUsers 固定进入封版群用户的邮箱
   * @param version 封版的版本
   * @return 创建群聊的 chatId
   */
  async createBusGroup(name: string, version: string, isCC: boolean): Promise<string> {
    const defaultUser = (await this.configService.queryConfigItem(
      '1775',
      VersionConfigKeys.releaseDefaultGroupUsers,
    )) as User[];
    const bms = await this.queryBothBM(version, isCC);
    const bmIds = values(bms)
      .flat()
      .map(it => it.openId);
    this.logger.info(
      `[Bus] [createBusGroup] 双端 BM 查询完成：${values(bms)
        .flat()
        .map(it => it.email)
        .join(', ')}`,
    );
    if (silentBusMode) {
      bmIds.length = 0;
    }
    const chat = await this.lark.createLarkGroup(
      {
        user_id_type: UserIdType.openId,
        set_bot_manager: false,
      },
      {
        description: '',
        name: `${name} ${version} 封版跟车群`,
        owner_id: defaultUser[0]?.open_id ?? sample(bmIds),
        user_id_list: defaultUser.map(it => it.open_id).concat(bmIds),
      },
    );
    await this.messageService.sendNormalMsg(new MsgBusStart(version, bms), UserIdType.chatId, chat.chat_id);
    return chat.chat_id;
  }

  /**
   * 搜索剪映可能会在车上的所有 MR
   * @return [合要求的 MR id, 暂未合要求的 MR id]
   */
  async potentialMrSearch(spaces: string[]): Promise<number[]> {
    return await Promise.all(
      spaces.map(it =>
        this.bitsService.searchAllMr({
          group_name: it,
          state: MrState.opened,
          target_branch: 'rc/develop',
          mr_type: MrType.feature,
          wip: 0,
          review_state: 'all',
          conflicted: -1,
        }),
      ),
    )
      .then(it => it.flat())
      .then(uniq);
  }

  /**
   * 搜索醒图可能会在车上的所有 MR
   */
  async potentialRetouchMrSearch(groupName: string): Promise<number[]> {
    const result = await this.bitsService.searchAllMr({
      group_name: groupName,
      state: MrState.opened,
      target_branch: 'develop',
      mr_type: MrType.feature,
      wip: 0,
      review_state: 'all',
      conflicted: -1,
    });
    return uniq(result);
  }

  /**
   * 搜索PC可能会在车上的所有 MR
   */
  async potentialPCMrSearch(version: string): Promise<number[]> {
    const meegoResult = await this.meegoOld.queryStoryProgressInfoEasy(version, PlatformType.PC);
    const mrIds: number[] = [];
    for (const item of meegoResult) {
      if (item.mrMerged !== MrStatus.MERGED && item.mrMerged !== MrStatus.NotExist && item.mrMerged !== true) {
        mrIds.push(item.mrId);
      }
    }
    return uniq(mrIds);
  }

  /**
   * 检查给定 MR 是否符合上车标准
   * @param id MR ID
   * @return [是否符合上车标准, 未 review QA 名单]
   */
  async isQualifiedMr(id: number) {
    return this.mrApproval.checkHostMrApproval(id);
  }

  /**
   * 返回给定的 MR 的 author 的 open id
   * @param mrAuthors
   */
  async getMrAuthorIds(mrAuthors: <AUTHORS>
    return mrAuthors.length <= 0
      ? {}
      : fromPairs(
          await this.lark
            .searchUserInfoBatch(mrAuthors)
            .then(it => it.map(u => [trim_suffix('@bytedance.com', u.email!), u.open_id])),
        );
  }

  /**
   * 将当期rdbm拉进跳过cr群，对剪c
   */
  async addRDBMInGroup(bms: Record<string, BuildMasterInfo[]>, version: string) {
    const bmIds = values(bms)
      .flat()
      .filter(it => it.type === BmType.rd || it.type === BmType.android_version_bm) // 剪c同时拉安卓版本bm
      .map(it => it.openId);
    this.logger.info(
      `[Bus] [addRDBMInGroup] 双端 RDBM 查询完成：${values(bms)
        .flat()
        .filter(it => it.type === BmType.rd || it.type === BmType.android_version_bm)
        .map(it => it.email)
        .join(', ')}`,
    );
    const skipCrChat = 'oc_7e51a96b9b548e0e71694545ce2d7d26';
    await this.lark.addUserToChatGroup(skipCrChat, UserIdType.openId, bmIds);
    await this.lark.sendTextMessage(
      UserIdType.chatId,
      skipCrChat,
      `今天是${version}封板日，本期RDBM已自动进群，本群会推送请求延后Code Review的MR信息，请在今天内关注消息，为达标MR手动合码。`,
    );
  }

  /**
   * 批量离群
   */
  async notifyObserverLeaveCodeReviewGroup(bus: BusMeta) {
    const allOnBusMrs = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });
    for (const mr of allOnBusMrs) {
      const mrInfo = await this.bitsService.getMrInfo({ mrId: mr.mrId });
      if (!mrInfo) {
        continue;
      }
      let res;
      // 剪映
      if (bus.appId) {
        res = await useInject(BusCodeReviewService).notifyObserverLeaveMrGroup(mrInfo.id);
      }
      this.logger.info(`[Bus] [notifyReviewerToStartCodeReview] mrId: ${mrInfo.id}, res: ${JSON.stringify(res)}`);
    }
  }

  /**
   * 通知reviewer去codeReview
   */
  async notifyReviewerToStartCodeReview(bus: BusMeta) {
    const allOnBusMrs = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });
    for (const mr of allOnBusMrs) {
      const mrInfo = await this.bitsService.getMrInfo({ mrId: mr.mrId });
      if (!mrInfo) {
        continue;
      }
      let res;
      // 剪映
      if (bus.appId && bus.appId === AppSettingId.LV_ANDROID) {
        res = await useInject(BusCodeReviewService).notifyReviewerToCodeReview(mrInfo.id);
      }
      this.logger.info(`[Bus] [notifyReviewerToStartCodeReview] mrId: ${mrInfo.id}, res: ${JSON.stringify(res)}`);
    }
  }

  /**
   * 通知Reviewer去处理CR任务
   */
  async notifyReviewerToHandleReviewTask(bus: BusMeta) {
    const allOnBusMrs = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });
    const mrIds: number[] = [];
    for (const mr of allOnBusMrs) {
      const mrInfo = await this.bitsService.getMrInfo({ mrId: mr.mrId });
      if (!mrInfo) {
        continue;
      }
      let res;
      // 剪映
      if (bus.appId && bus.appId === AppSettingId.LV_ANDROID) {
        mrIds.push(mrInfo.id);
      }
      this.logger.info(`[Bus] [notifyReviewerToHandleReviewTask] mrId: ${mrInfo.id}, res: ${JSON.stringify(res)}`);
    }
    this.logger.info(`[Bus] [notifyReviewerToHandleReviewTask] start！`);
    await useInject(BusCodeReviewService).notifyHandleCodeReviewTask(mrIds);
    this.logger.info(`[Bus] [notifyReviewerToHandleReviewTask] end！`);
  }
  /**
   * 下一个方法的helper function
   * @param appId
   */
  async findUnmergedMR(bus: BusMeta): Promise<MrInfo[]> {
    this.logger.info(`[Bus] [checkMrDelayCR] 正在封板的版本是${bus.version}`);
    const unmergedMR: MrInfo[] = await this.busMrModel
      .find({
        busId: bus._id,
        status: BusMrStatus.ON_BUS,
        merged: false,
      })
      // 每个 MR，在 bits 上获取 MR 信息
      .then(it =>
        it.map(({ mrId }, idx) =>
          // 每 0.5s 发一个请求
          wait(500 * idx).then(() => this.bitsService.getMrInfo({ mrId })),
        ),
      )
      // 等待所有信息获取完成
      .then(it => Promise.all(it))
      // 过滤没获取到的
      .then(mrInfos => compact(mrInfos.filter(it => it)));
    return unmergedMR;
  }
  /**
   * 下午四点前，定时触发多次，对所有 通过pipeline的 QA全过 RD过0.8的 MR，发卡提醒可以进入合码，未达标或者未过pipeline都会发卡提醒
   * @param appId
   */
  async checkMrDelayCR(bus: BusMeta): Promise<void> {
    const unmergedMR = await this.findUnmergedMR(bus);
    // 通过率
    const approvedRate = 0.8;
    // 查看是否通过pipeline同时cr过0.8
    for (const mr of unmergedMR) {
      if (mr.title.includes('封版占位MR')) {
        continue;
      }
      const hasTicket = mrHasTicket(mr, mr.product_version);
      const isCrPassed = await this.busCodeReviewService.isCodeReviewPassed(mr.id, approvedRate);
      const isPplPassed = await this.busCodeReviewService.isPipelinePassed(mr.id);
      const mrApprovedInfo = await this.mrApproval.checkHostMrApproval(mr.id);
      const isMrApproved = mrApprovedInfo.all_pass;
      const isConflicted = mr.conflicted !== 'not_conflicted';
      if (hasTicket && isCrPassed && isPplPassed && isMrApproved && !isConflicted) {
        const now = dayjs().utcOffset(8);
        const hour = now.hour();
        const minute = now.minute();
        // 只有在北京时间 11:30 - 23:59 之间才触发
        if ((hour === 11 && minute >= 29) || hour >= 12) {
          await this.busCodeReviewService.notifyReviewDelay(mr.id);
        } else {
          const groupId = mr.lark_group_id;
          // 可能不会触发，因为当时没有ticket
          await this.lark.sendTextMessage(UserIdType.chatId, groupId, '已满足合码并延后cr条件，11:30后可申请延后cr。');
        }
      } else {
        // 向mr群发一条消息，显示mr的ticket、cr、ppl状态
        await this.busCodeReviewService.notifyCRInMRGroup(mr.id, hasTicket);
      }
    }
  }
  async mrPrevCheck(appId: number): Promise<void> {
    const mrIds = await this.boardingMrDao.getMrIdsByAppId(appId.toString());
    for (const mrId of mrIds) {
      const mr = await this.bitsService.getMrInfo({ mrId });
      if (!mr) {
        this.logger.info(`[mrPrevCheck]mr信息丢失`);
        await this.boardingMrDao.deleteByMrId(mrId);
        continue;
      }
      await this.busCodeReviewService.notifyCRInMRGroup(mr.id, false);
    }
  }
  /**
   * 发 ticket 开始, 16 点
   */
  async onCodeFreezeStart(appId: number): Promise<void> {
    this.logger.info('[Bus] [onCodeFreezeStart] ticket 已发完回调触发！');
    const bus = await this.busDao.queryCurrentIntegrationBusByAppId(appId);
    if (!bus) {
      this.logger.info('[Bus] [onCodeFreezeStart] 搜索不到 bus！');
      return;
    }
    this.logger.info(`[Bus] [onCodeFreezeStart] 搜索到 bus ${bus.version} ${bus._id}！正在发消息！`);
    await this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, this.busCard.buildTicketStart(bus));
    // 把没合、没 ticket 的 MR 都下车
    await this.busMrModel
      .find({
        busId: bus._id,
        status: BusMrStatus.ON_BUS,
        merged: false,
      })
      // 每个 MR，在 bits 上获取 MR 信息
      .then(it =>
        it.map(({ mrId }, idx) =>
          // 每 0.5s 发一个请求
          wait(500 * idx).then(() => this.bitsService.getMrInfo({ mrId })),
        ),
      )
      // 等待所有信息获取完成
      .then(it => Promise.all(it))
      // 过滤没获取到的、已经有 ticket 的
      .then(mrInfos => compact(mrInfos.filter(it => it && !mrHasTicket(it, bus.version))))
      .then(it => (this.logger.info(`[Bus] [onCodeFreezeStart] 将要下车: ${it.map(v => v.id).join(', ')}`), it))
      // 获取一下这些 user id
      .then(
        async (mrInfos): Promise<[MrInfo[], Record<string, string>]> => [
          mrInfos,
          await this.getMrAuthorIds(mrInfos.map(it => it.author)),
        ],
      )
      // 对于所有没有 ticket 的 MR
      .then(([mrInfos, userIds]) =>
        mrInfos.map((mrInfo): Promise<any>[] => [
          // 下车
          this.getOffMr(bus, mrInfo, GetOffReason.WithoutTicket).then(() =>
            this.logger.info(`[Bus] [onCodeFreezeStart] 已下车：${mrInfo.id}`),
          ),
          // 私聊告知用户已经下车
          this.lark.sendCardMessage(
            UserIdType.openId,
            userIds[mrInfo.author] ?? 'ou_a8c39419c499642dc6b946b60cdfe28d',
            this.busCard.buildGetOffBusActionCard(bus, mrInfo),
          ),
        ]),
      )
      // 等待上述操作完成
      .then(it => Promise.all(it.flat()));
  }

  /**
   * 处理用户命令消息
   * @param chat_id
   * @param rawData
   */
  async handleBusLarkMessage(chat_id: string, rawData: string) {
    dayjs.extend(utc);
    this.logger.info(`[Bus] [handleBusLarkMessage] 收到来自 ${chat_id} 的指令: ${rawData}`);
    const args = rawData.trim().replace(/\s+/g, ' ').split(' ');
    this.logger.info(`[Bus] [handleBusLarkMessage] args = [${args.join(', ')}]`);
    if (args.length < 1) {
      this.logger.info('[Bus] [handleBusLarkMessage] args is empty, returning');
      return;
    }
    const bus = await this.busMetaModel.findOne({ chatId: chat_id });
    if (!bus) {
      this.logger.info('[Bus] [handleBusLarkMessage] no matching bus found');
      return;
    }
    this.logger.info(`[Bus] [handleBusLarkMessage] found matching bus ${bus.version} ${bus._id}`);

    const replyErrorMsg = (msg: string) => this.lark.sendTextMessage(UserIdType.chatId, bus?.chatId, msg);

    const [command, mrUrl] = args;

    const hBus = () => this.notifyBusMrList(bus, false);
    const hBusAll = () => this.notifyBusMrList(bus, true);
    const hPush = () => this.notifyPushBus(bus);
    const hOff = async () => {
      const mrId = parseInt(mrUrl.replace(/\S*\/([0-9]*)[?]?(.*)/, '$1'), 10);
      const mrInfo = await this.bitsService.getMrInfo({ mrId });
      if (!mrInfo) {
        await replyErrorMsg('找不到 MR 信息，请检查 url 合法性');
      } else {
        await this.getOffMr(bus, mrInfo, GetOffReason.GroupManual);
        await this.fastMergeService.invalideFastMergeMr(mrInfo, 'MR下车');
        // 取消下ticket
        mrInfo.tags.map(({ group_project_name }) =>
          this.bitsService.mrUnbindTag(mrId, `ticket:integration:${bus.version}`, group_project_name).catch(),
        );
      }
    };
    const hOn = async () => {
      const mrId = parseInt(mrUrl.replace(/\S*\/([0-9]*)[?]?(.*)/, '$1'), 10);
      const mrInfo = await this.bitsService.getMrInfo({ mrId });
      if (mrInfo) {
        this.getOnMr(bus, mrInfo, GetOnReason.GroupManual)
          .then(async err => {
            if (err) {
              await replyErrorMsg(err);
            }
          })
          .catch(async e => {
            await replyErrorMsg(`操作失败, ${JSON.stringify(e)}`);
          });
      } else {
        await replyErrorMsg('找不到 MR 信息，请检查 url 合法性');
      }
    };
    const hRefresh = () => this.refreshBusState(bus);
    const hClose = () => this.closeBus(bus, false);
    const hProtect = async () => {
      const mrId = parseInt(mrUrl.replace(/\S*\/([0-9]*)[?]?(.*)/, '$1'), 10);
      await this.conflictCheckService.setMMRProtected(mrId);
    };
    const hCancelProtect = async () => {
      const mrId = parseInt(mrUrl.replace(/\S*\/([0-9]*)[?]?(.*)/, '$1'), 10);
      await this.conflictCheckService.cancelProtected('手动取消', mrId, undefined);
    };
    const handleMap: Record<string, () => Promise<void>> = {
      '/bus': hBus,
      '/busall': hBusAll,
      '/push': hPush,
      '/off': hOff,
      '/on': hOn,
      '/refresh': hRefresh,
      '/close': hClose,
      '/protect': hProtect,
      '/cancel_protect': hCancelProtect,
    };

    this.logger.info(`[Bus] [handleBusLarkMessage] command: ${command}, has handler: ${Boolean(handleMap[command])}`);
    await (handleMap[command] ?? noop)();
  }

  /**
   * 提前一天提醒 MR 是否要上车
   * 该方法未经测试，只能先检查传入参数
   * @param appId
   * @param version
   * @param mrInfos
   * @param mrAuthorIdMap
   * @param urgent 是否加急
   */
  async remindMarkOnBus(
    appId: AppSettingId,
    version: string,
    mrInfos: MrInfo[],
    mrAuthorIdMap?: Record<string, string>,
    urgent = false,
  ): Promise<[number, string | undefined][]> {
    const mrAuthorIds = merge(
      {},
      mrAuthorIdMap ?? {},
      await this.getMrAuthorIds(without(uniq(mrInfos.map(it => it.author)), ...Object.keys(mrAuthorIdMap ?? {}))),
    );
    return await Promise.all(
      mrInfos.map((mr, idx) =>
        wait(400 * idx)
          .then(async () => {
            if (appId === AppSettingId.LV_WIN) {
              try {
                const ret = await this.lark.sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusInformCard(mr, version, appId, '剪映专业版'),
                  urgent,
                );
                return ret.data?.message_id;
              } catch {
                return undefined;
              }
            } else if (appId === AppSettingId.RETOUCH_IOS) {
              try {
                const ret_1 = await this.lark.sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusInformCard(mr, version, appId, '醒图-iOS'),
                  urgent,
                );
                return ret_1.data?.message_id;
              } catch {
                return undefined;
              }
            } else if (appId === AppSettingId.RETOUCH_ANDROID) {
              try {
                const ret_2 = await this.lark.sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusInformCard(mr, version, appId, '醒图-Android'),
                  urgent,
                );
                return ret_2.data?.message_id;
              } catch {
                return undefined;
              }
            } else if (appId === AppSettingId.LV_ANDROID) {
              try {
                const ret_3 = await this.lark.sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusInformCard(mr, version, appId, '剪映'),
                  urgent,
                );
                return ret_3.data?.message_id;
              } catch {
                return undefined;
              }
            } else {
              try {
                const ret_4 = await this.lark.sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusInformCard(mr, version, appId, 'CC'),
                  urgent,
                );
                return ret_4.data?.message_id;
              } catch {
                return undefined;
              }
            }
          })
          .then((message_id): [number, string | undefined] => [mr.id, message_id]),
      ),
    );
  }
  /**
   * 提醒不符合条件 MR 是否要上车
   * @param bus
   * @param mrInfos
   * @param mrAuthorIdMap （可选）MR author 的 ID
   * @param urgent 是否加急
   */
  async remindGetOnBus(
    bus: BusMeta,
    mrInfos: MrInfo[],
    mrAuthorIdMap?: Record<string, string>,
    urgent = false,
  ): Promise<[number, string | undefined][]> {
    const mrAuthorIds = merge(
      {},
      mrAuthorIdMap ?? {},
      await this.getMrAuthorIds(without(uniq(mrInfos.map(it => it.author)), ...Object.keys(mrAuthorIdMap ?? {}))),
    );
    return await Promise.all(
      mrInfos.map((mr, idx) =>
        wait(400 * idx)
          .then(async () => {
            const isOnboard = await this.boardingMrDao.findByMrIdAndAppId(mr.id, bus.appId?.toString());
            if (isOnboard) {
              await this.processAutoGetOnBus(bus, bus.appId, mr.id, mrAuthorIds[mr.author]);
              return;
            }
            if (bus.appId === AppSettingId.LV_WIN) {
              return this.lark
                .sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusActionCard(bus, mr, isOnboard, '剪映专业版'),
                  urgent,
                )
                .then(ret => ret.data?.message_id)
                .catch(() => undefined); // 确保 catch 也返回 undefined
            } else if (bus.appId === AppSettingId.RETOUCH_IOS) {
              return this.lark
                .sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusActionCard(bus, mr, isOnboard, '醒图-iOS'),
                  urgent,
                )
                .then(ret => ret.data?.message_id)
                .catch(() => undefined);
            } else if (bus.appId === AppSettingId.RETOUCH_ANDROID) {
              return this.lark
                .sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusActionCard(bus, mr, isOnboard, '醒图-Android'),
                  urgent,
                )
                .then(ret => ret.data?.message_id)
                .catch(() => undefined);
            } else {
              return this.lark
                .sendCardAndUrgent(
                  UserIdType.openId,
                  !mrAuthorIds[mr.author] ? 'ou_a8c39419c499642dc6b946b60cdfe28d' : mrAuthorIds[mr.author],
                  this.busCard.buildGetOnBusActionCard(bus, mr, isOnboard),
                  urgent,
                )
                .then(ret => ret.data?.message_id)
                .catch(() => undefined);
            }
          })
          .then((message_id): [number, string | undefined] => [mr.id, message_id]),
      ),
    );
  }

  /**
   * 发送车上 MR 状态卡片
   * @param bus 车次
   * @param withMergedDetail 是否展示合入mr详情
   */
  async notifyBusMrList(bus: BusMeta, withMergedDetail: boolean): Promise<void> {
    const allMr: BusMr[] = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
    });
    this.busCard
      .buildBusMrListCard(
        bus,
        allMr.filter(v => !v.merged).map(v => v.mrId),
        allMr.filter(v => v.merged).map(v => v.mrId),
        withMergedDetail,
      )
      .then(card => {
        this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, card);
      });
  }

  /**
   * 发送 Push 卡片
   * @param bus 车次
   */
  async notifyPushBus(bus: BusMeta): Promise<void> {
    dayjs.extend(utc);
    this.logger.info('[Bus] [notifyPushBus] start');
    const unmergedMrs: BusMr[] = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });
    this.logger.info(`[Bus] [notifyPushBus] get ${unmergedMrs.length} MRs`);
    await this.lark.sendCardMessage(
      UserIdType.chatId,
      bus.chatId,
      await this.busCard.buildPushBusCard(uniq(unmergedMrs.map(v => v.mrId))),
    );
    if (dayjs().utcOffset(8).hour() < 16) {
      this.logger.info('[Bus] [notifyPushBus] triggered notifyNotQualifiedMr');
      this.notifyNotQualifiedMr(bus);
      this.notifyFeatureNotReadyMR(bus);
    } else {
      this.logger.info('[Bus] [notifyPushBus] not triggered notifyNotQualifiedMr');
    }
  }

  /**
   * 提醒未达到准入标准的 MR
   * @param bus
   */
  async notifyNotQualifiedMr(bus: BusMeta) {
    dayjs.extend(utc);
    this.logger.info('[Bus] [notifyNotQualifiedMr] start');
    const allOnBusMrs = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });
    this.logger.info(`[Bus] [notifyNotQualifiedMr] get ${allOnBusMrs.length} MRs`);
    const badMrs: MrInfo[] = [];
    for (const mr of allOnBusMrs) {
      const mrInfo = await this.bitsService.getMrInfo({ mrId: mr.mrId });
      if (mrInfo && !mrHasTicket(mrInfo, bus.version)) {
        badMrs.push(mrInfo);
      }
    }
    const card = this.busCard.buildNotQualifiedMrNotifyCard(bus.version, badMrs);
    this.logger.info(`[notifyNotQualifiedMr] Card: ${JSON.stringify(card)}`);
    await this.lark.sendCardAndUrgent(UserIdType.chatId, bus.chatId, card, dayjs().utcOffset(8).hour() >= 15);
  }

  /**
   * 循环发放ticket
   * @param bus
   */
  async giveTicketToQualifiedMr(bus: BusMeta) {
    const allOnBusMrs = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });
    for (const mr of allOnBusMrs) {
      const mrInfo = await this.bitsService.getMrInfo({ mrId: mr.mrId });
      if (!mrInfo || mrHasTicket(mrInfo, bus.version)) {
        continue;
      }
      const qualified = await this.isQualifiedMr(mr.mrId);
      if (!qualified.all_pass) {
        continue;
      }
      let res;
      // 醒图
      if (
        bus.appId &&
        (bus.appId === AppSettingId.RETOUCH_IOS || (bus.appId && bus.appId === AppSettingId.RETOUCH_ANDROID))
      ) {
        res = await useInject(CollectAbnormalMultiMrService).addRetouchVersionTicket(mr.mrId, bus.version);
      } else if (bus.appId && bus.appId === AppSettingId.LV_WIN) {
        res = await useInject(CollectAbnormalMultiMrService).addLVPCVersionTicket(mr.mrId, bus.version);
      } else if (bus.appId === AppSettingId.CC_ANDROID) {
        res = await useInject(CollectAbnormalMultiMrService).addLvCcVersionTicketAuto(
          mr.mrId,
          versionUtils.cc2lvVersion(bus.version),
        );
      } else {
        // 触发ticket检测
        res = await useInject(CollectAbnormalMultiMrService).addLvCcVersionTicketAuto(mr.mrId, bus.version);
        // 记录下检测合格发放票据的原因
        try {
          await this.mrEfficiencyModelService.updateGetTicketRecord(mr.mrId, dayjs().unix(), 'auto_get_ticket');
        } catch (e) {
          if (e instanceof Error) {
            this.logger.info(
              `mrEfficiencyModelService updateGetTicketEvent error, mrId: ${mr.mrId}, source: auto_get_ticket`,
            );
            this.logger.error(e);
          }
        }
        await this.codeFreezeMrService.triggerAutoSyncBitsJobs(mr.mrId);
      }
      this.logger.info(`[Bus] [giveTicketToQualifiedMr] mrId: ${mr.mrId}, res: ${JSON.stringify(res)}`);
      if (mrInfo && res.code === NetworkCode.Success) {
        const card = this.busCard.buildMrGetTicketCard(mrInfo, '达到需求准入标准');
        await this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, card);
      } else {
        await this.lark.quickLog(
          `发放ticket失败，请 <at email=<EMAIL>></at> 检查。mrId:${mrInfo?.id} res:${JSON.stringify(res)}`,
        );
      }
    }
  }

  async hasRetouchMiddleware(mrInfo: MrInfo): Promise<boolean> {
    const relationList = await this.bitsService.getMrRelationList(mrInfo.id);
    if (relationList) {
      for (const relation of relationList) {
        if (relation.project_name.includes('RetouchMiddleware')) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * 仅标记MR为上车状态
   * @param bus 关联的封版车次信息
   * @param mrInfo 需要标记上车的MR信息
   * @returns 操作结果（成功返回undefined，失败返回错误信息）
   */
  async markMrOnBus(bus: BusMeta, mrInfo: MrInfo): Promise<string | undefined> {
    this.logger.info(`[Bus] [markMrOnBus] 开始标记MR上车，busId: ${bus._id}, mrId: ${mrInfo.id}`);

    // 车次必须为开启状态
    if (bus.status !== BusStatus.OPEN) {
      const errorMsg = `标记失败：车次[${bus.version}]状态非开启`;
      this.logger.error(errorMsg);
      return errorMsg;
    }

    // MR信息必须有效
    if (!mrInfo.id) {
      const errorMsg = '标记失败：MR信息不完整（缺少mrId）';
      this.logger.error(errorMsg);
      return errorMsg;
    }

    // 更新数据库中MR的状态为ON_BUS（不存在则创建记录）
    try {
      await this.busMrModel.updateOne(
        { busId: bus._id, mrId: mrInfo.id },
        {
          $set: {
            status: BusMrStatus.ON_BUS, // 标记为已上车
            merged: false,
            messageId: '',
          },
        },
        { upsert: true },
      );
      this.logger.info(`[Bus] [markMrOnBus] 标记成功，busId: ${bus._id}, mrId: ${mrInfo.id}`);
      return undefined;
    } catch (error) {
      const errorMsg = `标记失败：数据库操作异常 - ${JSON.stringify(error)}`;
      this.logger.error(errorMsg);
      return errorMsg;
    }
  }
  /**
     * 操作 MR 上车
     * @param bus 车次
     * @param mrInfo 需要上车的 MR 信息
     * @param reason 上车理由
     * @return 上车成功为 undefined，失败则返回理由

     */
  async getOnMr(bus: BusMeta, mrInfo: MrInfo, reason: GetOnReason): Promise<string | undefined> {
    this.logger.info(`[Bus] [getOnMr] bus: ${JSON.stringify(bus)} mrInfo: ${JSON.stringify(mrInfo)} reason: ${reason}`);
    dayjs.extend(utc);
    if (bus.status !== BusStatus.OPEN) {
      return '车次已关闭';
    }
    const user = await this.lark.searchUserInfoByEmail(add_suffix_ne('@bytedance.com')(mrInfo.author));
    if (!user) {
      return '上车失败，找不到 MR 作者信息';
    }
    if (reason !== GetOnReason.BindTicket && dayjs().utcOffset(8).hour() >= 16) {
      return '超过发 ticket 时间，如需手动上车请联系 BM 评估';
    }
    if (!silentBusMode) {
      await this.lark.addUserToChatGroup(bus.chatId, UserIdType.openId, [user.open_id]);
    }
    if (bus.appId === AppSettingId.RETOUCH_ANDROID || bus.appId === AppSettingId.RETOUCH_IOS) {
      const hasMiddle = await this.hasRetouchMiddleware(mrInfo);
      if (hasMiddle) {
        // RetouchMiddleware 需要双端都上车
        const adrBus = await this.busDao.queryCurrentIntegrationBusByAppId(AppSettingId.RETOUCH_ANDROID);
        const iosBus = await this.busDao.queryCurrentIntegrationBusByAppId(AppSettingId.RETOUCH_IOS);
        if (adrBus) {
          const hasOnBusMr = await this.busMrModel.findOne({
            status: BusMrStatus.ON_BUS,
            busId: adrBus._id,
            mrId: mrInfo.id,
          });
          this.logger.info(`[Bus] [getOnMr] hasOnBusMr: ${JSON.stringify(hasOnBusMr)}`);
          if (!hasOnBusMr) {
            await this.busDao.overwriteWithStatus(adrBus, mrInfo, BusMrStatus.ON_BUS);
            await this.lark.sendCardMessage(
              UserIdType.chatId,
              adrBus.chatId,
              this.busCard.buildMrOnBusCard(mrInfo, reason),
            );
          }
        }
        if (iosBus) {
          const hasOnBusMr = await this.busMrModel.findOne({
            status: BusMrStatus.ON_BUS,
            busId: iosBus._id,
            mrId: mrInfo.id,
          });
          this.logger.info(`[Bus] [getOnMr] hasOnBusMr: ${JSON.stringify(hasOnBusMr)}`);
          if (!hasOnBusMr) {
            await this.busDao.overwriteWithStatus(iosBus, mrInfo, BusMrStatus.ON_BUS);
            await this.lark.sendCardMessage(
              UserIdType.chatId,
              iosBus.chatId,
              this.busCard.buildMrOnBusCard(mrInfo, reason),
            );
          }
        }
        return undefined;
      }
    }
    const hasOnBusMr = await this.busMrModel.findOne({
      status: BusMrStatus.ON_BUS,
      busId: bus._id,
      mrId: mrInfo.id,
    });
    this.logger.info(`[Bus] [getOnMr] hasOnBusMr: ${JSON.stringify(hasOnBusMr)}`);
    // 添加上车的mr封版记录
    if (bus.appId !== AppSettingId.LV_WIN) {
      const onBusTime = dayjs().unix();
      await this.mrEfficiencyModelService.createMrEfficiencyDataWithFeature({
        mrId: mrInfo.id,
        reason,
        version: bus.version,
        onBusTime,
      });
      try {
        await this.bindBusTag(mrInfo.id, bus);
      } catch (e) {
        this.logger.info(`[Bus] [getOnMr] bindBusTag error: ${JSON.stringify(e)}`);
      }
    }

    if (!hasOnBusMr) {
      await this.busDao.overwriteWithStatus(bus, mrInfo, BusMrStatus.ON_BUS);
      await this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, this.busCard.buildMrOnBusCard(mrInfo, reason));
    }
    if (bus.appId !== AppSettingId.LV_WIN && (await this.bitsService.checkMrContainsPCRepo(mrInfo))) {
      // 剪映多主仓mr, 默认加入pc的车次
      const pcBus = await this.busDao.queryCurrentIntegrationBusByAppId(AppSettingId.LV_WIN);
      if (!pcBus) {
        return undefined;
      }
      const hasOnPCBusMr = await this.busMrModel.findOne({
        status: BusMrStatus.ON_BUS,
        busId: pcBus._id,
        mrId: mrInfo.id,
      });
      this.logger.info(`[Bus] [getOnMr] hasOnBusMr: ${JSON.stringify(hasOnPCBusMr)}`);
      if (!hasOnPCBusMr) {
        await this.busDao.overwriteWithStatus(pcBus, mrInfo, BusMrStatus.ON_BUS);
        await this.lark.sendCardMessage(UserIdType.chatId, pcBus.chatId, this.busCard.buildMrOnBusCard(mrInfo, reason));
      }
    }
    return undefined;
  }

  private async bindBusTag(mrId: number, bus: BusMeta) {
    const tagName = `bus:${bus.version}`;
    const projectName =
      bus.appId === AppSettingId.LV_ANDROID
        ? SpaceType.Android
        : bus.appId === AppSettingId.LV_IOS
          ? SpaceType.iOS
          : bus.appId === AppSettingId.LV_WIN
            ? SpaceType.PC
            : SpaceType.Android;
    return this.bindBusTagInner(tagName, mrId, projectName, bus.version);
  }

  private async bindBusTagInner(
    tagName: string,
    mrId: number | string,
    projectName: string,
    version: string,
    retry = false,
  ): Promise<string | undefined> {
    const result = await this.bitsService.mrBindTag(mrId, tagName, projectName);
    this.logger.info(`[mrBindTag] res:${JSON.stringify(result)}`);
    if (
      !retry &&
      (JSON.stringify(result.data).includes('record not found') ||
        JSON.stringify(result.data).includes('can not bind the same tag'))
    ) {
      const res = await this.bitsService.createTag(tagName, 'green', 'ticket', projectName);
      this.logger.info(`[createTag] res:${JSON.stringify(res)}`);
      return await this.bindBusTagInner(tagName, mrId, projectName, version, true);
    }
    if (
      result.code !== NetworkCode.Success &&
      result.code !== 200 &&
      !JSON.stringify(result.data).includes('can not bind the same tag')
    ) {
      return JSON.stringify(result);
    }
    return undefined;
  }

  /**
   * 操作 MR 下车
   * @param bus 车次
   * @param mrInfo 需要下车的 MR 信息
   * @param reason 下车理由
   * @return 下车成功为 undefined，失败则返回理由
   */
  async getOffMr(bus: BusMeta, mrInfo: MrInfo, reason: GetOffReason): Promise<string | undefined> {
    if (bus.status !== BusStatus.OPEN) {
      return '车次已关闭';
    }
    const hasOnBusMr = await this.busMrModel.findOne({
      busId: bus._id,
      mrId: mrInfo.id,
    });
    if (hasOnBusMr) {
      const shouldNotifyOff = hasOnBusMr.status === BusMrStatus.ON_BUS;
      await this.busDao.overwriteWithStatus(bus, mrInfo, BusMrStatus.OFF_BUS);
      // 下车记录
      if (shouldNotifyOff) {
        await this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, this.busCard.buildMrOffBusCard(mrInfo, reason));
      }
      await this.checkBusReady(bus);
      await this.mrEfficiencyModelService.updateOffBusRecord(mrInfo.id, dayjs().unix(), reason);
    }
    return undefined;
  }

  /**
   * 关闭车次
   * @param bus 车次
   * @param force
   */
  async closeBus(bus: BusMeta, force: boolean) {
    this.logger.info(`[Bus] [closeBus] 正在关闭车次 ${bus._id}`);
    if (!force) {
      const unmergedMr: BusMr[] = await this.busMrModel.find({
        busId: bus._id,
        status: BusMrStatus.ON_BUS,
        merged: false,
      });
      if (unmergedMr && unmergedMr.length > 0) {
        await this.lark.sendTextMessage(UserIdType.chatId, bus.chatId, '车上还有未合入的MR，无法关闭!');
        return;
      }
    }

    await this.busMetaModel.findByIdAndUpdate(bus._id, {
      status: BusStatus.CLOSED,
    });
    await this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, this.busCard.buildBusCloseCard(bus));
  }

  async refreshBusState(bus: BusMeta) {
    this.logger.info(`[Bus] [refreshBusState] 正在刷新车次 ${bus._id} MR 状态`);
    const mrs = await this.potentialMrSearch(getBitsSpacesById(bus.appId));
    const mrInDb: BusMr[] = await this.busMrModel.find({
      busId: bus._id,
    });
    const mrInfos = await Promise.all(
      uniq(mrs.concat(mrInDb.map(it => it.mrId))).map((it, idx) =>
        wait(400 * idx).then(() =>
          this.bitsService.getMrInfo({
            mrId: it,
          }),
        ),
      ),
    ).then(compact);
    const mrInfoMap: Record<number, MrInfo> = fromPairs(mrInfos.map(it => [it.id, it]));

    // mutates original array objects since they are no longer accessed
    for (const mr of mrInDb.filter(it => mrInfoMap[it.mrId])) {
      const info = mrInfoMap[mr.mrId];
      mr.merged = info.state === MrState.merged;
      if (info.state === MrState.closed) {
        this.logger.info(`[Bus] [refreshBusState] 正在下车 MR ${info.id}（已关闭）`);
        await this.getOffMr(bus, info, GetOffReason.MrClosed);
      }
      if (mrHasTicket(info, bus.version) && mr.status !== BusMrStatus.ON_BUS) {
        this.logger.info(`[Bus] [refreshBusState] 正在上车 MR ${info.id}（已取得 ticket）`);
        await this.getOnMr(bus, info, GetOnReason.BindTicket);
      }
    }

    for (const mrId of without(
      mrInfos.map(it => it.id),
      ...mrInDb.map(it => it.mrId),
    )) {
      const info = mrInfoMap[mrId];
      if (mrHasTicket(info, bus.version)) {
        this.logger.info(`[Bus] [refreshBusState] 正在上车 MR ${info.id}（已取得 ticket）`);
        await this.getOnMr(bus, info, GetOnReason.BindTicket);
      }
    }

    // save merged state
    await Promise.all(
      [true, false].map(cond =>
        this.busMrModel.updateMany(
          {
            busId: bus._id,
            mrId: {
              $in: mrInDb.filter(it => it.merged === cond).map(it => it.mrId),
            },
          },
          {
            $set: {
              merged: cond,
            },
          },
        ),
      ),
    );
  }

  /**
   * MR合入或者下车触发检查是否可以发车
   * @param bus
   */
  async checkBusReady(bus: BusMeta) {
    const unmerged = await this.busDao.queryBusAllMerged(bus._id);
    if (unmerged.length === 1) {
      const placeHolderMr = await this.bitsService.getMrInfo({
        mrId: unmerged[0].mrId,
      });
      if (placeHolderMr?.title?.includes('封版占位MR')) {
        if (bus.name === "Capcut") {
          const ios_ttp_status = await this.checkPodsTTPStatus.checkPodsTTPStatus('faceu-ios/CapCut', 'rc/develop');
          if (ios_ttp_status.code !== NetworkCode.Success) {
            this.logger.error(`[checkBusReady] faceu-ios/CapCut rc/develop TTP Pods Not Ready before bus ready`);
            return;
          }
        }
        if (!(await this.checkTTPVersion(bus))) {
          this.logger.info(`[checkBusReady] checkTTPVersion: has version not in ttp`);
          return;
        }
        await this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, this.busCard.buildBusReadyCard(bus));
        const res = await this.bitsService.closeMr(placeHolderMr.id, SpaceType.Android);
        this.logger.info(`[checkBusReady] closeMr: ${placeHolderMr.id} res:${JSON.stringify(res)}`);
      }
    } else if (unmerged.length === 0) {
      await this.lark.sendCardMessage(UserIdType.chatId, bus.chatId, this.busCard.buildBusReadyCard(bus));
      this.logger.info(`[checkBusReady] unmerged: ${unmerged.length}`);
    }
  }

  private async checkTTPVersion(bus: BusMeta): Promise<boolean> {
    if (bus.appId === AppSettingId.CC_ANDROID) {
      const network = new NetworkX('https://pa-build.bytedance.net/api/ktor/open', {
        'Content-Type': 'application/json',
      });
      const result = await network.get<{
        code: number;
        msg: string;
        data: boolean;
      }>('/checker/check_ttp_version');
      return result.code === 0 && result.data;
    }
    return true;
  }

  /**
   * 需求群推送未达准入MR
   */
  async notifyFeatureNotReadyMR(bus: BusMeta) {
    dayjs.extend(utc);
    this.logger.info('[Bus] [pushNotReadyMr] start');
    const allOnBusMrs = await this.busMrModel.find({
      busId: bus._id,
      status: BusMrStatus.ON_BUS,
      merged: false,
    });
    this.logger.info(`[Bus] [pushNotReadyMr] get ${allOnBusMrs.length} MRs`);
    for (const mr of allOnBusMrs) {
      const mrInfo = await this.bitsService.getMrInfo({ mrId: mr.mrId });
      if (!mrInfo || mrHasTicket(mrInfo, bus.version)) {
        continue;
      }
      const approval = await useInject(MrApprovalService).checkHostMrApproval(mrInfo.id, mrInfo);
      this.logger.info(`[Bus] [pushNotReadyMr] mrId: ${mrInfo.id} approval: ${JSON.stringify(approval)}`);
      if (!approval.all_pass) {
        await useInject(StoryReadyNotifyService).notifyMrNotReady(bus.version, mrInfo, approval, 0);
      }
    }
  }
}
