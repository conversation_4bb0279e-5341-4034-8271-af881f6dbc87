import { Inject, Injectable } from '@gulux/gulux';
import GitLabService from '../third/gitlab';
import fetch from 'node-fetch';
import { NetworkCode } from '@pa/shared/dist/src/core';
import DevOpsSendMessage from './devopsSendMsg';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const yaml = require('js-yaml');

interface PodSeerInfo {
  pod: string;
  source: string;
  version: string;
}

interface PodspecInfo {
  pod: string;
  source: string;
  version: string;
  podspec: string;
  status: number;
}

interface PodSpecResponse {
  code: number;
  data?: PodspecInfo[];
}

@Injectable()
export default class DevOpsCheckPodsTTPStatus {
  @Inject()
  private gitlab: GitLabService;

  @Inject()
  private sendMsg: DevOpsSendMessage;

  async getPodspecs(podInfos: PodSeerInfo[]): Promise<PodspecInfo[]> {
    const filelist: PodSeerInfo[] = [];

    for (const info of podInfos) {
      let podVersion = info.version;
      if (info.source.split('/').pop()?.includes('binary')) {
        if (podVersion.includes('-')) {
          if (!podVersion.endsWith('.1.binary')) {
            podVersion = `${podVersion}.1.binary`;
          }
        } else {
          if (!podVersion.endsWith('.1-binary')) {
            podVersion = `${podVersion}.1-binary`;
          }
        }
      }

      filelist.push({
        pod: info.pod,
        source: info.source,
        version: podVersion,
      });
    }

    const jsonData = {
      filelist,
    };

    // https://bytedance.larkoffice.com/wiki/wikcniz8ZXjExNpLMdzkuuADdxb
    const response = await fetch(`https://bitnest-us.byted.org/get_podspec`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(jsonData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: PodSpecResponse = await response.json();

    if (result.code === 0 && result.data) {
      return result.data;
    }

    return [];
  }

  async getDebugPods(project_id: string, branch: string): Promise<string[]> {
    const ignoreFile = await this.gitlab.getFileFromRepository(
      encodeURIComponent(project_id),
      branch,
      '.ttp/seer_ignored_bitnest_us_retrieve.yml',
    );
    const ignoreFileContent = ignoreFile.data;
    const ignoreFileYAML = yaml.load(ignoreFileContent);
    return ignoreFileYAML['debug_pods'];
  }

  async getSeers(project_id: string, branch: string, debug_pods: string[]) {
    const seerFile = await this.gitlab.getFileFromRepository(encodeURIComponent(project_id), branch, 'Podfile.seer');
    const seerFileContent = seerFile.data;
    if (seerFileContent) {
      const podInfos: PodSeerInfo[] = [];
      for (const line of seerFileContent.split('\n')) {
        const matchResult = line.match(/^-\s*(\S+)\s*\(\s*(\S+)\s*,\s*from\s*'(\S+)'\)/);
        if (matchResult) {
          const pod = matchResult[1];
          if (debug_pods.includes(pod)) {
            continue;
          }
          const version = matchResult[2];
          const source = matchResult[3];
          podInfos.push({ pod, version, source });
        }
      }
      return podInfos;
    }
    return [];
  }

  async checkPodsTTPStatus(project_id: string, branch: string) {
    const debug_pods = await this.getDebugPods(project_id, branch);
    const podInfos = await this.getSeers(project_id, branch, debug_pods);
    const podspecInfos = await this.getPodspecs(podInfos);
    const failed_pods = [];
    for (const podspecInfo of podspecInfos) {
      if (podspecInfo.podspec === '') {
        failed_pods.push(`${podspecInfo.pod}:${podspecInfo.version}`);
      }
    }
    if (failed_pods.length > 0) {
      await this.sendMsg.sendMessage(
        'checkPodsTTPStatus',
        'oc_838669b144472af285345d82d04b8b37',
        'TTP Pods Not Ready',
        'red',
        `Project: ${project_id}\nBranch: ${branch}\nPods: \n  - ${failed_pods.join('\n  - ')}\n<at email="<EMAIL>"></at>`,
      );
      return {
        code: NetworkCode.Error,
        failed_pods: failed_pods.join(','),
      };
    }
    await this.sendMsg.sendMessage(
      'checkPodsTTPStatus',
      'oc_838669b144472af285345d82d04b8b37',
      'TTP Pods Ready',
      'green',
      `Project: ${project_id}\nBranch: ${branch}\n<at email="<EMAIL>"></at>`,
    );
    return {
      code: NetworkCode.Success,
    };
  }
}
