import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkDocService from '../third/larkDocs';
import { LarkHttpResponseError } from '@gulux/gulux/lark';
import { FilterRule, Flight, FlightListResponse } from '@shared/libra/flight';
import _, { findLast } from 'lodash';
import {
  getAppIdByLibraAppId,
  MeegoTeamIdsForCapCutMobile,
  MeegoTeamIdsForJianyingMobile,
} from '@shared/libra/libraManageUtils';
import { isCNLibraByLibraAppId, isSGLibraByLibraAppId, LibraAppIds, LibraRegion } from '@shared/libra/commonLibra';
import { FlightType, FlightTypeName, LibraMeegoTeamSimpleInfo, LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { LibraNewInfoListService } from './LibraNewInfoListService';
import LibraAPIService from './LibraAPIService';
import { LibraControlReq } from '@shared/libra/libraControl';
import MeegoRawService from '../third/meego';
import { WorkItemInfo } from '@shared/typings/meego';
import { LibraMeego } from '@shared/libra/libraInfo';
import { getLibraVersionPlatformFromFilterRule } from '../../utils/libraUtil';
import { versionName2Code } from '@shared/utils/version_utils';
import { current_region, Region } from '../../utils/region';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';

export enum Level {
  ERROR,
  WARNING,
}

export enum Type {
  RULE, // 基本规则
  NOT_MATCH_DOC, // 和实验文档不一致
}

export interface AbCheckErrorItem {
  name: string;
  require?: string;
  actual?: string;
  level: Level;
  type: Type;
}

// 【说明】这个类迁移自 CUBA 的代码实现，用于检测飞书实验设计文档与 Libra 实验是否一致
@Injectable()
export class LibraDesignDocCheckService {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private larkDocService: LarkDocService;
  @Inject()
  private libraApi: LibraAPIService;
  @Inject()
  private libraNewInfoListService: LibraNewInfoListService;
  @Inject()
  private meegoService: MeegoRawService;
  @Inject()
  private rpcProxy: RpcProxyManager;

  check_rule_config = {
    exclude_string_lower_check: ['biz_scene', 'pf', 'user.is_creator', 'channel'],
  };

  isStringArray = (arr: unknown): arr is string[] => Array.isArray(arr) && arr.every(item => typeof item === 'string');
  isNumberArray = (arr: unknown): arr is number[] => Array.isArray(arr) && arr.every(item => typeof item === 'number');

  // 根据 MeegoTeamInfo 判断是否需要进行实验检测
  shouldCheckLibra(libraNewInfo: LibraNewInfo) {
    const { libraAppId } = libraNewInfo;
    if (libraAppId === 305) {
      // @FIXME: PM 反馈流程不够敏捷，先针对 CapCut 屏蔽掉实验设计文档检测
      return false;
    }

    // 暂时只处理剪映移动端的实验
    let shouldCheck = false;
    const meegoTeamInfos = libraNewInfo.meegoTeamInfo;
    const teamIds: number[] = meegoTeamInfos?.map(team => team.teamId) || []; // 筛选出所有的 teamId
    const mainTeamId = meegoTeamInfos?.find(team => team.isMainTeam)?.teamId || 0; // 筛选出主责团队的 teamId
    if (
      mainTeamId > 0 &&
      MeegoTeamIdsForJianyingMobile.includes(mainTeamId) /* || MeegoTeamIdsForCapCutMobile.includes(mainTeamId)*/
    ) {
      // 主责团队在剪映&CC移动端，则进行实验校验
      shouldCheck = true;
    } else if (
      teamIds.length > 0 &&
      mainTeamId === 0 &&
      teamIds.some(teamId => MeegoTeamIdsForJianyingMobile.includes(teamId)) /* ||
        teamIds.some(teamId => MeegoTeamIdsForCapCutMobile.includes(teamId))*/
    ) {
      // 没有主责团队，但是有剪映&CC移动端团队，则进行实验校验
      shouldCheck = true;
    }

    return shouldCheck;
  }

  async checkLibra(req: LibraControlReq) {
    const isSG = req.region === 'sg';
    const flightId = Number(req.flight_id);
    const libraAppId = Number(req.app_id);

    // 查询 LibraNewInfo 是否存在对应的记录
    let libraNewInfo: LibraNewInfo | null | undefined;
    const findOneQuery = {
      'flightInfo.id': flightId,
      libraAppId,
    };
    if (current_region() === Region.CN) {
      // CN 环境 RPC 转发一下
      const res = await this.rpcProxy.getQuality(true).libraNewInfoFindOne(findOneQuery);
      if (res) {
        libraNewInfo = res as LibraNewInfo;
      }
    } else {
      libraNewInfo = await this.libraNewInfoListService.findOne(findOneQuery);
    }

    if (libraNewInfo && !this.shouldCheckLibra(libraNewInfo)) {
      return {
        code: 0,
        msg: '无需校验',
      };
    }

    // 通过 API 查询 Libra 信息
    const flight = await this.libraApi.queryFlight(isSG ? LibraRegion.SG : LibraRegion.CN, Number(flightId));
    if (!flight) {
      // 如果通过 API 查询不到最新的 flight 信息，则返回失败
      return { code: -1, msg: `实验信息请求失败，flight_id: ${flightId}` };
    }

    // 没有 LibraNewInfo 就创建一个
    if (!libraNewInfo) {
      if (current_region() === Region.CN) {
        // CN 环境 RPC 转发一下
        const res = await this.rpcProxy
          .getQuality(true)
          .createLibraNewInfo(isSG ? LibraRegion.SG : LibraRegion.CN, flight.app_id, flight.id);
        if (res) {
          libraNewInfo = res as LibraNewInfo;
        }
      } else {
        // 没有 libraNewInfo，就顺便创建一个
        libraNewInfo = await this.libraNewInfoListService.createLibraNewInfo(
          isSG ? LibraRegion.SG : LibraRegion.CN,
          flight.app_id,
          flight.id,
        );
      }
      // 再次确认一下，是否需要校验
      if (libraNewInfo && !this.shouldCheckLibra(libraNewInfo)) {
        return {
          code: 0,
          msg: '无需校验',
        };
      }
    }

    // 获取 libra 关联的 Meego 信息，并进行实验设计文档校验
    const checkByDocResults = [];
    const meegoInfos: LibraMeego[] = flight.meego_info.meego_array;
    if (meegoInfos && meegoInfos.length > 0) {
      for (const meegoInfo of meegoInfos) {
        // 逐个遍历需求 Meego 信息（通过 Meego API 拉取需求信息）
        const meegoApiRsp = await this.meegoService.requestWorkItem(meegoInfo.meego_project, 'story', [
          Number(meegoInfo.meego_story),
        ]);
        if (meegoApiRsp && meegoApiRsp.data && meegoApiRsp.data.length > 0) {
          const meegoInfoByApi = meegoApiRsp.data[0];
          const { fields } = meegoInfoByApi;
          const targetFieldKeys = [
            'need_ab', // 是否需要 AB 实验
            'field_f8faa7', // 实验设计文档
          ];
          const filteredFields = fields.filter(field => targetFieldKeys.includes(field.field_key));
          let needAB = false;
          let libraDesignDocUrl = '';
          for (const field of filteredFields) {
            const { field_key } = field;
            if (field_key === 'need_ab') {
              // 是否开启 AB 实验
              needAB = field.field_value as boolean;
            } else if (field_key === 'field_f8faa7') {
              // 实验设计文档
              libraDesignDocUrl = field.field_value as string;
            }
          }
          if (needAB && libraDesignDocUrl.length === 0) {
            // 需要开启 AB，但是没有对应的实验设计文档，则进行管控，告知补充实验设计文档
            // https://meego.larkoffice.com/faceu/story/detail/6577319147
            const meegoUrl = `https://meego.larkoffice.com/${meegoInfo.meego_project}/story/detail/${meegoInfo.meego_story}`;
            return {
              code: -1,
              msg: `检测到实验 ${flightId} 对应 Meego: ${meegoUrl} “需要AB”字段为“是”，但未绑定实验设计文档，请进行绑定。参考链接处理：https://bytedance.larkoffice.com/wiki/JIKywsI0WiShQPksqE5czgo9nbh#XBj1dKhavoycdvxJ8YIcAdC5nvc`,
            };
          }
          if (needAB && libraDesignDocUrl.length > 0) {
            // 需要开启 AB，并且有对应的实验设计文档，则开始进行实验文档校验
            const checkByDocResult = await this.checkLibraByDoc(flight, libraDesignDocUrl);
            checkByDocResults.push(checkByDocResult);
          }
        }
      }
    }

    // 进行实验基本信息检测
    const basicCheckResult = await this.checkLibraBasic(flight, libraNewInfo?.meegoTeamInfo);

    return { code: 0, msg: 'check result', basicCheckResult, checkByDocResults };
  }

  // 从飞书文档中获取实验设计信息
  async getLibraDesignInfoFromDoc(
    libraDocUrl: string,
    flightId?: number, // 实验 id（可选），当传入具体的实验 id 后，将从电子表格中找到与实验 id 匹配的实验设计配置内容
  ): Promise<{ code: number; msg: string; data?: any }> {
    const { documentId } = await this.larkDocService.getDocumentIdByUrl(libraDocUrl);
    if (!documentId || !(await this.larkDocService.checkDocumentPermission(documentId))) {
      return {
        code: -1,
        msg: `实验文档无权限: ${libraDocUrl}，请参考链接处理：https://bytedance.larkoffice.com/wiki/JIKywsI0WiShQPksqE5czgo9nbh#BkHmdk9Q1og2BAxhuOdciAgJnMe`,
      };
    }

    const blocks = await this.larkDocService.findTargetBlocksFollowedAnchor(documentId, {
      anchor_block_type: [3, 4, 5, 6],
      anchor_text: '实验配置',
      targetblock_type: 30, // 电子表格
    });
    if (!blocks || blocks.length === 0) {
      console.error('parseTechDoc get sheetToken not found');
      return { code: -2, msg: '实验文档中没有找到电子表格' };
    }

    const queryValue = (infos: string[][] | undefined, target: string) =>
      infos?.find(item => item[0] === target)?.[1]?.trim() ?? '';

    let firstSheetInfos; // 第一个电子表格内容（可能存在多个电子表格，这里记录一下第一个）
    for (const block of blocks) {
      const token = block?.sheet?.token;
      if (!token) {
        console.error('parseTechDoc get sheetToken not found');
        return { code: -2, msg: '实验文档中没有找到电子表格' };
      }
      console.log(`parseTechDoc get sheetToken ${token}`);

      const [sheetToken, sheetId] = token.split('_');
      if (!sheetToken || !sheetId) {
        console.log('parseTechDoc sheet token error');
        // 后面增加的话就降级回退减少读取的数量，后面不想兼容了再走这里
        return { code: -3, msg: `内部错误：电子表格格式错误，token和sheetId解析失败: ${token}` };
      }
      // 实验设计文档模板：
      // 剪映：https://bytedance.larkoffice.com/docx/DnhhdBJfKoUEKyxFcVOclmtmnTd
      // CapCut：https://bytedance.larkoffice.com/docx/LfJLdkQXxo39KQxjFK8cJDI7nTd
      const info_range = `${sheetId}!A2:B15`;

      let sheetInfos;
      try {
        // {code:0,data:{value_ranges:[{range:"QLAxl5!A2:B14",values:[["每组实验流量","9%"],["分流方式","按did分流"],["实验类型","服务端"],["操作系统","iOS,Android"],["客户端起始版本","11.4.0"],["客户端结束版本","0"],["服务端起始版本","11.2.0"],["服务端结束版本","0"],["组数量（包含对照组）","2"],["实验周期（天）","90天"],["国家（英文）",""],["曝光时机","启动曝光"],["是否高优层",""]]}]},msg:""}
        const info_rsp = await this.larkDocService.getSpreadSheetPlainText(sheetToken, sheetId, [info_range]);
        sheetInfos = info_rsp.data.value_ranges.find(item => item.range === info_range)?.values;
      } catch (e) {
        if (e instanceof LarkHttpResponseError) {
          const { response } = e;
          const larkErrorBody = await response.json();
          if (larkErrorBody.code === 1310251 && larkErrorBody.msg?.includes('ranges out of sheet')) {
            return { code: -4, msg: '获取电子表格数据错误，请使用最新模板' };
          }
        } else {
          return { code: -4, msg: `获取电子表格数据错误，请检查表格格式是否符合要求` };
        }
      }

      if (firstSheetInfos === undefined && sheetInfos !== undefined) {
        // 记录一下第一个表格
        firstSheetInfos = sheetInfos;
      }

      if (flightId !== undefined) {
        // 当传入具体的实验 id 后，将从电子表格中找到与实验 id 匹配的实验设计配置内容
        const flightUrl = queryValue(sheetInfos, '实验链接');
        // 解析实验链接：以 “https://data.bytedance.net/libra/flight/{flightId}/”或者“https://libra-sg.tiktok-row.net/libra/flight/{flightId}/”样式
        if (flightUrl.length > 0 && flightUrl.includes(`/libra/flight/${flightId}/`)) {
          // 找到与实验 id 匹配的实验设计内容
          return {
            code: 0,
            msg: 'success',
            data: {
              flight_id: flightId ?? 0,
              version_resource_per_group: queryValue(sheetInfos, '每组实验流量'),
              resource_divide_type: queryValue(sheetInfos, '分流方式'),
              ab_type: queryValue(sheetInfos, '实验类型'),
              platform: queryValue(sheetInfos, '操作系统').split(',') ?? [],
              client_start_version: queryValue(sheetInfos, '客户端起始版本'),
              client_end_version: queryValue(sheetInfos, '客户端结束版本'),
              server_start_version: queryValue(sheetInfos, '服务端起始版本'),
              server_end_version: queryValue(sheetInfos, '服务端结束版本'),
              group_number: queryValue(sheetInfos, '组数量（包含对照组）'),
              ab_duration: parseInt(queryValue(sheetInfos, '实验周期（天）'), 10),
              countries: queryValue(sheetInfos, '国家（英文）')
                .split(',')
                .filter(item => item), // 国家（英文）
              exposure_stage: queryValue(sheetInfos, '曝光时机'),
              high_priority_layer: queryValue(sheetInfos, '是否高优层'),
            },
          };
        }
      }
    }

    // 如果 for 循环找遍了，发生以下情况：
    // 1. 没有传入 flightId，则默认使用第一个表格作为实验配置检测依据
    // 2. 传入了 flightId，但是没有找到实验id匹配的电子表格，则默认使用第一个表格作为实验配置检测依据
    // 总之：默认使用第一个电子表格
    const sheetInfos = firstSheetInfos;
    return {
      code: 0,
      msg: 'success',
      data: {
        flight_id: 0,
        version_resource_per_group: queryValue(sheetInfos, '每组实验流量'),
        resource_divide_type: queryValue(sheetInfos, '分流方式'),
        ab_type: queryValue(sheetInfos, '实验类型'),
        platform: queryValue(sheetInfos, '操作系统').split(',') ?? [],
        client_start_version: queryValue(sheetInfos, '客户端起始版本'),
        client_end_version: queryValue(sheetInfos, '客户端结束版本'),
        server_start_version: queryValue(sheetInfos, '服务端起始版本'),
        server_end_version: queryValue(sheetInfos, '服务端结束版本'),
        group_number: queryValue(sheetInfos, '组数量（包含对照组）'),
        ab_duration: parseInt(queryValue(sheetInfos, '实验周期（天）'), 10),
        countries: queryValue(sheetInfos, '国家（英文）')
          .split(',')
          .filter(item => item), // 国家（英文）
        exposure_stage: queryValue(sheetInfos, '曝光时机'),
        high_priority_layer: queryValue(sheetInfos, '是否高优层'),
      },
    };
  }

  /**
   * https://bytedance.larkoffice.com/wiki/FTc4wuln1iymE0kchq5cXcUNnrD
   * example:14.1.0
   * Android：剪映：1410 000 00；CC：1410 00 00
   * iOS：14001000
   */
  getMainVersionCode(appId: string, versionStr: string, client: 'Android' | 'iOS') {
    const normalVersion = versionStr;
    // if (appId === LibraAppIds.cc) {
    //   // CapCut 实验（先不处理了，文档里面填写的是多少，就是多少）
    //   const toCCVersion = (bitsVersion: string) => {
    //     const versions = bitsVersion.split('.');
    //     return `${parseInt(versions[0], 10) - 2}.${versions[1]}.${versions[2]}`;
    //   };
    //   normalVersion = toCCVersion(normalVersion);
    // }
    let versionCode: number | undefined;
    if (normalVersion.length > 5) {
      if (client === 'Android') {
        versionCode = parseInt(normalVersion.replace(/\./g, ''), 10) * (appId === LibraAppIds.lv ? 100000 : 10000);
      } else if (client === 'iOS') {
        versionCode = parseInt(
          normalVersion
            .split('.')
            .map(it => it.padStart(3, '0'))
            .join(''),
          10,
        );
      }
      return versionCode !== undefined ? versionCode.toString() : '';
    } else {
      return '';
    }
  }

  // 根据实验设计文档做检查
  async checkLibraByDoc(flight: Flight, libraDocUrl: string) {
    const checkResult: AbCheckErrorItem[] = [];
    if (!libraDocUrl) {
      return { code: -1, msg: '没有找到实验文档' };
    }
    const filter_function: string[] = flight.filter_function?.split('\n') ?? [];
    const filter_rule: FilterRule[] = flight.filter_rule ?? [];
    const isCN = isCNLibraByLibraAppId(flight.app_id);
    // 从过滤条件里面，获取版本相关信息
    const version_info_by_filter = getLibraVersionPlatformFromFilterRule(
      filter_rule,
      isCN ? LibraRegion.CN : LibraRegion.SG,
      flight.name,
      flight.description,
    );

    // 依赖实验文档中的配置做的判断
    const libraDesignInfo = await this.getLibraDesignInfoFromDoc(libraDocUrl, flight.id);
    if (libraDesignInfo.code !== 0) {
      // 实验文档信息获取失败
      return {
        code: libraDesignInfo.code,
        msg: libraDesignInfo.msg,
      };
    }
    const requireData = libraDesignInfo.data;
    if (!requireData) {
      // 实验文档信息获取失败
      return { code: -1, msg: '实验文档信息获取失败' };
    }

    const {
      version_resource_per_group, // 10%
      resource_divide_type,
      ab_type,
      platform,
      client_start_version,
      client_end_version,
      server_start_version,
      server_end_version,
      group_number,
      ab_duration,
      countries,
      exposure_stage,
    } = requireData;

    // 2. 分流方式 did, uid_only
    if (
      (resource_divide_type.includes('did') && !flight.layer_hash_strategy.includes('did')) ||
      (resource_divide_type.includes('uid') && !flight.layer_hash_strategy.includes('uid')) ||
      (resource_divide_type.includes('uid_ut') && !flight.layer_hash_strategy.includes('uid_ut')) ||
      (resource_divide_type.includes('uuid') && !flight.layer_hash_strategy.includes('uuid'))
    ) {
      checkResult.push({
        name: '分流方式',
        require: resource_divide_type,
        actual: flight.layer_hash_strategy,
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    const checkClientExist = (client: 'Android' | 'iOS' | '服务端') => {
      if (client === '服务端') {
        return true;
      }
      return Boolean(
        filter_rule.find(rule =>
          rule.conditions?.filter(
            c =>
              c.condition.key === 'device_platform' &&
              this.isStringArray(c.condition.value) &&
              c.condition.value.includes(client.toLowerCase()),
          ),
        ),
      );
    };

    // 4. 操作系统
    const actualPlatform: string[] = [];
    platform.forEach((platformItem: any) => {
      if (checkClientExist(platformItem)) {
        actualPlatform.push(platformItem);
      }
    });
    if (platform.length !== actualPlatform.length) {
      checkResult.push({
        name: '操作系统',
        require: platform.length > 0 ? platform.join(',') : '无',
        actual: actualPlatform.length > 0 ? actualPlatform.join(',') : '无',
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    // 7. 实验周期（天）
    if (flight.duration !== ab_duration) {
      checkResult.push({
        name: '实验时长',
        require: ab_duration.toString(),
        actual: flight.duration.toString(),
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    // 8. 国家（英文）
    const getActualRegions = () => {
      let actualRegion;
      for (const rule of filter_rule) {
        const condition = rule.conditions?.find(c =>
          ['region', 'cc_region', 'priority_region'].includes(c.condition.key),
        )?.condition;
        if (condition?.op === '==') {
          actualRegion = condition.value; // ["gb"]
        } else if (condition?.op === 'in_bundle') {
          actualRegion = [];
        }
      }
      return actualRegion;
    };
    const actualRegion = getActualRegions(); // // undefined：未找到，[]不用验证，[xxx, xxx]x一对一比较
    const expectedRegion = countries;
    let regionNotMatch = false;
    const appId = getAppIdByLibraAppId(flight.app_id); // 产品 ID
    if (isCN) {
      // 国内不处理
      regionNotMatch = false;
    } else if (!actualRegion) {
      if (countries.length > 0) {
        regionNotMatch = true;
      }
    } else if (Array.isArray(actualRegion) && actualRegion.length !== 0) {
      regionNotMatch = !_.isEqual(
        actualRegion.map((item: any) => item.toLowerCase().trim()).filter(i => i),
        expectedRegion.map((item: any) => item.toLowerCase().trim()).filter((i: any) => i),
      );
    }
    if (regionNotMatch) {
      let regionActualValue = '';
      if (Array.isArray(actualRegion)) {
        regionActualValue = actualRegion?.join(',');
      }
      checkResult.push({
        name: '实验国家',
        require: expectedRegion.join(','),
        actual: regionActualValue,
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    // 1. 检查流量
    const tolerance = 1; // 定义一个误差范围，例如 1%
    const actual_resource_per_group = (flight.version_resource ?? 0) / (flight.versions.length ?? 1); // 0.2/2
    const actual_resource_per_group_percentage = Math.round(actual_resource_per_group * 100);
    const experted_resource_per_group = parseInt(version_resource_per_group ?? '0', 10);
    // 如果是灰度实验，则校验是不是 100% 流量
    const isGrayLibra =
      (version_info_by_filter.android.isHit && version_info_by_filter.android.isBeta) ||
      (version_info_by_filter.iphone.isHit && version_info_by_filter.iphone.isBeta);
    if (isGrayLibra && flight.version_resource !== 1.0) {
      checkResult.push({
        name: '灰度需 100% 流量',
        require: '100%',
        actual: `${Math.round(flight.version_resource * 100)}%`,
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    // 每组实验流量，灰度实验不看每组流量
    if (!isGrayLibra && Math.abs(actual_resource_per_group_percentage - experted_resource_per_group) > tolerance) {
      checkResult.push({
        name: '每组实验流量',
        require: `${experted_resource_per_group}%`,
        actual: `${actual_resource_per_group_percentage}%`,
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    // 3. 实验类型
    if (
      (ab_type === '服务端' && flight.type !== FlightType.Server) ||
      (ab_type === '客户端-非本地实验' &&
        ![FlightType.ClientNormal, FlightType.SettingsClientSDK].includes(flight.type as FlightType)) ||
      (ab_type === '客户端-本地实验' && flight.type !== FlightType.AbLocalClient)
    ) {
      checkResult.push({
        name: '实验类型',
        require: ab_type,
        actual: FlightTypeName[flight.type as FlightType] ?? '',
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    // 5. 起始版本
    // 剪映 1640 CC 1440
    // 不用自动减 2
    // _version_code 与 _update_version_code
    const getActualClientVersion = (client: 'Android' | 'iOS') =>
      filter_rule
        .find(rule =>
          rule.conditions?.find(
            c =>
              c.condition.key === 'device_platform' &&
              Array.isArray(c.condition.value) &&
              c.condition.value.find(
                v =>
                  v.toString().toLowerCase() === client.toLowerCase() ||
                  (typeof v !== 'string' &&
                    typeof v !== 'number' &&
                    (v.id === 100358 ||
                      v.id === 102400 ||
                      v.id === 149 ||
                      v.name?.toString()?.toLowerCase() === 'ios_platform') &&
                    client === 'iOS'), // name: "iOS_platform"
              ),
          ),
        )
        ?.conditions?.find(c => c.condition.key === '_version_code')?.condition?.value;

    platform
      .filter((p: any) => p !== '服务端') // 服务端暂不检测
      .forEach((platformItem: any) => {
        // platformItem 为  "Android" | "iOS"
        let actualMinVersionNumber = ''; // 整型格式的版本号 16004000（最小版本号）
        let actualMaxVersionNumber = ''; // 整型格式的版本号 16004000（最大版本号）
        if (platformItem === 'Android' && version_info_by_filter.android.isHit) {
          actualMinVersionNumber = version_info_by_filter.android.minVersionCode.toString();
          actualMaxVersionNumber = version_info_by_filter.android.maxVersionCode.toString();
        } else if (platformItem === 'iOS' && version_info_by_filter.iphone.isHit) {
          actualMinVersionNumber = version_info_by_filter.iphone.minVersionCode.toString();
          actualMaxVersionNumber = version_info_by_filter.iphone.maxVersionCode.toString();
        }
        // Libra 实验设计文档中的版本号
        const expectedMinVersion = (client_start_version as string) ?? '';
        const expectedMaxVersion = (client_end_version as string) ?? '';
        const expectedMinVersionNumber = this.getMainVersionCode(appId, expectedMinVersion, platformItem);
        const expectedMaxVersionNumber = this.getMainVersionCode(appId, expectedMaxVersion, platformItem);
        if (expectedMinVersion.length > 0 && expectedMinVersion !== '0') {
          // 最小版本号有设定，需要校验
          if (actualMinVersionNumber !== expectedMinVersionNumber) {
            let minVersionCheckValid = false;
            if (actualMinVersionNumber.length === expectedMinVersionNumber.length && !isGrayLibra) {
              if (platformItem === 'Android') {
                // 比如 Android 正式实验，实验文档填写 16.2.0(162000000)，实验配置为 162001600
                const releaseVersionCode =
                  versionName2Code(version_info_by_filter.android.version, current_region() !== Region.SG) + 1600;
                if (Number(actualMinVersionNumber) >= releaseVersionCode) {
                  // 如果实际最小版本号，大于等于正式版本号，则判定是合法的
                  minVersionCheckValid = true;
                }
              }
            }
            if (actualMinVersionNumber.length > expectedMinVersionNumber.length && platformItem === 'iOS') {
              // iOS 可能用 _update_version_code（4位版本号），实验设计文档一般是 3 位版本号
              // 版本号：14002000(14.2.0)，小版本号：14002000095(14.2.0.95)
              // iOS x.x.x.80~99，最小 80 开始
              const expectedMinVersionNumber1000 = Number(expectedMinVersionNumber) * 1000 + 80;
              if (Number(actualMinVersionNumber) >= expectedMinVersionNumber1000) {
                minVersionCheckValid = true;
              }
            }

            if (!minVersionCheckValid) {
              checkResult.push({
                name: `${platformItem}起始版本号`,
                require: `${expectedMinVersionNumber}(${expectedMinVersion})`,
                actual: `${actualMinVersionNumber}`,
                level: Level.ERROR,
                type: Type.NOT_MATCH_DOC,
              } as AbCheckErrorItem);
            }
          }
        }
        if (expectedMaxVersion.length > 0 && expectedMaxVersion !== '0') {
          // 最大版本号有设定，需要校验
          if (actualMaxVersionNumber !== expectedMaxVersionNumber) {
            checkResult.push({
              name: `${platformItem}结束版本号`,
              require: `${expectedMaxVersionNumber}(${expectedMaxVersion})`,
              actual: `${actualMaxVersionNumber}`,
              level: Level.ERROR,
              type: Type.NOT_MATCH_DOC,
            } as AbCheckErrorItem);
          }
        }
      });

    // 6. 组数量（包含对照组），灰度实验不看分组数量
    if (!isGrayLibra && (flight.versions?.length ?? 0) !== Number(group_number)) {
      checkResult.push({
        name: '组数量（包含对照组）',
        require: group_number,
        actual: flight.versions?.length.toString() ?? '0',
        level: Level.ERROR,
        type: Type.NOT_MATCH_DOC,
      } as AbCheckErrorItem);
    }

    return { code: 0, msg: '', checkResult };
  }

  // 检查实验的基本信息
  async checkLibraBasic(flight: Flight, meegoTeamInfos?: LibraMeegoTeamSimpleInfo[]) {
    const checkResult: AbCheckErrorItem[] = [];
    const filter_rule: FilterRule[] = flight.filter_rule ?? [];
    const teamIds: number[] = meegoTeamInfos?.map(team => team.teamId) || []; // 筛选出所有的 teamId
    const mainTeamId = meegoTeamInfos?.find(team => team.isMainTeam)?.teamId || 0; // 筛选出主责团队的 teamId
    const appId = getAppIdByLibraAppId(flight.app_id); // 产品 ID
    const isSG =
      appId === LibraAppIds.cc ||
      appId === LibraAppIds.cc_web ||
      appId === LibraAppIds.cc_pc ||
      appId === LibraAppIds.hypic;
    // 1. 无需文档就能做的做的判断，主要检查一些异常case
    /* const vxRep = /^[v|V]\d$/
    // 1.1 分组名称检测
    const versionCheckFailedList = flight.versions?.filter(version=> {
        return vxRep.test(version.name) && !version.description // 分组名称为v0/v1等且没有描述时
    }).filter(version => {
        return version.name.toLowerCase() !== 'v0'
    })
    if (versionCheckFailedList.length > 0) {
        checkResult.push({
            name: '分组名称不要用v0/v1等无意义的词, 否则请带上描述信息',
            level: Level.WARNING,
            type: Type.RULE,
        } as AbCheckErrorItem)
    }*/
    filter_rule.forEach(rule => {
      rule.conditions?.forEach(c => {
        // 1.2. 文本类字段加上lower，忽略大小写
        const exclude_string_lower_check = this.check_rule_config?.exclude_string_lower_check ?? [];
        if (
          !exclude_string_lower_check.includes(c.condition.key) &&
          c.condition.op === '==' &&
          c.condition.type === 'string' &&
          c.condition.transformer !== 'lower'
        ) {
          checkResult.push({
            name: `文本类字段[${c.condition.key}]请加上lower，忽略大小写`,
            level: Level.ERROR,
            type: Type.RULE,
          } as AbCheckErrorItem);
        }
        // 1.3. 国家字段请使用cc_region，不要使用region
        if (c.condition.key === 'region' && flight.type !== FlightType.Server) {
          checkResult.push({
            name: '国家字段请使用cc_region，不要使用region',
            level: Level.ERROR,
            type: Type.RULE,
          } as AbCheckErrorItem);
        }
        // 1.4. iOS平台 ios iphone ipad都要选
        if (
          (mainTeamId > 0 && mainTeamId === 38227) ||
          (teamIds.length > 0 && mainTeamId === 0 && teamIds.includes(38227))
        ) {
          // 如果是生态的实验
          if (c.condition.key === 'device_platform') {
            if (
              !this.isStringArray(c.condition.value) &&
              !this.isNumberArray(c.condition.value) &&
              typeof c.condition.value !== 'number' &&
              typeof c.condition.value !== 'string' &&
              (c.condition.value[0]?.id === 100358 ||
                c.condition.value[0]?.id === 102400 ||
                c.condition.value[0]?.id === 149 ||
                c.condition.value[0]?.name?.toLowerCase() === 'ios_platform')
            ) {
              // name: "iOS_platform"
              // 不用检查 skip
            } else {
              if (
                this.isStringArray(c.condition.value) &&
                c.condition.value.some(v => ['ios', 'iphone', 'ipad'].includes(v.toString().toLowerCase()))
              ) {
                // 包含iOS平台
                if (
                  !['ios', 'iphone', 'ipad'].every(
                    v => this.isStringArray(c.condition.value) && c.condition.value.includes(v),
                  )
                ) {
                  checkResult.push({
                    name: 'iOS平台 ios iphone ipad都要选',
                    level: Level.ERROR,
                    type: Type.RULE,
                  } as AbCheckErrorItem);
                }
              }
            }
          }
        }
      });
      console.log(`check channel....${JSON.stringify(rule.conditions)}`);
      if (
        rule.conditions?.some(v => {
          console.log(`${v.condition.key}: ${JSON.stringify(v.condition.value)}`);
          return (
            v.condition.key === 'device_platform' &&
            this.isStringArray(v.condition.value) &&
            v.condition.value.some(platform => ['ios', 'iphone', 'ipad'].includes(platform.toString().toLowerCase()))
          );
        })
      ) {
        // iOS平台
        console.log('device_platform: iOS');
        const channelCondition = rule.conditions.find(v => v.condition.key === 'channel')?.condition;
        console.log(`channel: ${JSON.stringify(channelCondition)}`);
        if (
          channelCondition &&
          this.isStringArray(channelCondition.value) &&
          !channelCondition.value.some(v => ['app store', 'testflight'].includes(v.toString().toLowerCase()))
        ) {
          console.log(`channel: ${JSON.stringify(channelCondition.value)}`);
          checkResult.push({
            name: 'iOS平台 Channel 选择 "App Store" or "testflight"',
            level: Level.ERROR,
            type: Type.RULE,
          } as AbCheckErrorItem);
        }
      }
    });
    // 1.5. 普通客户端实验需要填写PSM
    if (FlightType.ClientNormal === flight.type) {
      // 普通客户端实验
      if (!flight?.specified_psms?.includes('toutiao.settings.settings')) {
        checkResult.push({
          name: '普通客户端实验需要填写PSM(toutiao.settings.settings)',
          level: Level.ERROR,
          type: Type.RULE,
        } as AbCheckErrorItem);
      }
    }
    // 1.6. 海外实验中应用功能模块需要选择Global
    if (appId === LibraAppIds.cc && !flight.product?.includes('Global')) {
      checkResult.push({
        name: '海外实验中应用功能模块需要选择Global',
        level: Level.ERROR,
        type: Type.RULE,
      } as AbCheckErrorItem);
    }
    // 1.7. 服务端实验需要填写PSM
    if (FlightType.Server === flight.type) {
      // 服务端实验
      if ((flight?.specified_psms?.length ?? 0) === 0) {
        checkResult.push({
          name: '服务端实验需要填写PSM',
          level: Level.ERROR,
          type: Type.RULE,
        } as AbCheckErrorItem);
      }
      // 内容生态服务端实验
      // 规则：faceu.feed.api、faceu.community.api、jianying.distribute.api 只要包含这个中间的任意一个，就必须再加上 jianying.distribute.platform
      else if ((flight?.specified_psms?.length ?? 0) > 0) {
        if (
          flight.specified_psms.some(psm =>
            ['faceu.feed.api', 'faceu.community.api', 'jianying.distribute.api'].some(item => psm.includes(item)),
          ) &&
          !flight.specified_psms.includes('jianying.distribute.platform')
        ) {
          checkResult.push({
            name: '实验PSM必须包含：jianying.distribute.platform',
            level: Level.ERROR,
            type: Type.RULE,
          } as AbCheckErrorItem);
        }
      }
    }

    return { code: 0, msg: 'success', checkResult };
  }
}
