import { User } from '@pa/shared/dist/src/core';
import { CrashType } from '@shared/releasePlatform/versionStageInfoCheckList';

export interface BaseItemInfo {}

export enum FunctionalBugItemStatus {
  Blocked = 0,
  Exempted = 1,
  Assessment = 2,
  resloved = 3,
  BusinessExempted = 4,
}

export interface FunctionalBugItemInfo extends BaseItemInfo {
  meego_id: number;
  meego_url: string;
  priority: string;
  need_resolve_priority?: string[];
  bug_name: string;
  operator: User;
  reporter: User;
  watcher?: User;
  status: FunctionalBugItemStatus;
  need_record: boolean;
  exempt_reason?: string;
  exempt_improvement_plan?: string;
  need_approve_members?: User[];
  approved_members?: User[];
  reject_member?: User;
  approveMember?: string[];
  exempt_chat_id?: string;
  exempt_message_id?: string;
  influence_scope?: string;
  influence_stage?: string;
}

export enum StabilityMetricsItemStatus {
  Blocked = 0,
  Exempted = 1,
  TBD = 2,
  Pass = 3,
}

export interface StabilityMetricItemInfo extends BaseItemInfo {
  slardar_info_id: string;
  crash_type: CrashType;
  status: StabilityMetricsItemStatus;
  owners: User[];
  owners_email?: string[];
  result_detail: string;
}

export enum BugResolveRatioItemStatus {
  Blocked = 0,
  Pass = 1,
  Exempted = 2,
  Assessment = 3,
}

export interface BugResolveRatioItemInfo extends BaseItemInfo {
  business_name: string;
  issue_view_id: string;
  owner: User;
  ratio: number;
  exempted_ratio: number;
  need_reslove_ratio: number;
  need_reslove_number: number;
  all_bug_count: number;
  resolved_bug_count: number;
  status: BugResolveRatioItemStatus;
  bug_item_list: FunctionalBugItemInfo[];
  exempt_reason?: string;
  exempt_improvement_plan?: string;
  approveMember?: string[];
  need_approve_members?: User[];
  approved_members?: User[];
  reject_member?: User;
  exempt_chat_id?: string;
  exempt_message_id?: string;
  influence_scope?: string;
  influence_stage?: string;
}

export interface MetricItemInfo extends BaseItemInfo {
  check_item_id: string;
  businessName: string;
  owner: User;
  email: string;
  status: BugResolveRatioItemStatus;
  itemInfos: MetricDetailItemInfo[];
  displayName: string;
  name: string;
  exempt_reason?: string;
  priority?: string;
  teaUrl?: string;
}

export interface MetricDetailItemInfo extends BaseItemInfo {
  owner: User;
  name: string;
  businessName: string;
  displayName: string;
  priority: string;
  exempt_reason?: string;
  status: BugResolveRatioItemStatus;
}

export interface TotalBugResolveRatioItemInfo extends BaseItemInfo {
  ratio: number;
  exempted_ratio: number;
  need_reslove_ratio: number;
  need_reslove_number: number;
  s_p0_p1_count: number;
  item_infos: BugResolveRatioItemInfo[];
}

export interface SendToHostFlightInfo {
  flightId: number;
  flightName: string;
  ownersEmail?: string[];
}

export interface LibraCheckItemInfo extends BaseItemInfo {
  meego_id: number;
  meego_name: string;
  owners: User[];
  status: LibraCheckItemStatus;
  flight_id: number;
  flight_name: string;
  exempt_reason?: string;
  exempt_improvement_plan?: string;
  approveMember?: string[];
  need_approve_members?: User[];
  approved_members?: User[];
  reject_member?: User;
  exempt_chat_id?: string;
  exempt_message_id?: string;
  influence_scope?: string;
  influence_stage?: string;
  flight_info_list?: SendToHostFlightInfo[];
}

export enum LibraCheckItemStatus {
  Blocked = 0,
  Exempted = 1,
  Assessment = 2,
}

export enum ManuallyCheckItemStatus {
  Blocked = 0,
  Exempted = 1,
  TBD = 2,
  Pass = 3,
}

export enum ManuallyCheckItemType {
  Default = 0,
  UnMergedMrs = 1,
  PCMetrics = 2,
}

export interface ManuallyCheckItemInfo extends BaseItemInfo {
  status: ManuallyCheckItemStatus;
  type: ManuallyCheckItemType;
}

export interface CodeFreezeCheckItemInfo extends BaseItemInfo {
  busId: string;
}

export type IndividualCheckItemStatus =
  | FunctionalBugItemStatus
  | StabilityMetricsItemStatus
  | BugResolveRatioItemStatus
  | ManuallyCheckItemStatus
  | CircuitBreakerCheckItemStatus;

export enum CircuitBreakerCheckItemStatus {
  Blocked = 0,
  Exempted = 1,
  Pass = 2,
  UnRelease = 3,
}

export interface PcCrashItemInfo extends BaseItemInfo {
  crash_id: string; // 唯一ID，对应数据库中的崩溃事件ID
  oncallLink: string; // Oncall群链接
  priority: string; // 优先级
  assignee: User; // 处理人邮箱
  status: string; // 状态，例如 Open, Resolved, Exempted
  name?: string; // 崩溃名称或描述
  rawCrashData?: any; // 存储从数据库获取的原始崩溃数据
  created_at?: Date; // 创建时间，可选
  stack_info: string; // 堆栈信息
  // 可以根据需要添加更多从 pcCrashRecord 读取的字段
}

export interface FeedbackMetricsItemInfo extends BaseItemInfo {
  [key: string]: number; // 添加索引签名
  total: number;
  processed: number;
  notHandled: number;
  noLabel: number;
  userNotContacted: number;
  qaInProgress: number;
  localReproduction: number;
  rdInProgress: number;
  userOccasional: number;
  autoRecovery: number;
  notABug: number;
  contactErrorNoReproduction: number;
  newBugReported: number;
  historicalBugRepushed: number;
  knownIssueOptimizationRequired: number;
  knownIssueFixed: number;
  knownIssueUnfixed: number;
  nonAggregated: number;
}

export enum FeedbackMetricsItemStatus {
  Blocked = 0,
  Exempted = 1,
  TBD = 2,
  Pass = 3,
}
