import { Api, Data, Post, useInject, useReq } from '@edenx/runtime/bff';
import { MrType } from '@shared/bits/mrInfo';
import { NetworkCode, NetworkResult, PlatformType } from '@pa/shared/dist/src/core';
import {
  mrRaiseOpSchema,
  queryHistorySchema,
  upadteBranchContentSchema,
  updateCloudAlbumVersionSchema,
  updateIOSVeRequestSchema,
  updateLynxVersionSchema,
  updatePippitRequestSchema,
  updateVeRequestSchema,
} from '@shared/schema';
import { UpdateVersionInfo } from '@shared/updateVersion';
import { z } from 'zod';
import GitLabService from '../service/third/gitlab';
import BitsService from '../service/third/bits';
import UserService from '@pa/backend/dist/src/service/user';
import BranchDiffRemain from '../service/handler/consul/branchDiffRemain';
import LynxService, { TemplateFile } from '../service/lynx';
import UpdateVersionService from '../service/model/updateVersionInfo';
import MrModelService from '../service/model/mrModule';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import VersionUpdate from '../service/versionUpdate';
import LarkService from '@pa/backend/dist/src/third/lark';
import SyncTranslation from '../service/SyncTranslation';
import MergeRequestService from '../service/mergeRequest';
import IosModuleUpdate from '../service/iosModuleUpdate';
import { MrSearchRequest } from '@shared/bits/mrSearchRequest';
import { ModuleInfoType, UpdateChatGroupType } from '@shared/UpdateIOSModelVersion';
import * as fs from 'fs';
import { create, isNumber } from 'lodash';
import { LoginInfo } from '@pa/shared/dist/src/oauthInfo';
import { GuluXSession } from '@gulux/gulux/session';
import { CodeProcessor } from '../service/codeProcess/codeProcessor';
import {
  createCCiOSUpdateLynxTemplate,
  createLViOSUpdateLynxTemplate,
  getOverseasJwtToken,
  createiOSUpdateAllLynxTemplate,
  createiOSUpdateAllProductLynxTemplate,
  generateAndroidUpdateAllLynxTemplate,
  generatePippitiOSUpdateAllLynxTemplate,
  generatePippitAndroidUpdateAllLynxTemplate,
} from '../service/codeProcess/codeProcessBuilder';
import UpdateLynxDao from 'api/service/dao/UpdateLynxDao';
import { blockConfig } from '@shared/bits/lynxInfo';
import { LanguageApp } from '@shared/multiLanguage/MulitiLanguageConfig';

export const getDiffRepos = Api(
  Post('/update_diff_repos'),
  Data(
    z.object({
      platform: z.string(),
      sourceBranch: z.string(),
      targetBranch: z.string(),
    }),
  ),
  async ({ data }) =>
    await useInject(GitLabService).checkBranchDiffList(
      data.platform === 'android' ? PlatformType.Android : PlatformType.iOS,
      data.targetBranch,
      data.sourceBranch,
    ),
);

export const raiseMr = Api(Post('/update_raise_mr'), Data(mrRaiseOpSchema), async ({ data }) => {
  const bits = useInject(MergeRequestService);
  const loginUser = await useInject(UserService).queryLoginName();
  return await bits.raiseMR(data.sourceBranch, data.targetBranch, loginUser, data.platforms, data.raiseReview);
});

export const searchMrVersionList = Api(Post('/update_get_target_combos'), async () => {
  const res = await useInject(BranchDiffRemain).getMrVersionList();

  return [res, ['develop'].concat(res)];
});

export const updateLynxTemplates = Api(Post('/update_lynx_template'), async () => {
  const { files, body } = useReq();
  const data = JSON.parse(body.data);
  const actualFiles: TemplateFile[] = [];
  if (files?.file) {
    for (const f of Array.isArray(files.file) ? files.file : [files.file]) {
      actualFiles.push({
        filename: f.originalFilename ?? '',
        content: await fs.readFileSync(f.filepath),
      });
    }
  }
  const user = await useInject(GuluXSession).get<LoginInfo>('user');
  useInject(BytedLogger).info(
    `updateLynxTemplates  files:${JSON.stringify(files)} body: ${JSON.stringify(body)} creator:${await useInject(UserService).queryLoginName()} user:${JSON.stringify(user)}`,
  );
  try {
    const res = await useInject(LynxService).updateTemplate(
      data.isOversea,
      data.branch,
      await useInject(UserService).queryLoginName(),
      data.qa,
      data.testRange,
      data.platform,
      data.selectedApp,
      actualFiles,
    );
    if (!isNumber(res) && res.mrUrl) {
      return {
        code: NetworkCode.Success,
        data: `MR地址: ${res.mrUrl}`,
      } as NetworkResult<string>;
    } else {
      return {
        code: NetworkCode.Error,
        data: '更新失败，请确认拥有提交MR权限、下载包地址正确',
      } as NetworkResult<string>;
    }
  } catch (e) {
    if (e instanceof Error) {
      useInject(BytedLogger).error(e);
    }
    return {
      code: NetworkCode.Error,
      data: '更新失败，请确认拥有提交MR权限、下载包地址正确',
    } as NetworkResult<string>;
  }
});

export const autoUpdateLynxTemplates = Api(
  Post('/auto_update_lynx_template'),
  Data(
    z.object({
      product: z.string(),
      platform: z.string(),
      sourceBranch: z.string(),
      targetBranch: z.string(),
      creator: z.string(),
    }),
  ),
  async ({ data }) => {
    const result = await useInject(LynxService).autoUpdateTemplate(data);
    return result;
  },
);

export const createMergeRequestForLynxTemplateUpdate = Api(
  Post('/create_mr_for_update_template'),
  Data(
    z.object({
      product: z.string(),
      platform: z.string(),
      sourceBranch: z.string(),
      targetBranch: z.string(),
      creator: z.string(),
    }),
  ),
  async ({ data }) => {
    const result = await useInject(LynxService).createMergeRequestForLynxTemplateUpdate(data);
    return result;
  },
);

export const updateLynxPackages = Api(Post('/update_lynx_packageids'), async () => {
  const { body } = useReq();
  const data = JSON.parse(body.data);
  const user = await useInject(GuluXSession).get<LoginInfo>('user');
  useInject(BytedLogger).info(
    `updateLynxTemplates body: ${JSON.stringify(body)} creator:${await useInject(UserService).queryLoginName()} user:${JSON.stringify(user)}`,
  );
  try {
    const res: NetworkCode.Error | { mrId: number; mrUrl: string; sourceBranch: string; creator: string } =
      await useInject(LynxService).updatePackage(
        data.isOversea,
        data.branch,
        await useInject(UserService).queryLoginName(),
        data.qa,
        data.testRange,
        data.platform,
        data.selectedApp,
      );
    if (!isNumber(res) && res.mrUrl && res.sourceBranch && res.creator) {
      return {
        code: NetworkCode.Success,
        creator: res.creator,
        data: `MR地址: ${res.mrUrl}`,
        sourceBranch: res.sourceBranch,
      };
    } else {
      return {
        code: NetworkCode.Error,
        data: '更新失败，请确认拥有提交MR权限、下载包地址正确',
      } as NetworkResult<string>;
    }
  } catch (e) {
    return {
      code: NetworkCode.Error,
      data: '更新失败，请确认拥有提交MR权限、下载包地址正确',
    } as NetworkResult<string>;
  }
});

export const createRTSDKUpdateMr = Api(Post('/create_rtsdk_mr'), async () => {
  const { body } = useReq();
  const data = JSON.parse(body.data);
  const logger = useInject(BytedLogger);
  logger.info(`createRTSDKMr: ${JSON.stringify(body)}`);

  const user = await useInject(GuluXSession).get<LoginInfo>('user');
  useInject(BytedLogger).info(
    `updateRTSDKLynx body: ${JSON.stringify(body)} creator:${await useInject(UserService).queryLoginName()} user:${JSON.stringify(user)}`,
  );
  try {
    const res: NetworkCode.Error | { mrId: number; mrUrl: string; sourceBranch: string; creator: string } =
      await useInject(LynxService).createRTSDKMr(data.branch, await useInject(UserService).queryLoginName());
    if (!isNumber(res) && res.mrUrl && res.sourceBranch && res.creator) {
      return {
        code: NetworkCode.Success,
        creator: res.creator,
        data: `MR地址: ${res.mrUrl}`,
        sourceBranch: res.sourceBranch,
      };
    } else {
      return {
        code: NetworkCode.Error,
        data: '更新失败，请确认拥有提交MR权限、下载包地址正确',
      } as NetworkResult<string>;
    }
  } catch (e) {
    return {
      code: NetworkCode.Error,
      data: '更新失败，请确认拥有提交MR权限、下载包地址正确',
    } as NetworkResult<string>;
  }
});

export const triggerJobUpdateLynxTemplate = Api(Post('/trigger_job_lynx_template'), async () => {
  const { body } = useReq();
  const data = JSON.parse(body.data);
  console.log(`triggerJobUpdateLynxTemplate:${data}`);
  let jobParams;
  if (data.isPackageIDModel && (data.product === 'lv' || data.product === 'cc')) {
    if (data.isOversea) {
      jobParams = await createiOSUpdateAllProductLynxTemplate(
        data.sourceBranch,
        'cc',
        data.creator,
        '{}',
        data.branch,
        data.packageIds.join(','),
      );
    } else {
      jobParams = await createiOSUpdateAllProductLynxTemplate(
        data.sourceBranch,
        'lv',
        data.creator,
        '{}',
        data.branch,
        data.packageIds.join(','),
      );
    }
  } else {
    const blackListString = JSON.stringify(data.blackList);
    switch (data.product) {
      case 'lv':
        jobParams = await createiOSUpdateAllProductLynxTemplate(
          data.sourceBranch,
          'lv',
          data.creator,
          blackListString,
          data.branch,
        );
        break;
      case 'cc':
        jobParams = await createiOSUpdateAllProductLynxTemplate(
          data.sourceBranch,
          'cc_row',
          data.creator,
          blackListString,
          data.branch,
        );
        break;
      case 'cc_ttp':
        jobParams = await createiOSUpdateAllProductLynxTemplate(
          data.sourceBranch,
          'cc_ttp',
          data.creator,
          blackListString,
          data.branch,
        );
        break;
      case 'ccus':
        jobParams = await createiOSUpdateAllProductLynxTemplate(
          data.sourceBranch,
          'ccus',
          data.creator,
          blackListString,
          data.branch,
        );
        break;
      case 'jm':
        if (data.isAndroid === PlatformType.Android) {
          jobParams = await generateAndroidUpdateAllLynxTemplate(data.sourceBranch, data.creator, blackListString);
        } else {
          jobParams = await createiOSUpdateAllProductLynxTemplate(
            data.sourceBranch,
            'jm',
            data.creator,
            blackListString,
            data.branch,
          );
        }
        break;
      case 'xt':
        jobParams = await createiOSUpdateAllProductLynxTemplate(
          data.sourceBranch,
          'xt',
          data.creator,
          blackListString,
          data.branch,
        );
        break;
      case 'hp':
        jobParams = await createiOSUpdateAllProductLynxTemplate(
          data.sourceBranch,
          'hp',
          data.creator,
          blackListString,
          data.branch,
        );
        break;
      case 'Pippit':
        if (data.isAndroid === PlatformType.Android) {
          jobParams = await generatePippitAndroidUpdateAllLynxTemplate(
            data.sourceBranch,
            data.creator,
            blackListString,
          );
        } else {
          jobParams = await generatePippitiOSUpdateAllLynxTemplate(
            data.sourceBranch,
            data.creator,
            blackListString,
          );
        }
        break;
      case 'TinyCut':
        jobParams = await createiOSUpdateAllProductLynxTemplate(
          data.sourceBranch,
          'TinyCut',
          data.creator,
          blackListString,
          data.branch,
        );
        break;
      default:
        jobParams = await createiOSUpdateAllLynxTemplate(data.branch, data.sourceBranch, data.creator, data.isOversea);
        break;
    }
  }

  if (!jobParams) {
    return {
      code: NetworkCode.Error,
      data: `job执行失败，无有效参数`,
    } as NetworkResult<string>;
  }

  await useInject(CodeProcessor).triggerJob(jobParams);
  return {
    code: NetworkCode.Success,
    data: `job执行成功`,
  } as NetworkResult<string>;
});

export const queryJWTToken = Api(Post('/query_jwt_token'), async () => {
  const { body } = useReq();
  const jwtToken = await getOverseasJwtToken(body.accessKey);
  return {
    code: NetworkCode.Success,
    data: jwtToken,
  } as NetworkResult<string>;
});

export const queryBlackList = Api(Post('/query_black_list'), async () => {
  const { body } = useReq();
  const data = JSON.parse(body.data);
  const updateLynxDao = useInject(UpdateLynxDao);
  const blackList = await updateLynxDao.queryBlackList(data.product);
  return {
    code: NetworkCode.Success,
    data: blackList,
  } as NetworkResult<blockConfig[]>;
});

export const updateBlackList = Api(Post('/update_black_list'), async () => {
  const { body } = useReq();
  const data = JSON.parse(body.data);
  const updateLynxDao = useInject(UpdateLynxDao);
  await updateLynxDao.updateBlackList(data.product, data.blackList);
  return {
    code: NetworkCode.Success,
    data: '更新成功',
  } as NetworkResult<string>;
});

export const queryUpdateHistory = Api(Post('/query_update_history'), Data(queryHistorySchema), async ({ data }) => {
  let updateInfo: UpdateVersionInfo[];

  const mrModel = useInject(MrModelService);
  const updateVersion = useInject(UpdateVersionService);
  const logger = useInject(BytedLogger);
  logger.info(`${JSON.stringify(data.userSelf)}`);
  if (data.userSelf) {
    const username = await useInject(UserService).queryLoginName();
    updateInfo = await updateVersion.searchUserUpdateInfoList(username);
  } else {
    updateInfo = await updateVersion.searchUpdateInfoList();
  }
  const updateMrs = await mrModel.findMrsByType(MrType.updateVersion);

  for (const updateVersionInfo of updateInfo) {
    if (!updateVersionInfo.mrInfos && updateVersionInfo.mrId && updateVersionInfo.mrUrl) {
      updateVersionInfo.mrInfos = [
        {
          mrId: updateVersionInfo.mrId,
          mrUrl: updateVersionInfo.mrUrl,
        },
      ];
    }

    for (const updateMr of updateMrs) {
      if (updateVersionInfo.mrInfos) {
        for (const mrInfo of updateVersionInfo.mrInfos) {
          if (mrInfo.mrId === updateMr.id) {
            mrInfo.mrInfo = updateMr;
          }
        }
      }
    }
    const mrWrapper = updateVersionInfo.mrInfos?.filter(value => !value.mrInfo);
    if (mrWrapper) {
      for (const mrWrapperElement of mrWrapper) {
        let mrInfo = await mrModel.getMrInfo(mrWrapperElement.mrId);
        if (!mrInfo) {
          mrInfo = await useInject(BitsService).getMrInfo({
            mrId: mrWrapperElement.mrId,
          });
        }
        mrWrapperElement.mrInfo = mrInfo;
        // mrWrapperElement.mrInfo =
      }
    }
  }

  return { code: NetworkCode.Success, message: 'success', data: updateInfo };
});

export const searchLynxVersion = Api(
  Post('/search_lynx_version'),
  Data(z.object({ version: z.string() })),
  async ({ data }) => await useInject(LynxService).searchLynxVersion(data.version),
);

/**
 * 查询云相册SDK版本号
 */
export const searchCloudAlbumVersion = Api(
  Post('/search_cloud_album_version'),
  Data(z.object({ version: z.string() })),
  async ({ data }) => await useInject(VersionUpdate).requestCloudAlbumInfo(data.version),
);

export const searchUpdateInfo = Api(
  Post('/search_update_info'),
  Data(z.object({ branch: z.string() })),
  async ({ data }) => await useInject(VersionUpdate).requestVersionInfo(data.branch),
);

export const searchPippitUpdateInfo = Api(
  Post('/search_update_info/pippit'),
  Data(z.object({ branch: z.string() })),
  async ({ data }) => await useInject(VersionUpdate).requestVersionInfoForPippit(data.branch),
);

export const updateLynxVersionRequest = Api(
  Post('/update_lynx_version'),
  Data(updateLynxVersionSchema),
  async ({ data }) => {
    const userName = await useInject(UserService).queryLoginName();
    await useInject(LynxService).updateLynxVersion(
      data.branch,
      data.lynxBranch,
      data.version,
      userName,
      data.qa,
      data.testRange,
    );
    return {
      code: NetworkCode.Success,
      data: '更新成功',
    } as NetworkResult<string>;
  },
);

export const updateVeAndCodeVersion = Api(
  Post('/update_ve_and_code_version'),
  Data(updateVeRequestSchema),
  async ({ data }) => await useInject(VersionUpdate).versionUpdate(data),
);

export const updatePippitVersionCode = Api(
  Post('/update_pippit_and_code_version'),
  Data(updatePippitRequestSchema),
  async ({ data }) => await useInject(VersionUpdate).pippitVersionUpdate(data),
);

export const updateCloudAlbumVersionRequest = Api(
  Post('/update_cloud_album_version'),
  Data(updateCloudAlbumVersionSchema),
  async ({ data }) => {
    const userName = await useInject(UserService).queryLoginName();
    await useInject(VersionUpdate).cloudAlbumUpdate(
      data.branch,
      data.lvCloundVersion,
      data.ccCloundVersion,
      userName,
      data.deliverTimes,
      data.testRange,
      data.isTestPackageMr,
    );
    return {
      code: NetworkCode.Success,
      data: '更新成功',
    } as NetworkResult<string>;
  },
);

function getProjectId(appId?: number) {
  switch (appId) {
    case LanguageApp.JY:
    case LanguageApp.TC:
      return 30586;
    case LanguageApp.CapCut:
    case LanguageApp.CapCut_TTP:
      return 798;
    default:
      return 798;
  }
}

export const updateBranch = Api(Post('/update_branch_content'), Data(upadteBranchContentSchema), async ({ data }) => {
  console.log(data);

  try {
    const user = useInject(UserService);
    const lark = useInject(LarkService);
    const userEmail = await user.queryLoginEmail();
    const userName = await user.queryLoginName();
    const userId = await lark.getUserIdByEmail(userEmail);
    const qaEmail = data.qa ? await lark.getUserEmail(data.qa?.open_id) : undefined;
    const qaName = qaEmail ? qaEmail.substring(0, qaEmail.indexOf('@')) : undefined;
    const { appId } = data;
    data.larkData.senderId = userId?.open_id;
    const syncTranslation = useInject(SyncTranslation);
    if (data.isBranch && !data.isTask && data.lvBranch) {
      // 全量更新分支
      return await syncTranslation.update(
        data.lvBranch,
        data.larkData,
        data.languageBranch,
        undefined,
        userName,
        qaName,
        appId,
      );
    } else if (!data.isBranch && !data.isTask && data.MrId) {
      // 全量更新MR
      return await syncTranslation.updateForMr(data.MrId.toString(), data.larkData, undefined, userName, appId);
    } else if (data.isTask && data.isBranch && data.Task && data.lvBranch) {
      // 任务更新分支
      return await syncTranslation.update(
        data.lvBranch,
        data.larkData,
        data.languageBranch,
        {
          taskId: data.Task,
          taskSpace: data.taskSpace,
          projectId: getProjectId(appId),
        },
        userName,
        qaName,
        appId,
      );
    } else if (data.isTask && !data.isBranch && data.MrId && data.Task) {
      // 任务更新mr
      return await syncTranslation.updateForMr(
        data.MrId.toString(),
        data.larkData,
        { taskId: data.Task, taskSpace: data.taskSpace, projectId: getProjectId(appId) },
        userName,
        appId,
      );
    } else {
      return {
        code: NetworkCode.Error,
        message: '请确认输入完整信息',
      } as NetworkResult<string>;
    }
  } catch (e) {
    const logger = useInject(BytedLogger);
    logger.info(`updateBranch happen error!`);
    if (e instanceof Error) {
      logger.error(e);
    }
    return {
      code: NetworkCode.Error,
      message: '更新失败，请确认拥有提交MR权限、下载包地址正确、文案安全性',
    } as NetworkResult<string>;
  }
});

export const searchIosModuleVersionInfo = Api(
  Post('/open/update/search/iOS/module/version'),
  Data(z.object({ branch: z.string() })),
  async ({ data }) => {
    const iosModuleUpdate = useInject(IosModuleUpdate);
    const result = await iosModuleUpdate.queryModelVersionInfo(data.branch);
    return result;
  },
);

export const searchMrInfoList = Api(
  Post('/open/update/search/mrinfo'),
  Data(z.object({ keyword: z.string(), branch: z.string().optional(), projectId: z.number() })),
  async ({ data }) => {
    const bits = useInject(BitsService);
    const mrSearchArgs: MrSearchRequest = {
      group_name: 'LV-iOS',
      target_project_id: data.projectId,
      state: 'opened',
      conflicted: -1,
      keyword: data.keyword,
      source: 'all',
    };
    if (data.branch) {
      mrSearchArgs.target_branch = data.branch;
    }

    const mrIdList = await bits.searchAllMr(mrSearchArgs);
    const mrInfos = await bits.fetchAllMrInfo(mrIdList);
    return mrInfos;
  },
);

export const updateIosVersion = Api(
  Post('/update/submit'),
  Data(
    z.object({
      branch: z.string(),
      group: z.nativeEnum(UpdateChatGroupType),
      versionList: z.array(
        z.object({
          name: z.string(),
          version: z.string(),
          type: z.nativeEnum(ModuleInfoType),
        }),
      ),
    }),
  ),
  async ({ data }) => {
    const iosModuleUpdate = useInject(IosModuleUpdate);
    const creator = await useInject(UserService).queryLoginName();
    const result = await iosModuleUpdate.updateVersion(creator, data.branch, data.group, data.versionList);
    console.log(JSON.stringify(data));
    if (result) {
      return { code: NetworkCode.Success, message: 'success', data: result } as NetworkResult<{
        mr_id: number;
        mr_url: string;
      }>;
    } else {
      return { code: NetworkCode.Error, message: 'success' } as NetworkResult<{
        mr_id: number;
        mr_url: string;
      }>;
    }
  },
);

export const searchNewMrBranch = Api(
  Post('/open/update/branch'),
  Data(z.object({ branch: z.string(), group: z.nativeEnum(UpdateChatGroupType) })),
  async ({ data }) => {
    const iosModuleUpdate = useInject(IosModuleUpdate);
    const newBranch = iosModuleUpdate.createSourceBranch(data.branch, data.group);
    return { newBranch };
  },
);

export const closeUpdateMr = Api(
  Post('/update/closeUpdateMr'),
  Data(
    z.object({
      mrId: z.number(),
      mrAuthor: z.string(),
    }),
  ),
  async ({ data }) => {
    const bits = useInject(BitsService);
    const creator = await useInject(UserService).queryLoginName();
    if (creator !== data.mrAuthor) {
      return { code: NetworkCode.Error, message: '你不是MR发起人，无法关闭' };
    }
    return bits.closeMr(data.mrId, 'LV-Android');
  },
);

export const updateAndroidVEVersionOpen = Api(
  Post('/open/android/update_ve_version'),
  Data(updateVeRequestSchema),
  async ({ data }) => await useInject(VersionUpdate).versionUpdate(data),
);

export const updateIOSVEVersionOpen = Api(
  Post('/open/ios/update_ve_version'),
  Data(updateIOSVeRequestSchema),
  async ({ data }) => {
    useInject(BytedLogger).info(`updateIOSVEVersionOpen  data:${JSON.stringify(data)}`);
    const branch = data.lvBranch;
    const group = data.autoReview ? UpdateChatGroupType.ve_test : UpdateChatGroupType.ve_online;
    const creator = data.creator ? data.creator : 'liuyanqing.ranger';
    const iosModuleUpdate = useInject(IosModuleUpdate);
    const result = await iosModuleUpdate.updateVersion(creator, branch, group, data.versionList, true);
    console.log(JSON.stringify(data));
    if (result) {
      return {
        code: NetworkCode.Success,
        message: 'success',
        data: {
          mrUrl: result.mr_url,
          mrId: result.mr_id,
          author: creator,
        },
      } as NetworkResult<UpdateVersionInfo>;
    } else {
      return { code: NetworkCode.Error, message: 'success' } as NetworkResult<UpdateVersionInfo>;
    }
  },
);

const IOSVersionUpdateData = z.object({
  branch: z.string(),
  group: z.nativeEnum(UpdateChatGroupType),
  creator: z.string(),
  versionList: z.array(
    z.object({
      name: z.string(),
      version: z.string(),
      type: z.nativeEnum(ModuleInfoType),
    }),
  ),
});

// deprecated
export const updateIosVersionOpen = Api(Post('/open/update/submit'), Data(IOSVersionUpdateData), async ({ data }) => {
  const iosModuleUpdate = useInject(IosModuleUpdate);
  const result = await iosModuleUpdate.updateVersion(data.creator, data.branch, data.group, data.versionList, true);
  console.log(JSON.stringify(data));
  if (result) {
    return { code: NetworkCode.Success, message: 'success', data: result } as NetworkResult<{
      mr_id: number;
      mr_url: string;
    }>;
  } else {
    return { code: NetworkCode.Error, message: 'success' } as NetworkResult<{
      mr_id: number;
      mr_url: string;
    }>;
  }
});
