import React, { useState } from 'react';
import { LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { Button, Modal, Space, Toast, Typography } from '@douyinfe/semi-ui';
import { IconAlertTriangle } from '@douyinfe/semi-icons';
import { libraDetailUrl } from '@shared/libra/libraManageUtils';
import { updateLibraFlightInfo } from '@api/libra';

const { Text } = Typography;

const FlightRefreshFlightInfoButton: React.FC<{
  libraInfo: LibraNewInfo;
  onLibraInfoUpdate: (newLibraInfo: LibraNewInfo) => void;
}> = ({ libraInfo, onLibraInfoUpdate }) => {
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const onClickRefresh = async () => {
    setConfirmModalVisible(true);
  };

  // 确认更新 Flight Info
  const handleConfirmOK = async () => {
    setConfirmLoading(true);
    try {
      // 开始请求网络，拉取最新的实验信息
      const result = await updateLibraFlightInfo({
        data: {
          flightId: libraInfo.flightInfo.id,
        },
      });
      if (result.code !== 0) {
        throw new Error(`${result.msg}`);
      }

      // 完成实验信息更新，再次刷新 UI，表示刷新完毕
      Toast.success('更新实验信息成功！');

      // 对外部触发更新回调
      onLibraInfoUpdate(result.data as LibraNewInfo);

      setConfirmLoading(false);
      setConfirmModalVisible(false);
    } catch (error) {
      // 发生错误，兼容处理，恢复成原始状态
      setConfirmLoading(false);
      setConfirmModalVisible(false);
      Toast.error(`更新实验信息失败，error: ${error}`);
    }
  };

  // 取消更新 Flight Info
  const handleConfirmCancel = () => {
    setConfirmModalVisible(false);
  };

  const customModalFooter = (
    <div style={{ display: 'flex', gap: '2px', justifyContent: 'flex-end' }}>
      <Button type="primary" onClick={handleConfirmCancel}>
        取消
      </Button>
      <Button type="primary" theme={'solid'} loading={confirmLoading} onClick={handleConfirmOK}>
        确定
      </Button>
    </div>
  );

  return (
    <>
      <Button theme="borderless" style={{ color: 'rgba(var(--semi-grey-5), 1)' }} onClick={onClickRefresh}>
        更新实验信息
      </Button>
      <Modal
        icon={<IconAlertTriangle style={{ color: 'orange', fontSize: 24 }} />}
        title={'确认是否更新实验信息？'}
        visible={confirmModalVisible}
        closeOnEsc={false}
        footer={customModalFooter}
        onCancel={handleConfirmCancel}
      >
        <>
          <Space vertical={false} align="start">
            <Text strong style={{ whiteSpace: 'nowrap' }}>
              实验名称:
            </Text>
            <Text
              strong
              link={{ href: libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id), target: '_blank' }}
            >
              {libraInfo.flightInfo.name}
            </Text>
          </Space>
          <br />
          <Space vertical={false} align="start">
            <Text>即将更新实验信息(from Libra)，过程耗时约 5s。</Text>
          </Space>
        </>
      </Modal>
    </>
  );
};

export default FlightRefreshFlightInfoButton;
