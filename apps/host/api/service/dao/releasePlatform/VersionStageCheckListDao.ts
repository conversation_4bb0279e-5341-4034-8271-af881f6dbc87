import { Inject, Injectable } from '@gulux/gulux';
import { ModelType } from '@gulux/gulux/typegoose';
import { VersionStageCheckListTable } from '../../../model/releasePlatform/VersionStageCheckListModel';
import {
  CheckItemStatus,
  VersionStageCheckItem,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { BytedLogger } from '@gulux/gulux/byted-logger';

export interface VersionStageCheckListItemQuery {
  app_id: number;
  version: string;
  stage: string;
}
@Injectable()
export default class VersionStageCheckListDao {
  @Inject(VersionStageCheckListTable)
  private versionStageCheckListModel: ModelType<VersionStageCheckListTable>;

  @Inject(BytedLogger)
  private logger: BytedLogger;

  async find(query: VersionStageCheckListItemQuery): Promise<VersionStageCheckList | null> {
    return this.versionStageCheckListModel.findOne(query);
  }
  // 保存一个VersionStageCheckList对象
  async save(versionStageCheckList: VersionStageCheckList) {
    const existed = await this.find({
      version: versionStageCheckList.version,
      stage: versionStageCheckList.stage,
      app_id: versionStageCheckList.app_id,
    });
    if (existed) {
      return undefined;
    }
    return await this.versionStageCheckListModel.create(versionStageCheckList);
  }

  // 更新VersionStageCheckList对象
  async update(query: VersionStageCheckListItemQuery, updateData: Partial<VersionStageCheckList>) {
    const result = await this.versionStageCheckListModel.updateOne(query, updateData);
    this.logger.info(`[VersionStageCheckListDao] update: ${JSON.stringify(query)} ,${JSON.stringify(updateData)}`);
    return result;
  }

  async delete(query: VersionStageCheckListItemQuery) {
    await this.versionStageCheckListModel.deleteMany(query);
  }

  // 更新具体的check item
  async updateCheckItem(
    query: VersionStageCheckListItemQuery,
    checkItemId: string,
    updateData: Partial<VersionStageCheckItem>,
  ): Promise<void> {
    await this.versionStageCheckListModel.updateOne(
      { ...query, 'check_items.check_item_id': checkItemId },
      { $set: { 'check_items.$': updateData } },
    );
  }

  async findBlockedOrExemptItems(
    appId: number,
    version: string,
    stage: string,
    status: CheckItemStatus,
  ): Promise<VersionStageCheckItem[]> {
    const checkList = await this.versionStageCheckListModel.findOne({ app_id: appId, version, stage }).exec();

    if (!checkList) {
      throw new Error('CheckList not found');
    }

    // 筛选出 status 为 Blocked 或 Exempt 的项
    const items = checkList.check_items.filter(item => item.status === status);

    return items;
  }

  // 添加一个新的check item
  async addCheckItem(query: VersionStageCheckListItemQuery, checkItem: VersionStageCheckItem): Promise<void> {
    await this.versionStageCheckListModel.updateOne(query, { $push: { check_items: checkItem } });
  }

  // 删除一个check item
  async removeCheckItem(query: VersionStageCheckListItemQuery, checkItemId: string): Promise<void> {
    await this.versionStageCheckListModel.updateOne(query, { $pull: { check_items: { _id: checkItemId } } });
  }

  async getVersionStageCheckList(version: string, stage: string, appId: number): Promise<VersionStageCheckList | null> {
    return this.versionStageCheckListModel.findOne({ version, stage, app_id: appId });
  }

  // 更新准出状态
  async updateVersionStageCheckList(
    version: string,
    stage: string,
    appId: number,
    checkItem: VersionStageCheckItem,
  ): Promise<void> {
    await this.versionStageCheckListModel.updateOne(
      { version, stage, app_id: appId },
      { $push: { check_items: checkItem } },
    );
  }

  // 发起审批
  async startVersionApproval(version: string, stage: string, appId: number, type: number): Promise<void> {
    // saveApprovalRecord(...) // Save approval record to the database
    // callRealApprovalEntrance(...) // Call the real approval entrance
  }

  // 检查是否需要进入异常流程
  async checkVersionStageDelay(version: string, stage: string, appId: number): Promise<void> {
    // Check the delay conditions and handle the logic
    // This is application-specific logic that should handle delay checks
    // and move the version to the abnormal process if necessary
  }
}
