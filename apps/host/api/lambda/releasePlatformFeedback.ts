import { Api, Data, Post, useInject } from '@edenx/runtime/bff';
import { number, z } from 'zod';
import { Feedback, FeedbackCallbackInfo, FeedbackMeegoInfo } from '@shared/releasePlatform/feedbackFollwUp';
import { FeedbackService } from '../service/releasePlatform/FeedbackService';
import { ExperienceFeedbackAnalysisService } from '@gulux-bam/ies_efficiency_experience_feedback_analysis';
import { TagLabelToFeedback, CemAnalysisService } from '@gulux-bam/ies_efficiency_cem_analysis';
import { experience_external_namespace } from '@gulux-bam/ies_efficiency_experience_feedback_analysis/lib/typings/apps/experience_feedback_analysis/idl/experience_external';
import { CheckItemStatus } from '@shared/releasePlatform/versionStageInfoCheckList';
import { FeedbackMetricsService } from '../service/releasePlatform/FeedbackMetricsService';
import FeedbackNotificationService from '../service/releasePlatform/FeedbackNotificationService';
import { fetchVersionInfoByCriteria } from '@api/releasePlatform';
import MeegoService from '../service/third/meego';
import { WorkItemInfo } from '@shared/meego/WorkItemResult';
import XGetFeedbackInfoWithFiltersReqOptions = experience_external_namespace.XGetFeedbackInfoWithFiltersReqOptions;

export const getAllFeedbacks = Api(
  Post('/release_platform/feedbacks/all'),
  Data(
    z.object({
      appId: z.number().optional(),
      version: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(FeedbackService);
    return service.getAllFeedbacks({
      ...(data.appId && { appId: data.appId }),
      ...(data.version && { version: data.version }),
    });
  },
);

export const analyzeFeedbackLabels = Api(
  Post('/release_platform/feedbacks/analyze_labels'),
  Data(
    z.object({
      appId: z.number().optional(),
      version: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    try {
      const service = useInject(FeedbackService);
      return await service.analyzeTopFeedbackLabels();
    } catch (error) {
      console.error('分析反馈标签时出错:', error);
      throw error;
    }
  },
);

// export const processFeedback = Api(
//   Post('/release_platform/feedback/process'),
//   Data(
//     z.object({
//       callbackInfo: z.custom<FeedbackCallbackInfo>(),
//       appId: z.number(),
//       version: z.string(),
//       versionInfo: z.any(),
//     }),
//   ),
//   async ({ data }) => {
//     if (!data.callbackInfo) {
//       return;
//     }
//     const service = useInject(FeedbackService);
//     // console.log('1111111111111', data.callbackInfo);
//     return await service.processFeedbackCallback(data.callbackInfo, data.versionInfo);
//   },
// );

export const batchProcessFeedback = Api(
  Post('/release_platform/feedback/batch_process'),
  Data(
    z.object({
      callbackInfo: z.custom<FeedbackCallbackInfo[]>(),
      appId: z.number(),
      version: z.string(),
      versionInfo: z.any(),
    }),
  ),
  async ({ data }) => {
    if (!data.callbackInfo) {
      return;
    }
    const service = useInject(FeedbackService);
    return await service.batchProcessFeedbackCallback(data.callbackInfo, data.versionInfo);
  },
);

// 更新批量更新所有反馈的API
export const updateAllFeedbacks = Api(
  Post('/release_platform/feedbacks/update_all'),
  Data(
    z.object({
      feedbacks: z.array(z.any()), // 接收反馈数据数组
      appId: z.number().optional(),
      version: z.string().optional(),
      userEmail: z.string().optional(), // 可选的用户标识，用于记录谁进行了操作
    }),
  ),
  async ({ data }) => {
    if (!data.feedbacks || !Array.isArray(data.feedbacks) || data.feedbacks.length === 0) {
      return { success: false, message: '没有提供要更新的数据' };
    }

    try {
      const service = useInject(FeedbackService);
      const result = await service.updateAllFeedbacks(data.feedbacks, data.userEmail);

      return {
        success: result,
        message: result ? '批量更新成功' : '批量更新失败',
        count: data.feedbacks.length,
      };
    } catch (error) {
      console.error('批量更新反馈API出错:', error);
      return {
        success: false,
        message: '批量更新处理过程中出错',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
);

export const trackFeedbackStatusChange = Api(
  Post('/release_platform/feedback/track_status_change'),
  Data(
    z.object({
      oldFeedback: z.custom<Feedback>().optional(),
      newFeedback: z.custom<Feedback>(),
    }),
  ),
  async ({ data }) => {
    const metricsService = useInject(FeedbackMetricsService);

    try {
      await metricsService.processFeedbackStatusChange(data.newFeedback, data.oldFeedback);

      return {
        success: true,
        message: 'Feedback status change tracked successfully',
      };
    } catch (error) {
      console.error('Error tracking feedback status change:', error);
      return {
        success: false,
        message: 'Failed to track feedback status change',
      };
    }
  },
);

export const getCurrentFeedbacks = Api(
  Post('/release_platform/feedbacks/get_current'),
  Data(
    z.object({
      method: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const rpc = useInject(ExperienceFeedbackAnalysisService);
    const rpcRequestBody = {
      filters: [
        {
          type: 1,
          filter: [
            {
              key: 'app_version',
              op: 1,
              value: ['15.5.0'],
            },
            {
              key: 'aid',
              op: 1,
              value: ['1775'],
            },
          ],
        },
      ],
      pagination: {
        page: 1,
        pageSize: 10,
      },
      period: {
        datetime: {
          startMs: (new Date().getTime() - 3600 * 1000 * 24) as unknown as string,
          endMs: new Date().getTime() as unknown as string,
        },
        interval: '',
      },
      sortList: [
        {
          orderBy: '_id',
          orderType: 'desc',
        },
      ],
      Base: {
        LogID: '',
        Caller: '',
        Addr: '',
        Client: '',
        TrafficEnv: {
          Open: false,
          Env: '',
        },
        Extra: {
          '': '',
          env: 'ppe_cem_2',
        },
      },
    };
    const res = await rpc.XGetFeedbackInfoWithFilters(rpcRequestBody as XGetFeedbackInfoWithFiltersReqOptions);
    return res;
  },
);

export const feedbacksTest = Api(Post('/release_platform/feedbacks/test'), async () => {
  const feedbackService = useInject(FeedbackService);
  const onProgressInfos = await feedbackService.getOnProgressTestFlightInfo();
  const allFeedbacks = [];
  for (const info of onProgressInfos) {
    for (const version of info.versions) {
      const result = await feedbackService.getAllFeedbacks({ appId: info.app_id, version: version.version });
      const statics = await feedbackService.getFeedbackHandleInfo(info.app_id, version.version, result);
      const { total, notHandled } = statics;
      if (total > 0) {
        allFeedbacks.push({ app_id: info.app_id, version, total, notHandled });
      }
      const feedbackNotificationService = useInject(FeedbackNotificationService);
      await feedbackNotificationService.sendFeedbackNotificationCard(statics, version);
    }
  }
  return { info: allFeedbacks, onProgressInfos };
});

export const feedbacksTestFIndPcPoblem = Api(Post('/release_platform/feedbacks/test_pc'), async () => {
  const feedbackService = useInject(FeedbackService);
  const onProgressInfos = await feedbackService.getOnProgressTestFlightInfo();
  const allFeedbacks = [];
  for (const info of onProgressInfos) {
    for (const version of info.versions) {
      const result = await feedbackService.getAllFeedbacks({ appId: info.app_id, version: version.version });
      const statics = await feedbackService.getFeedbackHandleInfo(info.app_id, version.version, result);
      if (info.app_id !== 177501 && info.app_id !== 177502) {
        allFeedbacks.push({ app_id: info.app_id, version: version.version, statics });
      }
    }
  }
  return allFeedbacks;
});

export const feedbacksTest2 = Api(Post('/release_platform/feedbacks/test2'), async () => {
  const feedbackService = useInject(FeedbackService);
  const onProgressInfos = await feedbackService.getOnProgressTestFlightInfo();
  const result = [];
  for (const info of onProgressInfos) {
    for (const version of info.versions) {
      const res = await feedbackService.getFeedbackDataOfVoc(info.app_id, version.version);
      if (res?.feedbacks) {
        const convertedFeedbacks = res?.feedbacks.map(feedback =>
          feedbackService.convertFeedbackInfoToCallback(feedback),
        );
        const processedFeedbacks = await feedbackService.batchProcessFeedbackCallback(convertedFeedbacks, version);
        result.push({
          appId: info.app_id,
          version: version.version,
          feedbacks: processedFeedbacks || [],
        });
      }
    }
  }
  return result;
});

export const feedbacksTest3 = Api(Post('/release_platform/feedbacks/test3'), async () => {
  const feedbackService = useInject(FeedbackService);
  const res = await feedbackService.getFeedbackDataOfVoc(1775, '15.5.0');
  return res?.feedbacks;
});

export const getMeegoInfo = Api(
  Post('/release_platform/feedbacks/get_meego_info'),
  Data(
    z.object({
      meegoUrl: z.array(z.string()),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MeegoService);
    const meegoIds: number[] = [];
    for (const url of data.meegoUrl) {
      const pattern = /detail\/(\d+)/;
      const match = url.match(pattern);
      const meegoId = match ? match[1] : 0;
      meegoIds.push(Number(meegoId));
    }
    const res = await meegoService.requestWorkItem('faceu', 'issue', meegoIds);
    const dataArray = res.data;
    const meegoRes: FeedbackMeegoInfo[] = [];
    for (const item of dataArray) {
      meegoRes.push({
        meegoUrl: `https://meego.larkoffice.com/faceu/issue/detail/${item.id}`,
        meegoId: String(item.id),
        meegoName: item.name,
      });
    }
    return meegoRes;
  },
);

// 删除所有反馈数据的接口
export const deleteAllFeedbacks = Api(
  Post('/release_platform/feedbacks/delete_all'),
  Data(
    z.object({
      appId: z.number().optional(),
      version: z.string().optional(),
      confirmed: z.boolean().default(false), // 确认操作标识，防止误操作
    }),
  ),
  async ({ data }) => {
    if (!data.confirmed) {
      return {
        success: false,
        message: '操作未确认，请设置confirmed=true确认此危险操作',
      };
    }

    try {
      const service = useInject(FeedbackService);
      const filters: Partial<Feedback> = {};

      // 根据参数构建过滤条件
      if (data.appId) {
        filters.appId = data.appId;
      }

      if (data.version) {
        filters.version = data.version;
      }

      const result = await service.deleteAllFeedbacks(filters);

      return {
        success: true,
        message: `成功删除反馈数据`,
        deletedCount: result.deletedCount,
      };
    } catch (error) {
      console.error('删除反馈数据API出错:', error);
      return {
        success: false,
        message: '删除反馈数据过程中出错',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
);

export const getMeegoWorkItemInfo = Api(
  Post('/release_platform/feedbacks/get_meego_work_item_info'),
  Data(
    z.object({
      meegoIds: z.array(z.number()),
    }),
  ),
  async ({ data }) => {
    const meegoService = useInject(MeegoService);
    const res = await meegoService.requestWorkItem('faceu', 'issue', data.meegoIds);
    return res;
  },
);

// export const getUserFeedbackBug = Api(
//   Post('/release_platform/feedbacks/get_user_feedback_bug'),
//   Data(
//     z.object({
//       feedbackIds: z.array(z.string()).optional(),
//     }),
//   ),
//   async ({ data }) => {
//     const meegoService = useInject(MeegoService);
//     const res = await meegoService.queryUserFeedbackBug();
//     return res;
//   },
// );

export const cronjobUpdateCheckItemStatus = Api(
  Post('/release_platform/feedbacks/cronjob_update_check_item_status'),
  Data(
    z.object({
      feedbackIds: z.array(z.string()).optional(),
    }),
  ),
  async ({ data }) => {
    const feedbackService = useInject(FeedbackService);
    const onProgressInfos = await feedbackService.getOnProgressTestFlightInfo();
    const res = [];
    for (const info of onProgressInfos) {
      for (const version of info.versions) {
        const result = await feedbackService.getAllFeedbacks({ appId: info.app_id, version: version.version });
        const statics = await feedbackService.getFeedbackHandleInfo(info.app_id, version.version, result);
        const { total, notHandled } = statics;
        const userStoryStage = version.version_stages.find(stage => stage.display_name === '用户故事');
        let userStoryStartTime;
        if (userStoryStage) {
          userStoryStartTime = userStoryStage.real_start_time;
        }

        const highPriorityMeegoItemInfo = await feedbackService.getHighPriorityMeegoItemInfo(
          info.app_id,
          version.version,
        );

        if (total > 0) {
          res.push({
            app_id: info.app_id,
            // version,
            total,
            notHandled,
            userStoryStartTime,
            highPriorityMeegoItemInfo,
            // versionId,
            // meegoWorkItemInfo,
            // p0Items: p0Stats,
            // p1Items: p1Stats,
          });
        }
      }
    }
    return res;
  },
);

export const getVersionBugResolveInfo = Api(
  Post('/release_platform/feedbacks/get_version_bug_resolve_info'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const feedbackService = useInject(FeedbackService);
    const bugResolveInfo = await feedbackService.getHighPriorityMeegoItemInfo(data.appId, data.version);
    // 达标了更新一下
    return bugResolveInfo;
  },
);

export const getFeedbackProcessInfo = Api(
  Post('/release_platform/feedbacks/get_feedback_process_info'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      endTimestamp: z.number(),
    }),
  ),
  async ({ data }) => {
    const feedbackService = useInject(FeedbackService);
    const result = await feedbackService.getAllFeedbacks({
      appId: data.appId,
      version: data.version,
      'feedbackCallbackInfo.eredarCreateTimestampMs': { $lte: data.endTimestamp },
      'feedbackCallbackInfo.labelType': 'BUG',
    });
    const validFeedback = result.filter(feedback => {
      const labelNames = feedback.feedbackCallbackInfo.labelNames || [];
      const len = labelNames.length;
      if (len > 0) {
        return labelNames[len - 1] !== '无效反馈';
      }
    });
    return await feedbackService.getFeedbackHandleInfo(data.appId, data.version, validFeedback);
  },
);

export const getRecentFeedbackRising = Api(
  Post('/release_platform/feedbacks/get_recent_feedback_info'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      endTimestamp: z.number(),
    }),
  ),
  async ({ data }) => {
    const feedbackService = useInject(FeedbackService);
    const recentFeedbacks = await feedbackService.getAllFeedbacks({
      appId: data.appId,
      version: data.version,
      'feedbackCallbackInfo.eredarCreateTimestampMs': { $lte: data.endTimestamp },
      'feedbackCallbackInfo.labelType': 'BUG',
    });
    // 版本反馈全看，无需考虑开始时间
    // const threeDaysAgo = new Date();
    // threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    // const threeDaysAgoTimestamp = threeDaysAgo.getTime();
    // const recentFeedbacks = result.filter(feedback => {
    //   const feedbackTime = feedback.feedbackCallbackInfo.eredarCreateTimestampMs;
    //   return feedbackTime && feedbackTime >= threeDaysAgoTimestamp;
    // });
    // 统计各级标签的出现次数
    const level2LabelCounter = new Map<string, number>();
    const level3LabelCounter = new Map<string, number>();
    recentFeedbacks.forEach(feedback => {
      const status = feedbackService.getHandleInfo(feedback.followUpStatus);
      if (status.isHandled) {
        return;
      }
      const labelNames = feedback.feedbackCallbackInfo.labelNames || [];
      const level2Label = labelNames[0] + '--' + labelNames[1];
      const level3Label = level2Label + '--' + labelNames[2];
      if (level2Label) {
        level2LabelCounter.set(level2Label, (level2LabelCounter.get(level2Label) || 0) + 1);
      }
      if (level3Label && level3Label !== '无效反馈') {
        level3LabelCounter.set(level3Label, (level3LabelCounter.get(level3Label) || 0) + 1);
      }
    });
    let areRising = false;
    const exceedingLabels = {
      level2: [] as Array<{ label: string; count: number }>,
      level3: [] as Array<{ label: string; count: number }>,
    };
    for (const [label, count] of level2LabelCounter.entries()) {
      if (count > 3) {
        areRising = true;
        exceedingLabels.level2.push({ label, count });
      }
    }
    for (const [label, count] of level3LabelCounter.entries()) {
      if (count > 3) {
        areRising = true;
        exceedingLabels.level3.push({ label, count });
      }
    }

    return {
      areRising,
      allLabels: {
        level2: exceedingLabels.level2,
        level3: exceedingLabels.level3,
      },
    };
  },
);

export const updateSpecificFeedbackItemStatus = Api(
  Post('/release_platform/feedbacks/update_specific_feedback_item_status'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      status: z.number(),
      metric: z.string(),
      stage: z.string(),
    }),
  ),
  async ({ data }) => {
    const feedbackService = useInject(FeedbackService);
    const itemInfo = await feedbackService.updateSpecificFeedbackCheckItemStatus(
      data.appId,
      data.version,
      data.status as CheckItemStatus,
      data.metric,
      data.stage,
    );
    // 达标了更新一下
    return itemInfo;
  },
);

export const updateFeedbackFollowStatusInitToVoc = Api(
  Post('/release_platform/feedbacks/update_feedback_follow_status_init_to_voc'),
  Data(
    z.object({
      appId: z.number().optional(),
    }),
  ),
  async ({ data }) => {
    const feedbackService = useInject(FeedbackService);
    const onProgressInfos = await feedbackService.getOnProgressTestFlightInfo();
    const res = [];
    for (const info of onProgressInfos) {
      for (const version of info.versions) {
        const result = await feedbackService.getAllFeedbacks({ appId: info.app_id, version: version.version });
        for (const feedback of result) {
          try {
            if (feedback.followUpStatus === '未处理') {
              const { feedbackCallbackInfo } = feedback;
              // const r = feedbackCallbackInfo.feedbackID;
              const r = await feedbackService.initializeFeedbackStatus(feedbackCallbackInfo.feedbackID);
              res.push(r, feedbackCallbackInfo.feedbackID);
            }
          } catch (e) {
            return {
              success: false,
              message: '更新反馈状态失败',
              error: e instanceof Error ? e.message : String(e),
            };
          }
        }
      }
    }
    return res;
  },
);
