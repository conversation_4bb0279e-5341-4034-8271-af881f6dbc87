import { Inject, Injectable } from '@gulux/gulux';
import BitsService from '../third/bits';
import MeegoService from '../third/meego';
import LarkService from '@pa/backend/dist/src/third/lark';
import { TccClients } from '@gulux/gulux/tcc';
import { ReleasePlatformUtilService } from './releasePlatformUtil';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import BusInfoDao from '../dao/releasePlatform/BusInfoDao';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import {
  ReleaseVersionType,
  VersionProcessInfo,
  VersionProcessStatus,
  VersionStageInfo,
  VersionStageStatus,
  VersionTimelineConfig,
} from '@shared/releasePlatform/versionStage';
import {
  BuildMasterInfo,
  DreaminaProductType,
  LVProductType,
  PCInfo,
  PippitProductType,
  ProductType,
  RetouchProductType,
  TinycutProductType,
} from '@shared/process/versionProcess';
import { Temporal } from '@js-temporal/polyfill';
import commonUtils from '../../utils/commonUtils';
import { DataItem, EventsItem } from '@shared/bits/calendar';
import { head } from 'lodash';
import versionUtils from '../../utils/versionUtils';
import { Version, VersionStatus } from '@shared/versionBot/version';
import { PlatformType } from '@pa/shared/dist/src/core';
import { BmType } from '@shared/bits/bmInfo';
import VersionModelService from '../model/versionModel';
import timeUtil from '../../utils/timeUtil';
import StageServiceFactory from './stageServiceFactory';
import { VersionStageCheckListService } from './versionStageCheckListService';
import { AppSettingId, BusinessAppInfo, BusinessType } from '@pa/shared/dist/src/appSettings/appSettings';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import dayjs from 'dayjs';
import { VersionMeegoProductDao } from '../dao/VersionMeegoProductDao';
import MeegoOldService from '../meegoOld';
import { IsFullReleased } from '@shared/releasePlatform/releasePlatformUtils';
import { LibraControlSetting } from '@pa/shared/dist/src/libra/LibraAttributionModel';
import VersionProcessDao from '../dao/VersionProcessDao';

@Injectable()
export class VersionProcessInfoService {
  @Inject()
  private bits: BitsService;
  @Inject()
  private meego: MeegoService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private tcc: TccClients;
  @Inject()
  private releasePlatformUtil: ReleasePlatformUtilService;
  @Inject()
  private businessConfigService: BusinessConfigService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private busInfoDao: BusInfoDao;
  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;
  @Inject()
  private versionModel: VersionModelService;
  @Inject()
  private stageServiceFactory: StageServiceFactory;
  @Inject()
  private checklistService: VersionStageCheckListService;
  @Inject()
  private versionMeegoProductDao: VersionMeegoProductDao;
  @Inject()
  private meegoOldService: MeegoOldService;
  @Inject()
  private versionProcess: VersionProcessDao;

  async createNewVersionProcessInfo() {
    const appInfos: BusinessAppInfo[] = await this.businessConfigService.getAppList();
    // const onWeek = 7 * 24 * 60 * 60;
    const currentSeconds = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    for (const appInfo of appInfos) {
      const timeline: VersionTimelineConfig | null = await this.businessConfigService.getVersionTimelineConfig(
        appInfo.app_id,
      );
      if (!timeline || timeline.version_stages.length <= 0) {
        this.logger.error(`No timeline config for app info : ${appInfo}`);
        continue;
      }
      const segment = timeline.version_stages[0];
      const nextEvent: DataItem[] = await this.bits.calendarWorkSpaceEventNext(
        segment.bits_calendar_segment,
        currentSeconds,
        appInfo.bits_workspace,
      );
      let new_version = head(nextEvent)?.name ?? '';
      new_version = versionUtils.calSpecificVersion(appInfo.bits_workspace, appInfo.product_type, new_version);
      await this.createNewVersionV2(appInfo, new_version);

      if (new_version.length <= 0) {
        continue;
      }
    }
  }

  async updateBMVersionInfo(appInfo: BusinessAppInfo, version: string) {
    if (!appInfo) {
      return;
    }
    const existVersionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      version,
      app_id: appInfo.app_id,
    });
    if (!existVersionProcessInfo) {
      return;
    }
    let finalVersion = version;
    if (
      existVersionProcessInfo.app_id === AppSettingId.CC_MAC ||
      existVersionProcessInfo.app_id === AppSettingId.CC_WIN ||
      existVersionProcessInfo.app_id === AppSettingId.CC_IOS ||
      existVersionProcessInfo.app_id === AppSettingId.CC_ANDROID
    ) {
      finalVersion = versionUtils.cc2lvVersion(version);
    } else if (
      existVersionProcessInfo.app_id === AppSettingId.HYPIC_ANDROID ||
      existVersionProcessInfo.app_id === AppSettingId.HYPIC_IOS
    ) {
      finalVersion = versionUtils.hypic2RetouchVersion(version);
    }
    const bmRecord = await this.initBMIfoV2(finalVersion, appInfo);
    if (bmRecord && Object.keys(bmRecord).length > 0) {
      existVersionProcessInfo.bmInfo = bmRecord;
    }
    await this.versionProcessInfoDao.updateByCriteria(
      { app_id: appInfo.app_id, version },
      existVersionProcessInfo,
      false,
    );
    return {
      code: 0,
      message: 'success',
    };
  }

  async updateVersionInfo(appInfo: BusinessAppInfo, version: string) {
    if (!appInfo) {
      return;
    }
    const existVersionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      version,
      app_id: appInfo.app_id,
    });
    if (!existVersionProcessInfo) {
      return;
    }
    const originVersion = versionUtils.calOriginVersion(appInfo.bits_workspace, appInfo.product_type, version);
    let finalVersion = originVersion;
    if (
      appInfo.app_id === AppSettingId.CC_WIN ||
      appInfo.app_id === AppSettingId.CC_MAC ||
      appInfo.app_id === AppSettingId.CC_IOS ||
      appInfo.app_id === AppSettingId.CC_ANDROID
    ) {
      finalVersion = versionUtils.cc2lvVersion(originVersion);
    }
    const bmRecord = await this.initBMIfoV2(finalVersion, appInfo);
    const pcInfo = await this.initPCInfo(existVersionProcessInfo.version);
    if (bmRecord && Object.keys(bmRecord).length > 0) {
      existVersionProcessInfo.bmInfo = bmRecord;
    }
    existVersionProcessInfo.pcInfo = pcInfo;
    const timeline: VersionTimelineConfig = await this.businessConfigService.getVersionTimelineConfig(appInfo.app_id);
    const versionCalendar = await this.getVersionCalendar(appInfo, version);
    // for (let i = 0; i < existVersionProcessInfo.version_stages.length; i++) {
    //   const versionStage = existVersionProcessInfo.version_stages[i];
    //   const stageService = this.stageServiceFactory.getServiceForStageName(versionStage.stage_name);
    //   const versionPhase = timeline?.version_stages?.find(it2 => it2.stage_name === versionStage.stage_name);
    //   if (versionPhase && versionCalendar?.segments.length > 3) {
    //     if (stageService && versionCalendar) {
    //       const newStageInfo = await stageService.createStageInfo(versionCalendar.segments, versionPhase);
    //       if (versionStage.status === VersionStageStatus.NotStart && newStageInfo) {
    //         existVersionProcessInfo.version_stages[i] = newStageInfo;
    //       }
    //       if (versionStage.status === VersionStageStatus.OnProgress && newStageInfo) {
    //         versionStage.end_time = newStageInfo.end_time;
    //         versionStage.mr_standard = newStageInfo.mr_standard;
    //         versionStage.version_phase = newStageInfo.version_phase;
    //         versionStage.sub_stages.forEach((subStage, index) => {
    //           const newSubStage = newStageInfo.sub_stages?.find(it4 => it4.stage_name === subStage.stage_name);
    //           if (newSubStage) {
    //             if (subStage.status === VersionStageStatus.NotStart) {
    //               versionStage.sub_stages[index] = newSubStage;
    //             }
    //             subStage.end_time = newSubStage.end_time;
    //             subStage.mr_standard = newSubStage.mr_standard;
    //             subStage.version_phase = versionPhase.sub_stages?.find(it3 => it3.stage_name === subStage.stage_name);
    //           }
    //         });
    //       }
    //     }
    //   }
    // }
    await this.versionProcessInfoDao.updateByCriteria(
      { app_id: appInfo.app_id, version },
      existVersionProcessInfo,
      false,
    );
    return existVersionProcessInfo;
  }

  async forceUpdateVersionInfo(appInfo: BusinessAppInfo, version: string) {
    if (!appInfo) {
      return;
    }
    const existVersionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      version,
      app_id: appInfo.app_id,
    });
    if (!existVersionProcessInfo) {
      return;
    }
    const originVersion = versionUtils.calOriginVersion(appInfo.bits_workspace, appInfo.product_type, version);
    let finalVersion = originVersion;
    if (
      appInfo.app_id === AppSettingId.CC_WIN ||
      appInfo.app_id === AppSettingId.CC_MAC ||
      appInfo.app_id === AppSettingId.CC_IOS ||
      appInfo.app_id === AppSettingId.CC_ANDROID
    ) {
      finalVersion = versionUtils.cc2lvVersion(originVersion);
    }
    const bmRecord = await this.initBMIfoV2(finalVersion, appInfo);
    const pcInfo = await this.initPCInfo(existVersionProcessInfo.version);
    if (bmRecord && Object.keys(bmRecord).length > 0) {
      existVersionProcessInfo.bmInfo = bmRecord;
    }
    existVersionProcessInfo.pcInfo = pcInfo;
    const timeline: VersionTimelineConfig = await this.businessConfigService.getVersionTimelineConfig(appInfo.app_id);
    const versionCalendar = await this.getVersionCalendar(appInfo, version);
    if (!versionCalendar) {
      return;
    }
    for (let i = 0; i < existVersionProcessInfo.version_stages.length; i++) {
      const versionStage = existVersionProcessInfo.version_stages[i];
      const stageService = this.stageServiceFactory.getServiceForStageName(versionStage.stage_name);
      const versionPhase = timeline?.version_stages?.find(it2 => it2.stage_name === versionStage.stage_name);
      if (versionPhase && versionCalendar?.segments.length > 3) {
        if (stageService && versionCalendar) {
          const newStageInfo = await stageService.createStageInfo(versionCalendar.segments, versionPhase);
          if (versionStage.status === VersionStageStatus.NotStart && newStageInfo) {
            existVersionProcessInfo.version_stages[i] = newStageInfo;
          }
          if (versionStage.status === VersionStageStatus.OnProgress && newStageInfo) {
            versionStage.end_time = newStageInfo.end_time;
            versionStage.mr_standard = newStageInfo.mr_standard;
            versionStage.version_phase = newStageInfo.version_phase;
            versionStage.sub_stages.forEach((subStage, index) => {
              const newSubStage = newStageInfo.sub_stages?.find(it4 => it4.stage_name === subStage.stage_name);
              if (newSubStage) {
                if (subStage.status === VersionStageStatus.NotStart) {
                  versionStage.sub_stages[index] = newSubStage;
                }
                subStage.end_time = newSubStage.end_time;
                subStage.mr_standard = newSubStage.mr_standard;
                subStage.version_phase = versionPhase.sub_stages?.find(it3 => it3.stage_name === subStage.stage_name);
              }
            });
          }
        }
      }
    }
    await this.versionProcessInfoDao.updateByCriteria(
      { app_id: appInfo.app_id, version },
      existVersionProcessInfo,
      false,
    );
    return existVersionProcessInfo;
  }

  async getVersionCalendar(appInfo: BusinessAppInfo, version: string) {
    const originVersion = versionUtils.calOriginVersion(appInfo.bits_workspace, appInfo.product_type, version);
    const versionCalendar = await this.initCalendarV2(originVersion, appInfo.app_id);
    if (!versionCalendar) {
      this.logger.error(`No version Calendar for app info : ${JSON.stringify(appInfo)}`);
      return versionCalendar;
    }
    if (versionCalendar.segments.length <= 3 || appInfo.calendar_segment_list.length > 1) {
      // 满足条件说明没有获取到新版本的灰度日历，因为上个版本还没开始灰度，这时候需要补全
      // 首先获取封版日期
      for (const segment of versionCalendar.segments) {
        const graySegmentName =
          appInfo.calendar_segment_list.length > 1
            ? appInfo.calendar_segment_list[1]
            : appInfo.calendar_segment_list[0];
        if (segment.name.includes('封版')) {
          const codeFreezeTime = timeUtil.keyNode2DDL(segment.calc_start_date).epochSeconds;
          const grayCalender = await this.getGrayCalender(
            originVersion,
            appInfo.platform,
            codeFreezeTime,
            graySegmentName,
            appInfo.bits_workspace,
          );
          if (!grayCalender) {
            this.logger.error(`No gray calendar for app info : ${appInfo}`);
            break;
          }
          // 只有当grayCalender和原来的versionCalender不相同的时候，才添加
          if (grayCalender.segments.length > 0 && grayCalender.segments[0].name !== segment.name) {
            versionCalendar.segments.push(...grayCalender.segments);
            break;
          }
        }
      }
    }
    return versionCalendar;
  }

  async initStageChecklist(versionInfo: VersionProcessInfo, stageInfo: VersionStageInfo) {
    await this.checklistService.getVersionStageCheckList(
      { app_id: versionInfo.app_id, version: versionInfo.version, stage: stageInfo.stage_name },
      false,
    );
    stageInfo.sub_stages.map(async it => {
      await this.initStageChecklist(versionInfo, it);
    });
  }

  async createNewVersionV2(appInfo: BusinessAppInfo, version: string, timestamp?: number) {
    if (!appInfo) {
      this.logger.error(`App Info can not be null`);
      return [];
    }

    const existVersionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      version,
      app_id: appInfo.app_id,
    });

    if (
      existVersionProcessInfo &&
      (existVersionProcessInfo.version_stages[0]
        ? existVersionProcessInfo.version_stages[0].status !== VersionStageStatus.NotStart
        : false)
    ) {
      await this.updateVersionInfo(appInfo, version);
      return {} as VersionProcessInfo;
    }

    let meegoId = 0;
    // Dreamina 创建版本流程前先尝试创建集成Meego单
    if (appInfo.app_id === AppSettingId.DREAMINA_ANDROID || appInfo.app_id === AppSettingId.DREAMINA_IOS) {
      const existVersionMeego = await this.versionMeegoProductDao.findVersionMeego(appInfo.business_id, version);
      if (existVersionMeego) {
        meegoId =
          appInfo.platform === PlatformType.Android
            ? Number(existVersionMeego.android_meego_id)
            : Number(existVersionMeego.ios_meego_id);
      } else {
        // 无meego集成单记录
        const meegoInfo = await this.meegoOldService.createTestStoryWorkItemMutiProduct(
          version,
          [PlatformType.Android, PlatformType.iOS],
          appInfo.product_type,
        );
        // 将需求单meegoID保存在VersionMeegoProduct表中
        meegoId = meegoInfo?.data ?? 0;
        await this.versionMeegoProductDao.updateMeegoInfo(
          appInfo.business_id,
          version,
          meegoId.toString(),
          meegoId.toString(),
          '',
          '',
        );
      }
    }

    // 有的产品和日历空间的版本不是对齐的，比如CC，比原始版本小两个数,用原始版本去请求日历数据
    const originVersion = versionUtils.calOriginVersion(appInfo.bits_workspace, appInfo.product_type, version);
    const versionCalendar = await this.initCalendarV2(originVersion, appInfo.app_id, timestamp);
    if (!versionCalendar) {
      this.logger.error(`No version Calendar for app info : ${JSON.stringify(appInfo)}`);
      return [];
    }
    if (versionCalendar.segments.length <= 3 || appInfo.calendar_segment_list.length > 1) {
      // 满足条件说明没有获取到新版本的灰度日历，因为上个版本还没开始灰度，这时候需要补全
      // 首先获取封版日期
      for (const segment of versionCalendar.segments) {
        const graySegmentName =
          appInfo.calendar_segment_list.length > 1
            ? appInfo.calendar_segment_list[1]
            : appInfo.calendar_segment_list[0];
        if (segment.name.includes('封版')) {
          const codeFreezeTime = timeUtil.keyNode2DDL(segment.calc_start_date).epochSeconds;
          const grayCalender = await this.getGrayCalender(
            originVersion,
            appInfo.platform,
            codeFreezeTime,
            graySegmentName,
            appInfo.bits_workspace,
          );
          if (!grayCalender) {
            this.logger.error(`No gray calendar for app info : ${appInfo}`);
            break;
          }
          // 只有当grayCalender和原来的versionCalender不相同的时候，才添加
          if (grayCalender.segments.length > 0 && grayCalender.segments[0].name !== segment.name) {
            versionCalendar.segments.push(...grayCalender.segments);
            break;
          }
        }
      }
    }

    if (version.length <= 1) {
      // 如果没传version，创建最近的版本
      version = versionCalendar?.event.name ?? '';
      // 有的产品和日历空间的版本不是对齐的，比如CC，比原始版本小两个数
      version = versionUtils.calSpecificVersion(appInfo.bits_workspace, appInfo.product_type, version);
    }

    if (version.length <= 1) {
      this.logger.warn(`No valid version to add : ${appInfo}`);
      return [];
    }

    let finalVersion = originVersion;
    if (
      appInfo.app_id === AppSettingId.CC_WIN ||
      appInfo.app_id === AppSettingId.CC_MAC ||
      appInfo.app_id === AppSettingId.CC_IOS ||
      appInfo.app_id === AppSettingId.CC_ANDROID
    ) {
      finalVersion = versionUtils.cc2lvVersion(originVersion);
    }
    const pcInfoResult =
      appInfo.product_type === LVProductType.lv ||
      appInfo.product_type === LVProductType.cc ||
      appInfo.product_type === LVProductType.pc ||
      appInfo.product_type === LVProductType.cc_pc
        ? await this.initPCInfo(finalVersion)
        : ({} as PCInfo);

    const bmInfo = await this.initBMIfoV2(finalVersion, appInfo);
    if (!bmInfo || Object.keys(bmInfo).length === 0) {
      this.logger.warn(`No Bm Info for app info: ${appInfo}`);
      return [];
    }

    let versionProcessInfo: VersionProcessInfo = {
      version,
      app_id: appInfo.app_id,
      version_stages: [],
      pcInfo: pcInfoResult,
      bmInfo,
      meegoId,
      status: VersionProcessStatus.OnProgress,
    };

    if (versionProcessInfo) {
      versionProcessInfo = await this.initVersionStage(versionProcessInfo, versionCalendar, appInfo);
      if (existVersionProcessInfo) {
        await this.versionProcessInfoDao.updateByCriteria(
          {
            version,
            app_id: appInfo.app_id,
          },
          versionProcessInfo,
        );
      } else {
        await this.versionProcessInfoDao.create(versionProcessInfo);
      }
    }

    // 初始化一次所有阶段的checklist
    for (const it of versionProcessInfo.version_stages) {
      await this.initStageChecklist(versionProcessInfo, it);
    }

    // 触发一次更新，让pc可以根据版本号更新灰度信息
    for (const it of versionProcessInfo.version_stages) {
      const stageService = this.stageServiceFactory.getServiceForStageName(it.stage_name);
      if (stageService) {
        await stageService.updateStageInfo(versionProcessInfo);
      }
    }
    return [versionProcessInfo ?? ({} as VersionProcessInfo)];
  }

  async initCalendarV2(version: string, appID: number, timestamp?: number): Promise<EventsItem | undefined> {
    // 支持向前向后跨越2周查询版本信息，优先查询此刻的next数据
    const singleWeekIterationApp = [
      // NOTE 多端接入配置，小包默认单周迭代，按需修改
      AppSettingId.DREAMINA_ANDROID,
      AppSettingId.DREAMINA_IOS,
      AppSettingId.PIPPIT_ANDROID,
      AppSettingId.PIPPIT_IOS,
      AppSettingId.TINYCUT_IOS,
      AppSettingId.TINYCUT_ANDROID,
    ];
    const onWeek = !singleWeekIterationApp.includes(appID) ? 7 * 24 * 60 * 60 : 24 * 60 * 60; // 简单处理单周迭代App，查询7天会导出跨两个版本

    let currentSeconds = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds - onWeek;
    if (timestamp) {
      currentSeconds = timestamp;
    }
    // test code
    // const currentSeconds = 1725242400;
    let calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds - onWeek);
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds + onWeek);
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds - onWeek);
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds - onWeek * 2);
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds + onWeek * 2);
    }
    if (!calendar) {
      const versionOld = await this.versionModel.findVersion({
        version,
      } as Version);
      if (versionOld && versionOld.keyNode) {
        calendar = versionOld.keyNode;
      }
    }
    return calendar;
  }
  async initPCInfo(version: string): Promise<PCInfo | undefined> {
    const result = await this.bits.getPCVersion(version);
    if (result) {
      const bmInfoResult = await this.initBMInfo(result.lvpro_version, PlatformType.PC);
      result.bmInfo = bmInfoResult;
    }
    return result;
  }
  async initBMIfoV2(version: string, appInfo: BusinessAppInfo): Promise<Record<number, BuildMasterInfo>> {
    return await this.initBMInfo(version, appInfo.platform, appInfo.group_name);
  }
  /**
   * 根据版本查询当前BM信息
   * @param version
   * @param platform
   */
  async initBMInfo(
    version: string,
    platform: PlatformType,
    groupName: string | undefined = undefined,
  ): Promise<Record<number, BuildMasterInfo>> {
    if (!groupName) {
      groupName = platform === PlatformType.Android ? 'LV-Android' : 'LV-iOS';
      if (platform === PlatformType.PC) {
        groupName = 'LV-Windows';
      }
    }
    const bmInfo = await this.bits.requestVersionMaster(groupName, version);
    const bmRecord: Record<number, BuildMasterInfo> = {};
    for (const bm of bmInfo) {
      const userInfo = await this.lark.searchUserInfoByEmail(bm.email);
      const _avatarUrl = typeof userInfo?.avatar === 'string' ? userInfo?.avatar : userInfo?.avatar?.avatar_240;
      const _nameCN = userInfo ? userInfo.name : '';
      const _openId = userInfo ? userInfo.open_id : '';
      if (bm.type === BmType.crash) {
        bmRecord[BmType.crash] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.crash,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.rd) {
        bmRecord[BmType.rd] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.rd,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.qa) {
        bmRecord[BmType.qa] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.qa,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.iosoom) {
        bmRecord[BmType.iosoom] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.iosoom,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.ioswatchdog) {
        bmRecord[BmType.ioswatchdog] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.ioswatchdog,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.java_crash) {
        bmRecord[BmType.java_crash] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.java_crash,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.native_crash) {
        bmRecord[BmType.native_crash] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.native_crash,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.oom_crash) {
        bmRecord[BmType.oom_crash] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.oom_crash,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.anr_crash) {
        bmRecord[BmType.anr_crash] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.anr_crash,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.ve_pc_crash) {
        bmRecord[BmType.ve_pc_crash] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.ve_pc_crash,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.android_version_bm) {
        bmRecord[BmType.android_version_bm] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.android_version_bm,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
    }
    return bmRecord;
  }
  /**
   * 初始化版本状态(根据版本日历)
   */
  async initVersionStage(versionProcess: VersionProcessInfo, calendarOriginal: EventsItem, appInfo: BusinessAppInfo) {
    // 获取tcc的timelineConfig中的versionPhase
    const timeline: VersionTimelineConfig = await this.businessConfigService.getVersionTimelineConfig(appInfo.app_id);
    if (!timeline || timeline.version_stages.length <= 0) {
      this.logger.error(`No timeline config for app info : ${versionProcess.app_id}`);
      return versionProcess;
    }
    // 根据versionPhase数组和calendarOriginal的segment数组进行匹配，每个stage生成VersionStageInfo以及对应的subStage
    for (const stage of timeline.version_stages) {
      const stageService = this.stageServiceFactory.getServiceForStageName(stage.stage_name);
      if (stageService) {
        const stageInfo = await stageService.createStageInfo(calendarOriginal.segments, stage);
        stageInfo ? versionProcess.version_stages.push(stageInfo) : {};
        continue;
      }
      const calendar = calendarOriginal.segments.find(segment => segment.name === stage.bits_calendar_segment);
      // 子阶段有可能没有bits日历
      // if (!calendar) {
      //   this.logger.error(
      //     `No calendar for app info : ${versionProcess.app_id}, stage name: ${stage.bits_calendar_segment}`,
      //   );
      //   continue;
      // }
      let hour = '00';
      if (
        stage.stage_name !== 'integrationTest' &&
        stage.stage_name !== 'integration' &&
        stage.stage_name !== 'dreamina_integration' &&
        stage.stage_name !== 'default_integration'
      ) {
        hour = '19';
      }
      const startTime = calendar ? timeUtil.keyNode2Hour(calendar.calc_start_date, hour).epochSeconds : 0;
      const endTime = calendar ? timeUtil.keyNode2Hour(calendar.calc_end_date, '23').epochSeconds : 0;
      const versionStageInfo: VersionStageInfo = {
        stage_name: stage.stage_name,
        display_name: stage.display_name,
        start_time: startTime,
        real_start_time: startTime,
        end_time: endTime,
        real_end_time: endTime,
        status: VersionStageStatus.NotStart,
        sub_stages: [],
        message_count_map: {},
        is_delay: false,
        mr_standard: stage.mr_standard,
      };
      // 用subStage和calendarOriginal的segment数组进行匹配，如果除了主轴和次轴还有第三轴，需要改写为递归
      for (const subStage of stage.sub_stages) {
        const subCalendar = calendarOriginal.segments.find(segment => segment.name === subStage.bits_calendar_segment);
        const startTime1 = subCalendar ? timeUtil.keyNode2Hour(subCalendar.calc_start_date, '0').epochSeconds : 0;
        const endTime1 = subCalendar ? timeUtil.keyNode2Hour(subCalendar.calc_end_date, '23').epochSeconds : 0;
        const versionSubStageInfo1: VersionStageInfo = {
          stage_name: subStage.stage_name,
          display_name: subStage.display_name,
          start_time: startTime1,
          real_start_time: startTime1,
          end_time: endTime1,
          real_end_time: endTime1,
          status: VersionStageStatus.NotStart,
          sub_stages: [],
          message_count_map: {},
          is_delay: false,
          parent_stage_name: stage.stage_name,
          mr_standard: stage.mr_standard,
        };
        versionStageInfo.sub_stages?.push(versionSubStageInfo1);
      }
      versionProcess.version_stages.push(versionStageInfo);
    }
    return versionProcess;
  }

  async getCurrentVersionProcessInfo(appID: number, version: string): Promise<VersionProcessInfo | undefined> {
    const versionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: appID,
      version,
    });
    if (!versionProcessInfo) {
      return undefined;
    }
    return versionProcessInfo;
  }

  async getBackupUpdateTime(appID: number, version: string) {
    const versionProcessInfo = await this.versionProcessInfoDao.backupUpdateTime(appID, version);
    if (!versionProcessInfo) {
      return;
    }
    return versionProcessInfo.backupTime;
  }

  async getCurrentVersionProcessInfoOfBackup(appID: number, version: string): Promise<VersionProcessInfo | undefined> {
    const versionProcessInfo = await this.versionProcessInfoDao.findOneByCriteriaBackup({
      app_id: appID,
      version,
    });
    if (!versionProcessInfo) {
      return undefined;
    }
    return versionProcessInfo;
  }

  async findOnProgressVersions(): Promise<VersionProcessInfo[] | null> {
    return this.versionProcessInfoDao.findOnProgressVersions();
  }

  async getGrayCalender(
    version: string,
    platform: string,
    timestamp: number,
    segmentNameInput: string,
    workSpaceId?: number,
  ): Promise<EventsItem | undefined> {
    const extract_event_item = async (d: DataItem[]): Promise<EventsItem | undefined> => {
      const h = head(d);
      return h?.name === version ? await this.bits.getOpenapiCalendarEventId(h.event_id) : undefined;
    };
    const onWeek = 7 * 24 * 60 * 60;
    const dateItem = await this.bits.calendarWorkSpaceEventNext(segmentNameInput, timestamp, workSpaceId);
    const result = await extract_event_item(dateItem);
    return result;
  }
  async updateVersionProcessInfo(versionProcessInfo: VersionProcessInfo) {
    const existVersionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      version: versionProcessInfo.version,
      app_id: versionProcessInfo.app_id,
    });
    if (existVersionProcessInfo) {
      await this.versionProcessInfoDao.updateByCriteria(
        {
          version: versionProcessInfo.version,
          app_id: versionProcessInfo.app_id,
        },
        versionProcessInfo,
      );
    } else {
      await this.versionProcessInfoDao.create(versionProcessInfo);
    }
  }

  async backupVersionProcessInfo(versionProcessInfo: VersionProcessInfo) {
    versionProcessInfo.backupTime = dayjs().valueOf();
    await this.versionProcessInfoDao.backup(
      {
        version: versionProcessInfo.version,
        app_id: versionProcessInfo.app_id,
      },
      versionProcessInfo,
    );
  }

  async replaceVersionProcessInfo(versionProcessInfo: VersionProcessInfo) {
    const existVersionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      version: versionProcessInfo.version,
      app_id: versionProcessInfo.app_id,
    });
    if (existVersionProcessInfo) {
      await this.versionProcessInfoDao.replaceByCriteria(
        {
          version: versionProcessInfo.version,
          app_id: versionProcessInfo.app_id,
        },
        versionProcessInfo,
      );
    } else {
      await this.versionProcessInfoDao.create(versionProcessInfo);
    }
  }

  async createVersionProcessInfo(versionProcessInfo: VersionProcessInfo) {
    await this.versionProcessInfoDao.create(versionProcessInfo);
  }

  async isFullRelease(version: string, appId: number) {
    const versionProcessInfo = await this.versionProcessInfoDao.findOneByCriteria({
      version,
      app_id: appId,
    });
    if (!versionProcessInfo) {
      this.logger.error(`[isFullRelease] No version process info for app id: ${appId}, version: ${version}`);
      return false;
    }
    const appInfo = await this.businessConfigService.appID2AppInfo(appId);
    if (!appInfo) {
      this.logger.error(`[isFullRelease] No app info for app id: ${appId}`);
      return false;
    }
    const isFullReleased = IsFullReleased(versionProcessInfo, appInfo);
    return isFullReleased;
  }

  async isAfterThirdGray(version: string, appId: number) {
    const certainVersionProcess = await this.versionProcessInfoDao.findOneByCriteria({
      version,
      app_id: appId,
    });
    if (!certainVersionProcess) {
      this.logger.error(`[isAfterThirdGray] No certain version process info for app id: ${appId}, version: ${version}`);
      return false;
    }
    const certainIntegrationStage = certainVersionProcess.version_stages.find(
      it => it.stage_name === 'integration' && it.status === VersionStageStatus.Complete,
    );
    if (!certainIntegrationStage) {
      this.logger.info(`[isAfterThirdGray] No certain integration stage for app id: ${appId}, version: ${version}`);
      return false;
    }
    const onProgressVersionProcessInfo = await this.versionProcessInfoDao.findOnProgressVersions(appId);
    if (!onProgressVersionProcessInfo) {
      this.logger.error(`[isAfterThirdGray] No on progress version process info for app id: ${appId}`);
      return false;
    }
    let grayVersion, grayStage;
    if (appId === AppSettingId.LV_IOS || appId === AppSettingId.CC_IOS) {
      for (const it of onProgressVersionProcessInfo) {
        const tempGray = it.version_stages.find(
          it2 => it2.stage_name === 'testFlight' && it2.status === VersionStageStatus.OnProgress,
        );
        if (tempGray) {
          grayVersion = it;
          break;
        }
      }
    } else if (appId === AppSettingId.LV_ANDROID || appId === AppSettingId.CC_ANDROID) {
      for (const it of onProgressVersionProcessInfo) {
        const tempGray = it.version_stages.find(
          it2 => it2.stage_name === 'gray' && it2.status === VersionStageStatus.OnProgress,
        );
        if (tempGray) {
          grayVersion = it;
          break;
        }
      }
    }
    if (!grayVersion) {
      this.logger.error(`[isAfterThirdGray] No gray version for app id: ${appId}`);
      return false;
    }
    let displayName = '';
    if (appId === AppSettingId.LV_IOS || appId === AppSettingId.CC_IOS) {
      grayStage = grayVersion.version_stages.find(it => it.stage_name === 'testFlight');
      displayName = '第2轮灰度';
    } else if (appId === AppSettingId.LV_ANDROID || appId === AppSettingId.CC_ANDROID) {
      grayStage = grayVersion.version_stages.find(it => it.stage_name === 'gray');
      displayName = '第3轮灰度';
    }
    if (!grayStage) {
      this.logger.error(`[isAfterThirdGray] No gray stage for app id: ${appId}, version: ${grayVersion.version}`);
      return false;
    }
    const thirdGrayStage = grayStage.sub_stages.find(it => it.display_name === displayName);
    if (!thirdGrayStage) {
      this.logger.error(`[isAfterThirdGray] No third gray stage for app id: ${appId}, version: ${grayVersion.version}`);
      return false;
    }
    return thirdGrayStage.status === VersionStageStatus.Complete;
  }

  async getLibraControlSetting() {
    const settings = JSON.parse(await this.tcc.keys.get(`libra_control_setting`)) as LibraControlSetting[];
    this.logger.info(`[getLibraControlSetting] settings: ${JSON.stringify(settings)}`);
    if (settings.length > 0) {
      for (const se of settings) {
        if (se.type === 'all') {
          return se.enable;
        }
      }
    }
    return false;
  }
  async createVersionMeego(version: string, businessType: BusinessType) {
    const exists = await this.versionMeegoProductDao.findVersionMeego(businessType, version);
    if (exists) {
      return exists;
    }
    let androidMeegoId: string | undefined, iosMeegoId: string | undefined;
    let productType: ProductType = LVProductType.lv;
    // NOTE 多端接入配置
    if (businessType === BusinessType.Dreamina) {
      productType = DreaminaProductType.dreamina;
    } else if (businessType === BusinessType.Retouch) {
      productType = RetouchProductType.retouch;
    } else if (businessType === BusinessType.Pippit) {
      productType = PippitProductType.pippit;
    } else if (businessType === BusinessType.TinyCut) {
      productType = TinycutProductType.tinycut;
    }
    const androidVersion = await this.versionProcess.queryVersionProcessInfo(
      productType,
      PlatformType.Android,
      version,
    );
    const iosVersion = await this.versionProcess.queryVersionProcessInfo(productType, PlatformType.iOS, version);
    if (androidVersion?.meegoId || iosVersion?.meegoId) {
      androidMeegoId = androidVersion?.meegoId?.toString() ?? iosVersion?.meegoId?.toString();
      iosMeegoId = androidVersion?.meegoId?.toString() ?? iosVersion?.meegoId?.toString();
    } else {
      const meegoInfo = await this.meegoOldService.createTestStoryWorkItemMutiProduct(
        version,
        [PlatformType.Android, PlatformType.iOS],
        productType,
      );
      androidMeegoId = meegoInfo?.data.toString();
      iosMeegoId = meegoInfo?.data.toString();
    }
    if (androidMeegoId && iosMeegoId) {
      // 将需求单meegoID保存在VersionMeegoProduct表中
      return this.versionMeegoProductDao.updateMeegoInfo(businessType, version, androidMeegoId, iosMeegoId, '', '');
    }
  }

  async getLast2Version(appId: number) {
    const onProgressVersionProcessInfo = await this.versionProcessInfoDao.findByCriteria({
      app_id: appId,
      is_inhouse: 0,
      status: VersionProcessStatus.OnProgress,
      releaseVersionType: ReleaseVersionType.Normal,
    });
    if (!onProgressVersionProcessInfo) {
      this.logger.error(`[getLast2Version] No on progress version process info for app id: ${appId}`);
      return [];
    }
    const filterList = onProgressVersionProcessInfo.filter(it =>
      it.version_stages.find(it2 => it2.stage_name === 'integration' && it2.status === VersionStageStatus.Complete),
    );
    if (filterList.length === 0) {
      this.logger.error(`[getLast2Version] No integration version process info for app id: ${appId}`);
      return [];
    }
    const versionList = onProgressVersionProcessInfo.map(it => it.version);
    const sortList = versionList.sort((a, b) => {
      const aVersion = a.split('.').map(Number);
      const bVersion = b.split('.').map(Number);
      for (let i = 0; i < aVersion.length; i++) {
        if (aVersion[i] !== bVersion[i]) {
          return bVersion[i] - aVersion[i];
        }
      }
      return 0;
    });
    return sortList.slice(0, 2);
  }

  async getRetouchExperimentWarningVersion(appId: number) {
    const versionProcessInfo = await this.versionProcessInfoDao.findOnProgressVersions(appId);
    if (!versionProcessInfo) {
      this.logger.error(
        `[getRetouchExperimentWarningVersion] No on progress version process info for app id: ${appId}`,
      );
      return '';
    }
    // 找出当前灰度的版本
    const grayVersion = versionProcessInfo.find(it =>
      it.version_stages.find(it2 => it2.display_name === '灰度' && it2.status === VersionStageStatus.OnProgress),
    );
    if (!grayVersion) {
      this.logger.error(`[getRetouchExperimentWarningVersion] No gray version for app id: ${appId}`);
      return '';
    }
    return grayVersion.version;
  }

  // 线下拉取封板前的时间
  async getVersionIntegrationTime(version: string, appId: number) {
    const result = await this.versionProcessInfoDao.findOneByCriteria({ app_id: appId, version });
    // const integrationStartTime = result?.version_stages?.find(v =>
    //   ['integration'].includes(v.stage_name),
    // )?.end_time;
    const integrationStage = result?.version_stages?.find(v => v.stage_name === 'integration');
    // 优先取 real_end_time，如果其为0或不存在，则取 end_time
    const integrationStartTime = integrationStage?.start_time;
    return integrationStartTime;
  }

  // 线下拉取封板后开始计算的时间
  async getVersionIntegrationEndTime(version: string, appId: number) {
    const result = await this.versionProcessInfoDao.findOneByCriteria({ app_id: appId, version });
    // const integrationStartTime = result?.version_stages?.find(v =>
    //   ['integration'].includes(v.stage_name),
    // )?.end_time;
    const integrationStage = result?.version_stages?.find(v => v.stage_name === 'integration');
    // 优先取 real_end_time，如果其为0或不存在，则取 end_time
    const integrationStartTime = integrationStage?.real_end_time;
    return integrationStartTime;
  }

  async getAllVersions(appId: number) {
    const allVersionInfos = await this.versionProcessInfoDao.findByCriteria({ app_id: appId, is_inhouse: 0 });
    if (!allVersionInfos || allVersionInfos.length === 0) {
      this.logger.error(`[getAllVersions] No versions found for app id: ${appId}`);
      return [];
    }

    const versions = allVersionInfos
      .filter(info => {
        const integrationStage = info.version_stages?.find(stage => stage.stage_name === 'integration');
        // 过滤掉集成阶段 end_time 和 real_end_time 都无效的版本
        return integrationStage && (integrationStage.end_time || integrationStage.real_end_time);
      })
      .map(info => info.version);

    if (versions.length === 0) {
      this.logger.warn(`[getAllVersions] No versions with a valid integration end_time found for app id: ${appId}`);
    }
    return versions;
  }

  /**
   * 获取封板时间减去14天后在当前日期28天内的版本信息
   * @param app_id 应用ID
   * @param size 返回的版本数量，默认为20
   * @returns 符合条件的版本列表
   */
  async getPullVersionsByIntegrationTime(app_id: number) {
    const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
    const daysInSeconds = 24 * 60 * 60; // 一天的秒数
    // TODO 过渡 下周需要改回21days
    const startTime = now - 28 * daysInSeconds; // 28天前的时间戳

    // 查询所有版本
    const allVersions = await this.versionProcessInfoDao.findByCriteria({
      app_id,
      is_inhouse: 0,
    });

    this.logger.error(`[allVersions]:${allVersions}`);

    if (!allVersions || allVersions.length === 0) {
      this.logger.error(
        `[getRecentVersionsByIntegrationTime] No on progress version process info for app id: ${app_id}`,
      );
      return [];
    }

    const filteredVersions = allVersions.filter(version => {
      // 查找封板阶段
      const integrationStage = version.version_stages?.find(stage => stage.stage_name === 'integration');

      if (
        integrationStage &&
        integrationStage.start_time &&
        (integrationStage.end_time || integrationStage.real_end_time)
      ) {
        // 封板时间减去14天
        const adjustedTime = integrationStage.start_time - 14 * daysInSeconds;
        // 检查是否在当前日期的28天内
        return adjustedTime >= startTime && adjustedTime <= now;
      }
      return false;
    });

    // 按版本号排序
    return filteredVersions
      .sort((a, b) => {
        const aVersion = a.version.split('.').map(Number);
        const bVersion = b.version.split('.').map(Number);
        for (let i = 0; i < Math.max(aVersion.length, bVersion.length); i++) {
          const aNum = aVersion[i] || 0;
          const bNum = bVersion[i] || 0;
          if (aNum !== bNum) {
            return bNum - aNum;
          }
        }
        return 0;
      })
      .map(v => v.version);
  }
}
