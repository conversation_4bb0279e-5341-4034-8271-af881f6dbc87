import { NetworkCode, PlatformType } from '@pa/shared/dist/src/core';
import { CustomBuildRepo, CustomBuildType } from '@shared/customBuild';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { Body, Controller, Get, Post, Query } from '@gulux/application-http';
import { Inject } from '@gulux/gulux';
import BuildHandlerDispatch from '../service/handler/customBuild/buildHandlerDispatch';
import CustomBuildService from '../service/customBuild';
import ComponentModelService from '../service/model/customBuildModel';
import LarkService from '@pa/backend/dist/src/third/lark';
import { BytedLogger } from '@gulux/gulux/byted-logger';

@Controller('')
export default class CustomBuildController {
  @Inject()
  private buildHandlerDispatch: BuildHandlerDispatch;

  @Inject()
  private customBuild: CustomBuildService;

  @Inject()
  private componentModel: ComponentModelService;

  @Inject()
  private lark: LarkService;

  @Inject()
  private logger: BytedLogger;

  /**
   * 读取构建所需的信息，用于开启组件
   * @param body
   */
  @Post('/customBuild/buildInfo')
  async requestBuildInfo(@Body() body: any) {
    const { buildId } = body;
    return await this.customBuild.requestBuildInfo(buildId);
  }

  @Post('/callback/buildResult')
  async buildResultHandler(@Body() body: any) {
    await this.buildHandlerDispatch.dispatch(body);
    return {};
  }

  @Post('/customBuild/ve')
  async customBuildByVe(@Body() body: any) {
    const { target_branch, veVersion, lvveBranch, ve_info, is32, oversea, debug, cloudVersion, mavenType } = body;
    const buildSubRepo: CustomBuildRepo[] = [];
    if (lvveBranch) {
      buildSubRepo.push({
        projectId: 134421,
        branch: lvveBranch,
        openComponent: [],
      });
    }
    if (cloudVersion) {
      buildSubRepo.push({
        projectId: 338872,
        branch: target_branch,
        openComponent: [],
      });
    }
    const result = await this.customBuild.customBuild({
      lvBranch: target_branch,
      type: [CustomBuildType.VE],
      arch: PlatformType.Android,
      cloudVersion,
      repos: buildSubRepo,
      veVersion,
      is32,
      isOversea: oversea,
      isDebug: debug,
      isAnyWhereDorOpen: false,
      extra: ve_info,
      mavenType,
    });
    if (result.code === NetworkCode.Success) {
      return {
        data: {
          buildResult: {
            data: {
              jobURL: result?.data?.jobUrl,
              jobId: result?.data?.jobId,
            },
          },
        },
      };
    } else {
      return {
        code: result.code,
        message: result.message,
      };
    }
  }

  @Post('/customBuild/meegoDebug')
  async customBuildByMeego(@Body() body: any) {
    await this.lark.sendTextMessage(UserIdType.chatId, 'oc_421725f66bd9b296fff97e71a9e50466', JSON.stringify(body));
    return {};
  }

  @Post('/customBuild/releaseDebug')
  async customBuildByBitsRelease(@Body() body: any) {
    const { branch, overseas, callback_addr, job_id, username } = body;
    this.logger.error(username);

    const result = await this.customBuild.customBuild({
      arch: PlatformType.Android,
      lvBranch: branch,
      type: [CustomBuildType.OUT],
      repos: [],
      isOversea: overseas,
      isDebug: true,
      isDebugFix: true,
      isLynxDebug: true,
      isOpenNetWorkTrustUser: true,
      extra: {
        callback_addr,
        job_id,
        username: username.indexOf('@') === -1 ? `${username}@bytedance.com` : username,
      },
    });

    if (result.code === NetworkCode.Success && result.data) {
      return {
        code: 0,
        message: `构建触发成功,请到打包job[${result.data.jobUrl}]中找产物`,
        data: result.data,
      };
    } else {
      return {
        code: 0,
        message: `构建触发失败`,
        data: result.data,
      };
    }
  }

  @Post('/customBuild/lastReleaseInfo')
  async requestLastBuildInfo(@Body() body: any) {
    const { platform, version, is32, isOversea } = body;
    if (platform) {
      const res = await this.componentModel.getLastCustomBuildInfo(platform, isOversea, is32, version);
      if (res) {
        return {
          code: 0,
          data: {
            params: res.buildParams,
            artifacts: res.artifacts,
          },
        };
      } else {
        return {
          code: -1,
          msg: '版本构建信息不存在',
        };
      }
    }
  }

  @Post('/open/android_publish_cache')
  async androidPublishCache(@Body() params: { branch: string; isOversea: boolean; buildTarget: string }) {
    const { branch } = params;
    const buildList: {
      type: CustomBuildType[];
      isOversea: boolean;
      isDebug?: boolean;
      isOutBuild?: boolean;
      isOutTest?: boolean;
      isByteInsight?: boolean;
      isPublishCache?: boolean;
      isPublishGradleCache?: boolean;
      isForceAllSource?: boolean;
      buildTemplate?: string;
    }[] = [
      // without lyra
      // MR合入+缓存-剪映，中间层产物
      {
        type: [CustomBuildType.PUBLISH_CACHE],
        isOversea: params.isOversea,
        isPublishCache: true,
        isPublishGradleCache: true,
      },
      // MR合入+缓存-CC-debug
      {
        type: [CustomBuildType.PUBLISH_CACHE],
        isOversea: params.isOversea,
        isPublishCache: true,
        isPublishGradleCache: true,
        isDebug: true,
      },

      // with lyra
      // MR合入+缓存
      {
        type: [CustomBuildType.PUBLISH_CACHE, CustomBuildType.MR_MERGED],
        isOversea: params.isOversea,
        isPublishCache: true,
        isPublishGradleCache: true,
        isForceAllSource: true,
      },
      // MR合入+缓存-剪映-debug
      {
        type: [CustomBuildType.PUBLISH_CACHE, CustomBuildType.MR_MERGED],
        isOversea: params.isOversea,
        isPublishCache: true,
        isPublishGradleCache: true,
        isForceAllSource: true,
        isDebug: true,
      },
      // MR合入+缓存-剪映-外发
      {
        type: [CustomBuildType.PUBLISH_CACHE, CustomBuildType.OUT_BUILD],
        isOversea: params.isOversea,
        isPublishGradleCache: true,
        isForceAllSource: true,
        isOutBuild: true,
      },
    ];
    for (const buildListElement of buildList) {
      await this.customBuild.customBuild({
        lvBranch: branch,
        repos: [],
        type: buildListElement.type,
        arch: PlatformType.Android,
        buildTarget: params.buildTarget,
        isOversea: buildListElement.isOversea,
        isDebug: buildListElement.isDebug,
        isByteInsight: buildListElement?.isByteInsight,
        isOutBuild: buildListElement.isOutBuild,
        isPublishCache: buildListElement.isPublishCache,
        isPublishGradleCache: buildListElement.isPublishGradleCache,
        isOutTest: buildListElement.isOutTest,
        isForceAllSource: buildListElement.isForceAllSource,
        buildTemplate: buildListElement.buildTemplate,
        extra: {
          author: 'zhanglinwei.yimu',
        },
      });
    }
  }
}
