import { Api, Data, Get, Post, Query, useInject } from '@edenx/runtime/bff';
import { z } from 'zod';
import BusAssistService from '../service/bus/busAssist';
import BitsService from '../service/third/bits';
import LarkService from '@pa/backend/dist/src/third/lark';
import BusMrHandler from '../service/handler/bits/busMrHandler';
import MeegoOldService from '../service/meegoOld';
import PeopleMapModelService from '../service/model/peopleMapModel';
import StoryBusVersionCheck from '../service/StoryBusVersionCheck';
import MeegoService from '../service/third/meego';
import { PlatformType, successRsp } from '@pa/shared/dist/src/core';
import inspirecloud from '@byted/inspirecloud-api';
import dbUtils from '../utils/dbUtils';
import CustomBuildService from '../service/customBuild';
import Card<PERSON>allbackHandlerService from 'api/service/CardCallbackHandler';
import {
  Action,
  CardActionValue,
  CardCallback,
  CardCallbackType,
  CardElement,
  CardElementTag,
  CardTemplate,
  CardTextTag,
  MessageType,
} from '@pa/shared/dist/src/lark/larkCard';
import VersionProcessDao from '../service/dao/VersionProcessDao';
import VersionReleaseService from '../service/releasePlatform/versionReleaseService';
import AdrGrayStageService from '../service/releasePlatform/stageServices/adrGrayStageService';
import { VersionStageStatus } from '@shared/releasePlatform/versionStage';
import { Temporal } from '@js-temporal/polyfill';
import commonUtils from '../utils/commonUtils';
import { VersionProcessInfoService } from '../service/releasePlatform/versionProcessInfoService';
import { MrInfo, MrState, MrType } from '@shared/bits/mrInfo';
import MRProfilerHostService from '../service/mrProfiler/MRProfilerHostService';
import HostCronJobTrigger from '../trigger/HostCronJobTrigger';
import { handleManualTrigger } from '@pa/backend/dist/src/utils/cronJob';
import { AcceptanceFeatureService } from '../service/acceptanceCenter/acceptanceFeatureService';
import { CodeProcessor, CodeProcessorParams, FinishType } from 'api/service/codeProcess/codeProcessor';
import { AcceptanceFeatureBitableService } from '../service/acceptanceCenter/acceptanceFeatureBitableService';
import { TransactionStatus, VersionTransactionType } from '@shared/releasePlatform/versionTransaction';
import { VersionTransactionService } from '../service/releasePlatform/versionTransactionService';
import { CrucialRequirementService } from '../service/releasePlatform/CrucialRequirementService';
import { VersionProcessInfoEditorService } from '../service/releasePlatform/editor/VersionProcessInfoEditorService';
import dayjs from 'dayjs';
import ApprovalService from '../service/approval/ApprovalService';
import { ApprovalType } from '@shared/approval/ApprovalOrder';
import MessageService from '@pa/backend/dist/src/service/message';
import { MsgStrategy, MsgTemplate, MsgType } from '@pa/shared/dist/src/message';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import LarkCardService from '../service/larkCard';
import { MrChangeReviewService } from '../service/mrAporoval/mrChangeReview';
import { RuleConfig } from '@shared/zhongkui';
import VersionProcessInfoDao from '../service/dao/releasePlatform/VersionProcessInfoDao';
import StoryReadyNotifyService from '../service/handler/consul/storyReadyNotify';
import AcceptanceFeatureDao from '../service/dao/acceptanceCenter/AcceptanceFeatureDao';
import AcceptanceFeatureIssueDao from '../service/dao/acceptanceCenter/AcceptanceFeatureIssueDao';
import AcceptanceOperationDao from '../service/dao/acceptanceCenter/AcceptanceOperationDao';
import VersionFeatureService from '../service/releasePlatform/versionFeatureService';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import { CustomBuildModel } from '../model/CustomBuildModel';
import { ApprovalDBService } from '../service/approval/ApprovalDBService';
import { ApprovalInfoTable } from '../model/approval/ApprovalInfoModel';
import { VersionStageCheckListService } from '../service/releasePlatform/versionStageCheckListService';
import { FunctionalBugItemInfo, TotalBugResolveRatioItemInfo } from '@shared/releasePlatform/versionStageCheckItemInfo';
import VersionStageCheckListDao from '../service/dao/releasePlatform/VersionStageCheckListDao';
import { ItemType } from '@shared/releasePlatform/versionStageInfoCheckList';
import ScheduleBuildService from '../service/ScheduleBuildService';
import { VersionAutoDriveActionInfo, VersionAutoDriveActionType } from '@shared/releasePlatform/autoDriveActionInfo';
import LibraGray100PercentNotifyAction from '../service/releasePlatform/versionAutoDrive/LibraManage/LibraGray100PercentNotifyAction';
import { lv2ccVersion } from '../utils/versionUtils';
import SmallFlowStageService from '../service/releasePlatform/stageServices/smallFlowStageService';
import FullReleaseStageService from '../service/releasePlatform/stageServices/fullReleaseStageService';
import ExperimentSource from '../service/dao/ExperimentSource';
import { TBCCodeSyncService } from '../service/tbc/TBCCodeSyncService';
import { GetOnReason } from '@shared/bus/busType';
import BusDao from '../service/dao/BusDao';
import { getBitsSpacesById } from '@shared/bus/config';
import VersionFeatureInfoDao from '../service/dao/releasePlatform/versionFeatureInfoDao';
import MrMergedBuildService from '../service/handler/bits/mrMergeBuild';
import ReleaseCheckOutInformService from '../service/handler/bits/releaseCheckoutInform';
import CollectAbnormalMultiMrService from '../service/tasks/collectAbnormalMultiMr';
import CircuitBreakerService from '../service/circuitBreaker/circuitBreakerService';
import {
  CircuitBreakerCallbackActionType,
  CircuitBreakerTicket,
  CircuitBreakerTicketType,
} from '@shared/circuitBreaker/circuitBreakerTicket';
import AdrLvFullReleaseService from 'api/service/releasePlatform/stageServices/adrLvFullReleaseService';
import AdrCCFullReleaseStageService from 'api/service/releasePlatform/stageServices/adrCCFullReleaseStageService';
import CodebaseService from '../service/third/codebase';
import { IntegrationMrsRequest } from '@shared/bits/integration';

/**
 * 用到 ctx 的全删了
 * 有需要再加进去
 */

export const debug_version_feature = Api(
  Post('/open/debug/version_feature'),
  Data(
    z.object({
      version: z.string(),
      appId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(VersionFeatureService);
    const result = await service.updateVersionFeatureInfoByMrId(data.appId, data.version, 7243028);
  },
);

export const debug_ad_notice = Api(
  Post('/open/debug/ad_notice'),
  Data(
    z.object({
      id: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(ExperimentSource);
    const res = await service.AutoCommercialNotice();
    return {
      code: 200,
      res,
    };
  },
);

export const test_mr_merge_build = Api(
  Post('/open/debug/mr_merge_build'),
  Data(
    z.object({
      data: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(MrMergedBuildService);
    const result = await service.handler(data.data);
  },
);

export const need_ab_meego = Api(
  Post('/open/debug/need_ab_meego'),
  Data(
    z.object({
      version: z.string(),
      adrAppId: z.number(),
      iosAppId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(VersionFeatureService);
    const adrResult = await service.getVersionFeatureInfo(data.adrAppId, data.version);
    const iosResult = await service.getVersionFeatureInfo(data.iosAppId, data.version);
    let result = [];
    if (adrResult && iosResult) {
      result.push(...adrResult);
      result.push(...iosResult);
      // 去重
      const map = new Map();
      result.forEach(item => {
        if (!map.has(item.meegoId)) {
          map.set(item.meegoId, item);
        }
      });
      result = Array.from(map.values());
    }
    let needAbCount = 0;
    if (result) {
      for (const feature of result) {
        const meegoService = useInject(MeegoService);
        const meegoDetail = await meegoService.requestWorkflow('faceu', 'story', feature.meegoId.toString());
        let needAb = false;
        if (meegoDetail && meegoDetail.data) {
          for (const node of meegoDetail.data.workflow_nodes) {
            if (node.name === '需求提出') {
              for (const field of node.fields) {
                if (field.field_key === 'need_ab') {
                  needAb = field.field_value as boolean;
                  break;
                }
              }
            }
          }
        }
        if (!needAb) {
          continue;
        }
        needAbCount++;
      }
    }
    return {
      code: 200,
      needAbCount,
      meegoAllCount: result.length,
    };
  },
);

export const debug_libra_noti = Api(
  Post('/open/debug/libra_noti'),
  Data(
    z.object({
      version: z.string(),
      appId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(VersionProcessInfoDao);
    const result = await service.findOneByCriteria({
      version: data.version,
      app_id: data.appId,
    });
    if (result) {
      const stage = result.version_stages.find(item => item.display_name === '灰度');
      if (stage) {
        const testStage = stage.sub_stages.find(item => item.display_name === '第3轮灰度');
        if (testStage) {
          const autoDrive: VersionAutoDriveActionInfo = {
            app_id: data.appId,
            action_type: VersionAutoDriveActionType.LibraGray100PercentNotify,
            version: data.version,
            stage: '第3轮灰度',
            trigger_count: 0,
            last_trigger_time: 0,
          };
          const checkListService = useInject(VersionStageCheckListService);
          const checklist = await checkListService.getVersionStageCheckList({
            app_id: data.appId,
            version: data.version,
            stage: testStage.stage_name,
          });
          if (checklist) {
            const autoDriveAction = useInject(LibraGray100PercentNotifyAction);
            const res = await autoDriveAction.executeAction(result, testStage, autoDrive, checklist);
            return {
              code: 200,
              res,
            };
          }
        }
      }
    }
  },
);

export const debug_1 = Api(Get('/open/debug/1'), async () => {
  throw new Error('test');
  // return busAssist.queryBothBM('12.4.0');
});

export const is_crucual_requirement = Api(
  Query(z.object({ story: z.string() })),
  Get('/open/debug/git is_crucual_requirement'),
  async ({ query }) => useInject(CrucialRequirementService).isCrucialStory(Number(query.story)),
);

export const crucual_requirement_update = Api(Get('/open/debug/crucual_requirement_update'), async () => {
  const crucialRequirementService = useInject(CrucialRequirementService);
  const result = await crucialRequirementService.fetch();
  const stories = await crucialRequirementService.getCrucialStories();

  return {
    result,
    stories,
  };
});

export const debug_2 = Api(
  Get('/open/debug/2'),
  async () => await useInject(LarkService).searchUserInfoBatch(new Array(51).fill('linhouyan')),
);

export const vt_get_config = Api(Get('/open/debug/version/transaction/get_config'), async () => {
  const service = useInject(VersionTransactionService);
  const response = await service.getVersionTransactionConfig(177502, VersionTransactionType.GrayDelaySync);
  return {
    code: 200,
    response,
  };
});

export const vt_init_data = Api(Get('/open/debug/version/transaction/init_data'), async () => {
  const service = useInject(VersionTransactionService);

  // const newTransaction = service.createVersionTransaction({
  //   bindings: [{ app_id: 2, platform: PlatformType.Android }],
  //   type: VersionTransactionType.GrayDelaySync,
  //   title: `测试一个${Math.random()}`,
  //   extra: {
  //     attributions: [`归因${Math.random()}`, `归因${Math.random()}`],
  //     reason: `原因${Math.random()}`,
  //   } as VersionTransactionExtraData,
  // });
  //
  // const response = await service.update(newTransaction);

  return {
    code: 200,
  };
});

export const vt_update = Api(Get('/open/debug/version/transaction/update'), async () => {
  const service = useInject(VersionTransactionService);

  const transactions = await service.find({
    app_id: [2],
    platform: [PlatformType.Android],
    type: [VersionTransactionType.GrayDelaySync],
  });

  if (transactions) {
    for (const transaction of transactions) {
      if (transaction?.extra) {
        transaction.status = TransactionStatus.Canceled;
        const extra = transaction?.extra;
        // extra.attributions = [`修改归因${Math.random()}`, `修改归因${Math.random()}`];
        await service.update(transaction);
      }
    }
  }

  const response = await service.find({
    app_id: [2],
    platform: [PlatformType.Android],
    type: [VersionTransactionType.GrayDelaySync],
  });

  return {
    code: 200,
    response,
  };
});

export const debug_5 = Api(
  Post('/open/debug/5'),
  Data(
    z.object({
      version: z.string(),
      appId: z.number(),
    }),
  ),
  async ({ data }) => {
    // const res = await useInject(MergeRequestService).createPreciseTestMR(data.version);
    if (data.appId === AppSettingId.LV_ANDROID) {
      const res = await useInject(BusAssistService).initIntegrationBus(AppSettingId.LV_ANDROID, data.version);
      return {
        res,
      };
    } else if (data.appId === AppSettingId.CC_ANDROID) {
      const res = await useInject(BusAssistService).initIntegrationBus(AppSettingId.CC_ANDROID, data.version);
      return {
        res,
      };
    }
    const versionInfo = await useInject(VersionProcessInfoDao).getCurrentVersionProcessInfo(data.appId, data.version);
    if (!versionInfo) {
      return 'null';
    }
    const res = await useInject(BusAssistService).initPCOrRetouchBus(versionInfo);
    return {
      res,
    };
  },
);

export const debug_7 = Api(Get('/open/debug/7'), async () => {
  const mrInfo = await useInject(BitsService).getMrInfo({
    mrId: 6578782,
  });
  if (!mrInfo) {
    return 'null';
  }
  const res = await useInject(BitsService).getMrRealLvPlatform(mrInfo);
  return {
    checkLv: await useInject(BitsService).checkMrContainsLvRepo(mrInfo),
    platforms: [...res],
    mrInfo,
  };
});

export const debug_8 = Api(
  Post('/open/debug/codeFreezeStart'),
  Data(
    z.object({
      appId: z.number(),
    }),
  ),
  async ({ data }) => {
    const res = await useInject(BusAssistService).onCodeFreezeStart(data.appId);
    return res;
  },
);

export const debug_9 = Api(
  Get('/open/debug/9'),
  async () => {
    const fixRate = await useInject(MeegoOldService).queryModelIssueFixRate(`剪映-iOS-13.2.0`, ['剪映', 'CapCut']);
    return fixRate;
  },
  // return 'ok';
);

export const debug_build = Api(
  Get('/open/debug/build'),
  async () => {
    const versionInfo = await useInject(VersionProcessInfoDao).findOneByCriteria({
      app_id: AppSettingId.LV_IOS,
      version: '15.7.0',
    });
    if (!versionInfo) {
      return;
    }
    const result = await useInject(ScheduleBuildService).isSameCommitBuild(versionInfo, 'release/15.7.0');
  },
  // return 'ok';
);

export const debugReleaseCheckout = Api(
  Post('/open/debug/releaseCheckout'),
  Data(
    z.object({
      data: z.string(),
    }),
  ),
  async ({ data }) => {
    const res = await useInject(ReleaseCheckOutInformService).handler(data.data);
    return res;
  },
);

export const debugBusMrHook = Api(
  Post('/open/debug/busMrHook'),
  Data(
    z.object({
      action: z.string(),
      content: z.string(),
    }),
  ),
  async ({ data }) => {
    const handler = useInject(BusMrHandler);
    if (handler.canHandle(data.action)) {
      await handler.handler(data.content);
    }
  },
);

export const debugBusAction = Api(
  Post('/open/debug/busAction'),
  Data(
    z.object({
      chatId: z.string(),
      data: z.string(),
    }),
  ),
  async ({ data }) => {
    const handler = useInject(BusAssistService);
    await handler.handleBusLarkMessage(data.chatId, data.data);
  },
);

export const debugCreateDirection = Api(
  Post('/open/debug/createDirection'),
  Data(
    z.object({
      name: z.string(),
      leader: z.string(),
      androidOwner: z.string(),
      iosOwner: z.string(),
    }),
  ),
  async ({ data }) => {
    const res = await useInject(PeopleMapModelService).quickCreateDirection(
      data.name,
      data.leader,
      data.androidOwner,
      data.iosOwner,
    );
    return res;
  },
);

export const debug_10 = Api(Get('/open/debug/10'), async () => {
  const ms = useInject(StoryBusVersionCheck);
  const res = await ms.entry();
  console.log(JSON.stringify(res));
  return res;
});

export const debugAutoTestBug = Api(
  Post('/open/debug/autoTestBug'),
  Data(
    z.object({
      version: z.string(),
      platform: z.string(),
    }),
  ),
  async ({ data }) => {
    const meego = useInject(MeegoService);
    const list = await meego.autoTestBugList(data.version, data.platform as PlatformType);
    return list;
  },
);

export const debugTriggerIntegrationBuild = Api(
  Post('/open/debug/triggerIntegrationBuild'),
  Data(
    z.object({
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const currentVersion = data.version;
    const versionMeego = await useInject(VersionProcessDao).findVersionMeego(currentVersion);
    if (versionMeego) {
      await useInject(CustomBuildService).customBuildWithMrMerged({
        target_branch: `release/${data.version}`,
        platform: PlatformType.Android,
      } as MrInfo);
      await useInject(CustomBuildService).customBuildWithMrMerged({
        target_branch: `release/${data.version}`,
        platform: PlatformType.iOS,
      } as MrInfo);
      // await useInject(CustomBuildService).customBuildWithVersion(versionMeego.androidMeegoId);
      // await useInject(CustomBuildService).customBuildWithVersion(versionMeego.iosMeegoId);
    }
    return 'ok';
  },
);

export const debugDataClean = Api(
  Get('/open/debug/dataClean'),
  async () => {
    const meegoStoryTable = inspirecloud.db.table(`${dbUtils.getTestRepoName('MeegoStoryTable')}`);
    const cnt = await meegoStoryTable.where().set({ businessId: 1775 }).save();
    return cnt;
  },
  // return 'ok';
);

export const debugApproveBalance = Api(
  Post('/open/debug/approveBalance'),
  Data(
    z.object({
      id: z.string(),
      isApproved: z.string(),
      chatId: z.string(),
    }),
  ),
  async ({ data }) => {
    const callbackHandle = useInject(CardCallbackHandlerService);
    const callbackData = {
      action: {} as Action,
    } as CardCallback;
    callbackData.action.value = {} as CardActionValue;
    callbackData.action.value.balanceRecordId = data.id;
    callbackData.action.value.isApproved = data.isApproved;
    callbackData.action.value.chatId = data.chatId;
    await callbackHandle.handle(CardCallbackType.WithdrawBanlance, callbackData);
  },
);
export const debugTurnToNextStageForAndroid = Api(
  Post('/open/debug/new_version/turn_to_next_stage'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      stageName: z.string(),
    }),
  ),
  async ({ data }) => {
    if (data.appId !== 177502) {
      return;
    }
    const versionReleaseService = useInject(VersionReleaseService);
    await versionReleaseService.turnVersionStageToNext(data.appId, data.version);
    return {};
  },
);
export const debugCheckStageStatus = Api(
  Post('/open/debug/new_version/check_stage_status'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      stageName: z.string(),
    }),
  ),
  async ({ data }) => {
    if (data.stageName === 'submit') {
      const service = useInject(FullReleaseStageService);
      const versionInfo = await useInject(VersionProcessInfoDao).findOneByCriteria({
        app_id: data.appId,
        version: data.version,
      });
      if (versionInfo) {
        await service.checkStageStatus(versionInfo, true);
      }
    } else {
      const service = useInject(SmallFlowStageService);
      const versionInfo = await useInject(VersionProcessInfoDao).findOneByCriteria({
        app_id: data.appId,
        version: data.version,
      });
      if (versionInfo) {
        await service.checkStageStatus(versionInfo, true);
      }
    }
  },
);

export const debugAdrCheckStageStatus = Api(
  Post('/open/debug/new_version/check_adr_stage_status'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      stageName: z.string(),
    }),
  ),
  async ({ data }) => {
    if (data.stageName === 'adr_lv_submit') {
      const service = useInject(AdrLvFullReleaseService);
      const versionInfo = await useInject(VersionProcessInfoDao).findOneByCriteria({
        app_id: data.appId,
        version: data.version,
      });
      if (versionInfo) {
        await service.checkStageStatus(versionInfo, true);
      }
    } else {
      const service = useInject(AdrCCFullReleaseStageService);
      const versionInfo = await useInject(VersionProcessInfoDao).findOneByCriteria({
        app_id: data.appId,
        version: data.version,
      });
      if (versionInfo) {
        await service.checkStageStatus(versionInfo, true);
      }
    }
  },
);

export const forceGetOnMrByTicket = Api(
  Post('/open/debug/new_version/force_get_on_mr_by_ticket'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      mrId: z.number(),
    }),
  ),
  async ({ data }) => {
    const mrInfo = await useInject(BitsService).getMrInfo({
      mrId: data.mrId,
    });
    if (!mrInfo) {
      return;
    }
    const currentBus = await useInject(BusDao).queryCurrentIntegrationBusByAppId(data.appId);
    if (!currentBus) {
      return;
    }
    await useInject(BusAssistService).getOnMr(currentBus, mrInfo, GetOnReason.BindTicket);
  },
);
export const debugModifyStatus = Api(
  Post('/open/debug/new_version/modify_version_status'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      stageName: z.string(),
      versionStageStatus: z.nativeEnum(VersionStageStatus),
    }),
  ),
  async ({ data }) => {
    if (data.appId !== 177502) {
      return;
    }

    const versionProcessService = useInject(VersionProcessInfoService);
    const versionProcessInfo = await versionProcessService.getCurrentVersionProcessInfo(data.appId, data.version);
    if (versionProcessInfo) {
      const currentIndex = versionProcessInfo.version_stages.findIndex(it => it.stage_name === data.stageName);
      versionProcessInfo.version_stages[currentIndex].status = data.versionStageStatus;
      versionProcessInfo.version_stages[currentIndex].real_start_time = Temporal.Now.zonedDateTimeISO(
        commonUtils.defaultTimeZone,
      ).epochSeconds;
      await versionProcessService.updateVersionProcessInfo(versionProcessInfo);
    }
    return {};
  },
);

export const debugMeegoItem = Api(
  Post('/open/debug/new_version/debug_meego_item'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      stageName: z.string(),
      meegoId: z.number(),
      status: z.number(),
    }),
  ),
  async ({ data }) => {
    const checklist = await useInject(VersionStageCheckListService).getVersionStageCheckList(
      {
        version: data.version,
        app_id: data.appId,
        stage: data.stageName,
      },
      false,
    );
    if (checklist) {
      for (const item of checklist.check_items) {
        if (item.check_item_id.includes(data.meegoId.toString())) {
          item.status = data.status;
          const info = item.item_info as FunctionalBugItemInfo;
          info.status = data.status;
          await useInject(VersionStageCheckListDao).updateCheckItem(
            {
              app_id: data.appId,
              version: data.version,
              stage: data.stageName,
            },
            item.check_item_id,
            item,
          );
          break;
        }
      }
    }
    return {};
  },
);

export const debugBugResolveItem = Api(
  Post('/open/debug/new_version/debug_bug_resolve_item'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      stageName: z.string(),
      meegoId: z.number(),
      status: z.number(),
    }),
  ),
  async ({ data }) => {
    const checklist = await useInject(VersionStageCheckListService).getVersionStageCheckList(
      {
        version: data.version,
        app_id: data.appId,
        stage: data.stageName,
      },
      false,
    );
    if (checklist) {
      const bugItem = checklist.check_items.find(it => it.item_type === ItemType.BugResolveRatio);
      if (bugItem) {
        const info = bugItem.item_info as TotalBugResolveRatioItemInfo;
        for (const businessItem of info.item_infos) {
          for (const bugInfo of businessItem.bug_item_list) {
            if (bugInfo.meego_id === data.meegoId) {
              bugInfo.status = data.status;
              await useInject(VersionStageCheckListDao).updateCheckItem(
                {
                  app_id: data.appId,
                  version: data.version,
                  stage: data.stageName,
                },
                bugItem.check_item_id,
                bugItem,
              );
              break;
            }
          }
        }
      }
    }
    return {};
  },
);

export const debugClearGrayVersionForAndroid = Api(
  Post('/open/debug/new_version/clear_gray_version'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      stageName: z.string(),
      versionStageStatus: z.nativeEnum(VersionStageStatus),
    }),
  ),
  async ({ data }) => {
    const versionProcessService = useInject(VersionProcessInfoService);
    const versionProcessInfo = await versionProcessService.getCurrentVersionProcessInfo(data.appId, data.version);
    if (versionProcessInfo) {
      const currentIndex = versionProcessInfo.version_stages.findIndex(it => it.stage_name === data.stageName);
      versionProcessInfo.version_stages[currentIndex].status = data.versionStageStatus;
      versionProcessInfo.version_stages[currentIndex].real_start_time = Temporal.Now.zonedDateTimeISO(
        commonUtils.defaultTimeZone,
      ).epochSeconds;
      versionProcessInfo.version_stages[currentIndex].sub_stages = [];
      await versionProcessService.replaceVersionProcessInfo(versionProcessInfo);
    }
    return {};
  },
);

export const backupVersion = Api(
  Post('/open/debug/new_version/backupVersion'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
    await versionProcessEditorService.backupVersion(data.appId, data.version);
    return;
  },
);

export const getUpdateTime = Api(
  Post('/open/debug/new_version/backup_status'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessEditorService = useInject(VersionProcessInfoService);
    return await versionProcessEditorService.getBackupUpdateTime(data.appId, data.version);
  },
);

export const hideVersion = Api(
  Post('/open/debug/new_version/hideVersion'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
    await versionProcessEditorService.hideFixVersion(data.appId, data.version);
    return;
  },
);

export const cleanFixVersion = Api(
  Post('/open/debug/new_version/cleanFixVersion'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
    await versionProcessEditorService.cleanFixVersion(data.appId, data.version);
    return;
  },
);

export const recoverVersion = Api(
  Post('/open/debug/new_version/recoverVersion'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
    await versionProcessEditorService.recoverVersion(data.appId, data.version);
    return;
  },
);

export const createFixVersion = Api(
  Post('/open/debug/new_version/createFixVersion'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      fixVersion: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
    // const fixVersion = versionUtils.plusVersion(data.version);
    // const version = versionUtils.minusVersion(data.version);
    const { fixVersion, version } = data;
    if (version && fixVersion) {
      await versionProcessEditorService.createFixVersion(data.appId, version, fixVersion, dayjs().unix());
    }
    return;
  },
);

export const insertFixVersion = Api(
  Post('/open/debug/new_version/insertFixVersion'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      fixVersion: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
    await versionProcessEditorService.insertBugfixVersion(data.appId, data.version, data.fixVersion, dayjs().unix());
    return {};
  },
);

export const tmp_fixVersion = Api(Get('/open/debug/new_version/tmp_fixVersion_2'), async () => {
  const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
  await versionProcessEditorService.fixVersionType();
  return successRsp('success');
});

export const tmp_notify_approval = Api(Get('/open/debug/new_version/tmp_notify_fix_version_approval_5'), async () => {
  // const versionProcessEditorService = useInject(VersionProcessInfoEditorService);
  // await versionProcessEditorService.fixVersionBMInfo();
  const approvalDBService = useInject(ApprovalDBService);
  const approvalService = useInject(ApprovalService);
  const approval = await approvalDBService.findOneApproval(
    '0E0D600B-511C-4B43-BF3D-76E99B8C8BC0',
    'D034E0DB-EC20-46A2-AB11-B1C8B2D4AA7D',
  );
  if (approval) {
    await approvalService.sendFixVersionCreateSuccessToCoreGroup(approval as ApprovalInfoTable);
  }
  return successRsp('success');
});

export const countApproval = Api(
  Post('/open/debug/approval/count'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const { appId, version } = data;
    if (appId && version) {
      const approvalService = useInject(ApprovalService);
      return await approvalService.getApprovalCount(appId, version, ApprovalType.FixedVersionRequirements);
    }
    return {};
  },
);

export const debugAddGrayVersionForAndroid = Api(
  Post('/open/debug/new_version/add/gray_version'),
  Data(
    z.object({
      appId: z.number(),
      version: z.string(),
      versionCode: z.string(),
      product: z.string(),
      platform: z.nativeEnum(PlatformType),
    }),
  ),
  async ({ data }) => {
    if (data.appId !== 177502) {
      return;
    }
    const adrGrayStageService = useInject(AdrGrayStageService);
    await adrGrayStageService.adrGrayReleased(data.version, data.platform, data.product, data.versionCode);
    return {};
  },
);

export const inDevelopVersion = Api(
  Get('/open/debug/inDevelopVersion'),
  async () => ({
    oldVersions: await useInject(VersionProcessDao).queryVersionProcessInDevelop(),
    newVersions: await useInject(VersionProcessInfoDao).findVersionInDevelop(),
    dayGap: await useInject(StoryReadyNotifyService).nearestVersionWithGap(),
  }),
  // return 'ok';
);

export const unbindTicket = Api(
  Post('/open/debug/unbindTicket'),
  Data(
    z.object({
      mrId: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const mrInfo = await useInject(BitsService).getMrInfo({ mrId: data.mrId });
    // 取消下ticket保险一点
    mrInfo?.tags?.map(({ group_project_name }) =>
      useInject(BitsService).mrUnbindTag(data.mrId, `ticket:integration:${data.version}`, group_project_name).catch(),
    );
    return 'ok';
  },
);

export const testMeego = Api(
  Post('/open/testMeego'),
  Data(
    z.object({
      meegoId: z.number(),
      type: z.string(),
    }),
  ),
  async ({ data }) => useInject(MeegoService).requestWorkItem('faceu', data.type, [data.meegoId]),
);

export const manualtriggerCronJob = Api(
  Query(z.object({ job: z.string() })),
  Get('/open/triggerCronJob'),
  async ({ query }) => await handleManualTrigger(useInject(HostCronJobTrigger), query.job),
);

export const test_updateAllExistFeatures = Api(
  Post('/open/updateAllExistFeatures'),
  Data(z.object({})),
  async ({ data }) => {
    await useInject(AcceptanceFeatureService).updateAllExistFeatures();
    return {
      result: 'ok',
    };
  },
);

export const test_syncExistAcceptanceFeatureIssue = Api(
  Post('/open/syncExistAcceptanceFeatureIssue'),
  Data(z.object({})),
  async ({ data }) => {
    await useInject(AcceptanceFeatureBitableService).syncExistAcceptanceFeatureIssue();
    return {
      result: 'ok',
    };
  },
);

export const listAcceptInProgressFeature = Api(
  Get('/open/listAcceptInProgressFeature'),
  async () => await useInject(AcceptanceFeatureDao).listAcceptInProgressFeature(),
);

export const listAcceptExpireDDLFeature = Api(
  Get('/open/listAcceptExpireDDLFeature'),
  async () => await useInject(AcceptanceFeatureDao).listAcceptExpireDDLFeature(),
);

export const listSubmitTestExpireDDLFeature = Api(
  Get('/open/listSubmitTestExpireDDLFeature'),
  async () => await useInject(AcceptanceFeatureDao).listSubmitTestExpireDDLFeature(),
);

export const exportAcceptanceFeatures = Api(Get('/open/exportAcceptanceFeatures'), async () => {
  try {
    return await useInject(AcceptanceFeatureDao).export();
  } catch (e) {
    if (e instanceof Error) {
      return {
        message: e.message,
        stack: e.stack,
      };
    } else {
      return JSON.stringify(e);
    }
  }
});

export const exportAcceptanceIssues = Api(Get('/open/exportAcceptanceIssues'), async () => {
  try {
    return await useInject(AcceptanceFeatureIssueDao).export();
  } catch (e) {
    if (e instanceof Error) {
      return {
        message: e.message,
        stack: e.stack,
      };
    } else {
      return JSON.stringify(e);
    }
  }
});

export const exportAcceptanceOperations = Api(
  Get('/open/exportAcceptanceOperations'),
  async () => await useInject(AcceptanceOperationDao).list({}),
);

export const debugAcceptanceBitable = Api(
  Get('/open/debugAcceptanceBitable'),
  async () => await useInject(AcceptanceFeatureBitableService).manualHandleBitableRecordChanged('faceu', 5285235331),
);

export const debugLynxUpdate = Api(
  Post('/open/debug/updateLynx'),
  Data(
    z.object({
      sourceBranch: z.string(),
      packageID: z.string(),
    }),
  ),
  async ({ data }) => {
    const params: CodeProcessorParams = {
      pipelineId: 62689,
      operator: 'xuyutong.1998',
      finishType: FinishType.MR,
      buildParams: [
        {
          id: 2,
          inputs: [
            {
              name: 'MAIN_GIT_BRANCH',
              value: data.sourceBranch,
            },
          ],
        },
        {
          id: 89483,
          inputs: [
            {
              name: 'PACKAGEIDS',
              value: data.packageID,
            },
            {
              name: 'IS_CAPCUT',
              value: 'true',
            },
            {
              name: 'JWT_TOKEN',
              value:
                'eyJhbGciOiJSUzI1NiIsImtpZCI6IiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************.wUUavlqikdxbNujrMW9_jNHiscXRZDM1aa5U550IWFm0ESycQh-aLgTJFYwiO5y5sPCrKx5VjLFNFrwvM3vi_WCZMj37hX5NuxlGs7151wJ9Dczuy5gY63jeSBUjQ31PSTvT-8f2uHfLyeS8mkdDyk8SSb1tkJ7sO7ehvT9dXsc',
            },
          ],
        },
      ],
    };
    await useInject(CodeProcessor).triggerJob(params);
  },
);

export const testMrAttrCommand = Api(
  Post('/open/debug/testMrAttrCommand'),
  Data(
    z.object({
      chatId: z.string(),
      rawData: z.string(),
    }),
  ),
  async ({ data }) => {
    await useInject(MRProfilerHostService).handleMrAttrMessage(data.chatId, data.rawData);
    return 'ok';
  },
);

export const sendMsg = Api(
  Post('/open/debug/send_msg'),
  Data(
    z.object({
      name: z.string(),
      category: z.string(),
      subCategory: z.string(),
    }),
  ),
  async ({ data }) => {
    await useInject(MessageService).sendNormalMsg(
      {
        category: data.category,
        subCategory: data.subCategory,
        name: data.name,
        msgContent: 'testtesttesttest',
        type: MsgType.GroupChat,
        strategy: MsgStrategy.Manual,
        larkType: MessageType.text,
      } as MsgTemplate,
      UserIdType.chatId,
      'oc_729b199d5332b33d64ba14263e78e13c',
    );
    return 'ok';
  },
);

export const sendLegacyMsg = Api(
  Post('/open/debug/send_legacy_msg'),
  Data(
    z.object({
      title: z.string(),
      content: z.string(),
    }),
  ),
  async ({ data }) => {
    const card = useInject(LarkCardService).buildBaseCard({
      title: data.title,
      template: CardTemplate.blue,
      config: {
        enable_forward: false,
      },
    });
    card.elements = [
      {
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: data.content,
        },
      } as CardElement,
    ];
    const ret = await useInject(MessageService).sendLegacyMsg(
      UserIdType.chatId,
      'oc_729b199d5332b33d64ba14263e78e13c',
      JSON.stringify(card),
    );
    return {
      ret,
    };
  },
);

export const mrNotify = Api(Query(z.object({ mrId: z.string() })), Get('/open/debug/mrNotify'), async ({ query }) => {
  const mrInfo = await useInject(BitsService).getMrInfo({ mrId: Number(query.mrId) });
  if (mrInfo) {
    const config = JSON.stringify([
      {
        name: '数据库改动确认',
        desc: '检测到有数据库相关改动，请确认是否升级数据库版本号并验证升级场景',
        repo_id: 40279,
        branch_regex: '(^release/+[0-9.]+$)|(^overseas/release/+[0-9.]+$)|(^develop+$)',
        match_type: 'ModifyFileContent',
        match_regex: [
          'import androidx.room.Database',
          'import androidx.room.RoomDatabase',
          'import androidx.room.Entity',
          'import androidx.room.migration.Migration',
          'sortTopLevelComponentsIfNeed',
        ],
        approval_users: ['weijingdong', 'liuyunjuan'],
      },
    ]);
    const res = await useInject(MrChangeReviewService).checkChangeApproval(mrInfo, { custom: config } as RuleConfig);
    return res;
  }
  return 'ok';
});

export const migrateBuildRecord = Api(Get('/open/debug/migrateBuildRecord'), async () => {
  const lvAndroid = await CustomBuildModel.updateMany(
    {
      $and: [
        { appId: { $exists: false } },
        {
          $or: [{ 'buildParams.arch': { $exists: false } }, { 'buildParams.arch': 'Android' }],
        },
        {
          $or: [{ 'buildParams.isOversea': { $exists: false } }, { 'buildParams.isOversea': false }],
        },
      ],
    },
    {
      $set: {
        appId: AppSettingId.LV_ANDROID,
      },
    },
  ).exec();
  const ccAndroid = await CustomBuildModel.updateMany(
    {
      $and: [
        { appId: { $exists: false } },
        {
          $or: [{ 'buildParams.arch': { $exists: false } }, { 'buildParams.arch': 'Android' }],
        },
        {
          'buildParams.isOversea': true,
        },
      ],
    },
    {
      $set: {
        appId: AppSettingId.CC_ANDROID,
      },
    },
  ).exec();
  const lvIOS = await CustomBuildModel.updateMany(
    {
      $and: [
        { appId: { $exists: false } },
        {
          'buildParams.arch': 'iOS',
        },
        {
          $or: [{ 'buildParams.isOversea': { $exists: false } }, { 'buildParams.isOversea': false }],
        },
      ],
    },
    {
      $set: {
        appId: AppSettingId.LV_IOS,
      },
    },
  ).exec();
  const ccIOS = await CustomBuildModel.updateMany(
    {
      $and: [
        { appId: { $exists: false } },
        {
          'buildParams.arch': 'iOS',
        },
        {
          'buildParams.isOversea': true,
        },
      ],
    },
    {
      $set: {
        appId: AppSettingId.CC_IOS,
      },
    },
  ).exec();
  return {
    lvAndroid,
    ccAndroid,
    lvIOS,
    ccIOS,
  };
});

export const debug_crucial_version = Api(
  Post('/open/debug/update_crucial_version'),
  Data(
    z.object({
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const tmpVersion = data.version;
    for (const appId of [AppSettingId.LV_IOS, AppSettingId.CC_IOS, AppSettingId.LV_ANDROID, AppSettingId.CC_ANDROID]) {
      const version = [AppSettingId.CC_ANDROID, AppSettingId.CC_IOS].includes(appId)
        ? lv2ccVersion(tmpVersion)
        : tmpVersion;

      const versionFeatureService = useInject(VersionFeatureService);
      const versionProcessInfoDao = useInject(VersionProcessInfoDao);
      // const larkCard = useInject(LarkCardService);
      // const v = useInject(ReleasePlatformMessageService);
      const crucialFeatures = await versionFeatureService.getCrucialFeatures(appId, version);
      if (crucialFeatures && crucialFeatures?.length > 0) {
        const versionInfo = await versionProcessInfoDao.findOneByCriteria({ app_id: appId, version: data.version });
        if (versionInfo) {
          await versionProcessInfoDao.updateVersionCrucial(versionInfo, true);
          // const versionInfo = await versionProcessInfoDao.findOneByCriteria({
          //   app_id: appId,
          //   version,
          // });
          console.info(
            `find crucial version. appId ${appId} version ${version}, crucialFeatures: ${JSON.stringify(crucialFeatures)}`,
          );
          // const crucialVersionInfoCard = larkCard.buildCrucialVersionInfoCard(version, crucialFeatures, versionInfo);
          // console.info(crucialVersionInfoCard);
          // await v.sendVersionMessageToGroup(
          //   '重保版本信息推送',
          //   VersionTransactionType.VersionInfoSync,
          //   MsgStrategy.Auto,
          //   crucialVersionInfoCard,
          //   versionInfo,
          //   '重保版本信息推送',
          //   ReleasePlatformMessageGroupType.VersionReleaseGroup,
          //   [],
          //   ReleasePlatformMessageSource.InformationSync,
          //   '重保版本信息通知',
          // );
        }
      }
    }
  },
);

export const debug_tbc_code_sync = Api(Post('/open/debug/tbc_code_sync'), Data(z.any()), async ({ data }) => {
  const service = useInject(TBCCodeSyncService);
  return await service.syncCode(PlatformType.iOS, 'develop');
});

export const debug_tbc_code_sync_notify = Api(
  Post('/open/debug/tbc_code_sync_notify'),
  Data(
    z.object({
      jobId: z.number(),
      retCode: z.number(),
      timestamp: z.number(),
      appName: z.string(),
      syncBranch: z.string(),
      message: z.string().optional(),
      commitHash: z.string().optional(),
      commitHashUrl: z.string().optional(),
      commitMessage: z.string().optional(),
      sendPrivate: z.boolean().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(TBCCodeSyncService);
    return await service.notifyResult(data);
  },
);

export const inviteUser2Group = Api(
  Post('/open/debug/inviteUser2Group'),
  Data(
    z.object({
      chatId: z.string(),
      email: z.string(),
    }),
  ),
  async ({ data }) => {
    const lark = useInject(LarkService);
    const user = await lark.getUserInfoByEmail(data.email);
    if (user) {
      await lark.addUserToChatGroup(data.chatId, UserIdType.openId, [user.open_id]);
    }
    const bus = useInject(BusAssistService);
    const spaces = getBitsSpacesById(AppSettingId.CC_ANDROID);
    return {
      spaces,
      mrs: await bus.potentialMrSearch(spaces),
    };
  },
);
export const test_pc_circuit_breaker_notify = Api(
  Post('/open/test/pc_circuit_breaker_notify'),
  Data(z.any()),
  async ({ data }) => {
    const service = useInject(HostCronJobTrigger);
    return {
      msg: 'okk debug',
      result: await service.handleGrayCircuitBreakerNotify(),
    };
  },
);

export const addLvTicket = Api(
  Post('/open/add_lv_ticker'),
  Data(
    z.object({
      mrId: z.number(),
      lvVersion: z.string(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(CollectAbnormalMultiMrService);
    return service.addLvCcVersionTicketAuto(data.mrId, data.lvVersion);
  },
);

export const versionGrayReleaseInfo = Api(
  Post('/open/debug/version_gray_release_information'),
  Data(
    z.object({
      app_id: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const versionProcessInfoDao = useInject(VersionProcessInfoDao);
    const versionInfo = await versionProcessInfoDao.findOneByCriteria({ app_id: data.app_id, version: data.version });
    const grayStageInfo = versionInfo?.version_stages?.find(
      v => v.stage_name === 'retouch_adr_gray' || v.stage_name === 'gray',
    );
    const grayReleaseInfo = grayStageInfo?.sub_stages?.map((v, index) => ({
      round: index + 1,
      versionCode: v?.extra_data?.versionCode,
      releaseTime: v?.extra_data?.releaseTime,
    }));
    return {
      app_id: data.app_id,
      version: data.version,
      grayReleaseInfo,
    };
  },
);

export const debugCircuitBreakerNotify = Api(
  Post('/open/debug/circuit_breaker_notify'),
  Data(
    z.object({
      app_id: z.number(),
      ticket_id: z.string(),
    }),
  ),
  async ({ data }) => {
    const circuitBreakerService = useInject(CircuitBreakerService);
    const infoList = await circuitBreakerService.getCircuitBreakerRecords(
      data.app_id,
      CircuitBreakerTicketType.Slardar,
      new Map<string, any>([['ticketId', data.ticket_id]]),
    );
    const ticketInfo: CircuitBreakerTicket = infoList.data[0];
    if (ticketInfo) {
      await circuitBreakerService.buildAndSendCircuitBreakerNotify(
        data.app_id,
        ticketInfo,
        CircuitBreakerCallbackActionType.StartProcess,
      );
    }
  },
);

export const getVersionMultiLanguageDiff = Api(
  Post('/open/version/multi-language-diff'),
  Data(
    z.object({
      app_id: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const bits = useInject(BitsService);
    const codebase = useInject(CodebaseService);
    interface KeyDiffModel {
      key: string;
      type: 'modify' | 'add';
      newValue: string;
      oldValue: string;
    }
    interface FileDiffModel {
      path: string;
      diff: KeyDiffModel[];
    }
    interface ChangeDiffModel {
      changeId: number;
      diff: FileDiffModel[];
    }
    const getIntegrationAppId = () => {
      switch (data.app_id) {
        case AppSettingId.CC_IOS:
          return 2020095701;
        case AppSettingId.CC_ANDROID:
          return 2020095699;
        default:
          return data.app_id;
      }
    };
    const integrationMrs = await bits.getIntegrationMrs({
      app_id: getIntegrationAppId(),
      version: data.version,
      mr_type: MrType.feature,
      mr_state: MrState.merged,
      last_id: 0,
      limit: 200,
    } as IntegrationMrsRequest);

    const integrationMrsSourceBranch = integrationMrs?.mr_lists?.map(it => it.source_branch) ?? [];
    const getGroupName = () => {
      switch (data.app_id) {
        case AppSettingId.LV_IOS:
          return 'LV-iOS';
        case AppSettingId.LV_ANDROID:
          return 'LV-Android';
        case AppSettingId.CC_IOS:
          return 'capcut_ios_new';
        case AppSettingId.CC_ANDROID:
          return 'capcut_android';
        default:
          return 'LV-iOS';
      }
    };
    const getProjectId = () => {
      switch (data.app_id) {
        case AppSettingId.LV_IOS:
          return 39995;
        case AppSettingId.LV_ANDROID:
          return 40279;
        case AppSettingId.CC_IOS:
          return 798331;
        case AppSettingId.CC_ANDROID:
          return 798270;
        default:
          return 39995;
      }
    };

    const mrList = await bits.searchAllMr(
      {
        group_name: getGroupName(),
        state: 'merged',
        keyword: 'update i18n translation',
      },
      200,
    );
    const result = [];
    for (const mrId of mrList) {
      const mrInfo = await bits.getMrInfo({ mrId, projectId: getProjectId() });
      if (
        !integrationMrsSourceBranch.includes(mrInfo?.target_branch ?? '') &&
        !integrationMrsSourceBranch.includes(mrInfo?.from_branch ?? '')
      ) {
        continue;
      }
      const integrationMrInfo = integrationMrs?.mr_lists.find(
        it => mrInfo?.target_branch === it.source_branch || mrInfo?.from_branch === it.source_branch,
      );

      if (mrInfo) {
        const changeInfo = await codebase.getChangeFromCodeBase(getProjectId(), mrInfo.iid);
        if (changeInfo) {
          const changeDetails = await codebase.batchGetChangeDetails([
            {
              change_id: changeInfo.id,
              repo_id: changeInfo.target.repo.id,
            },
          ]);
          const changesDiff: ChangeDiffModel[] = [];
          for (const changeDetailInfo of changeDetails.changes) {
            const filesDiff: FileDiffModel[] = [];
            for (const file of changeDetailInfo?.current_files ?? []) {
              const to_content = await codebase.getChangeContents(
                changeDetailInfo.target.repo.id,
                encodeURIComponent(file.path ?? ''),
                changeDetailInfo?.patch_sets?.at(-1)?.sha ?? '',
              );
              const from_content = await codebase.getChangeContents(
                changeInfo.target.repo.id,
                encodeURIComponent(file.path ?? ''),
                changeDetailInfo?.patch_sets?.at(-1)?.base_sha ?? '',
              );
              // 将from_content进行base64 decode并转换成json对象
              const fromContentJson = from_content?.content
                ? JSON.parse(new (Buffer as any).from(from_content?.content ?? '', 'base64').toString('utf8'))
                : {};
              const toContentJson = to_content?.content
                ? JSON.parse(new (Buffer as any).from(to_content?.content ?? '', 'base64').toString('utf8'))
                : {};
              // 比较fromContentJson和toContentJson，提取出toContentJson中和fromContentJson不同的key和value
              const diff: KeyDiffModel[] = Object.keys(toContentJson)
                .filter(it => toContentJson[it] !== fromContentJson[it])
                .map(
                  it =>
                    ({
                      key: it,
                      type: fromContentJson[it] ? 'modify' : 'add',
                      newValue: toContentJson[it],
                      oldValue: fromContentJson[it] ?? '',
                    }) as KeyDiffModel,
                );
              filesDiff.push({ path: file.path ?? '', diff } as FileDiffModel);
            }
            changesDiff.push({ changeId: changeDetailInfo.id, diff: filesDiff } as ChangeDiffModel);
          }
          result.push({
            mrInfo: integrationMrInfo,
            diff: changesDiff,
          });
          break;
        }
      }
    }
    return result;
  },
);
