import { Database, getModelForClass, Prop } from '@gulux/gulux/typegoose';
import { Inject, Injectable } from '@gulux/gulux';
import { CrashListInfo, CrashType, RemarkableDimensions } from '@shared/typings/slardar/crash/issueListSearch';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { DeviceLevel } from '@shared/common';
import { useInject } from '@edenx/runtime/bff';
import SlardarCrashIssueModel from './SlardarCrashIssueTable';
import AlarmVersionModel from './AlarmVersionInfoTable';
import MeegoService from '../service/meego';
import SlardarVersionMetricsModel, { MetricType } from './SlardarVersionMetricsTable';
import SlardarUserCountModel from './SlardarUserCountTable';
import {
  get_previous_version,
  get_previous_version_dreamina,
  IosTypeOf,
  versionCodeToVersion,
  VersionType,
} from '@shared/utils/version_utils';
import { pick, pickBy, uniqBy } from 'lodash';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { SelectedName } from '@shared/utils/offlineslardar';

@Database('main')
export class SlardarCrashIssueListTable {
  @Prop()
  aid: number;

  @Prop()
  issue_id: string;

  @Prop()
  crash_type: CrashType;

  @Prop()
  version_code: string;

  @Prop()
  version?: string;

  @Prop()
  count: number;

  @Prop()
  users: number;

  @Prop()
  status: string; // 处理状态，默认待处理

  @Prop()
  platform: SlardarPlatformType;

  @Prop()
  issue_level: number; // 优先级

  @Prop()
  issue_level_reason?: string; // 定级原因

  @Prop()
  issue_level_time?: number;

  @Prop()
  is_warning: boolean;

  @Prop()
  ranking: number;

  @Prop()
  remarkable_dimensions?: RemarkableDimensions[];

  @Prop()
  has_memory_graph?: boolean;

  @Prop()
  device_level: DeviceLevel;

  @Prop()
  memory_object_size?: number;

  @Prop()
  instance_num?: number;

  @Prop()
  rate?: string;
}

export interface DBSlardarCrashIssueList extends SlardarCrashIssueListTable {}

function field_spilt(d: DBSlardarCrashIssueList, isLeveled: boolean) {
  const replace_fields: string[] = [
    'count',
    'users',
    'status',
    'ranking',
    'is_warning',
    'remarkable_dimensions',
    'has_memory_graph',
    'issue_level_reason',
    'memory_object_size',
    'instance_num',
    'rate',
  ];
  if (isLeveled) {
    replace_fields.push('issue_level');
  }
  const to_set = pick(d, replace_fields);
  const to_insert = pickBy(d, (_, k) => !replace_fields.concat(['_id', '__bytedoc', '__v']).includes(k));
  return [to_set, to_insert];
}

@Injectable()
export default class SlardarCrashIssueListModel {
  private slardarCrashIssueModel = getModelForClass(SlardarCrashIssueListTable);

  @Inject()
  private logger: BytedLogger;

  async queryCreateBugIssueList(
    aid: number,
    versionCode: string,
    platform: SlardarPlatformType,
  ): Promise<DBSlardarCrashIssueList[]> {
    return this.slardarCrashIssueModel
      .where({
        aid,
        version_code: versionCode,
        platform,
        issue_level: { $lte: 1 },
        crash_type: {
          $nin: [CrashType.NativeOOM, CrashType.JavaOOM],
        },
      })
      .find()
      .lean();
  }

  async queryCreateBugIssueListByVersion(
    aid: number,
    version: string,
    platform: SlardarPlatformType,
  ): Promise<DBSlardarCrashIssueList[]> {
    return this.slardarCrashIssueModel
      .where({
        aid,
        version,
        platform,
        issue_level: { $lte: 1 },
        crash_type: {
          $in: [CrashType.NativeCrash, CrashType.JavaCrash, CrashType.JavaStartCrash, CrashType.ANR],
        },
      })
      .find()
      .lean();
  }

  async findPrevIssueListRanking(
    aid: number,
    issue_id: string,
    version_code: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
  ): Promise<number | undefined> {
    return (
      await this.slardarCrashIssueModel.findOne({
        aid,
        issue_id,
        version_code,
        platform,
        crash_type,
        device_level: DeviceLevel.ALL,
      })
    )?.ranking;
  }

  async findPrevIssueListUser(
    aid: number,
    issue_id: string,
    version_code: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
  ): Promise<number | undefined> {
    return (
      await this.slardarCrashIssueModel.findOne({
        aid,
        issue_id,
        version_code,
        platform,
        crash_type,
        device_level: DeviceLevel.ALL,
      })
    )?.users;
  }

  getFindPrevIssueListUser(
    aid: number,
    issue_id: string,
    version_code: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
  ): number | undefined {
    return this.slardarCrashIssueModel.findOne({
      aid,
      issue_id,
      version_code,
      platform,
      crash_type,
      device_level: DeviceLevel.ALL,
    })?.users;
  }

  async findPrevIssueListUsers(
    aid: number,
    issue_id: string,
    version_code: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
  ): Promise<number | undefined> {
    return (
      await this.slardarCrashIssueModel.findOne({
        aid,
        issue_id,
        version_code,
        platform,
        crash_type,
      })
    )?.users;
  }

  async findPrevIssueListLevel(
    aid: number,
    issue_id: string,
    version_code: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
  ): Promise<number | undefined> {
    return (
      await this.slardarCrashIssueModel.findOne({
        aid,
        issue_id,
        version_code,
        platform,
        crash_type,
        issue_level: DeviceLevel.ALL,
      })
    )?.issue_level;
  }

  async findPrevIssueListLevelByVersion(
    aid: number,
    issue_id: string,
    version: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
  ): Promise<number | undefined> {
    return (
      await this.slardarCrashIssueModel.findOne({
        aid,
        issue_id,
        version,
        platform,
        crash_type,
        issue_level: DeviceLevel.ALL,
      })
    )?.issue_level;
  }

  // /**
  //  * 保存前触发，将当前所有排名置为2001
  //  * @param version_code
  //  * @param version
  //  */
  // async reset(version_code: string, version?: string) {
  // 	return this.slardarCrashIssueModel.update({ version_code, version }, { ranking: 2001 });
  // }

  async save(doc: DBSlardarCrashIssueList[], isLeveled: boolean) {
    const n = doc.length;
    let i = 0;
    for (const d of doc) {
      const [to_set, to_insert] = field_spilt(d, isLeveled);
      const filter: { [key: string]: any } = {
        aid: d.aid,
        issue_id: d.issue_id,
        crash_type: d.crash_type,
        device_level: d.device_level,
      };
      if (d.version) {
        filter.version = d.version;
      } else {
        filter.version_code = d.version_code;
      }
      await this.slardarCrashIssueModel.updateOne(
        filter,
        {
          $set: to_set,
          $setOnInsert: to_insert,
        },
        {
          upsert: true,
        },
      );
      if (++i % 100 === 0) {
        this.logger.info(`Saved ${i} of ${n} issue lists`);
      }
    }
    this.logger.info(`Finished saving ${n} issue lists`);
  }

  async findBaseSlardarCrashIssue(
    aid: number,
    platform: SlardarPlatformType,
    versionCode: string,
    crashType: CrashType,
    issueIds: string[],
    deviceLevel: DeviceLevel,
  ) {
    return await this.slardarCrashIssueModel
      .where({
        aid,
        device_level: deviceLevel,
        platform: { $in: [platform] },
        version_code: { $in: [versionCode] },
        crash_type: { $in: [crashType] },
        issue_id: { $in: issueIds },
      })
      .find()
      .exec();
  }

  async findLastVersionSlardarCrashIssue(
    aid: number,
    platform: SlardarPlatformType,
    version: string,
    crashType: CrashType,
    issueIds: string[],
    deviceLevel: DeviceLevel,
  ) {
    return await this.slardarCrashIssueModel
      .where({
        aid,
        device_level: deviceLevel,
        platform: { $in: [platform] },
        version,
        crash_type: { $in: [crashType] },
        issue_id: { $in: issueIds },
      })
      .find()
      .exec();
  }

  async findONLINEversionSlardarCrashIssue(
    aid: number,
    platform: SlardarPlatformType,
    versions: string[],
    deviceLevel: DeviceLevel = DeviceLevel.ALL,
  ) {
    return this.slardarCrashIssueModel.find(
      {
        aid,
        version_code: { $in: versions },
        platform,
        device_level: deviceLevel,
      },
      {
        issue_id: 1,
      },
    );
  }

  async buildSubQueryParamByVersion(
    aid: number,
    platform: SlardarPlatformType,
    version: string,
    crashType: CrashType,
    filterOOM?: boolean,
    isNew?: boolean,
    deviceLevel: DeviceLevel = DeviceLevel.ALL,
  ) {
    let filterOOMIds: string[] = [];
    let filterOldIds: string[] = [];
    const allIds = await this.slardarCrashIssueModel.find(
      { aid, platform, version, crash_type: crashType, device_level: deviceLevel },
      { issue_id: 1 },
    );
    if (filterOOM) {
      filterOOMIds = (
        await this.slardarCrashIssueModel.find(
          {
            aid,
            version,
            platform,
            crash_type: { $in: [CrashType.NativeOOM, CrashType.JavaOOM] },
            device_level: deviceLevel,
          },
          {
            issue_id: 1,
          },
        )
      ).map(it => it.issue_id as string);
      filterOOMIds = filterOOMIds.concat(
        await useInject(SlardarCrashIssueModel).queryOOMSlardarIssueIds(
          aid,
          allIds.map(v => v.issue_id),
          platform,
        ),
      );
    }
    if (isNew) {
      filterOldIds = await useInject(SlardarCrashIssueModel).queryNewSlardarIssueIds(
        aid,
        allIds.map(v => v.issue_id),
        version,
      );
    }
    return Array.from(new Set(filterOOMIds.concat(filterOldIds)));
  }

  async buildSubQueryParam(
    aid: number,
    platform: SlardarPlatformType,
    versionCode: string,
    crashType: CrashType,
    filterOOM?: boolean,
    isNew?: boolean,
    deviceLevel: DeviceLevel = DeviceLevel.ALL,
  ) {
    let filterOOMIds: string[] = [];
    let historyIssueIds: string[] = [];
    if (filterOOM) {
      filterOOMIds = (
        await this.slardarCrashIssueModel.find(
          {
            aid,
            version_code: versionCode,
            platform,
            crash_type: { $in: [CrashType.NativeOOM, CrashType.JavaOOM] },
            device_level: deviceLevel,
          },
          {
            issue_id: 1,
          },
        )
      ).map(it => it.issue_id as string);
      const allIds = await this.slardarCrashIssueModel.find(
        { aid, platform, version_code: versionCode, crash_type: crashType, device_level: deviceLevel },
        { issue_id: 1 },
      );
      filterOOMIds = filterOOMIds.concat(
        await useInject(SlardarCrashIssueModel).queryOOMSlardarIssueIds(
          aid,
          allIds.map(v => v.issue_id),
          platform,
        ),
      );
    }
    if (isNew) {
      const alarmVersionModel = useInject(AlarmVersionModel);
      const last_version = await alarmVersionModel.findAndroidCompareVersion(aid, versionCode);
      // 计算出大版本号
      const Version = versionCodeToVersion(last_version!.version_code);
      const last_issueIds = (
        await this.slardarCrashIssueModel.find(
          {
            aid,
            version: Version,
            platform,
            crash_type: crashType,
            device_level: deviceLevel,
          },
          { issue_id: 1 },
        )
      ).map(v => v.issue_id);
      historyIssueIds = historyIssueIds.concat(last_issueIds);
    }
    return Array.from(new Set(filterOOMIds.concat(historyIssueIds)));
  }

  async querySlardarCrashIssueListByIssueIdAndVersionCode(
    aid: number,
    platform: SlardarPlatformType,
    issue_ids: string[],
    version_code: string,
  ): Promise<DBSlardarCrashIssueList[]> {
    return this.slardarCrashIssueModel
      .where({
        aid,
        issue_id: { $in: issue_ids },
        version_code,
        platform,
      })
      .find()
      .lean();
  }

  async querySlardarCrashIssueList(
    aid: number,
    platform: SlardarPlatformType,
    crashType: CrashType,
    pageSize: number,
    current: number,
    filters: {
      columnKey: string;
      filter: any[];
    }[] = [],
    filterOOM?: boolean,
    isNew?: boolean,
    isMemoryGraph?: boolean,
    versionCode?: string,
    version?: string,
    deviceLevel: DeviceLevel = DeviceLevel.ALL,
    sorter?: Record<string, 'ascend' | 'descend'>,
  ): Promise<{
    total: number;
    data: DBSlardarCrashIssueList[];
    totalDone: number;
    totalOwner: number;
  }> {
    const filterParams = filters.reduce(
      (acc, curr) => {
        acc[curr.columnKey as keyof DBSlardarCrashIssueList] = { $in: curr.filter };
        return acc;
      },
      {} as Partial<Record<keyof DBSlardarCrashIssueList, any>>,
    );
    filterParams.aid = aid;
    filterParams.device_level = deviceLevel;
    if (versionCode) {
      filterParams.version_code = { $in: [versionCode] };
    } else if (version) {
      filterParams.version = { $in: [version] };
    }
    filterParams.platform = { $in: [platform] };
    filterParams.crash_type = { $in: [crashType] };
    if (versionCode && (filterOOM || isNew)) {
      filterParams.issue_id = {
        $nin: await this.buildSubQueryParam(aid, platform, versionCode, crashType, filterOOM, isNew, deviceLevel),
      };
    }
    if (version && (filterOOM || isNew)) {
      filterParams.issue_id = {
        $nin: await this.buildSubQueryParamByVersion(aid, platform, version, crashType, filterOOM, isNew, deviceLevel),
      };
    }
    if (isMemoryGraph) {
      filterParams.has_memory_graph = true;
    }
    const query = this.slardarCrashIssueModel.where(filterParams);
    const sortOptions: { [key: string]: 1 | -1 } = {};
    if (sorter) {
      for (const [key, value] of Object.entries(sorter)) {
        if (value) {
          sortOptions[key] = value === 'ascend' ? 1 : -1;
        }
      }
    }
    if (Object.keys(sortOptions).length > 0) {
      query.sort(sortOptions);
    } else {
      query.sort({ ranking: 1 });
    }
    const finder = query.skip((current - 1) * pageSize).limit(pageSize);
    // const finder = this.slardarCrashIssueModel
    //   .where(filterParams)
    //   .sort({ ranking: 1 })
    //   .skip((current - 1) * pageSize)
    //   .limit(pageSize);
    const totalSize = await this.slardarCrashIssueModel.where(filterParams).count();
    const result = await finder.find().lean();
    filterParams.ranking = { $lte: 100 };
    const issue_ids = (await this.slardarCrashIssueModel.where(filterParams).find()).map(res => res.issue_id);

    const slardarCrashIssueModel = useInject(SlardarCrashIssueModel);
    const totalOwner = await slardarCrashIssueModel.querySlardarCrashIssueSum(aid, issue_ids);

    filterParams.status = { $in: ['done'] };
    const totalDone = await this.slardarCrashIssueModel.where(filterParams).count();
    return { data: result, total: totalSize, totalDone, totalOwner };
  }

  async queryOfflineSlardarCrashIssueList(
    aid: number,
    platform: SlardarPlatformType,
    crashType: CrashType,
    pageSize: number,
    current: number,
    filters: {
      columnKey: string;
      filter: any[];
    }[] = [],
    filterOOM?: boolean,
    isNew?: boolean,
    isMemoryGraph?: boolean,
    versionCode?: string,
    version?: string,
    deviceLevel: DeviceLevel = DeviceLevel.ALL,
    stage?: SelectedName, // 新增 stage 参数，用于区分封板前/后 before|after
  ): Promise<{
    total: number;
    data: DBSlardarCrashIssueList[];
    totalDone: number;
    totalOwner: number;
  }> {
    const filterParams = filters.reduce(
      (acc, curr) => {
        acc[curr.columnKey as keyof DBSlardarCrashIssueList] = { $in: curr.filter };
        return acc;
      },
      {} as Partial<Record<keyof DBSlardarCrashIssueList, any>>,
    );
    filterParams.aid = aid;
    filterParams.device_level = deviceLevel;
    const zeros = aid === 1775 ? '00000' : aid === 3006 ? '0000' : '';
    if (version) {
      filterParams.version_code =
        platform === SlardarPlatformType.Android ? `${version.replaceAll('.', '')}${zeros}` : `${version}.1`;
      // 根据 stage 参数动态设置 version 查询条件
      if (stage === SelectedName.AfterIntegration) {
        // 封板后
        filterParams.version = { $in: [`${version}-later`] };
      } else {
        // 默认为 'before' 封板前
        filterParams.version = { $in: [`${version}-offline`] };
      }
    }
    filterParams.platform = { $in: [platform] };
    filterParams.crash_type = { $in: [crashType] };
    if (version && (filterOOM || isNew)) {
      filterParams.issue_id = {
        $nin: await this.buildSubQueryParamByVersion(aid, platform, version, crashType, filterOOM, isNew, deviceLevel),
      };
    }
    if (isMemoryGraph) {
      filterParams.has_memory_graph = true;
    }
    const finder = this.slardarCrashIssueModel
      .where(filterParams)
      .sort({ ranking: 1 })
      .skip((current - 1) * pageSize)
      .limit(pageSize);
    const totalSize = await this.slardarCrashIssueModel.where(filterParams).count();
    const result = await finder.find().lean();
    filterParams.ranking = { $lte: 100 };
    const issue_ids = (await this.slardarCrashIssueModel.where(filterParams).find()).map(res => res.issue_id);

    const slardarCrashIssueModel = useInject(SlardarCrashIssueModel);
    const totalOwner = await slardarCrashIssueModel.querySlardarCrashIssueSum(aid, issue_ids);

    filterParams.status = { $in: ['done'] };
    const totalDone = await this.slardarCrashIssueModel.where(filterParams).count();
    return { data: result, total: totalSize, totalDone, totalOwner };
  }

  async find(query: Partial<Record<keyof DBSlardarCrashIssueList, any>>): Promise<DBSlardarCrashIssueList[]> {
    return this.slardarCrashIssueModel.find(query);
  }

  async delete_all() {
    return this.slardarCrashIssueModel.deleteMany({});
  }

  async delete_version(aid: number, version: string) {
    return this.slardarCrashIssueModel.deleteMany({ aid, version, version_code: '' });
  }

  async delete_oom() {
    const alarmVersionModel = useInject(AlarmVersionModel);
    const versions = await alarmVersionModel.findVersionsIn(21 * 24 * 30, []);
    const v: string[] = [];
    for (const d of versions) {
      if (d.platform === SlardarPlatformType.Android) {
        v.push(d.version_code);
      }
    }
    return this.slardarCrashIssueModel.deleteMany({
      version_code: { $in: v },
      crash_type: { $in: [CrashType.JavaOOM, CrashType.NativeOOM] },
    });
  }

  async updateSlardarCrashWarn(
    aid: number,
    platform: SlardarPlatformType,
    versionCode: string,
    issue_id: string,
    crashType: CrashType,
    is_warning?: boolean,
  ) {
    if (is_warning || is_warning === false) {
      await this.slardarCrashIssueModel.updateOne(
        {
          aid,
          issue_id,
          version_code: versionCode,
          crash_type: crashType,
        },
        {
          $set: {
            is_warning,
          },
        },
      );
    }
  }

  async queryOOMIds(
    aid: number,
    platform: SlardarPlatformType,
    versionCode: string,
    deviceLevel: DeviceLevel = DeviceLevel.ALL,
  ) {
    const allIds = await this.slardarCrashIssueModel.find(
      {
        aid,
        platform,
        version_code: versionCode,
        device_level: deviceLevel,
      },
      { issue_id: 1 },
    );
    let filterOOMIds: string[] = (
      await this.slardarCrashIssueModel.find(
        {
          aid,
          version_code: versionCode,
          platform,
          crash_type: { $in: [CrashType.NativeOOM, CrashType.JavaOOM] },
          device_level: deviceLevel,
        },
        {
          issue_id: 1,
        },
      )
    ).map(it => it.issue_id as string);
    filterOOMIds = filterOOMIds.concat(
      await useInject(SlardarCrashIssueModel).queryOOMSlardarIssueIds(
        aid,
        allIds.map(v => v.issue_id),
        platform,
      ),
    );
    return filterOOMIds;
  }

  async queryWarningIssueList(
    aid: number,
    platform: SlardarPlatformType,
    versionCode: string,
    isWarning: boolean,
  ): Promise<DBSlardarCrashIssueList[]> {
    const filterParams: {
      [p: string]: any;
    } = {
      aid,
      platform,
      version_code: versionCode,
      device_level: DeviceLevel.ALL,
    };
    if (isWarning) {
      filterParams.is_warning = true;
    }
    if (platform === SlardarPlatformType.Android && isWarning) {
      filterParams.issue_id = { $nin: await this.queryOOMIds(aid, platform, versionCode) };
    }
    const finder = this.slardarCrashIssueModel.where(filterParams);
    const result = await finder.find().lean();
    return result;
  }

  async addAndroidPrevVersionRanking(
    aid: number,
    crashListInfos: CrashListInfo[],
    version: string,
    platform: SlardarPlatformType,
    issueIds: string[],
    crashType: CrashType,
    deviceLevel: DeviceLevel,
  ) {
    const prev_version = aid !== 581595 ? get_previous_version(version) : get_previous_version_dreamina(version);
    const baseInfos = await this.findLastVersionSlardarCrashIssue(
      aid,
      platform,
      prev_version,
      crashType,
      issueIds,
      deviceLevel,
    );
    for (const crashListInfo of crashListInfos) {
      if (baseInfos) {
        baseInfos.forEach(baseInfo => {
          if (baseInfo.issue_id === crashListInfo.issue_id) {
            crashListInfo.baseRanking = baseInfo.ranking;
            crashListInfo.base_user_rate = baseInfo.rate;
            crashListInfo.user_rate = crashListInfo.rate;
          }
        });
      }
    }
  }

  async addPrevRanking(
    aid: number,
    crashListInfos: CrashListInfo[],
    versionCode: string,
    platform: SlardarPlatformType,
    issueIds: string[],
    crashType: CrashType,
    deviceLevel: DeviceLevel,
  ) {
    const alarmVersionModel = useInject(AlarmVersionModel);
    const slardarUserCountModel = useInject(SlardarUserCountModel);
    const baseVersionDB = await alarmVersionModel.findAndroidCompareVersion(aid, versionCode);
    const baseInfos = await this.findBaseSlardarCrashIssue(
      aid,
      platform,
      baseVersionDB ? baseVersionDB.version_code : versionCode,
      crashType,
      issueIds,
      deviceLevel,
    );
    const cur_user_count = await slardarUserCountModel.lastTimestamp(aid, versionCode, deviceLevel);
    const cur_user = cur_user_count ? cur_user_count.count : undefined;
    const prev_user_count = baseVersionDB
      ? await slardarUserCountModel.lastTimestamp(aid, baseVersionDB.version_code, deviceLevel)
      : undefined;
    const prev_user = prev_user_count ? prev_user_count.count : undefined;
    for (const crashListInfo of crashListInfos) {
      if (cur_user) {
        crashListInfo.user_rate = ((crashListInfo.users / cur_user) * 1000).toFixed(10);
      }
      if (baseInfos) {
        baseInfos.forEach(baseInfo => {
          if (baseInfo.issue_id === crashListInfo.issue_id) {
            crashListInfo.baseRanking = baseInfo.ranking;
            if (prev_user) {
              crashListInfo.base_user_rate = ((baseInfo.users / prev_user) * 1000).toFixed(10);
            }
          }
        });
      }
    }
  }

  async addiOSPrevInfo(
    aid: number,
    crashListInfos: CrashListInfo[],
    versionCode: string,
    platform: SlardarPlatformType,
    issueIds: string[],
    crashType: CrashType,
    deviceLevel: DeviceLevel,
  ) {
    const alarmVersionModel = useInject(AlarmVersionModel);
    const slardarVersionMetricsModel = useInject(SlardarVersionMetricsModel);
    const slardarUserCountModel = useInject(SlardarUserCountModel);
    const version = versionCodeToVersion(versionCode);
    const baseVersionDB = await alarmVersionModel.findiOSLastVersion(
      aid,
      IosTypeOf(versionCode, aid === 3006) !== VersionType.TF_GRAY ? VersionType.FULL : VersionType.TF_GRAY,
      version,
    );
    const cur_count = await slardarVersionMetricsModel.getValue(
      aid,
      versionCode,
      crashType === CrashType.OOMCrash ? MetricType.OOM_START_COUNT : MetricType.START_COUNT,
      deviceLevel,
    );
    const cur_user_count = await slardarUserCountModel.lastTimestamp(aid, versionCode, deviceLevel);
    const cur_user = cur_user_count ? cur_user_count.count : undefined;
    const prev_count = baseVersionDB
      ? await slardarVersionMetricsModel.getValue(
          aid,
          baseVersionDB.version_code,
          crashType === CrashType.OOMCrash ? MetricType.OOM_START_COUNT : MetricType.START_COUNT,
          deviceLevel,
        )
      : undefined;
    const prev_user_count = baseVersionDB
      ? await slardarUserCountModel.lastTimestamp(aid, baseVersionDB.version_code, deviceLevel)
      : undefined;
    const prev_user = prev_user_count ? prev_user_count.count : undefined;
    const baseInfos = await this.findBaseSlardarCrashIssue(
      aid,
      platform,
      baseVersionDB ? baseVersionDB.version_code : versionCode,
      crashType,
      issueIds,
      deviceLevel,
    );
    for (const crashListInfo of crashListInfos) {
      if (cur_user) {
        crashListInfo.user_rate = ((crashListInfo.users / cur_user) * 1000).toFixed(10);
      }
      if (cur_count) {
        crashListInfo.count_rate = ((crashListInfo.count / cur_count) * 1000).toFixed(10);
      }
      if (baseInfos) {
        baseInfos.forEach(baseInfo => {
          if (baseInfo.issue_id === crashListInfo.issue_id) {
            crashListInfo.baseRanking = baseInfo.ranking;
            if (prev_user) {
              crashListInfo.base_user_rate = ((baseInfo.users / prev_user) * 1000).toFixed(10);
            }
            if (prev_count) {
              crashListInfo.base_count_rate = ((baseInfo.count / prev_count) * 1000).toFixed(10);
            }
          }
        });
      }
    }
  }

  async addMeegoInfo(crashListInfos: CrashListInfo[]) {
    const meegoService = useInject(MeegoService);
    const meegoIds: number[] = [];
    for (const d of crashListInfos) {
      if (d.meego_url) {
        meegoIds.push(meegoService.getMeegoIdByUrl(d.meego_url));
      }
    }
    const { data } = await meegoService.getMeegoInfo(meegoIds);
    for (const d of crashListInfos) {
      if (d.meego_url) {
        const meegoId = meegoService.getMeegoIdByUrl(d.meego_url).toString();
        if (meegoId in data) {
          d.meego_level = data[meegoId].priority;
          d.meego_status = data[meegoId].status;
          d.meego_operator = data[meegoId].issue_operator;
        }
      }
    }
  }

  async findP0P1ForVersion(aid: number, version: string) {
    const version_model = useInject(AlarmVersionModel);
    return await version_model
      .genericFind({
        aid,
        version,
        version_step: {
          $lt: 16,
        },
      })
      .then(it => it.map(c => c.version_code))
      .then(versions =>
        this.find({
          aid,
          version_code: {
            $in: versions,
          },
          issue_level: {
            $lte: 1,
          },
          crash_type: {
            $nin: [CrashType.JavaOOM, CrashType.NativeOOM],
          },
          platform: SlardarPlatformType.Android,
        }),
      )
      .then(list => uniqBy(list, 'issue_id'));
  }

  /**
   * 返回聚合定级后(issue_level<=2>)的Issue列表
   * @param aid
   * @param version
   */
  async queryAggregatedLeveled(aid: number, version: string) {
    return await this.find({
      aid,
      version,
      version_code: '',
      platform: SlardarPlatformType.Android,
      device_level: DeviceLevel.ALL,
      crash_type: {
        $in: [CrashType.JavaCrash, CrashType.NativeCrash, CrashType.ANR],
      },
    });
  }

  async findIssuesByMeegoVersion(aid: number, version: string, issue_id: string) {
    const version_model = useInject(AlarmVersionModel);
    return await version_model
      .genericFind({
        aid,
        version,
        version_step: {
          $lt: 16,
        },
      })
      .then(it => it.map(c => c.version_code))
      .then(versions =>
        this.find({
          aid,
          version_code: {
            $in: versions,
          },
          issue_id,
          // issue_level: {
          // 	$lte: 1,
          // },
          platform: SlardarPlatformType.Android,
          device_level: DeviceLevel.ALL,
        }),
      )
      .then(list => list.sort((a, b) => b.version_code.localeCompare(a.version_code)));
  }

  // 更新提单原因
  async UpdateIssueLevelReason(
    issue_id: string,
    issue_level: number,
    levelReason: string,
    version: string | undefined,
    version_code: string | undefined,
  ) {
    if (version) {
      await this.slardarCrashIssueModel.updateOne(
        { issue_id, version },
        { $set: { issue_level_reason: levelReason, issue_level } },
      );
    } else {
      await this.slardarCrashIssueModel.updateOne(
        { issue_id, version_code },
        { $set: { issue_level_reason: levelReason, issue_level } },
      );
    }
  }
}
