export interface TTPCodebaseInfo {
  project_id: number;
  repo_id: number;
  artifactId: string;
  groupId: string;
  version_suffix: string; // flavor 组件升级的时候，有的组件会自动带上后缀
}

export interface TTPSubCodebaseAndroidConfig {
  sub_ttp_codebase: TTPCodebaseInfo[];
  sub_normal_ttp_codebase: TTPCodebaseInfo[];
}

export interface BuildInfo {
  id: number;
  history_id: number;
  profile: string;
  status: number;
  build_config: string;
  repo_name: string;
  version: string;
  result_url: string;
  build_url: string;
  log_url: string;
  error_code: number;
  error_msg: string;
  ios_ext_info: string;
  android_ext_info: string;
  flutter_ext_info: string;
  job_id: number;
  created_at: string | null;
  updated_at: string | null;
  job_info: any | null;
}

export interface UpgradeHistoryDetail {
  id: number;
  repo_name: string;
  repo_group_name: string;
  version: string;
  create_time: string;
  finish_time: string;
  commit_id: string;
  operate_user: string;
  app_id: string;
  changelog: string;
  branch: string;
  gitlab_mr_iid: number;
  repo_id: number;
  builds: BuildInfo[];
}

export interface UpgradeHistoryResponse {
  err_no: number;
  err_msg: string;
  data: UpgradeHistoryDetail;
  message: string;
}
