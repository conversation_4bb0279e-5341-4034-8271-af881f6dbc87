import { Api, Data, Get, Post, Query, useInject, useReq } from '@edenx/runtime/bff';
import SlardarRequestEngine from '../service/SlardarRequestEngine';
import BitsService from '../service/bits';
import TeaService from '../service/third/tea';
import MeegoService from '../service/meego';
import SlardarCrashIssueListModel from '../model/SlardarCrashIssueListTable';
import { z } from 'zod';
import AlarmVersionModel, { DBAlarmVersion } from '../model/AlarmVersionInfoTable';
import LarkService from '@pa/backend/dist/src/third/lark';
import TeaMetricService from '../service/TeaMetric';
import TeaMetricValueModel from '../model/TeaMetricValueTable';
import TeaMetricModel from '../model/TeaMetricTable';
import TeaAutoReportService from '../service/tea/report/report';
import { TeaSamplePeriod } from '@shared/typings/tea/report';
import TeaMetricGoalModel from '../model/TeaMetricGoalTable';
import { GoalType } from '@shared/typings/tea/goal';
import TeaRequestEngine from '../service/tea/TeaRequestEngine';
import { DataSourceType } from '../service/tea/dataSource/DataSourceFactory';
import iOSVersionQualityService from '../service/slardar/iOSVersionQuality';
import MeegoRawService from '../service/third/meego';
import LibraService from '../service/third/libra';
import SlardarTestBugItemModel from '../model/SlardarTestBugTable';
import SlardarDAU3IssueListModel from '../model/SlardarDAU3IssueListTable';
import SlardarVersionQualityRateModel from '../model/SlardarVersionQualityRateTable';
import MeegoItemViewModel from '../model/MeegoItemViewTable';
import SlardarVersionNewIssueRateModel from '../model/SlardarVersionNewIssueRateTable';
import SlardarVersionNewIssueListModel from '../model/SlardarVersionNewIssueListTable';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { initSlardar } from '../utils/initSlardar';
import ExperimentService from '../service/experiment/experiment';
import OwnerSyncEngine from '../service/OwnerSyncEngine';
import { getLibraVersionPlatformFromFilterRule } from '../utils/libraUtil';
import TeaMetricImport from '../service/tea/TeaMetricImport';
import BatchConsumeService from '../service/slardar/batchConsume';
import DataRequestEngine from '../service/DataRequestEngine';
import TeaTokenManager from 'api/service/tea/TeaTokenManager';
import { parseSlardarLink } from '../utils/slardarLink';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import WarningSyncHandlers from '../service/titan/warningSyncHandlers';
import SettingsService from '../service/third/settings';
import VersionSyncService from '../service/slardar/VersionSync';
import { SLARDAR_APP_ID } from '../service/slardar/shared';
import { getModelForClass } from '@gulux/gulux/typegoose';
import { RedisClient } from '@gulux/gulux/redis';
import SlardarService from '../service/slardar/slardar';
import { GetCrashFiledPercentParams } from '@shared/typings/slardar/crash/issueListSearch';
import RedisLock from '../service/RedisLock';
import IssueAutoPush from '../service/slardar/IssueAutoPush';
import MRCodeChangeService from '../service/mrProfiler/MRCodeChangeService';
import { LVMRProfilerMQInfo, LVMRProfilerMQMsgType } from '../proto/LVMRProfiler';
import { SlardarIssueBugItemTable } from '../model/SlardarIssueBugItem';
import QualityCronJobTrigger from '../trigger/QualityCronJobTrigger';
import { handleManualTrigger } from '@pa/backend/dist/src/utils/cronJob';
import LibraAPIService from '../service/libra/LibraAPIService';
import { isSGLibraByLibraAppId, LibraRegion } from '@shared/libra/commonLibra';
import { FlightType, LibraOverdueNotifySetting } from '@shared/libra/LibraNewInfo';
import { LibraEventBusHandler } from '../service/libra/LibraEventBusHandler';
import AirplaneConfigService from '../service/AirplaneConfigService';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import { StoryRevenueTaskService } from '../service/storyRevenueReviewPlatform/StoryRevenueTaskService';
import { GrayScaleCreateBug } from '../service/slardar/GrayscaleCreateBug';
import { VersionType } from '@shared/utils/version_utils';
import SlardarInfoModel from '../model/SlardarInfoTable';
import { DeviceLevel } from '@shared/common';
import LibraOverdueNotifySettingDao from '../dao/libra/LibraOverdueNotifySettingDao';
import { AbnormalFlightReportInfoService } from '../service/libra/AbnormalFlightReportInfoService';
import { calculateDayDiffResult } from '@shared/libra/libraManageUtils';
import { LibraControlService } from '../service/libra/LibraControlService';
import { UserData, UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import AutoVersionStageService from '../service/stage/autoVersionStageService';
import LibraPatrolConfigDao from '../dao/libra/LibraPatrolConfigDao';
import { LibraDesignDocCheckService } from '../service/libra/LibraDesignDocCheckService';
import { LibraNewInfoListService } from '../service/libra/LibraNewInfoListService';
import { ProcessorLT } from '../service/experiment/ProcessorLT';
import { ProcessorCommercial } from 'api/service/experiment/ProcessorCommercial';
import { ComponentMatchRule } from '@shared/typings/slardar/crash/issueAutoAssign';
import { ProcessorAd } from 'api/service/experiment/ProcessorAd';
import { getPullOfflineVersions } from '@api/version';
import { SelectedName } from '@shared/utils/offlineslardar';
import { StartFlightControlHandler } from 'api/service/libra/StartFlightControlHandler';
import { FlightEventType, LibraControlReq } from '@shared/libra/libraControl';

const tea_metrics =
  'Seek耗时-P90,剪映,工具,iOS,冯夏巍,耗时,熔断,https://data.bytedance.net/tea-next/project/509/event-analysis/result/zade8e5ba89237fcdf37cd3,A\n' +
  'Seek耗时-P90,剪映,工具,Android,温华舟,耗时,熔断,https://data.bytedance.net/tea-next/project/509/event-analysis/result/zade8e63960e6814726d9,A\n' +
  '播放低帧率占比,剪映,工具,Android,温华舟,失败率,熔断,https://data.bytedance.net/tea-next/project/509/event-analysis/result/zade8e5b1d5fed0e5fd0e43,(B/A)\n' +
  '播放低帧率占比,剪映,工具,iOS,冯夏巍,失败率,熔断,https://data.bytedance.net/tea-next/project/509/event-analysis/result/zade8e63ac46d27458025,(B/A)\n' +
  '草稿加载耗时-P90,剪映,工具,Android,温华舟,耗时,熔断,https://data.bytedance.net/tea-next/project/509/event-analysis/result/zade8e5a171e52981641c44,A\n' +
  '草稿加载耗时-P90,剪映,工具,iOS,冯夏巍,耗时,熔断,https://data.bytedance.net/tea-next/project/509/event-analysis/result/zade8e5e3cd5e7f0b4b68,A';

export const debug_1 = Api(Get('/debug/1'), async () =>
  // throw new Error("哈哈哈哈哈");
  ({
    ios: await useInject(BitsService).pulliOSHypicVersion(),
    Android: await useInject(BitsService).pullAndroidHypicVersion(),
    aa: parseSlardarLink(
      'https://slardar.bytedance.net/node/app_detail/?aid=1775&os=Android#/abnormal/detail/app/0cda6c9fb80b4545fa2330687b91521b',
      // "https://slardar-us.bytedance.net/node/app_detail/?region=maliva&os=Android&aid=3006&subregion=row&type=app&lang=zh#/abnormal/detail/anr/d66dd4bdc9730eebfa87400e46d57492?params=%7B%22token%22%3A%22%22%2C%22token_type%22%3A0%2C%22crash_time_type%22%3A%22insert_time%22%2C%22start_time%22%3A1713238560%2C%22end_time%22%3A1713843360%2C%22granularity%22%3A86400%2C%22filters_conditions%22%3A%7B%22type%22%3A%22and%22%2C%22sub_conditions%22%3A%5B%5D%7D%2C%22event_index%22%3A1%7D"
    ),
  }),
);

export const debug_save_abnormal = Api(
  Post('/open/debug/save_abnormal'),
  Data(
    z.object({
      meegoId: z.number(),
    }),
  ),
  async ({ data }) => {
    const model = useInject(AbnormalFlightReportInfoService);
    const ret = await model.saveFlightNotStartMeego(data.meegoId);
    return {
      code: 0,
      message: 'success',
      data: ret,
    };
  },
);

export const debug_libra_list = Api(Get('/open/debug/libra/list'), async () => {
  const libraAPIService = useInject(LibraControlService);
  await libraAPIService.checkGrayLibra100Percent(6083688303);
  return {
    code: 0,
    message: 'success',
  };
});

export const cleanView = Api(Get('/clean/view'), async () => ({
  res: await useInject(MeegoItemViewModel).clean(),
}));
export const debug_2 = Api(
  Get('/debug/2'),
  Query(
    z.object({
      aid: z.number(),
      ver: z.string(),
    }),
  ),
  async ({ query }) => {
    const model = useInject(SlardarCrashIssueListModel);
    const ret = await model.findP0P1ForVersion(query.aid, query.ver);
    return {
      count: ret.length,
      issue_ids: ret.map(it => it.issue_id),
    };
  },
);

export const queryLibraApplicationList = Api(Get('/debug/libra/query_apps'), async () => {
  const libraAPIService = useInject(LibraAPIService);
  const response = await libraAPIService.queryApplicationList(LibraRegion.CN);
  return { status: 'ok', result: response };
});

export const createLibraFlight = Api(Get('/debug/libra/create_flight'), async () => {
  const libraAPIService = useInject(LibraAPIService);
  const response = await libraAPIService.createFlight(LibraRegion.CN, {
    skip_create: true,
    name: '纸飞机自动创建实验测试',
    type: FlightType.SettingsClientSDK,
    layer_id: 141006,
    version_resource: 0.012,
    duration: 32,
    description: '测试一下纸飞机创建实验',
    expectation: '预期能创建成功哦',
    filter_type: 'rule',
    filter_rule: [],
    versions: [],
    owner: ['zhoubaoding.1'],
    tags: [],
    kind: 'normal',
    specified_psms: [],
    is_launch: 0,
    need_review: true,
    watching_metric_groups: [],
    effected_regions: [], // 为什么['CN']不行？
    ad_flight_hit_rules: [],
    // product_id: 444,
    // token: 'lv_client_token', // TODO,需要这个参数吗
    // app_id: 147,
  });
  return { status: 'ok', result: response };
});

export const triggerTeaDataCollect = Api(Get('/debug/tea'), async () => {
  console.log('Triggered TEA data collect');
  const engine = useInject(TeaRequestEngine);
  await engine.dataCollector();
  return { status: 'ok' };
});

export const clearLock = Api(Get('/debug/tea/clear/new_lock'), async () => {
  console.log('TeaClearLock');
  const redis = useInject(RedisClient);
  await redis.del('lock:tea_new_collect');
  return { status: 'ok' };
});

export const triggerAutoWarn = Api(Get('/debug/autowarn'), async () =>
  // const slardarRequestEngine = useInject(SlardarRequestEngine);
  // await slardarRequestEngine.slardarAutoNotice();
  ({ status: 'ok' }),
);

export const triggerSlardarDataCollect = Api(Get('/open/debug/slardar'), async () => {
  // const s = useInject(SlardarCrashIssueModel);
  // const y = useInject(SlardarCrashIssueListModel);
  // await s.delete_all();
  // await y.delete_version();
  console.log('Triggered Slardar data collect');
  const engine = useInject(SlardarRequestEngine);
  await engine.slardarDataCollect();
  return { status: 'ok' };
});

export const triggerSlardarRefresh = Api(Get('/debug/slardar/refresh'), async () => {
  await initSlardar();
  return { status: 'ok' };
});

export const debugTest = Api(Get('/debug/test'), async () => {
  const larkService = useInject(LarkService);
  const res = await larkService.getUserInfoByEmails(['<EMAIL>']);
  return {
    res,
  };
});

export const saveRecentVersions = Api(Get('/open/debug/recent_version'), async () => {
  const srv = useInject(VersionSyncService);
  // const res1 = await useInject(AlarmVersionModel).delete_all();
  // const res2 = await useInject(AlarmVersionModel).model.deleteMany({
  // 	bits_app_id: HypicBitsAppId(false, current_region() !== Region.SG),
  // });
  await srv.saveAllVersions();
  // await srv.saveHypicIosVersions();
  return { status: 'ok' };
});

export const drop = Api(Get('/debug/drop'), async () => {
  for (const i of [
    TeaMetricValueModel,
    TeaMetricModel,
    // SlardarCrashIssueListModel,
    // SlardarInfoModel,
    // SlardarCrashIssueModel,
    // SlardarVersionQualityRateModel,
    // SlardarDAU3IssueListModel,
    // SlardarValueModel,
  ]) {
    await (useInject(i as unknown as any) as any).delete_all();
    console.log('DELETED!');
  }
  // return await m1.saveRecentIosVersions();
  return { status: 'ok' };
});

export const drop_val = Api(Get('/debug/drop/value'), async () => {
  const m = useInject(TeaMetricValueModel);
  // const bad = await m.query({ versionCode: /^.*\..*$/ });
  const x = await m.delete_all({ versionCode: /^.*\..*$/ });
  return JSON.stringify(x);
});

export const fetch_token = Api(Get('/debug/fetch/tea/token'), async () => {
  const m = useInject(TeaTokenManager);
  const token = await m.fetchToken();
  if (token) {
    return token && token.code;
  } else {
    return -1;
  }
});

export const tea = Api(Get('/debug/tea-test'), async () => {
  const t = useInject(TeaService);
  return await t.translateTeaDsl('7213274089253765687', true);
});

export const identifier = Api(Get('/debug/identifier'), async () => {
  const meegoService = useInject(MeegoService);
  return await meegoService.queryVersionId('faceu', '剪映-Android-11.7.0');
});

// debug查询接口，issueID是否存在
export const queryIssueId = Api(
  Post('/debug/queryIssueId'),
  Data(
    z.object({
      platform: z.nativeEnum(SlardarPlatformType), // Android/iOS
      test_id: z.string().optional(),
      category: z.array(z.string()).optional(),
      issue_id: z.string().optional(),
    }),
  ),
  async ({ data }) => ({
    res: await useInject(SlardarTestBugItemModel).isExist(data),
  }),
);

export const cleanByPpe = Api(
  Post('/debug/clean/ppe'),
  Data(
    z.object({
      aid: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data }) => {
    const y = useInject(SlardarCrashIssueListModel);
    await y.delete_version(data.aid, data.version);
    return { res: 0 };
    // return await meegoService.updateMeegoWorkView(data.version, data.platform, data.id);
  },
);

export const deleteValueByDate = Api(
  Post('/debug/delete/rate/date'),
  Data(
    z.object({
      date: z.string(),
    }),
  ),
  async ({ data }) => {
    await useInject(SlardarDAU3IssueListModel).delete_all({ date: data.date });
    await useInject(SlardarVersionQualityRateModel).delete_all({ date: data.date });
    return { res: 0 };
    // return await meegoService.updateMeegoWorkView(data.version, data.platform, data.id);
  },
);

export const teaImport = Api(Get('/debug/tea/import'), async () => {
  try {
    await useInject(TeaMetricImport).import(tea_metrics);
    return { status: 'ok' };
  } catch (e) {
    console.log('EXCEPTION');
    console.log(e);
    return JSON.stringify(e);
  }
});

export const pullNewIssueListRate = Api(
  Post('/debug/pull/new/issue/rate'),
  Data(
    z.object({
      version_code: z.array(z.string()).optional(),
    }),
  ),
  async ({ data }) => ({
    res: await useInject(SlardarRequestEngine).slardarNewCrashIssueListCollect(data.version_code),
  }),
);

export const deleteNewIssueListRate = Api(
  Post('/debug/delete/new/issue/rate'),
  Data(
    z.object({
      version_code: z.string(),
    }),
  ),
  async ({ data }) => ({
    res1: await useInject(SlardarVersionNewIssueListModel).delete_all({ version_code: data.version_code }),
    res2: await useInject(SlardarVersionNewIssueRateModel).delete_all({ version_code: data.version_code }),
  }),
);

export const pullCrashRate = Api(
  Post('/debug/pull/issue/rate'),
  Data(
    z.object({
      time: z.number().optional(),
    }),
  ),
  async ({ data }) => {
    await useInject(SlardarRequestEngine).slardarVersionDataWeekly(data.time);
    return { res: 0 };
    // return await meegoService.updateMeegoWorkView(data.version, data.platform, data.id);
  },
);

export const getIssueInfo = Api(Get('/debug/issue_level'), async () => {
  const versionModel = useInject(AlarmVersionModel);
  return { res: await versionModel.findGrayingVersions(SLARDAR_APP_ID()) };
});

export const washBadTea = Api(Get('/debug/wash/tea'), async () => {
  await useInject(TeaMetricService).createMetric;
});

export const addOverdueNotifySetting = Api(
  Post('/debug/add/overdue/notify/setting'),
  Data(
    z.object({
      businessLine: z.string(),
      days: z.number(),
      chatId: z.string(),
    }),
  ),
  async ({ data }) => {
    const srv = useInject(LibraOverdueNotifySettingDao);
    const setting = {
      businessLine: data.businessLine,
      days: data.days,
      chatId: data.chatId,
    } as LibraOverdueNotifySetting;
    await srv.save(setting);
    return { status: 'ok' };
  },
);

export const tryFetch = Api(Get('/debug/tea/gugu'), async () => {
  const tmm = useInject(TeaMetricModel);
  const tre = useInject(TeaRequestEngine);
  const m = await tmm.findOne({});
  return { status: 'ok' };
});

export const testReport = Api(Get('/debug/tea/report'), async () => {
  await useInject(TeaAutoReportService).report({
    displayName: 'Some Report',
    metrics: ['template_import_success_rate'],
    samplePeriod: TeaSamplePeriod.DAY_1,
    subscribers: ['oc_92f68060997d876018512911920edcea'],
    dataSourceInfo: DataSourceType.SERIAL_DOD,
  });
});

export const newTea = Api(Get('/debug/tea/new'), async () => {
  const ret = await TeaRequestEngine.translateDsl('zadeb330f38fa8d43ac3e', 'snapshot');
  return TeaRequestEngine.trimDsl(ret);
});

export const testGoal = Api(Get('/debug/tea/goal'), async () => {
  await useInject(TeaMetricGoalModel).set({
    goals: [
      {
        type: GoalType.YEARLY,
        value: 0.99,
      },
      {
        type: GoalType.QUARTERLY,
        value: 0.98,
      },
      {
        type: GoalType.MONTHLY,
        value: 0.97,
      },
    ],
    name: 'template_import_success_rate',
    owner: 'linhouyan',
  });
});

export const deleteMetric = Api(Post('/debug/delete/error_metric'), Data(z.object({})), async ({ data: {} }) => {
  const srv = useInject(TeaMetricValueModel);
  await srv.deleteErrorData();
  return { status: 'ok' };
});

export const repullSlardar = Api(
  Post('/open/debug/slardar/repull'),
  Data(
    z.object({
      aid: z.number(),
      version: z.string(),
    }),
  ),
  async ({ data: { aid, version } }) => {
    const srv = useInject(SlardarRequestEngine);
    await srv.rePullValue(aid, [version], '/debug/slardar/repull');
    return { status: 'ok' };
  },
);

export const iosQualityTest = Api(
  Post('/debug/slardar/ios/quality'),
  Data(
    z.object({
      funcname: z.string().optional(),
      issueid: z.string().optional(),
      crashtype: z.string().optional(),
      count: z.number().optional(),
      needresignuser: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const xxxxx = useInject(iOSVersionQualityService);
    return await xxxxx.entry(data);
  },
);

export const updateMeegoItem = Api(
  Post('/debug/update/meego/item'),
  Data(
    z.object({
      meegoId: z.number(),
      title: z.string(),
    }),
  ),
  async ({ data }) => ({
    res: await useInject(MeegoRawService).requestMeegoUserInfos({ emails: ['<EMAIL>'] }),
  }),
);

export const flightDetail = Api(Get('/debug/flightDetail'), async () => {
  const { flightId } = useReq().query;
  const res = await useInject(LibraAPIService).queryFlight(LibraRegion.CN, Number(flightId));
  const isSG = isSGLibraByLibraAppId(res?.app_id ?? 0);
  return {
    params: useReq().query,
    filter: getLibraVersionPlatformFromFilterRule(
      res?.filter_rule ?? [],
      isSG ? LibraRegion.SG : LibraRegion.CN,
      res?.name ?? '',
      res?.description ?? '',
    ),
    detail: res,
    history: await useInject(LibraService).queryFlightHistoryById(flightId as string),
    group: await useInject(LibraService).queryFlightGroupInfo(flightId as string),
  };
});

export const flightVersionPlatform = Api(Get('/open/debug/flightVersionPlatform'), async () => {
  const { flightId } = useReq().query;
  const res = await useInject(LibraAPIService).queryFlight(LibraRegion.SG, Number(flightId));
  const isSG = isSGLibraByLibraAppId(res?.app_id ?? 0);
  const filter = getLibraVersionPlatformFromFilterRule(
    res?.filter_rule ?? [],
    isSG ? LibraRegion.SG : LibraRegion.CN,
    res?.name ?? '',
    res?.description ?? '',
  );
  return {
    params: useReq().query,
    filter,
    detail: res,
    history: await useInject(LibraService).queryFlightHistoryById(flightId as string),
    group: await useInject(LibraService).queryFlightGroupInfo(flightId as string),
  };
});

export const settingDetail = Api(Get('/debug/settingDetail'), async () => {
  const { item_id } = useReq().query;
  return {
    setting_detail: await useInject(SettingsService).queryItemDetail(Number(item_id)),
    ab_list: await useInject(SettingsService).querySettingsItemABConfigs(Number(item_id)),
  };
});

export const combineFlight = Api(Get('/debug/combineFlight'), async () => ({
  res: await useInject(LibraService).queryBetaCombineGroup(147, '462e3912892545d387a28709ea651a98'),
}));

export const migrateAllIssue = Api(Get('/debug/migrateAllIssue'), async () => ({
  res: await useInject(SlardarTestBugItemModel).migrateAllIssue('2342816', '2342817'),
}));

export const testUser = Api(Get('/debug/testUser'), async () => {
  const res = await useInject(LarkService).checkUserValid('jiangyunkai');
  const res2 = await useInject(LarkService).batchCheckUserValid([
    'zhanglinwei.yimu',
    'jiangyunkai',
    'guozhi.kevin',
    'fenghaiyun',
    'lijianxin',
    'zhangyue.zz123',
  ]);
  const res3 = await useInject(LarkService).checkUserValid('zhangyue.zz123');
  return {
    res,
    res2,
    res3,
  };
});

export const sync = Api(Get('/debug/sync'), async () => {
  const res = await useInject(OwnerSyncEngine).clearRule([ComponentMatchRule.LibraryName]);
  return {
    res,
  };
});

export const experimentWarnTest = Api(
  Post('/debug/test/experiment/warn'),
  Data(
    z.object({
      id: z.string(),
      isTest: z.boolean(),
    }),
  ),
  async ({ data }) => {
    const experimentService = useInject(ExperimentService);
    await experimentService.warnById(data.id, data.isTest);
    return {
      res: 0,
    };
  },
);

export const libraEventMock = Api(Post('/debug/libra/event_mock'), Data(z.any()), async ({ data }) => {
  await useInject(LibraEventBusHandler).didReceiveEvent(data, 'debug_test');
  return 'ok';
});

export const consumeTopN = Api(
  Post('/debug/consumeTopN'),
  Data(
    z.object({
      crashType: z.string(),
      N: z.number(),
      start: z.number(),
      count: z.number(),
      onlyShow: z.boolean(),
    }),
  ),
  async ({ data }) =>
    await useInject(BatchConsumeService).consumeTopN(data.crashType, data.N, data.start, data.count, data.onlyShow),
  // return 'ok';
);

export const pullTeaDataWithTime = Api(
  Get('/debug/tea/time'),
  Query(
    z.object({
      time: z.string().optional(),
    }),
  ),
  async ({ query: { time } }) => {
    const redisLock = useInject(RedisLock);
    await redisLock.release('tea_old_collect');
    await useInject(DataRequestEngine).triggerDataCollect(time);
    return { status: 'ok' };
  },
);

export const testDayjs = Api(
  Post('/debug/testDayjs'),
  Data(
    z.object({
      date: z.string(),
    }),
  ),
  async ({ data: { date } }) => {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    return {
      date: dayjs(date).unix(),
      date2: dayjs.tz(date, 'UTC').unix(),
      date3: dayjs.tz(date, 'America/Adak').unix(),
      date4: dayjs.tz(date, 'Asia/Shanghai').unix(),
    };
  },
);

export const titanWarningSync = Api(Get('/debug/titan/warning/quality'), async () => {
  await useInject(WarningSyncHandlers).handler('warningSyncQualityWeekly');
  return 'ok';
});

export const titanWarningSyncList = Api(Get('/debug/titan/warning/list'), async () => {
  await useInject(WarningSyncHandlers).handler('warningSyncList');
  return 'ok';
});

export const debugDataClean = Api(Get('/open/debug/dataClean'), async () => {
  /* const res = await Promise.all(
		[
			SlardarCrashIssueListTable,
			SlardarUserCountTable,
			SlardarValueTable,
			SlardarVersionMetricsTable,
			SlardarVersionQualityIssueTable,
			SlardarVersionQualityValueTable,
		].map((table) =>
			getModelForClass(table).updateMany(
				{
					device_level: {
						$exists: false,
					},
				},
				{
					$set: {
						device_level: DeviceLevel.ALL,
					},
				}
			)
		)
	); */
  const res = await Promise.all(
    [SlardarIssueBugItemTable].map(table =>
      getModelForClass(table).updateMany(
        {
          aid: {
            $exists: false,
          },
        },
        {
          $set: {
            aid: SLARDAR_APP_ID(),
          },
        },
      ),
    ),
  );
  return {
    res,
  };
});

export const testSlardar = Api(Get('/debug/slardar/test'), async () => {
  // const res = await useInject(MeegoRawService).requestWorkItem("faceu", "issue", [16071751]);
  if (useReq().query.timer === '1') {
    const res = await useInject(IssueAutoPush).pushByMeegoIdTimer(Number(useReq().query.meegoId));
  } else {
    const res = await useInject(IssueAutoPush).pushByMeegoIdWhenCreate(Number(useReq().query.meegoId));
  }
  return 'ok';
});

/**
 * Slardar维度数据请求接口测试
 */
export const testCrashFiledPercent = Api(Get('/debug/slardar/field_percent'), async () => {
  const result = [];
  for (const params of [
    {
      aid: 1775,
      platform: SlardarPlatformType.Android,
      start_time: 1722520800,
      end_time: 1722587159,
      crash_type: 'native',
      // update_version_code: "145000400", // 不指定小版本
      // version: "144001600", // 不指定大版本
      field: 'device_model',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.Android,
      start_time: 1722520800,
      end_time: 1722587159,
      crash_type: 'native',
      update_version_code: '145000400', // 指定一个小版本
      field: 'device_model',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.Android,
      start_time: 1722520800,
      end_time: 1722587159,
      crash_type: 'native',
      update_version_code: ['145000400', '145000300'], // 指定多个小版本
      field: 'update_version_code',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.Android,
      start_time: 1722520800,
      end_time: 1722587159,
      crash_type: 'native',
      version: '14.4.0',
      field: 'update_version_code',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.Android,
      start_time: 1722520800,
      end_time: 1722587159,
      crash_type: 'native',
      version: ['14.4.0', '14.3.0'],
      field: 'update_version_code',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.Android,
      start_time: 1722520800,
      end_time: 1722587159,
      crash_type: 'native',
      map_key: 'is_64_devices', // 自定义维度
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.iOS,
      start_time: 1720666800,
      end_time: 1720926000,
      crash_type: 'crash',
      // update_version_code: "14.3.0.96",
      // version: "14.3.0",
      field: 'device_model',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.iOS,
      start_time: 1720666800,
      end_time: 1720926000,
      crash_type: 'crash',
      update_version_code: '14.3.0.96',
      // version: "14.3.0",
      field: 'device_model',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.iOS,
      start_time: 1720666800,
      end_time: 1720926000,
      crash_type: 'crash',
      update_version_code: ['14.3.0.96', '14.3.0.91'],
      // version: "14.3.0",
      field: 'raw_update_version_code',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.iOS,
      start_time: 1720666800,
      end_time: 1720926000,
      crash_type: 'crash',
      // update_version_code: ["14.3.0.96", "14.3.0.91"],
      version: '14.3.0',
      field: 'raw_update_version_code',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.iOS,
      start_time: 1720666800,
      end_time: 1720926000,
      crash_type: 'crash',
      // update_version_code: ["14.3.0.96", "14.3.0.91"],
      version: ['14.3.0', '14.2.0'],
      field: 'raw_update_version_code',
    },
    {
      aid: 1775,
      platform: SlardarPlatformType.iOS,
      start_time: 1720666800,
      end_time: 1720926000,
      crash_type: 'crash',
      map_key: 'LV_resource',
    },
  ] as GetCrashFiledPercentParams[]) {
    result.push(await useInject(SlardarService).getCrashFiledPercent(params));
  }
  return result;
});

export const testMrAttrCommand = Api(
  Post('/open/debug/testMrAttrCommand'),
  Data(
    z.object({
      chatId: z.string(),
      rawData: z.string(),
    }),
  ),
  async ({ data }) => {
    const msg: LVMRProfilerMQInfo = {
      type: LVMRProfilerMQMsgType.LVMRProfilerMQMsgTypeMrAttr,
      extInfo: JSON.stringify({
        chat_id: data.chatId,
        raw_data: data.rawData,
      }),
    };
    await useInject(MRCodeChangeService).handleMrAttrMessage(msg);
    return 'ok';
  },
);

export const manualtriggerCronJob = Api(
  Query(z.object({ job: z.string() })),
  Get('/open/triggerCronJob'),
  async ({ query }) => await handleManualTrigger(useInject(QualityCronJobTrigger), query.job),
);

export const triggerPartrol = Api(
  Get('/open/triggerPartrol'),
  async ({}) => await useInject(QualityCronJobTrigger).libraPatrolAd(),
);

export const testLibraPatrol = Api(
  Post('/open/debug/testLibraPatrol'),
  Data(
    z.object({
      flightId: z.number(),
      libraAppId: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(ProcessorAd);
    await libraService.testPatrol(data.flightId, data.libraAppId);
  },
);

export const testCommercialPatrol = Api(
  Post('/open/debug/testCommercialPatrol'),
  Data(
    z.object({
      flightId: z.number(),
      libraAppId: z.number(),
    }),
  ),
  async ({ data }) => {
    const libraService = useInject(ProcessorCommercial);
    await libraService.getExperimentPatrolData([147]);
  },
);

export const addLibraMetricDim = Api(
  Post('/open/debug/addLibraMetricDim'),
  Data(
    z.object({
      metricGroupId: z.number(),
      dimName: z.number(),
      dimValue: z.array(z.string()),
    }),
  ),
  async ({ data }) => {
    const config = await useInject(LibraPatrolConfigDao).findOne({ metric_group_id: data.metricGroupId });
    if (config) {
      config.dim_id = data.dimName;
      config.dim_values = data.dimValue;
      config.switch = true;
      await useInject(LibraPatrolConfigDao).updateOne({ metric_group_id: data.metricGroupId }, config);
    }
    return 'ok';
  },
);

export const queryAirplaneConfig = Api(
  Post('/debug/query_config_time_data'),
  Data(z.object({ platform: z.nativeEnum(SlardarPlatformType) })),
  async ({ data }) => {
    const configService = useInject(AirplaneConfigService);
    return await configService.queryConfigItemByName(VersionConfigKeys.autoLevelBlackList, data.platform);
  },
);

export const testTaskInfoIntegrality = Api(
  Post('/debug/test_task_info_integrality'),
  Data(z.object({ meegoId: z.string(), reviewPeriodId: z.string() })),
  async ({ data }) => {
    const taskService = useInject(StoryRevenueTaskService);
    const taskInfo = await taskService.findOneStoryRevenueTask(data.meegoId, data.reviewPeriodId);
    let result = false;
    if (taskInfo) {
      result = taskService.checkTaskInfoIntegration(taskInfo);
    }
    return {
      code: 0,
      message: 'ok',
      integrality: result,
      data: taskInfo,
    };
  },
);

export const DebugGrayScaleCreateBug = Api(
  Post('/debug/ScaleCreateBug'),
  Data(
    z.object({
      aid: z.number(),
      version: z.string().optional(),
      version_code: z.string().optional(),
      timestamp: z.number(),
      version_step: z.number(),
      version_type: z.nativeEnum(VersionType),
      platform: z.nativeEnum(SlardarPlatformType),
    }),
  ),
  async ({ data }) => {
    const currentTimestampInSeconds = Math.floor(Date.now() / 1000);
    // const data1: DBAlarmVersion = {
    //   aid: 1775,
    //   version: '15.7.0',
    //   version_code: '157000600',
    //   timestamp: 1737725208,
    //   version_step: 6,
    //   version_type: 1,
    //   platform: SlardarPlatformType.Android,
    // };
    if (data.version) {
      const data1: DBAlarmVersion = {
        aid: data.aid,
        version: data.version,
        version_code: '',
        timestamp: data.timestamp,
        version_step: data.version_step,
        version_type: data.version_type,
        platform: data.platform,
      };
      await new GrayScaleCreateBug(data.platform, currentTimestampInSeconds, true).filterCreateBug(data1);
    } else {
      const data1: DBAlarmVersion = {
        aid: data.aid,
        version: '',
        version_code: data.version_code || '',
        timestamp: data.timestamp,
        version_step: data.version_step,
        version_type: data.version_type,
        platform: data.platform,
      };
      await new GrayScaleCreateBug(data.platform, currentTimestampInSeconds, false).filterCreateBug(data1);
    }
    return {
      code: 0,
      message: 'ok',
    };
  },
);

export const UpdateSlardarModelData = Api(
  Post('/debug/UpdateSlardarModelData'),
  Data(z.object({ aid: z.number(), index: z.number() })),
  // 刷最近几个大版本的数据，主要是为了灰度提单刷数据，把前面几个版本的数据给补上
  async ({ data }) => {
    const versions = await useInject(AlarmVersionModel).findOnLineVersionDeWeight(data.aid, VersionType.ONLINE);
    const versionCodesList = versions.slice(0, data.index);
    const slardarInfoModel = useInject(SlardarInfoModel);
    const srv = useInject(SlardarRequestEngine);
    const metrics = await slardarInfoModel.findEnabled(data.aid, [SlardarPlatformType.Android]);
    await srv.slardarDataCollectForAllVersionMetrics(data.aid, versionCodesList, metrics, DeviceLevel.ALL, false);
    return {
      code: 0,
      message: 'ok',
    };
  },
);

export const DebugAssignVersionTest = Api(
  Post('/debug/AssignVersionTest'),
  Data(z.object({ aid: z.number(), version: z.string() })),
  async ({ data }) => {
    const versionModel = useInject(AlarmVersionModel);
    const srv = useInject(SlardarRequestEngine);
    const slardarInfoModel = useInject(SlardarInfoModel);
    const version = await versionModel.findOnlineVersion(data.aid, data.version, SlardarPlatformType.Android);
    // const metrics = await slardarInfoModel.findEnabled(data.aid, [SlardarPlatformType.Android]);
    const metrics1 = await slardarInfoModel.findEnabled(data.aid, [SlardarPlatformType.Android]); // 取其两个
    const metrics = metrics1.slice(0, 3);
    const version_code = await versionModel.findGrayVersionCodes(data.aid, data.version, SlardarPlatformType.Android);
    // const last_version_code = version_code[0];
    await srv.slardarDataCollectForVersionMetrics(
      data.aid,
      version_code,
      metrics,
      DeviceLevel.ALL,
      '3',
      SlardarPlatformType.Android,
      version_code,
      true,
    );
    // await srv.slardarDataCollectForAllVersionMetrics(data.aid, version, metrics, DeviceLevel.ALL, true);
    return {
      code: 0,
      message: 'ok',
    };
  },
);

export const DebugAssignVersionTestAndroid = Api(
  Post('/debug/AssignVersionTestAndroid'),
  Data(z.object({ aid: z.number(), version: z.string() })),
  async ({ data }) => {
    // const versionModel = useInject(AlarmVersionModel);
    const srv = useInject(SlardarRequestEngine);
    const slardarInfoModel = useInject(SlardarInfoModel);
    let offlineAndroidVersions: string[] = [];
    let offlineiOSVersions: string[] = [];

    const Androidmetrics = await slardarInfoModel.findEnabled(data.aid, [SlardarPlatformType.Android]);
    const iOSmetrics = await slardarInfoModel.findEnabled(data.aid, [SlardarPlatformType.iOS]);

    // 对于特定aid，直接从接口拉取线下版本列表
    if (data.aid === 1775 || data.aid === 3006) {
      const offlineAndroidVersionsResponse = await getPullOfflineVersions({
        data: { aid: data.aid, platform: SlardarPlatformType.Android },
      });
      if (offlineAndroidVersionsResponse.code === 200 && offlineAndroidVersionsResponse.data?.length > 0) {
        // 修正：将返回的对象数组映射为字符串数组
        offlineAndroidVersions = offlineAndroidVersionsResponse.data;
      }
      const offlineiOSVersionsResponse = await getPullOfflineVersions({
        data: { aid: data.aid, platform: SlardarPlatformType.iOS },
      });
      if (offlineiOSVersionsResponse.code === 200 && offlineiOSVersionsResponse.data?.length > 0) {
        // 修正：将返回的对象数组映射为字符串数组
        offlineiOSVersions = offlineiOSVersionsResponse.data;
      }

      // aid为1775 或者 3006
      if (offlineAndroidVersions.length > 0) {
        await srv.slardarOfflineDataCollectForVersionMetrics(
          data.aid,
          offlineAndroidVersions,
          Androidmetrics,
          DeviceLevel.ALL,
          SlardarPlatformType.Android,
          SelectedName.BeforeIntegration,
        );
        await srv.slardarOfflineDataCollectForVersionMetrics(
          data.aid,
          offlineAndroidVersions,
          Androidmetrics,
          DeviceLevel.ALL,
          SlardarPlatformType.Android,
          SelectedName.AfterIntegration,
        );
      }
      if (offlineiOSVersions.length > 0) {
        await srv.slardarOfflineDataCollectForVersionMetrics(
          data.aid,
          offlineiOSVersions,
          iOSmetrics,
          DeviceLevel.ALL,
          SlardarPlatformType.iOS,
          SelectedName.BeforeIntegration,
        );
        await srv.slardarOfflineDataCollectForVersionMetrics(
          data.aid,
          offlineiOSVersions,
          iOSmetrics,
          DeviceLevel.ALL,
          SlardarPlatformType.iOS,
          SelectedName.AfterIntegration,
        );
      }
    }
  },
);

export const calculateDayDiffResultTest = Api(
  Post('/debug/cal_day_diff'),
  Data(
    z.object({
      startTimestamp: z.number(),
      stopTimestamp: z.number(),
    }),
  ),
  async ({ data }) => {
    const { startTimestamp, stopTimestamp } = data;
    return calculateDayDiffResult(startTimestamp, stopTimestamp);
  },
);

export const createLarkGroup = Api(Post('/debug/create_lark_group'), Data(z.any()), async ({ data }) => {
  const larkService = useInject(LarkService);
  const emails = ['<EMAIL>'];
  const groupUserIds = await larkService.batchGetUserId(UserIdType.userId, { emails });
  const result = await larkService.createLarkGroup(
    {
      user_id_type: UserIdType.userId,
    },
    {
      name: `测试拉群`,
      description: `用于测试自动拉群`,
      owner_id: groupUserIds[0].user_id,
      user_id_list: groupUserIds
        .filter((value: UserData) => value.user_id)
        .map((value: UserData) => value.user_id)
        .filter(value => value),
      bot_id_list: ['cli_9c8628b7b1f1d102'],
    },
  );
  if (result.chat_id) {
    // TODO: 发送飞书卡片
  }
  return {
    code: 0,
    message: 'ok',
    chatId: result.chat_id ?? '',
  };
});

export const testAutoCheck = Api(Get('/open/debug/auto_check'), async () => {
  const autoVersionStageService = useInject(AutoVersionStageService);
  const res = await autoVersionStageService.autoCheck();
  return {
    code: 0,
    message: 'ok',
    res,
  };
});

export const testParseLibraDesignDoc = Api(
  Post('/debug/libra_design_doc_parse'),
  Data(
    z.object({
      docUrl: z.string(),
      flightId: z.number().optional(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraDesignDocCheckService);
    return await service.getLibraDesignInfoFromDoc(data.docUrl, data.flightId);
  },
);

export const testLibraDesignDocCheck = Api(
  Post('/debug/libra_design_doc_check'),
  Data(z.object({})),
  async ({ data }) => {
    const service = useInject(LibraDesignDocCheckService);
    const libraApi = useInject(LibraAPIService);
    const docUrl = 'https://bytedance.larkoffice.com/docx/AmWDdLRz8opBQTx4IoZcfl1nnvh';
    const flight = await libraApi.queryFlight(LibraRegion.CN, 3870050);
    if (flight) {
      const result = await service.checkLibraByDoc(flight, docUrl);
      return result;
    }
    return {
      code: -1,
      message: 'flight not found',
    };

    // 解析文档配置信息
    // const service = useInject(LibraDesignDocCheckService);
    // return await service.getLibraDesignInfoFromDoc('https://bytedance.larkoffice.com/docx/JZNgdHag5o3epyxG7WxcJotfnRb');
    // return await service.getLibraDesignInfoFromDoc('https://bytedance.larkoffice.com/docx/LfJLdkQXxo39KQxjFK8cJDI7nTd');
    // 检查实验配置是否合法;
    // const handler = useInject(StartFlightControlHandler);
    // const req: LibraControlReq = {
    //   app_id: '305',
    //   flight_id: '71274956',
    //   user: 'wendachuan',
    //   event_type: FlightEventType.StartFlight,
    //   version_resource: 0,
    //   region: 'sg',
    //   owners: ['wendachuan'],
    //   tags: [],
    //   configs: [],
    //   priority: 0,
    // };
    // return await handler.checkLibraValid(req);
    // 获取纸飞机的空间原信息
    // const service = useInject(LarkDocService);
    // const rootFolderMetaResult = await service.getRootFolderMeta();
    // const rootFolderMeta = rootFolderMetaResult.data;
    // const folderToken = rootFolderMeta.token;
    // // 拷贝实验设计文档
    // const copyFileResult = await service.copyFile(
    //   '实验设计文档创建(测试)',
    //   'DnhhdBJfKoUEKyxFcVOclmtmnTd',
    //   LarkFileType.docx,
    //   folderToken,
    // );
    // const copyFile = copyFileResult.data.file;
    // // 创建完成后，将权限转移
    // if (copyFileResult.code === 0 && copyFile && copyFile.token.length > 0) {
    //   // 错误码，非 0 表示失败
    //   const transOwnerResult = await service.transferOwner(
    //     copyFile.token,
    //     LarkFileType.docx,
    //     'email',
    //     '<EMAIL>',
    //   );
    //   return transOwnerResult;
    // }
    // return {
    //   code: 0,
    //   message: 'ok',
    // };
  },
);

export const testIsGrayLibra = Api(
  Post('/debug/is_gray_libra'),
  Data(
    z.object({
      flightId: z.number(),
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraNewInfoListService);
    return await service.isGrayFlight(data.flightId);
  },
);

export const testStartFlightControl = Api(
  Post('/open/debug/start_flight_control'),
  Data(z.object({})),
  async ({ data }) => {
    const handler = useInject(StartFlightControlHandler);
    const req: LibraControlReq = {
      app_id: '399',
      flight_id: '4012047',
      user: 'zhangyonggen',
      event_type: FlightEventType.StartFlight,
      version_resource: 0,
      region: 'cn',
      owners: ['zhangyonggen'],
      tags: [],
      configs: [],
      priority: 0,
    };
    const libraRegion = LibraRegion.CN;
    req.flight = await useInject(LibraAPIService).queryFlight(libraRegion, Number(req.flight_id));
    return await handler.handle(req);
  },
);

export const testLibraSmoothTrafficChange = Api(
  Post('/debug/libra_smooth_traffic_change'),
  Data(
    z.object({
      flightId: z.number(),
      event_type: z.string(),
      version_resource: z.number(),
      duration: z.number().optional(), // 平滑放量持续时间，单位：分钟
    }),
  ),
  async ({ data }) => {
    const service = useInject(LibraService);
    return await service.smoothChangeFlightResource(
      data.flightId.toString(),
      data.event_type,
      data.version_resource,
      data.duration ?? 0,
    );
  },
);
