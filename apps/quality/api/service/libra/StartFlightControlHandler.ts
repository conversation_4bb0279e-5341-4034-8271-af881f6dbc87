import { BaseControlHandler } from './BaseControlHandler';
import { Inject, Injectable } from '@gulux/gulux';
import {
  AbnormalFlightReportInfoType,
  FlightEventType,
  isBetaFlight,
  LibraControlReq,
  LibraControlRes,
  LibraControlTakeOverType,
} from '@shared/libra/libraControl';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LibraInfoEngine from '../LibraInfoEngine';
import { Ad_inland, Ad_oversea, LibraControllerRule, LibraPlatform, LibraRegion } from '@shared/libra/commonLibra';
import LibraService from '../third/libra';
import { LibraMeego } from '@shared/libra/libraInfo';
import { ConditionBundleItem, ConditionItem, Flight } from '@shared/libra/flight';
import SlardarTestBugItemModel from '../../model/SlardarTestBugTable';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import { NetworkX } from '@pa/backend/dist/src/utils/network';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import MeegoRawService from '../third/meego';
import { WorkItemInfo } from '@shared/typings/meego';
import { OnlineVersion } from '@shared/libra/NonOpenCommon';
import { cc2lvVersion, lv2ccVersion } from '@shared/utils/version_utils';
import { LibraNewInfoListService } from './LibraNewInfoListService';
import { LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { getLibraVersionPlatformFromFilterRule, isBetaByFilterRule } from '../../utils/libraUtil';
import { GrayOrRelease } from '@shared/libra/LibraCreate';
import { AbnormalFlightReportInfoService } from './AbnormalFlightReportInfoService';
import { current_region, Region } from '../../utils/region';
import { LibraDesignDocCheckService } from './LibraDesignDocCheckService';
import { LibraPCCheckService } from './LibraPCCheckService';

/**
 * 开实验管控规则:
 * 1. 3灰后不允许开新灰度实验
 * 2. 开全量实验必须关联灰度实验
 * 3. 开实验商业化管控，https://bytedance.larkoffice.com/wiki/P9ZRwCx4xiVeNYkfedcc1x9mnkg
 */
@Injectable()
export class StartFlightControlHandler extends BaseControlHandler {
  @Inject()
  private logger: BytedLogger;

  @Inject()
  private libraEngine: LibraInfoEngine;

  @Inject()
  private libraService: LibraService;

  @Inject()
  private slardarTestBugModel: SlardarTestBugItemModel;

  @Inject()
  private rpcProxy: RpcProxyManager;
  @Inject()
  private meegoService: MeegoRawService;
  @Inject()
  private libraNewInfoListService: LibraNewInfoListService;
  @Inject()
  private abnormalReportService: AbnormalFlightReportInfoService;
  @Inject()
  private libraDesignDocCheckService: LibraDesignDocCheckService;
  @Inject()
  private libraPCCheckService: LibraPCCheckService;

  async canHandle(req: LibraControlReq): Promise<boolean> {
    return req.event_type === FlightEventType.StartFlight;
  }

  async getMeegoOnlineVersion(flightDetail: Flight) {
    const response: OnlineVersion[] = [];
    if (!flightDetail.meego_info) {
      this.logger.info(`[checkAfterThreeGray] flightDetail.meego_info is null, flight_id=${flightDetail.id}`);
      return response;
    }
    const meegoInfos: WorkItemInfo[] = [];
    const libraMeegoGroups: Record<string, LibraMeego[]> = {};
    for (const libraMeegoInfo of flightDetail.meego_info.meego_array) {
      if (!libraMeegoGroups[libraMeegoInfo.meego_project]) {
        libraMeegoGroups[libraMeegoInfo.meego_project] = [];
      }
      libraMeegoGroups[libraMeegoInfo.meego_project].push(libraMeegoInfo);
    }
    for (const project of Object.keys(libraMeegoGroups)) {
      const workItemIds = libraMeegoGroups[project].map(meegoInfo => parseInt(meegoInfo.meego_story, 10));
      const groupMeegoInfos = await this.meegoService.requestWorkItem(project, 'story', workItemIds);
      meegoInfos.push(...(groupMeegoInfos.data ?? []));
    }
    // 如果有一个meego单的上车版本大于3灰，不允许开灰度实验
    for (const meegoInfo of meegoInfos) {
      const planingVersions = meegoInfo?.fields?.find(f => f.field_key === 'planning_version')?.field_value as number[];
      if (planingVersions !== undefined) {
        const versionId = planingVersions ?? [];
        const result = await this.meegoService.queryVersionById(meegoInfo.project_key, versionId);
        if (result !== 0) {
          const versions = (result as { versionId: number; versionName: string }[]).map(value =>
            value.versionName.toLowerCase(),
          );
          let appid: number, platform: string, version: string;
          for (const rawString of versions) {
            if (['剪映'].some(v => rawString.includes(v))) {
              appid = 1775;
            } else if (['cc', 'capcut'].some(v => rawString.includes(v))) {
              appid = 3006;
            } else if (['醒图'].some(v => rawString.includes(v))) {
              appid = 2515;
            } else if (['hypic'].some(v => rawString.includes(v))) {
              appid = 7356;
            } else if (['即梦'].some(v => rawString.includes(v))) {
              appid = 581595;
            } else {
              continue;
            }

            if (['android'].some(v => rawString.includes(v))) {
              platform = 'android';
            } else if (['ios'].some(v => rawString.includes(v))) {
              platform = 'ios';
            } else {
              continue;
            }

            const match = rawString.match(/(\d+\.\d+\.\d+)$/);
            if (match) {
              version = match[1];
              response.push({
                appid,
                platform,
                version,
              });
            }
          }
        }
      }
    }
    return response;
  }

  async checkIsGray(req: LibraControlReq) {
    // return req.flight?.beta_combine_type === BETA_COMBINE_TYPE_RELEASE;
    const isCN = req.region === 'cn';
    const p = getLibraVersionPlatformFromFilterRule(
      req.flight?.filter_rule ?? [],
      isCN ? LibraRegion.CN : LibraRegion.SG,
      req.flight?.name ?? '',
      req.flight?.description ?? '',
    );
    const filterRes = isBetaFlight(p);
    let queryRes = false;
    const query = {
      'flightInfo.id': Number(req.flight_id),
    };
    let libraNewInfo = await this.libraNewInfoListService.findOne(query);
    if (!libraNewInfo && current_region() === Region.CN) {
      const overseasList = await this.rpcProxy.getQuality(true).getLibraNewInfoList(query);
      if (overseasList && Array.isArray(overseasList)) {
        const flightList = overseasList as LibraNewInfo[];
        if (flightList.length > 0) {
          libraNewInfo = flightList[0];
        }
      }
    }
    if (libraNewInfo) {
      queryRes =
        libraNewInfo.extraInfo?.grayOrRelease === GrayOrRelease.gray ||
        isBetaByFilterRule(
          libraNewInfo.flightInfo.filterRule,
          libraNewInfo.flightInfo.region,
          libraNewInfo.flightInfo.name,
          libraNewInfo.flightInfo.description,
        );
    }
    return filterRes || queryRes;
  }

  async handle(req: LibraControlReq): Promise<LibraControlRes | undefined> {
    const checkByCubaResult = await this.checkCommercialLibraRuleByCuba(req.app_id, req.flight_id, req.owners);
    if (checkByCubaResult?.take_over === true) {
      return checkByCubaResult;
    }
    // pc检测
    if (req.app_id === '360' || req.app_id === '399') {
      const checkPCLibraResult = await this.libraPCCheckService.checkPCLibra(req);
      if (checkPCLibraResult?.take_over === true) {
        return checkPCLibraResult;
      }
    }
    // 纸飞机实验检测：实验基础信息 + libra 文档检测
    const checkLibraValidResult = await this.checkLibraValid(req);
    if (checkLibraValidResult?.take_over === true) {
      return checkLibraValidResult;
    }
    // const commercialLibraType = this.getCommercialType(req);
    // if (commercialLibraType !== 'unknown' && (req.flight?.type === 'strategy' || req.flight?.type === 'ad_user')) {
    //   const controlType = this.checkCommercialLibraRule(req, commercialLibraType);
    //   if (controlType !== 'unknown') {
    //     return {
    //       take_over: true,
    //       take_over_reason: controlType.toString(),
    //       take_over_type: LibraControlTakeOverType.CommercialLibraCheckFailed,
    //     };
    //   }
    // }
    if (!req.flight) {
      return {
        take_over: false,
        take_over_reason: 'openapi未找到对应的实验信息',
      };
    }
    // if (req.flight.kind !== KIND_BETA_COMBINE) {
    //   return {
    //     take_over: true,
    //     take_over_reason:
    //       '剪映/Capcut客户端需求实验必须走“众内测组合实验”, 详见: [剪映&CapCut A/B 实验流程SOP](https://bytedance.larkoffice.com/wiki/wikcn3FSeBpV6tP78qanqPVAd3b)',
    //   };
    // }
    const needCheck = await this.rpcProxy.getHost().getLibraControlSetting();
    const isGray = await this.checkIsGray(req);
    if (isGray) {
      // 开灰度实验
      // 1. 灰度实验开启时间不得大于三灰
      if (!req.flight) {
        this.logger.info(`[checkAfterThreeGray] flight is null, flight_id=${req.flight_id}`);
        return {
          take_over: false,
          take_over_reason: 'openapi未找到对应的实验信息',
        };
      }
      const isCN = req.region === 'cn';
      const hitVersionPlatform = getLibraVersionPlatformFromFilterRule(
        req.flight.filter_rule,
        isCN ? LibraRegion.CN : LibraRegion.SG,
        req.flight?.name ?? '',
        req.flight?.description ?? '',
      );
      if (!hitVersionPlatform.android.isHit && !hitVersionPlatform.iphone.isHit) {
        // 不是安卓也不是iOS，不是客户端实验
        return {
          take_over: false,
          take_over_reason: `无需管控`,
        };
      }
      const response = await this.getMeegoOnlineVersion(req.flight);
      for (const version of response) {
        const lvVersion = version.appid === 1775 ? version.version : cc2lvVersion(version.version);
        const ret = await this.checkAfterThreeGray(
          lvVersion,
          version.platform === 'android' ? LibraPlatform.android : LibraPlatform.iOS,
          needCheck,
          version.appid,
        );
        if (ret?.take_over) {
          // 记录异常信息
          await this.abnormalReportService.saveAfterThirdGrayOrFullReleaseFlightByType(
            Number(req.flight.meego_info.meego_array[0].meego_story),
            req.flight.id,
            AbnormalFlightReportInfoType.StoryStartFlightAfterThirdGray,
          );
          return ret;
        }
      }
      // const versionPlatform: LibraVersionPlatform = getLibraVersionPlatformFromFilterRule(req.flight.filter_rule);
      // if (versionPlatform.android.isHit) {
      //   const ret = await this.checkAfterThreeGray(versionPlatform.android.version, LibraPlatform.android);
      //   if (ret?.take_over) {
      //     return ret;
      //   }
      // }
      // if (versionPlatform.iphone.isHit) {
      //   const ret = await this.checkAfterThreeGray(versionPlatform.iphone.version, LibraPlatform.iPhone);
      //   if (ret?.take_over) {
      //     return ret;
      //   }
      // }
      // }
    } else {
      // 开正式实验
      // 1. 判断关联的meego已有灰度实验
      if (!req.flight) {
        this.logger.info(`[checkAfterThreeGray] flight is null, flight_id=${req.flight_id}`);
        return {
          take_over: false,
          take_over_reason: 'openapi未找到对应的实验信息',
        };
      }
      //判断绑定的meego是否有"前后端部署"节点，若有，则判断是否已完成该节点
      const meegoNodeStatusRes = await this.checkMeegoNode(req.flight);
      if (meegoNodeStatusRes.take_over) {
        return meegoNodeStatusRes;
      }
      const allVersions = await this.getMeegoOnlineVersion(req.flight);
      this.logger.info(`[checkAfterThreeGray] allVersions=${JSON.stringify(allVersions)}`);
      const isCN = req.region === 'cn';
      const hitVersionPlatform = getLibraVersionPlatformFromFilterRule(
        req.flight.filter_rule,
        isCN ? LibraRegion.CN : LibraRegion.SG,
        req.flight.name,
        req.flight.description,
      );
      if (!hitVersionPlatform.android.isHit && !hitVersionPlatform.iphone.isHit) {
        // 不是安卓也不是iOS，不是客户端实验
        return {
          take_over: false,
          take_over_reason: `无需管控`,
        };
      }
      for (const version of allVersions) {
        let appId = AppSettingId.LV_IOS;
        if (version.appid === 1775 && version.platform === 'android') {
          appId = AppSettingId.LV_ANDROID;
        } else if (version.appid === 3006 && version.platform === 'android') {
          appId = AppSettingId.CC_ANDROID;
        } else if (version.appid === 3006 && version.platform === 'ios') {
          appId = AppSettingId.CC_IOS;
        } else {
          continue;
        }
        this.logger.info(`[checkAfterThreeGray] isFullRelease: appId=${appId}, version=${version.version}`);
        const isFullRelease = await this.rpcProxy.getHost().isFullRelease(version.version, appId);
        if (!isFullRelease) {
          continue;
        }
        // 根据meego单查找已有的实验，如果没有灰度实验，不允许开正式实验
        let hasGray = false;
        if (req.flight.meego_info.meego_array.length === 0) {
          continue;
        }
        for (const libraMeego of req.flight.meego_info.meego_array) {
          const query = {
            'meegoInfo.id': Number(libraMeego.meego_story),
          };
          let flightList = [];
          if (current_region() === Region.CN) {
            const overseasList = await this.rpcProxy.getQuality(true).getLibraNewInfoList(query);
            this.logger.info(
              `[checkAfterThreeGray] isGray: appId=${appId}, version=${version.version}, overseasList=${JSON.stringify(overseasList)}`,
            );
            if (overseasList && Array.isArray(overseasList)) {
              flightList.push(...(overseasList as LibraNewInfo[]));
            }
          } else {
            flightList = (await this.libraNewInfoListService.listAll(query)) ?? [];
          }
          if (!flightList || flightList.length === 0) {
            continue;
          }
          for (const flight of flightList) {
            const versionPlatform = getLibraVersionPlatformFromFilterRule(
              flight.flightInfo.filterRule,
              flight.flightInfo.region,
              flight.flightInfo.name,
              flight.flightInfo.description,
            );
            this.logger.info(
              `[checkAfterThreeGray] isGray: appId=${appId}, version=${version.version}, flight=${flight.flightInfo.id}, versionPlatform=${JSON.stringify(versionPlatform)}`,
            );
            if (
              flight.extraInfo?.grayOrRelease === GrayOrRelease.gray ||
              isBetaByFilterRule(
                flight.flightInfo.filterRule,
                flight.flightInfo.region,
                flight.flightInfo.name,
                flight.flightInfo.description,
              )
            ) {
              hasGray = true;
              return {
                take_over: false,
                take_over_reason: `无需管控`,
              };
            }
          }
          if (!hasGray) {
            await this.abnormalReportService.saveAfterThirdGrayOrFullReleaseFlightByType(
              Number(libraMeego.meego_story),
              req.flight.id,
              AbnormalFlightReportInfoType.StoryReleaseFlightWithNoGrayFlight,
            );
            if (!needCheck) {
              return {
                take_over: false,
                take_over_reason: '未命中开实验管控策略',
              };
            }
            return {
              take_over: true,
              take_over_reason: `该实验关联的meego单${libraMeego.meego_story}没有灰度实验，不允许开正式实验`,
              take_over_type: LibraControlTakeOverType.StartReleaseWithoutGray,
            };
          }
          return {
            take_over: false,
            take_over_reason: `无需管控`,
          };
        }
      }
      // 2. 继承灰度实验的所有bug单
      // await this.slardarTestBugModel.migrateAllIssue(
      //   combineGroup.gray_flight.id.toString(),
      //   combineGroup.release_flight.id.toString(),
      // );
      //
      // // 3. 判断实验没有未解决的P0/P1问题
      // const allBugs = await useInject(LibraControlService).queryFlightIssueList(req.flight_id);
      // const unsolvedBugs = allBugs.filter(info => !info.isFixed && info.priority <= 1);
      // if (unsolvedBugs.length >= 0) {
      //   return {
      //     take_over: true,
      //     take_over_reason: `该实验有未解决的P0/P1bug共${unsolvedBugs.length}个，阻塞放量`,
      //   };
      // }
    }
    // if (req.user.includes('zhanglinwei')) {
    //   return {
    //     take_over: true,
    //     take_over_reason: '命中黑名单用户管控',
    //   };
    // }
    return {
      take_over: false,
      take_over_reason: '未命中开实验管控策略',
    };
  }

  private async checkAfterThreeGray(version: string, platform: LibraPlatform, needCheck: boolean, aId: number) {
    const appId = platform === LibraPlatform.android ? 177502 : 177501;
    const isAfter = await this.rpcProxy.getHost().isAfterThirdGray(version, appId);
    // const versionInfo = await this.libraEngine.getVersionProcessInfo(currentProduct(), version);
    // if (globalBusinessType() === BusinessType.LV) {
    //   if (platform === LibraPlatform.android) {
    //     // android根据小版本号判断
    //     if (Number(versionInfo.android.versionCode) % 10000 >= 210) {
    //       return {
    //         take_over: true,
    //         take_over_reason: `三灰之后不允许开新实验（当前Android版本${versionInfo.android.versionCode}）`,
    //       };
    //     }
    //   } else if (platform === LibraPlatform.iPhone) {
    //     // ios根据阶段判断
    //     if (!['', '一灰', '二灰'].includes(versionInfo.iOS.stage)) {
    //       return {
    //         take_over: true,
    //         take_over_reason: `三灰之后不允许开新实验（当前iOS阶段: ${versionInfo.iOS.stage}）`,
    //       };
    //     }
    //   }
    // }
    const actualVersion = aId === 3006 ? lv2ccVersion(version) : version;
    if (isAfter) {
      if (!needCheck) {
        return {
          take_over: false,
          take_over_reason: '未命中开实验管控策略',
        };
      }
      return {
        take_over: true,
        take_over_reason: `三灰之后不允许开新灰度实验（当前${platform}版本${actualVersion}）`,
        take_over_type: LibraControlTakeOverType.GrayNotAllowedAfter3rdGray,
      };
    }
    return undefined;
  }

  //判断绑定的meego是否有"前后端部署"节点，若有，则判断是否已完成该节点
  async checkMeegoNode(flightDetail: Flight) {
    if (!flightDetail.meego_info) {
      this.logger.info(`[checkAfterThreeGray] flightDetail.meego_info is null, flight_id=${flightDetail.id}`);
      return {
        take_over: false,
        take_over_reason: '未绑定Meego',
      };
    }
    const meegoInfos: WorkItemInfo[] = [];
    const libraMeegoGroups: Record<string, LibraMeego[]> = {};
    for (const libraMeegoInfo of flightDetail.meego_info.meego_array) {
      if (!libraMeegoGroups[libraMeegoInfo.meego_project]) {
        libraMeegoGroups[libraMeegoInfo.meego_project] = [];
      }
      libraMeegoGroups[libraMeegoInfo.meego_project].push(libraMeegoInfo);
    }
    for (const project of Object.keys(libraMeegoGroups)) {
      const workItemIds = libraMeegoGroups[project].map(meegoInfo => parseInt(meegoInfo.meego_story, 10));
      for (const workItemId of workItemIds) {
        const groupMeegoInfos = await this.meegoService.requestWorkflow('story', workItemId);
        if (
          groupMeegoInfos &&
          groupMeegoInfos.data &&
          groupMeegoInfos.data.workflow_nodes &&
          Array.isArray(groupMeegoInfos.data.workflow_nodes)
        ) {
          for (const workflowNode of groupMeegoInfos.data.workflow_nodes) {
            if (workflowNode.name === '前后端部署' && workflowNode.status !== 3) {
              return {
                take_over: true,
                take_over_reason: '绑定的需求未完成前后端部署, 安全合规节点可能未完成，可能引发安全问题',
              };
            }
          }
        }
      }
    }
    return {
      take_over: false,
      take_over_reason: '未命中开实验管控策略',
    };
  }

  private checkFilterRule(req: LibraControlReq, commercialLibraType: string) {
    const checkRes = {
      take_over: false,
      take_over_reason: '',
    };
    const ruleList = req.flight?.filter_rule ?? [];
    for (const { conditions } of ruleList) {
      let find = false;
      for (const condition of conditions) {
        if (condition.condition.key === 'app_id') {
          find = true;
        } else if (
          ['_version_code', '_version_code_normal', '_update_version_code'].includes(condition.condition.key) &&
          // (commercialLibraType === Subscribe_oversea || commercialLibraType === Subscribe_inland) &&  //广告有部分也用的_version_code字段
          this.checkSubscribeVersionCodeRule(condition.condition.value)
        ) {
          // 订阅：版本号规则_version_code,配置错误
          return LibraControllerRule.VersionCodeSubscribeRule;
        } else if (
          ['android_vc', 'ios_vc'].includes(condition.condition.key) &&
          (commercialLibraType === Ad_inland || commercialLibraType === Ad_oversea) &&
          this.checkAdVersionCodeRule(condition.condition, commercialLibraType)
        ) {
          // 广告版本号规则_version_code,配置错误
          return LibraControllerRule.VersionCodeAdRule;
        } else if (
          condition.condition.type === 'string' &&
          condition.condition.transformer !== 'lower' &&
          condition.condition.key !== 'path'
        ) {
          // 字符串参数，值有英文的未配置兼容小写规则
          const valueList = condition.condition.value as string[];
          for (const value of valueList) {
            if (/[A-Za-z]+/g.test(value)) {
              return LibraControllerRule.StrLowerRule;
            }
          }
        }
      }
      if (!find) {
        return LibraControllerRule.AppIdRule;
      }
    }
    return 'unknown';
  }

  private checkSubscribeVersionCodeRule(version: number | string[] | ConditionBundleItem[] | string | number[]) {
    const versionCode = parseInt(version.toString(), 10);
    // 103000,100030 不合法
    return version.toString().length !== 6 || versionCode % 100 > 10 || (versionCode / 100) % 100 > 10;
  }

  private checkAdVersionCodeRule(conditionItem: ConditionItem, commercialLibraType: string) {
    if (conditionItem.key === 'android_vc') {
      const versionCode = conditionItem.value.toString();
      if (commercialLibraType === Ad_inland) {
        return versionCode.length !== 9 || versionCode[4] !== '0';
      } else if (commercialLibraType === Ad_oversea) {
        return versionCode.length !== 8;
      }
    } else if (conditionItem.key === 'ios_vc') {
      const versionCode = conditionItem.value.toString().split('.');
      // 13.5.0.16 12.6.0.80 合法，中间两位>10非法
      return versionCode.length !== 4 || parseInt(versionCode[1], 10) > 10 || parseInt(versionCode[2], 10) > 10;
    }
  }

  private checkLayerName(req: LibraControlReq, takeOverFilterRuleType: string) {
    const { flight } = req;
    return (
      takeOverFilterRuleType === Ad_inland &&
      (flight?.layer_domain === undefined ||
        flight?.layer_domain.id !== 972 || // 互斥域 https://data.bytedance.net/libra/access/147/layers
        flight?.layer_domain.name !== '剪映-广告-功能实验')
    );
  }

  private checkCommercialLibraRule(req: LibraControlReq, commercialLibraType: string) {
    const takeOverFilterRuleType = this.checkFilterRule(req, commercialLibraType);
    if (takeOverFilterRuleType !== 'unknown') {
      // 订阅&广告：FilterRule check
      return takeOverFilterRuleType;
    }
    if (this.checkLayerName(req, commercialLibraType)) {
      // 流量层不属于互斥域
      return LibraControllerRule.layerNameRule;
    }
    return 'unknown';
  }

  private async checkCommercialLibraRuleByCuba(app_id: string, flight_id: string, owners: string[]) {
    // 转发给 Cuba 检查商业化的实验
    // http://localhost:8082/api/check_libra
    const host =
      current_region() === Region.SG ? 'https://cuba-sg.sg-fn.bytedance.net' : 'https://mycuba.fn.bytedance.net';
    const network = new NetworkX(`${host}/api/check_libra`, {});
    let checkResult: any, handOverResult: any;
    try {
      checkResult = await network.post('', {
        app_id,
        flight_id,
        owners,
      } as any);
    } catch (e) {
      this.logger.error(
        `[StartFlightControlHandler] checkCommercialLibraRuleByDoc to CUBA failed: ${JSON.stringify(e)}`,
      );
    }
    this.logger.error(
      `[handleControl] StartFlightControlHandler checkCommercialLibraRuleByDoc result: ${JSON.stringify(checkResult)}`,
    );

    const checkCode = checkResult?.code ?? -1000;
    const checkMsg = checkResult?.msg;
    const basicCheckCode = checkResult?.result?.basicCheckResult?.code;
    const basicCheckResult = checkResult?.result?.basicCheckResult?.checkResult ?? [];
    const byDocCheckCode = checkResult?.result?.checkByDocResult?.code;
    const byDocCheckMsg = checkResult?.result?.checkByDocResult?.msg;
    const byDocCheckResult = checkResult?.result?.checkByDocResult?.checkResult ?? [];

    // 1. 基础检查 2. 文档检查
    let reason = '';
    if (checkCode && checkCode < 0) {
      reason += `检查错误：${checkMsg}`;
    }
    if (basicCheckCode && basicCheckCode < 0) {
      reason += '\n';
      reason += `[基础检查]：${basicCheckResult.msg}`;
    } else if (basicCheckResult.length > 0) {
      reason += '\n';
      reason += `[基础检查]：${basicCheckResult.map((e: any) => e.name).join('; ')}`;
    }

    if (byDocCheckCode && byDocCheckCode < 0) {
      reason += '\n';
      reason += `[文档检查]：${byDocCheckMsg}`;
    } else if (byDocCheckResult.length > 0) {
      reason += '\n';
      reason += `[文档检查]：${byDocCheckResult.map((e: any) => `${e.name}: 期望 ${e.require}, 实际: ${e.actual}`).join('; ')}`;
    }

    if (reason.length > 0) {
      handOverResult = {
        take_over: true,
        take_over_reason: `【商业化校验(负责人:zhengzinan.zzn,zhuruibin)】Cuba实验检查错误： ${reason}`,
        take_over_type: LibraControlTakeOverType.CommercialLibraCubaCheckFailed,
      };
    } else {
      handOverResult = {
        take_over: false,
        take_over_reason: '',
      };
    }
    this.logger.error(
      // eslint-disable-next-line max-len
      `[handleControl] StartFlightControlHandler checkCommercialLibraRuleByDoc handover result: ${JSON.stringify(handOverResult)}`,
    );
    return handOverResult;
  }

  async checkLibraValid(req: LibraControlReq) {
    const checkResult = await this.libraDesignDocCheckService.checkLibra(req);
    let handOverResult: any;
    this.logger.info(
      `[handleControl] StartFlightControlHandler checkLibraValid result: ${JSON.stringify(checkResult)}`,
    );

    const checkCode = checkResult?.code ?? -1000;
    const checkMsg = checkResult?.msg ?? '';
    const basicCheckCode = checkResult?.basicCheckResult?.code;
    const basicCheckMsg = checkResult?.basicCheckResult?.msg ?? '';
    const basicCheckResult = checkResult?.basicCheckResult?.checkResult ?? [];
    const byDocCheckResults = checkResult?.checkByDocResults ?? []; // 一个实验可能对应多个需求，每个需求都可能有实验文档

    // 1. 基础检查
    let reason = '';
    if (checkCode && checkCode < 0) {
      reason += `检查错误：${checkMsg}`;
    }
    if (basicCheckCode && basicCheckCode < 0) {
      reason += '\n';
      reason += `[基础检查]：${basicCheckMsg}`;
    } else if (basicCheckResult.length > 0) {
      reason += '\n';
      reason += `[基础检查]：${basicCheckResult.map(e => e.name).join('; ')}`;
    }

    //  2. 文档检查
    for (const byDocCheckSingleResult of byDocCheckResults) {
      const byDocCheckCode = byDocCheckSingleResult?.code;
      const byDocCheckMsg = byDocCheckSingleResult?.msg ?? '';
      const byDocCheckResult = byDocCheckSingleResult?.checkResult ?? [];
      if (byDocCheckCode && byDocCheckCode < 0) {
        reason += '\n';
        reason += `[文档检查]：${byDocCheckMsg}`;
      } else if (byDocCheckResult.length > 0) {
        reason += '\n';
        reason += `[文档检查]：${byDocCheckResult.map(e => `${e.name}: 期望 ${e.require}, 实际: ${e.actual}`).join('; ')}。请参考链接处理：https://bytedance.larkoffice.com/wiki/JIKywsI0WiShQPksqE5czgo9nbh#IyOsdvmJVomrb2xGSQzcCfjZn4e`;
      }
    }

    if (reason.length > 0) {
      handOverResult = {
        take_over: true,
        take_over_reason: `【实验检查错误（基础信息+实验设计文档）】${reason}`,
        take_over_type: LibraControlTakeOverType.BasicInfoCheckFailed,
      };
    } else {
      handOverResult = {
        take_over: false,
        take_over_reason: '',
      };
    }
    this.logger.info(
      `[handleControl] StartFlightControlHandler checkLibraValid handover result: ${JSON.stringify(handOverResult)}`,
    );
    return handOverResult;
  }
}
