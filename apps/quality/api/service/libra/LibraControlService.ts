import { Inject, Injectable } from '@gulux/gulux';
import {
  ControlProcessType,
  ControlStatus,
  FlightEventType,
  getFlightEventName,
  LibraControlRecord,
  LibraControlRecordExt,
  LibraControlReq,
  LibraControlRes,
  LibraControlTakeOverType,
} from '@shared/libra/libraControl';
import { useInject, useReq } from '@edenx/runtime/bff';
import { LibraControlRecordModel, LibraControlRecordTable } from '../../model/LibraControlRecordTable';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import dayjs from 'dayjs';
import { ChangeResourceControlHandler } from './ChangeResourceControlHandler';
import LarkService from '@pa/backend/dist/src/third/lark';
import { NetworkCode } from '@pa/shared/dist/src/core';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { add_suffix_ne, trim_suffix } from '@shared/utils/tools';
import LarkCardService from '../larkCard';
import { fail, success } from '@shared/utils/api';
import LibraService from '../third/libra';
import SlardarTestBugItemModel from '../../model/SlardarTestBugTable';
import MeegoService from '../meego';
import { LarkCard } from '@pa/shared/dist/src/lark/larkCard';
import { currentProductName } from '../../utils/basic';
import LibraInfoModel from '../../model/LibraInfoTable';
import { LastGradualStatus } from '@shared/libra/flight';
import { StartFlightControlHandler } from './StartFlightControlHandler';
import { StopFlightControlHandler } from './StopFlightControlHandler';
import timezone from 'dayjs/plugin/timezone';
import { inPeakTime, isBetaByFilterRule, isSmallFlow } from '../../utils/libraUtil';
import LibraNewInfoListDao from '../../dao/libra/LibraNewInfoListDao';
import { LibraFlightStatus, LibraFlightType, LibraNewInfo, SendToHostFlightInfo } from '@shared/libra/LibraNewInfo';
import { GrayOrRelease } from '@shared/libra/LibraCreate';
import LibraAPIService from './LibraAPIService';
import { LibraRegion } from '@shared/libra/commonLibra';

/**
 * 第三方控制实验流程技术方案
 * https://bytedance.larkoffice.com/docx/VWZNdYkgqoW2RExP9X9cboElnOA
 */
@Injectable()
export class LibraControlService {
  @Inject()
  private recordModel: LibraControlRecordModel;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private larkCard: LarkCardService;
  @Inject()
  private libraService: LibraService; // 主要用于实验管控相关：实验放量、实验流量 reset
  @Inject()
  private libraServiceNew: LibraAPIService; // 主要用于实验信息查询
  @Inject()
  private testBugModel: SlardarTestBugItemModel;
  @Inject()
  private meegoService: MeegoService;
  @Inject()
  private libraInfoModel: LibraInfoModel;
  @Inject()
  private libraNewInfoListDao: LibraNewInfoListDao;

  getHandlerList() {
    return [
      useInject(StartFlightControlHandler),
      useInject(ChangeResourceControlHandler),
      useInject(StopFlightControlHandler),
    ];
  }

  async handleControl(req: LibraControlReq) {
    this.logger.info(`[handleControl] req:${JSON.stringify(req)}`);
    let takeOverResult: LibraControlRes | undefined;
    const libraRegion = this.getLibraRegion(req.region);
    req.flight = await this.libraServiceNew.queryFlight(libraRegion, Number(req.flight_id));
    await this.tryFixLastGradualTraffic(req);
    for (const handler of this.getHandlerList()) {
      if (await handler.canHandle(req)) {
        takeOverResult = await handler.handle(req);
        break;
      }
    }
    if (takeOverResult) {
      const item = req as LibraControlRecord;
      item.status = takeOverResult.take_over ? ControlStatus.TakeOver : ControlStatus.Pass;
      item.take_over = takeOverResult.take_over;
      item.take_over_reason = takeOverResult.take_over_reason;
      if (takeOverResult.take_over_type) {
        item.take_over_type = takeOverResult.take_over_type;
      }
      item.take_over_logid = useReq().header['x-tt-logid'];
      item.create_ts = dayjs().unix();
      item.flight_name = item.flight?.name;
      // 设置一下平滑放量信息
      item.last_gradual_traffic = req.flight?.last_gradual_traffic;
      if (item.last_gradual_traffic === undefined) {
        // 如果为 undefined，则删除一下 last_gradual_traffic 字段
        delete item.last_gradual_traffic;
      }
      this.logger.info(`[handleControl] res: ${JSON.stringify(item)}`);
      const newItem = await this.recordModel.save(item);
      if (item.take_over) {
        // 提示已被管控
        const result = await this.commonSendCard(item.user, this.larkCard.libraTakeOverCard(newItem[0]));
        if (result?.data?.message_id && result.data?.mentions && result.data.mentions.length > 0) {
          this.larkService.makeUrgent(result.data.message_id, [result.data.mentions[0].id]).catch();
        }
      } else {
        // 未被管控，记录放量信息
        if (req.event_type === FlightEventType.ChangeVersionResource) {
          await this.libraInfoModel.updateActualTraffic(req.flight_id, req.version_resource, dayjs().unix());
        }
      }
    }
    return success(
      {
        take_over: takeOverResult?.take_over ?? false,
      },
      takeOverResult?.take_over_reason,
    );
  }

  /**
   * 前端触发重新检测
   * @param recordId
   */
  async reCheck(recordId: string) {
    const record = await this.recordModel.queryById(recordId);
    if (!record) {
      return fail('找不到实验管控记录');
    }
    if (record.status !== ControlStatus.TakeOver) {
      return fail('当前实验处于非管控状态，无法重新检测');
    }
    let takeOverResult: LibraControlRes | undefined;
    const req = record as LibraControlReq;
    const libraRegion = this.getLibraRegion(req.region);
    req.flight = await this.libraServiceNew.queryFlight(libraRegion, Number(req.flight_id));
    for (const handler of this.getHandlerList()) {
      if (await handler.canHandle(req)) {
        takeOverResult = await handler.handle(req);
        break;
      }
    }
    this.logger.info(`[reCheck] res: ${JSON.stringify(takeOverResult)}`);
    if (takeOverResult) {
      record.status = takeOverResult.take_over ? ControlStatus.TakeOver : ControlStatus.Pass;
      await this.addAndSaveProcess(
        record,
        ControlProcessType.Retry,
        !takeOverResult.take_over,
        takeOverResult.take_over_reason,
      );
    }
    if (record.status === ControlStatus.Pass) {
      // 重新检测后通过执行操作
      await this.processNext(record);
    }
    return success(
      {
        take_over: takeOverResult?.take_over ?? false,
      },
      takeOverResult?.take_over_reason,
    );
  }

  /**
   * 执行管控下一步操作
   * @param record
   */
  async processNext(record: LibraControlRecordTable) {
    if ([FlightEventType.StartFlight, FlightEventType.ChangeVersionResource].includes(record.event_type)) {
      // 获取一下上次平滑放量的信息
      const lastGradualTraffic = record.last_gradual_traffic;
      let duration = 0;
      if (lastGradualTraffic) {
        // eslint-disable-next-line prefer-destructuring
        duration = lastGradualTraffic.duration;
      }
      const ret = await this.libraService.smoothChangeFlightResource(
        record.flight_id,
        record.event_type,
        record.version_resource,
        duration,
      );
      await this.addAndSaveProcess(
        record,
        ControlProcessType.Operate,
        ret.code === NetworkCode.Success,
        `纸飞机操作实验${ret.code === NetworkCode.Success ? '成功' : `失败(${JSON.stringify(ret)})`}，执行动作：${getFlightEventName(record)}`,
      );
      if (ret.code === NetworkCode.Success) {
        // 发送管控通过通知
        await this.commonSendCard(record.user, this.larkCard.libraProcessCard(record));
        // 记录放量信息
        await this.libraInfoModel.updateActualTraffic(record.flight_id, record.version_resource, dayjs().unix());
      }
      return ret;
    }
    return fail('不是合法的管控类型');
  }

  /**
   * 获取审批人列表
   */
  async getApprovalAdmin(recordId: string) {
    const record = await this.recordModel.queryById(recordId);

    if (record?.take_over_type === LibraControlTakeOverType.BasicInfoCheckFailed) {
      return [
        'longguoyang',
        'linwenbang',
        'zhangxiaohui.2023',
        'zhengzinan.zzn',
        'guozhi.kevin',
        'huangyanan.1',
        'weijingdong',
        'hanwei.ccat',
      ];
    }

    if (record?.take_over_type === LibraControlTakeOverType.PCLibraCheckFailed) {
      return [
        'zhengbolun.patlon',
        'yanglu.mango',
        'longguoyang',
        'zhengzinan.zzn',
        'laixiaohui',
        'zhongyue.1018',
        'huangruobing.work',
      ];
    }

    if (
      record?.take_over_type === LibraControlTakeOverType.CommercialLibraCubaCheckFailed ||
      record?.take_over_type === LibraControlTakeOverType.CommercialLibraCheckFailed
    ) {
      return ['zhengzinan.zzn', 'zhuruibin', 'yuanqianhao', 'huangyanan.1', 'weijingdong', 'longguoyang'];
    }

    return [
      'guozhi.kevin',
      'huangyanan.1',
      'weijingdong',
      'hanwei.ccat',
      'zhengzinan.zzn',
      'zhuruibin',
      'yuanqianhao',
      'longguoyang',
    ];
  }

  /**
   * 审批跳过检查
   * @param recordId
   * @param loginEmail
   * @param reason
   */
  async approveSkip(recordId: string, loginEmail: string, reason: string) {
    const record = await this.recordModel.queryById(recordId);
    if (!record) {
      return fail('找不到实验管控记录');
    }
    const adminList = await this.getApprovalAdmin(recordId);
    if (!adminList.includes(trim_suffix('@bytedance.com')(loginEmail))) {
      return fail(`您没有权限执行此操作，请联系${JSON.stringify(adminList)}`);
    }
    if (record.status !== ControlStatus.TakeOver) {
      return fail('当前实验处于非管控状态，无法审批通过');
    }
    await this.addAndSaveProcess(
      record,
      ControlProcessType.Approval,
      true,
      `由【${loginEmail}】审批通过，审批理由：${reason}`,
    );
    const ret = await this.processNext(record);
    if (ret.code === NetworkCode.Success) {
      record.status = ControlStatus.Approve;
      await record.save();
    }
    return ret;
  }

  /**
   * 查询实验关联的所有bug单
   * @param flightId
   */
  async queryFlightIssueList(flightId: string) {
    const allBugs = await useInject(SlardarTestBugItemModel).queryAll(flightId);
    const meegoList = await this.meegoService.getMeegoInfo(allBugs.map(v => v.meego_id));
    return Object.values(meegoList.data);
  }

  async commonSendCard(targetUserEmail: string, card: LarkCard) {
    let ret;
    const userId = await this.larkService.getUserIdByEmail(add_suffix_ne('@bytedance.com')(targetUserEmail));
    if (userId) {
      try {
        ret = this.larkService.sendCardMessage(UserIdType.openId, userId.open_id, card);
      } catch (e) {
        this.logger.error(`sendLibraMessage err:${targetUserEmail}`, e);
      }
    } else {
      this.logger.error(`getUserIdError email:${targetUserEmail} ${userId}`);
    }
    return ret;
  }

  /**
   * 实验管控拉群
   * @param recordId
   * @param loginEmail
   */
  async joinGroupChat(recordId: string, loginEmail: string) {
    const record = await this.recordModel.queryById(recordId);
    if (!record) {
      return fail('找不到实验管控记录');
    }
    if (!record.chat_id) {
      const allUser = [...(await this.getApprovalAdmin(recordId)), record.user];
      const userIds = await this.larkService.getUserIdByEmails(allUser.map(v => add_suffix_ne('@bytedance.com')(v)));
      if (userIds) {
        const res = await this.larkService.createLarkGroup(
          {
            user_id_type: UserIdType.openId,
          },
          {
            name: `【${currentProductName()}-纸飞机实验管控拉群】${record.flight_name}(${recordId})`,
            description: `【${currentProductName()}-纸飞机实验管控拉群】${record.flight_name}(${recordId})`,
            owner_id: userIds[0].user_id,
            user_id_list: userIds.map(v => v.user_id),
          },
        );
        if (res.chat_id) {
          record.chat_id = res.chat_id;
          await record.save();
          // 往群里发下管控消息
          if (record.take_over) {
            await this.larkService.sendCardMessage(
              UserIdType.chatId,
              res.chat_id,
              this.larkCard.libraTakeOverCard(record),
            );
          }
        }
      }
    }
    const loginUser = await this.larkService.getUserIdByEmail(add_suffix_ne('@bytedance.com')(loginEmail));
    if (loginUser && record.chat_id) {
      await this.larkService.inviteUsers2Group([loginUser?.open_id], record.chat_id);
    }
    return success({
      chat_id: record.chat_id,
    });
  }

  async cancelOperation(recordId: string) {
    const record = await this.recordModel.queryById(recordId);
    if (!record) {
      return fail('找不到实验管控记录');
    }
    if (record.status !== ControlStatus.TakeOver) {
      return fail('当前实验处于非管控状态，无法取消操作');
    }
    let libraRegion = LibraRegion.UNKNOWN;
    if (record.region === 'cn') {
      libraRegion = LibraRegion.CN;
    } else if (record.region === 'sg') {
      libraRegion = LibraRegion.SG;
    } else if (record.region === 'va') {
      libraRegion = LibraRegion.VA;
    }
    const flight = await this.libraServiceNew.queryFlight(libraRegion, Number(record.flight_id));
    if (!flight) {
      return fail('找不到实验详情信息');
    }
    if (!flight.last_gradual_traffic) {
      return fail('找不到上次放量信息，取消管控失败');
    }
    let targetVersionResource = flight.last_gradual_traffic.effective_traffic;
    if (targetVersionResource < 0 || targetVersionResource >= 1) {
      return fail(`上次放量流量异常(${targetVersionResource * 100}%)，取消操作失败`);
    }
    if (targetVersionResource === 0) {
      targetVersionResource = 0.01;
    }
    const ret = await this.libraService.resetFlightResource(record.flight_id, targetVersionResource);
    if (ret.code === NetworkCode.Success) {
      record.status = ControlStatus.Cancel;
      await record.save();
    }
    await this.addAndSaveProcess(
      record,
      ControlProcessType.Cancel,
      ret.code === NetworkCode.Success,
      `纸飞机取消实验操作${ret.code === NetworkCode.Success ? `成功，流量已回滚到${targetVersionResource * 100}%` : `失败(${JSON.stringify(ret)})`}}`,
    );
    return ret;
  }

  getLibraRegion(regionStr: string) {
    let libraRegion = LibraRegion.UNKNOWN;
    if (regionStr === 'cn') {
      libraRegion = LibraRegion.CN;
    } else if (regionStr === 'sg') {
      libraRegion = LibraRegion.SG;
    } else if (regionStr === 'va') {
      libraRegion = LibraRegion.VA;
    }
    return libraRegion;
  }

  private async addAndSaveProcess(record: LibraControlRecord, type: ControlProcessType, suc: boolean, reason: string) {
    if (!record.process_history) {
      record.process_history = [];
    }
    record.process_history?.push({
      process_type: type,
      success: suc,
      reason,
      logid: useReq().header['x-tt-logid'],
      ts: dayjs().unix(),
    });
    await this.recordModel.save(record);
  }

  /**
   * 使用 last_gradual_traffic 字段修正上次放量时间
   * @param req
   * @private
   */
  private async tryFixLastGradualTraffic(req: LibraControlReq) {
    const actualTrafficRecords = await this.libraInfoModel.getActualTrafficRecord(req.flight_id);
    if (actualTrafficRecords.length === 0 && req.flight?.last_gradual_traffic?.status === LastGradualStatus.Finish) {
      this.logger.info(`fixLastGradualTraffic ${req.flight_id} ${JSON.stringify(req.flight.last_gradual_traffic)}`);
      await this.libraInfoModel.updateActualTraffic(
        req.flight_id,
        req.flight.last_gradual_traffic.effective_traffic,
        req.flight.last_gradual_traffic.end_time,
      );
    }
  }

  async queryControlRecord(params: any) {
    const query: Partial<Record<keyof LibraControlRecordTable, any>> = {};
    if (params.title) {
      query.flight_name = {
        $regex: new RegExp(params.title),
      };
    }
    if (params.user) {
      query.user = {
        $regex: new RegExp(params.user),
      };
    }
    if (params.flight_id) {
      query.flight_id = params.flight_id;
    }
    if (params.event_type && params.event_type.length > 0) {
      query.event_type = {
        $in: params.event_type,
      };
    }
    if (params.create_ts && params.create_ts.length === 2) {
      dayjs.extend(timezone);
      query.create_ts = {
        $gte: dayjs.tz(params.create_ts[0], 'Asia/Shanghai').unix(),
        $lte: dayjs.tz(params.create_ts[1], 'Asia/Shanghai').unix(),
      };
    }
    if (params.onlyChange) {
      query.status = {
        $in: [ControlStatus.Pass, ControlStatus.Approve],
      };
    } else {
      query.take_over = true;
    }
    let skip = 0;
    let limit = 100;
    if (params.current && params.pageSize) {
      skip = (params.current - 1) * params.pageSize;
      limit = params.pageSize;
    }
    const res = await this.recordModel.genericFind(query, skip, limit, {
      create_ts: -1,
    });
    if (params.onlyChange) {
      // 填充额外的字段
      return {
        ...res,
        data: await Promise.all(
          res.data.map(async item => {
            const flight = await this.libraInfoModel.queryById(item.flight_id);
            const actual_ts =
              item.process_history?.find(v => v.process_type === ControlProcessType.Operate)?.ts ?? item.create_ts;
            return {
              ...item.toObject(),
              is_small_flow: isSmallFlow(item),
              in_peak_time: inPeakTime(actual_ts),
              version: flight?.version,
              platform: flight?.platform,
              actual_ts,
            } as LibraControlRecordExt;
          }),
        ),
      };
    }
    return res;
  }

  async checkHasGrayLibra(meegoId: number) {
    const query = {
      'meegoInfo.id': meegoId,
    };
    const storyLibraList = await this.libraNewInfoListDao.list(query);
    if (storyLibraList.length === 0) {
      return false;
    }
    for (const item of storyLibraList) {
      // if (item.flightInfo.type === LibraFlightType.Gray || isBetaByFilterRule(item.flightInfo.filterRule)) {
      //   if (item.flightInfo.status === LibraFlightStatus.InProgress) {
      //     return true;
      //   }
      // }
      if (
        item.flightInfo.status === LibraFlightStatus.InProgress ||
        item.flightInfo.status === LibraFlightStatus.InDebug
      ) {
        return true;
      }
    }
    return false;
  }

  async checkGrayLibraOk(
    storyId: number,
  ): Promise<{ result: boolean; reason: string; flight_id: number; flight_name: string }> {
    const storyLibraList = await this.libraNewInfoListDao.list({
      meegoInfo: {
        $elemMatch: {
          meegoId: storyId,
        },
      },
    });
    if (storyLibraList.length === 0) {
      return {
        result: true,
        reason: '需求未关联任何实验',
        flight_id: 0,
        flight_name: '',
      };
    }
    let grayLibra: LibraNewInfo | undefined;
    for (const item of storyLibraList) {
      if (
        item.flightInfo.type === LibraFlightType.Gray ||
        isBetaByFilterRule(
          item.flightInfo.filterRule,
          item.flightInfo.region,
          item.flightInfo.name,
          item.flightInfo.description,
        )
      ) {
        grayLibra = item;
        if (
          item.flightInfo.status === LibraFlightStatus.InProgress &&
          (item.flightInfo.trafficInfo.currentTrafficValue === 1 || item.flightInfo.trafficInfo.endTrafficValue === 1)
        ) {
          return {
            result: true,
            reason: `需求关联的灰度实验-${item.flightInfo.id}已调整流量到100%`,
            flight_id: item.flightInfo.id,
            flight_name: item.flightInfo.name,
          };
        }
      }
    }
    return {
      result: false,
      reason: grayLibra
        ? `需求绑定的灰度实验-${grayLibra.flightInfo.id}尚未开启并调整流量到100%`
        : `未找到需求关联的灰度实验，请确认实验是否限制在灰度阶段`,
      flight_id: grayLibra?.flightInfo.id ?? 0,
      flight_name: grayLibra?.flightInfo.name ?? '',
    };
  }

  async checkGrayLibra100Percent(
    storyId: number,
  ): Promise<{ result: boolean; reason: string; flightInfo: SendToHostFlightInfo[] }> {
    const query = {
      'meegoInfo.id': storyId,
    };
    const storyLibraList = await this.libraNewInfoListDao.list(query);
    this.logger.info(`checkGrayLibra100Percent storyLibraList ${JSON.stringify(storyLibraList)}`);
    if (storyLibraList.length === 0) {
      return {
        result: false,
        reason: '需求未关联任何实验',
        flightInfo: [
          {
            flightId: 0,
            flightName: '',
          },
        ],
      };
    }
    let grayLibra: LibraNewInfo | undefined;
    const flightInfoList: SendToHostFlightInfo[] = [];
    for (const item of storyLibraList) {
      if (
        item.flightInfo.type === LibraFlightType.Gray ||
        isBetaByFilterRule(
          item.flightInfo.filterRule,
          item.flightInfo.region,
          item.flightInfo.name,
          item.flightInfo.description,
        ) ||
        item.extraInfo?.grayOrRelease === GrayOrRelease.gray
      ) {
        grayLibra = item;
        flightInfoList.push({
          flightId: item.flightInfo.id,
          flightName: item.flightInfo.name,
        });
        if (
          item.flightInfo.status === LibraFlightStatus.InProgress &&
          (item.flightInfo.trafficInfo.currentTrafficValue === 1 || item.flightInfo.trafficInfo.endTrafficValue === 1)
        ) {
          return {
            result: true,
            reason: `需求关联的灰度实验-${item.flightInfo.id}已调整流量到100%`,
            flightInfo: flightInfoList,
          };
        }
      }
    }
    return {
      result: false,
      reason: grayLibra
        ? `需求绑定的灰度实验-${grayLibra.flightInfo.id}尚未开启并调整流量到100%`
        : `未找到需求关联的灰度实验，请确认实验是否限制在灰度阶段`,
      flightInfo: flightInfoList,
    };
  }
}
