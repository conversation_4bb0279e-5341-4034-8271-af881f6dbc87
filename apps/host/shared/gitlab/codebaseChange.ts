export interface RefModel {
  ref: string;
  repo: RepoModel;
}

export interface RepoModel {
  id: number;
  ref: string;
  name: string;
  external_id: string;
  external_url: string;
}

export interface ChangeModel {
  id: number;
  title: string;
  description: string;
  external_url: string;
  source: RefModel;
  target: RefModel;
  patch_sets?: PatchSetModel[];
  current_files?: FileModel[];
}

export interface AuthorModel {
  id: number;
  name: string;
  email: string;
  username: string;
}

export interface CommentModel {
  id: number;
  type: string;
  content: string;
  thread_id: number;
  in_reply_to: number;
  author: AuthorModel;
  patchset_num: number;
  side: string;
  left_patchset_num: number;
  right_patchset_num: number;
  left_sha: string;
  right_sha: string;
  path: string;
  start_line: number;
  start_column: number;
  end_line: number;
  end_column: number;
  draft: boolean;
  created_at: string;
  updated_at: string;
  published_at: string;
  outdated: boolean;
  status: string;
  resolved: boolean; // 这个字段貌似已经废弃掉了，在 codebase 新的 response 里面没再看到
}
export interface CommentsModel {
  comments: CommentModel[];
}

export interface PatchUploaderModel {
  id: number;
  name?: string;
  email?: string;
  username?: string;
  en_name?: string;
}

export interface PatchSetModel {
  number: number;
  uploader?: PatchUploaderModel;
  sha?: string;
  base_sha?: string;
  created_at?: string;
}

export interface FileModel {
  path?: string;
  from_path?: string;
  from_sha?: string;
  from_mode?: string;
  to_path?: string;
  to_sha?: string;
  to_mode?: string;
  change_type?: string;
  binary?: string;
  lines_inserted?: string;
  lines_deleted?: string;
}

export interface ChangeContentsModel {
  type?: string;
  name?: string;
  path?: string;
  sha?: string;
  size?: number;
  content?: string; // base64 编码后的内容
  encoding?: string;
}
