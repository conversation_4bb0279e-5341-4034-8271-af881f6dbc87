import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Radio, RadioGroup, Select, Space, Tag, TextArea, Toast, Typography } from '@douyinfe/semi-ui';
import {
  LibraFlightCloseAttributionInfo,
  LibraFlightCloseFullReleaseSubType,
  LibraFlightCloseMainType,
  LibraFlightCloseOfflineSubType,
  LibraFlightCloseReopenSubType,
  LibraFlightCloseReopenSubTypeCanPreInterceptType,
  LibraFlightCloseReopenSubTypeDetailType,
  LibraFlightStatus,
  LibraNewInfo,
} from '@shared/libra/LibraNewInfo';
import {
  flightTagColorArray,
  getReopenReasonDisplayText,
  LibraCloseAttributionSubTypeLabelPrefixByMainType,
  libraDetailUrl,
  libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap,
  libraFlightCloseAttributionMainType2DisplayNameMap,
  libraFlightCloseAttributionOfflineSubType2DisplayNameMap,
  libraFlightCloseAttributionOfflineSubTypeOrder,
  libraFlightCloseAttributionReopenSubType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubTypeCanPreInterceptType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap,
} from '@shared/libra/libraManageUtils';
import { RadioChangeEvent } from '@douyinfe/semi-foundation/lib/es/radio/radioInnerFoundation';
import { fetchFlightList, libraCloseAttributionFinishedNotify, updateLibraNewInfo } from '@api/libra';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import UserSettingModule from '@/model/userSettingModel';
import { useModel } from '@edenx/runtime/model';
import { Token } from '@shared/libra/NonOpen';
import { getCNToken, getSGToken } from '@shared/libra/LibraCreate';
import { batch_stop_experiment } from '@api/libraNonOpen';
import GetLibraTokens from '@/pages/libra/list/component/GetLibraTokens';

const { Text, Paragraph } = Typography;

// 下拉菜单数据源
const getOptionList = (
  displayNameMap: { [key: number]: string },
  filter?: number[],
  orderKeys?: number[] | string[],
) => {
  // 转换为选项列表
  let innerOrderKeys: number[] | string[] = Object.keys(displayNameMap);
  if (orderKeys) {
    innerOrderKeys = orderKeys;
  }
  const options = innerOrderKeys.map(it => ({
    value: Number(it),
    label: displayNameMap[Number(it)],
  }));
  // 应用过滤逻辑（如果 filter 存在）
  return filter ? options.filter(option => !filter.includes(option.value)) : options;
};
// 下拉菜单列表渲染菜单
const getOptionRenderList = (optionList: { label: string; value: number }[]) =>
  optionList.map((it, index) => ({
    value: it.value,
    label: (
      <Tag
        color={flightTagColorArray[index % flightTagColorArray.length]}
        style={{ maxWidth: '100%' }}
        size={'large'}
        key={it.value}
      >
        {it.label}
      </Tag>
    ),
  }));
// 实验关闭-重开子分类 下拉菜单
const reopenSubTypeOptionList = getOptionList(libraFlightCloseAttributionReopenSubType2DisplayNameMap);
const reopenSubTypeOptionRenderList = getOptionRenderList(reopenSubTypeOptionList);
// 实验关闭-全量子分类 下拉菜单
const fullReleaseSubTypeOptionList = getOptionList(libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap);
const fullReleaseSubTypeOptionRenderList = getOptionRenderList(fullReleaseSubTypeOptionList);
// 实验关闭-下线子分类 下拉菜单
const offlineSubTypeOptionList = getOptionList(
  libraFlightCloseAttributionOfflineSubType2DisplayNameMap,
  undefined,
  libraFlightCloseAttributionOfflineSubTypeOrder,
);
const offlineSubTypeOptionRenderList = getOptionRenderList(offlineSubTypeOptionList);
// 实验关闭-重开子分类-详细分类 下拉菜单
const reopenSubTypeDetailTypeRenderListByDetailTypes = (detailTypeList: LibraFlightCloseReopenSubTypeDetailType[]) => {
  const optionList = detailTypeList.map(it => ({
    value: it,
    label: libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[it],
  }));
  return getOptionRenderList(optionList);
};
const reopenSubTypeDetailTypeRenderList = (reopenSubType: LibraFlightCloseReopenSubType) => {
  let detailTypeList: LibraFlightCloseReopenSubTypeDetailType[] = [];
  switch (reopenSubType) {
    case LibraFlightCloseReopenSubType.Normal:
      detailTypeList = [
        LibraFlightCloseReopenSubTypeDetailType.Normal_CloseGray,
        LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasNegativeBenefits,
        LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasBenefitsForFullRelease,
        LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopConfigError,
        LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopStrategyChange,
      ];
      break;
    case LibraFlightCloseReopenSubType.CodeRelatedAnomaly:
      detailTypeList = [
        LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_NewAddedCodes,
        LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_HistoryCodes,
        LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_ExposureCodeProblem,
        LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_FlightInReleaseConfigError,
        LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_OtherTechRelatedChanges,
      ];
      break;
    case LibraFlightCloseReopenSubType.StrategyChangeAnomaly:
      detailTypeList = [
        LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_FlightDesignDefect,
        LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_LRProblem,
        LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OperationConfigsImpact,
        LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OtherProductStrategyChanges,
      ];
      break;
    case LibraFlightCloseReopenSubType.OtherAnomaly:
      detailTypeList = [
        LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_PreAAProblem,
        LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_LibraProblem,
        LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_ThirdPartyDependency,
        LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_UnknownReasons,
        LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_Others,
      ];
      break;
    default:
      break;
  }
  return reopenSubTypeDetailTypeRenderListByDetailTypes(detailTypeList);
};

const reopenSubTypeCanPreInterceptTypeRenderList = () => {
  const typeList = [
    LibraFlightCloseReopenSubTypeCanPreInterceptType.CanNotPreIntercept,
    LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInDevelopmentPeriod,
    LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInFeatureFuncTestPeriod,
    LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInIntegrationPeriod,
    LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInLibraConfigCheckPeriod,
    LibraFlightCloseReopenSubTypeCanPreInterceptType.CanPreInterceptInOnlineLibraPatrolPeriod,
  ];
  const optionList = typeList.map(it => ({
    value: it,
    label: libraFlightCloseAttributionReopenSubTypeCanPreInterceptType2DisplayNameMap[it],
  }));
  return getOptionRenderList(optionList);
};

export interface FlightCloseAttributionSubmitFormValidateResult {
  isOk: boolean;
  errorMsg: string;
}

export interface FlightCloseAttributionSubmitFormProps {
  libraInfo: LibraNewInfo;
  onSaveLoadingChange: (loading: boolean) => void;
  onResetLoadingChange: (loading: boolean) => void;
  onCancelLoadingChange: (loading: boolean) => void;
  onValidChange: (isValid: boolean) => void;
}

export interface FlightCloseAttributionSubmitFormRef {
  validate: () => FlightCloseAttributionSubmitFormValidateResult;
  saveForm: () => Promise<LibraNewInfo | undefined>;
  resetForm: () => Promise<LibraNewInfo | undefined>;
  cancelForm: () => Promise<void>;
  configState: () => void;
  resetState: () => void;
}

const FlightCloseAttributionSubmitForm: React.ForwardRefRenderFunction<
  FlightCloseAttributionSubmitFormRef,
  FlightCloseAttributionSubmitFormProps
> = ({ libraInfo, onSaveLoadingChange, onResetLoadingChange, onCancelLoadingChange, onValidChange }, ref) => {
  const [mainType, setMainType] = useState(
    libraInfo.flightInfo.closeAttributionInfo?.mainType ?? LibraFlightCloseMainType.Reopen,
  );
  const [reopenSubType, setReopenSubType] = useState(libraInfo.flightInfo.closeAttributionInfo?.reopenSubType);
  const [fullReleaseSubType, setFullReleaseSubType] = useState(
    libraInfo.flightInfo.closeAttributionInfo?.fullReleaseSubType,
  );
  const [offlineSubType, setOfflineSubType] = useState(libraInfo.flightInfo.closeAttributionInfo?.offlineSubType);
  const [customReason, setCustomReason] = useState(libraInfo.flightInfo.closeAttributionInfo?.customReason ?? '');
  const [reopenSubTypeDetailType, setReopenSubTypeDetailType] = useState(
    libraInfo.flightInfo.closeAttributionInfo?.reopenSubTypeDetailType,
  );
  const [reopenSubTypeCanPreInterceptType, setReopenSubTypeCanPreInterceptType] = useState(
    libraInfo.flightInfo.closeAttributionInfo?.reopenSubTypeCanPreInterceptType,
  );
  const [userSettingState] = useModel(UserSettingModule);
  const [tokens, setTokens] = useState<Token[]>([]);
  const searchParams = new URLSearchParams(window.location.search);
  const libraAppId = searchParams.get('libraAppId'); // 兼容两种大小写
  const isStopGray = searchParams.get('is_stop_gray') === 'true';
  const shouldFillInCustomReason = () => {
    const reopenCustomReasonValidCondition =
      mainType === LibraFlightCloseMainType.Reopen &&
      reopenSubTypeDetailType &&
      (reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInReleaseHasNegativeBenefits || // 正式实验-收益负向关闭
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopConfigError || // 开发期间实验配置错误
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.Normal_FlightInDevelopStrategyChange || // 开发期间实验策略变更
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_NewAddedCodes || // 新增代码逻辑导致数据异常
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_HistoryCodes || // 历史代码逻辑导致数据异常
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_ExposureCodeProblem || // 曝光代码问题导致进组不均
        reopenSubTypeDetailType ===
          LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_FlightInReleaseConfigError || // 正式实验期间-实验配置错误
        reopenSubTypeDetailType ===
          LibraFlightCloseReopenSubTypeDetailType.CodeRelatedAnomaly_OtherTechRelatedChanges || // 其他技术问题变动
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_FlightDesignDefect || // 实验需求变更-实验设计缺陷
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_LRProblem || // 实验需求变更- LR 引入
        reopenSubTypeDetailType ===
          LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OperationConfigsImpact || // 素材等上新运营配置耦合影响
        reopenSubTypeDetailType ===
          LibraFlightCloseReopenSubTypeDetailType.StrategyChangeAnomaly_OtherProductStrategyChanges || // 其他产品策略变动
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_UnknownReasons || // 原因不明，重开观察继续分析
        reopenSubTypeDetailType === LibraFlightCloseReopenSubTypeDetailType.OtherAnomaly_Others); // 其他
    const offlineCustomReasonValidCondition =
      mainType === LibraFlightCloseMainType.Offline && offlineSubType === LibraFlightCloseOfflineSubType.Others;
    return reopenCustomReasonValidCondition || offlineCustomReasonValidCondition;
  };

  const shouldShowCustomReason = () => {
    if (
      mainType === LibraFlightCloseMainType.Reopen &&
      reopenSubType !== undefined &&
      reopenSubTypeDetailType !== undefined
    ) {
      return true;
    }

    if (mainType === LibraFlightCloseMainType.FullRelease && fullReleaseSubType !== undefined) {
      return true;
    }

    if (mainType === LibraFlightCloseMainType.Offline && offlineSubType !== undefined) {
      return true;
    }

    return false;
  };

  const shouldShowReopenCanPreInterceptTypeOption = () =>
    mainType === LibraFlightCloseMainType.Reopen &&
    reopenSubType === LibraFlightCloseReopenSubType.CodeRelatedAnomaly &&
    reopenSubTypeDetailType !== undefined;

  const checkValidate = (): FlightCloseAttributionSubmitFormValidateResult => {
    if (mainType === LibraFlightCloseMainType.Reopen) {
      if (reopenSubType === undefined) {
        onValidChange(false);
        return {
          isOk: false,
          errorMsg: '重开原因归类不能为空',
        };
      }
      if (reopenSubTypeDetailType === undefined) {
        onValidChange(false);
        return {
          isOk: false,
          errorMsg: '重开细分归类不能为空',
        };
      }
    }

    if (mainType === LibraFlightCloseMainType.FullRelease) {
      if (fullReleaseSubType === undefined) {
        onValidChange(false);
        return {
          isOk: false,
          errorMsg: '全量原因归类不能为空',
        };
      }
    }

    if (mainType === LibraFlightCloseMainType.Offline) {
      if (offlineSubType === undefined) {
        onValidChange(false);
        return {
          isOk: false,
          errorMsg: '下线原因归类不能为空',
        };
      }
    }

    if (shouldFillInCustomReason()) {
      if (customReason.length === 0) {
        onValidChange(false);
        return {
          isOk: false,
          errorMsg: '补充填写原因不能为空',
        };
      }
    }

    onValidChange(true);
    return {
      isOk: true,
      errorMsg: '',
    };
  };

  const handleSaveForm = async (): Promise<LibraNewInfo | undefined> => {
    // 将选择的关闭原因封装成 LibraFlightCloseAttributionInfo 结构
    const closeAttributionInfo: LibraFlightCloseAttributionInfo = {
      mainType,
    };
    let op_type = 'FlightStop';
    let op_reason;

    // 根据 mainType 添加相关属性值
    if (mainType === LibraFlightCloseMainType.Reopen) {
      op_type = 'Reopen';
      if (reopenSubType !== undefined) {
        closeAttributionInfo.reopenSubType = reopenSubType;
        if (reopenSubTypeDetailType !== undefined) {
          closeAttributionInfo.reopenSubTypeDetailType = reopenSubTypeDetailType;
          op_reason = getReopenReasonDisplayText(
            closeAttributionInfo.reopenSubType,
            closeAttributionInfo.reopenSubTypeDetailType,
          );
        }
        // 实验重开-代码相关异常-是否可前置拦截
        if (reopenSubTypeCanPreInterceptType !== undefined) {
          closeAttributionInfo.reopenSubTypeCanPreInterceptType = reopenSubTypeCanPreInterceptType;
        }
      }
    }

    if (mainType === LibraFlightCloseMainType.FullRelease) {
      op_type = 'FlightComplete';
      if (fullReleaseSubType !== undefined) {
        closeAttributionInfo.fullReleaseSubType = fullReleaseSubType;
        if (closeAttributionInfo.fullReleaseSubType === LibraFlightCloseFullReleaseSubType.Overdue) {
          op_reason = '超量全量';
        } else {
          op_reason = '正常全量';
        }
      }
    }

    if (mainType === LibraFlightCloseMainType.Offline) {
      op_type = 'FlightStop';
      if (offlineSubType !== undefined) {
        closeAttributionInfo.offlineSubType = offlineSubType;
        if (offlineSubType === LibraFlightCloseOfflineSubType.CollectDataReadyToFullRelease) {
          op_reason = 'CollectData';
        } else if (offlineSubType === LibraFlightCloseOfflineSubType.OnlineEmergency) {
          op_reason = 'OnlineEmergency';
        } else if (offlineSubType === LibraFlightCloseOfflineSubType.UserNegativeFeedback) {
          op_reason = 'UserNegativeFeedback';
        } else if (offlineSubType === LibraFlightCloseOfflineSubType.IndicatorNotMatch) {
          op_reason = 'IndicatorNotMatch';
        }
      }
    }

    if (
      mainType !== LibraFlightCloseMainType.Reopen ||
      reopenSubType !== LibraFlightCloseReopenSubType.CodeRelatedAnomaly
    ) {
      // 如果不是重开，或者不是“实验重开-代码相关异常-是否可前置拦截”，需要清空一下
      if (closeAttributionInfo.reopenSubTypeCanPreInterceptType !== undefined) {
        delete closeAttributionInfo.reopenSubTypeCanPreInterceptType;
      }
    }

    if (customReason !== undefined && customReason.length > 0) {
      closeAttributionInfo.customReason = customReason;
      if (mainType !== LibraFlightCloseMainType.Offline) {
        op_reason += `/${customReason}`;
      }
    }

    // 添加更新时间、更新人
    // 引入插件和时区配置
    dayjs.extend(utc);
    dayjs.extend(timezone);
    closeAttributionInfo.updateTime = dayjs().tz('Asia/Shanghai').unix();
    closeAttributionInfo.updateUser = userSettingState.info.email;

    // 触发网络请求，更新实验关闭归因信息
    onSaveLoadingChange(true);
    // 先查新最新的 libraInfo，然后再更新
    const queryResult = await fetchFlightList({
      data: {
        args: {
          'flightInfo.id': libraInfo.flightInfo.id,
        },
        page: 1,
        pageSize: 10,
      },
    });
    if (!queryResult.data || queryResult.data.length === 0) {
      onSaveLoadingChange(false);
      // Toast 提示更新失败
      Toast.error(`更新实验关闭原因失败！未查询到实验 flightId: ${libraInfo.flightInfo.id}}`);
      return;
    }
    const latestLibraInfo = queryResult.data[0];
    if (isStopGray) {
      // 如果实验未关闭，先调用关闭API
      if (latestLibraInfo.flightInfo.status === LibraFlightStatus.InProgress) {
        try {
          const stopResult = await batch_stop_experiment({
            headers: {
              origin: window.location.origin,
            },
            data: {
              libra_app_id: latestLibraInfo.libraAppId,
              tokens,
              flight_id: latestLibraInfo.flightInfo.id,
              enable_gradual: false,
              op_reason,
              op_type,
            },
          });

          if (!stopResult) {
            Toast.error('关闭实验失败');
            onResetLoadingChange(false);
            return;
          }
        } catch (error) {
          console.error('关闭实验出错:', error);
          Toast.error('关闭实验失败');
          onResetLoadingChange(false);
          return;
        }
      }
    }
    // 更新实验关闭归因
    latestLibraInfo.flightInfo.closeAttributionInfo = closeAttributionInfo;
    // 更新一下是否为质量问题实验
    if (!latestLibraInfo.flightInfo.isManualMarkedQualityProblem) {
      // 如果是“代码相关的重开实验”，则默认标记为质量问题实验
      latestLibraInfo.flightInfo.isQualityProblem =
        closeAttributionInfo && closeAttributionInfo.reopenSubType === LibraFlightCloseReopenSubType.CodeRelatedAnomaly;
    }

    const updateResult = await updateLibraNewInfo({
      data: {
        libraInfo: latestLibraInfo,
      },
    });
    if (updateResult.code !== 0) {
      onSaveLoadingChange(false);
      // Toast 提示更新失败
      Toast.error(`更新实验关闭原因失败！${updateResult.message}`);
      return;
    }

    // 发送飞书通知，告知实验填写了关闭归因(不阻塞，直接发送通知)
    libraCloseAttributionFinishedNotify({
      data: {
        flightId: latestLibraInfo.flightInfo.id,
      },
    });

    onSaveLoadingChange(false);
    return latestLibraInfo;
  };

  const handleResetForm = async (): Promise<LibraNewInfo | undefined> => {
    // 触发网络请求，更新实验关闭归因信息
    onResetLoadingChange(true);
    // 先查新最新的 libraInfo，然后再更新
    const queryResult = await fetchFlightList({
      data: {
        args: {
          'flightInfo.id': libraInfo.flightInfo.id,
        },
        page: 1,
        pageSize: 10,
      },
    });
    if (!queryResult.data || queryResult.data.length === 0) {
      onResetLoadingChange(false);
      // Toast 提示更新失败
      Toast.error(`更新实验关闭原因失败！未查询到实验 flightId: ${libraInfo.flightInfo.id}}`);
      return;
    }
    const latestLibraInfo = queryResult.data[0];
    // 清空实验关闭归因
    if (latestLibraInfo.flightInfo.closeAttributionInfo) {
      delete latestLibraInfo.flightInfo.closeAttributionInfo;
      if (!latestLibraInfo.flightInfo.isManualMarkedQualityProblem) {
        // 如果没有手动标记过是否是质量问题实验，则同时也清空一下 isQualityProblem 标记
        if (
          latestLibraInfo.flightInfo.isQualityProblem !== undefined ||
          latestLibraInfo.flightInfo.isQualityProblem !== null
        ) {
          delete latestLibraInfo.flightInfo.isQualityProblem;
        }
      }
      const updateResult = await updateLibraNewInfo({
        data: {
          libraInfo: latestLibraInfo,
        },
      });
      if (updateResult.code !== 0) {
        onResetLoadingChange(false);
        // Toast 提示更新失败
        Toast.error(`更新实验关闭原因失败！${updateResult.message}`);
        return;
      }
    }

    onResetLoadingChange(false);
    return latestLibraInfo;
  };

  const handelCancelForm = async () => {
    // do nothing
  };

  const handleResetState = () => {
    setMainType(LibraFlightCloseMainType.Reopen);
    setReopenSubType(undefined);
    setFullReleaseSubType(undefined);
    setOfflineSubType(undefined);
    setCustomReason('');
    setReopenSubTypeDetailType(undefined);
    setReopenSubTypeCanPreInterceptType(undefined);
  };

  const handleConfigState = () => {
    // 从 libra info 中读取数据
    setMainType(libraInfo.flightInfo.closeAttributionInfo?.mainType ?? LibraFlightCloseMainType.Reopen);
    setReopenSubType(libraInfo.flightInfo.closeAttributionInfo?.reopenSubType);
    setFullReleaseSubType(libraInfo.flightInfo.closeAttributionInfo?.fullReleaseSubType);
    setOfflineSubType(libraInfo.flightInfo.closeAttributionInfo?.offlineSubType);
    setCustomReason(libraInfo.flightInfo.closeAttributionInfo?.customReason ?? '');
    setReopenSubTypeDetailType(libraInfo.flightInfo.closeAttributionInfo?.reopenSubTypeDetailType);
    setReopenSubTypeCanPreInterceptType(libraInfo.flightInfo.closeAttributionInfo?.reopenSubTypeCanPreInterceptType);
  };

  useImperativeHandle(ref, () => ({
    validate: checkValidate,
    saveForm: handleSaveForm,
    resetForm: handleResetForm,
    cancelForm: handelCancelForm,
    configState: handleConfigState,
    resetState: handleResetState,
  }));

  // 主要分类
  const onMainTypeChange = (e: RadioChangeEvent) => {
    setMainType(e.target.value);
    // 主因变动的时候，重置次要分类
    setReopenSubType(undefined);
    setFullReleaseSubType(undefined);
    setOfflineSubType(undefined);
    setReopenSubTypeDetailType(undefined);
    setReopenSubTypeCanPreInterceptType(undefined);
    setCustomReason('');
  };

  const handleCustomReasonChange = (e: string) => {
    setCustomReason(e);
  };

  return (
    <>
      {isStopGray && (
        <GetLibraTokens
          changeTokens={(newTokens: React.SetStateAction<Token[]>) => setTokens(newTokens)}
          tokenType={libraAppId === '147' ? 'cn' : 'sg'}
          email={userSettingState.info.email}
        />
      )}
      <Space vertical={true} align={'start'}>
        <Space vertical={false} align={'start'} style={{ marginBottom: '10px' }}>
          <Text style={{ color: 'var(--semi-color-danger)', fontSize: 14 }}>*</Text>
          <b>实验名称：</b>
          <Text
            ellipsis={{ rows: 2, showTooltip: { type: 'popover', opts: { style: { width: 300 } } } }}
            style={{ width: 300 }}
            link={{ href: libraDetailUrl(libraInfo.flightInfo.region, libraInfo.flightInfo.id), target: '_blank' }}
          >
            {libraInfo.flightInfo.name}
          </Text>
        </Space>
        <Space vertical={false} style={{ marginBottom: '10px' }}>
          {/* [必选]实验关闭主要原因（大分类）：实验重开、实验全量、实验下线*/}
          <Text style={{ color: 'var(--semi-color-danger)', fontSize: 14 }}>*</Text>
          <b>实验关闭原因：</b>
          <RadioGroup
            onChange={onMainTypeChange}
            value={mainType}
            aria-label="实验关闭原因"
            name="close-attribution-main-type"
          >
            <Radio value={LibraFlightCloseMainType.Reopen}>
              {libraFlightCloseAttributionMainType2DisplayNameMap[LibraFlightCloseMainType.Reopen]}
            </Radio>
            <Radio value={LibraFlightCloseMainType.FullRelease}>
              {libraFlightCloseAttributionMainType2DisplayNameMap[LibraFlightCloseMainType.FullRelease]}
            </Radio>
            <Radio value={LibraFlightCloseMainType.Offline}>
              {libraFlightCloseAttributionMainType2DisplayNameMap[LibraFlightCloseMainType.Offline]}
            </Radio>
          </RadioGroup>
        </Space>
        <Space vertical={false} style={{ marginBottom: '10px' }}>
          {/* [可选]实验关闭子类展示 */}
          <Text style={{ color: 'var(--semi-color-danger)', fontSize: 14 }}>*</Text>
          <b>{LibraCloseAttributionSubTypeLabelPrefixByMainType(mainType)}原因归类：</b>
          {/* 实验关闭-重开子分类 下拉菜单 */}
          {mainType === LibraFlightCloseMainType.Reopen && (
            <Select
              style={{ width: 270 }}
              placeholder={'请选择重开原因'}
              showClear={true}
              onClear={() => {
                setReopenSubType(undefined);
                setReopenSubTypeDetailType(undefined);
              }}
              optionList={reopenSubTypeOptionRenderList}
              onChange={e => {
                setReopenSubType(Number(e) as LibraFlightCloseReopenSubType);
                // 重置一下 detailType
                setReopenSubTypeDetailType(undefined);
                // 重置一下 canPreInterceptType（是否可前置拦截）
                setReopenSubTypeCanPreInterceptType(undefined);
                // 重置一下 customReason
                setCustomReason('');
              }}
              value={reopenSubType}
            />
          )}
          {/* 实验关闭-全量子分类 下拉菜单 */}
          {mainType === LibraFlightCloseMainType.FullRelease && (
            <Select
              style={{ width: 270 }}
              placeholder={'请选择全量原因'}
              showClear={true}
              onClear={() => {
                setFullReleaseSubType(undefined);
              }}
              optionList={fullReleaseSubTypeOptionRenderList}
              onChange={e => {
                setFullReleaseSubType(Number(e) as LibraFlightCloseFullReleaseSubType);
                // 重置一下 customReason
                setCustomReason('');
              }}
              value={fullReleaseSubType}
            />
          )}
          {/* 实验关闭-下线子分类 下拉菜单 */}
          {mainType === LibraFlightCloseMainType.Offline && (
            <Select
              style={{ width: 270 }}
              placeholder={'请选择下线原因'}
              showClear={true}
              onClear={() => {
                setOfflineSubType(undefined);
              }}
              optionList={offlineSubTypeOptionRenderList}
              onChange={e => {
                setOfflineSubType(Number(e) as LibraFlightCloseOfflineSubType);
                // 重置一下 customReason
                setCustomReason('');
              }}
              value={offlineSubType}
            />
          )}
        </Space>
        {mainType === LibraFlightCloseMainType.Reopen && reopenSubType !== undefined && (
          <Space vertical={false} style={{ marginBottom: '10px' }}>
            {/* [可选]实验重开详细原因 */}
            <Text style={{ color: 'var(--semi-color-danger)', fontSize: 14 }}>*</Text>
            <b>重开细分归类：</b>
            <Select
              style={{ width: 270 }}
              placeholder={'请选择细分归类'}
              showClear={true}
              onClear={() => {
                setReopenSubTypeDetailType(undefined);
              }}
              optionList={reopenSubTypeDetailTypeRenderList(reopenSubType)}
              onChange={e => {
                setReopenSubTypeDetailType(Number(e) as LibraFlightCloseReopenSubTypeDetailType);
                // 重置一下 customReason
                setCustomReason('');
              }}
              value={reopenSubTypeDetailType}
            />
          </Space>
        )}
        {shouldShowCustomReason() && (
          <Space vertical={false} align={'start'} style={{ marginBottom: '10px' }}>
            {/* [可选]自定义原因补充填写 */}
            <Text style={{ color: shouldFillInCustomReason() ? 'red' : 'white', fontSize: 14 }}>*</Text>
            <b style={{ width: '98px' }}>
              {mainType === LibraFlightCloseMainType.Reopen ? '重开详细描述：' : '补充填写：'}
            </b>
            <TextArea
              style={{ width: '270px' }}
              maxCount={100}
              showClear
              value={customReason}
              onChange={handleCustomReasonChange}
            />
          </Space>
        )}
        {shouldShowReopenCanPreInterceptTypeOption() && (
          <Space vertical={false} style={{ marginBottom: '10px' }}>
            {/* [可选]重开-代码相关问题-是否可前置拦截 */}
            <Text style={{ color: 'white', fontSize: 14 }}>&nbsp;</Text>
            <b>可否前置拦截：</b>
            <Select
              style={{ width: 270 }}
              placeholder={'请选择问题是否可前置拦截'}
              showClear={true}
              onClear={() => {
                setReopenSubTypeCanPreInterceptType(undefined);
              }}
              optionList={reopenSubTypeCanPreInterceptTypeRenderList()}
              onChange={e => {
                setReopenSubTypeCanPreInterceptType(Number(e) as LibraFlightCloseReopenSubTypeCanPreInterceptType);
              }}
              value={reopenSubTypeCanPreInterceptType}
            />
          </Space>
        )}
        {!checkValidate().isOk && (
          <Space vertical={false} style={{ marginBottom: '10px' }}>
            {/* [可选]自定义原因 */}
            <Text style={{ color: 'white', fontSize: 14 }}>*</Text>
            <Text style={{ color: 'var(--semi-color-danger)', fontSize: 14 }}>
              提示: {checkValidate().errorMsg ?? ''}
            </Text>
          </Space>
        )}
      </Space>
    </>
  );
};

export default React.forwardRef(FlightCloseAttributionSubmitForm);
