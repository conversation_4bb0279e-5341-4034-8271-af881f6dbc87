import { Inject, Injectable } from '@gulux/gulux';
import { VersionProcessInfoTable } from '../../model/releasePlatform/VersionProcessInfoModel';
import { ModelType } from '@gulux/gulux/typegoose';
import LarkService from '@pa/backend/dist/src/third/lark';
import { LarkCard, SendLarkMessageResponse } from '@pa/shared/dist/src/lark/larkCard';
import { isPippitApp, isRetouchApp, isTinycutApp, VersionProcessInfo } from '@shared/releasePlatform/versionStage';
import { ChatInfo, PlatformType, User } from '@pa/shared/dist/src/core';
import MeegoService from '../third/meego';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import BitsService from '../third/bits';
import versionUtils, { cc2lvVersion } from '../../utils/versionUtils';
import { chunk, omit } from 'lodash';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { ReleasePlatformUtilService } from './releasePlatformUtil';
import AirplaneConfigService from '../AirplaneConfigService';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import MessageService from '@pa/backend/dist/src/service/message';
import axios from 'axios';
import { MsgCategory, MsgStrategy, MsgTemplate, MsgType } from '@pa/shared/dist/src/message';
import { VersionTransactionType } from '@shared/releasePlatform/versionTransaction';
import { BytedEnv } from '@gulux/gulux/byted-env';
import { teaCollect, TeaEvent } from '../../tea';
import { VersionMeegoProductDao } from '../dao/VersionMeegoProductDao';
import { ReleasePlatformMsgRecordDao } from '../dao/releasePlatform/ReleasePlatformMsgRecordDao';
import { ReleasePlatformMsgRecord } from '@shared/releasePlatform/releasePlatformMsgRecord';
import { TccClients } from '@gulux/gulux/tcc';
import { BitsAppIdToAid } from '@shared/bits';

export enum ReleasePlatformMessageGroupType {
  BmReleaseGroup = 0, // BM发版群
  VersionReleaseGroup = 1, // 版本大群
  LVUserStoryGroup = 2, // 剪映用户故事群
  RetouchUserStoryGroup = 3, // 醒图用户故事群
  RetouchSystemTestBugGroup = 4, // 醒图系统测试bug群
  RetouchSystemTestAllGroup = 6, // 醒图系统测试大群
  RetouchRDQAGroup = 7, // 醒图研发横向群
  RetouchPublishGroup = 8, // 醒图发布群
  HypicPublishGroup = 9, // hypic发布群
  BussinessCoreGroup = 10, // 业务核心群
  RetouchCoreGroup = 14, // 醒图核心群
  PCTestQAGroup = 21, // PC测试QA群

  DreaminaTestGroup = 11, // Dreamina测试群
  DreaminaCoreGroup = 12, // Dreamina核心群
  DreaminaIntegrationGroup = 13, // Dreamina集成群

  CircuitBreakerNotifyGroup = 22, // 熔断通知到容灾运营群

  // 群组配置
  DefaultTestGroup = 15, // 调试群
  DefaultBMReleaseGroup = 16, // BM群
  DefaultVersionReleaseGroup = 17, // 版本大群
  DefaultIntegrationGroup = 18, // 集成群
  DefaultBusinessCoreGroup = 19, // 业务核心群
  DefaultPublishGroup = 20, // 发布群
}

interface productChatIdConfig {
  isDynamic?: boolean; // 是否动态获取，目前仅支持动态获取集成群
  groupId?: string;
}

const TestChatId = 'oc_568745868df8983bf70f7f453a8e4924';

const RetouchSystemTestBugGroup = 'oc_8a035b9814b4b3d9340ed1e7c677aad9';
const RetouchSystemTestAllGroup = 'oc_e4143990480f3fcb2d64f3a89d1edf95';
const RetouchRDQAGroup = 'oc_97ca4ee55589e3688ab159c97ae4eee4';
const RetouchUserStoryGroup = 'oc_b758ad807a95b4b434bbb04e829cb13a';
const RetouchPublishGroup = 'oc_21977088137ec95c4474ce4a3a9ae156';
const HypicPublishGroup = 'oc_e13b5f91c2a4f35ce9f6baae717afce8';
const RetouchCoreGroup = 'oc_7d9eda316a806f13a9a7549bd37dcd04';

const DreaminaTestGroup = 'oc_568745868df8983bf70f7f453a8e4924';
const DreaminaCoreGroup = 'oc_833106279fb1b740089b5d1cf96c9d2c';

const PCReleaseGroupChatId = 'oc_983a8f7f9fabcf905003747f1c71b81d';
const PCTestQAGroupChatId = 'oc_983a8f7f9fabcf905003747f1c71b81d';
const PCGrayGroupChatId = 'oc_87865489758b5aa2583028ae82a705c0';
const PPETestChatId = 'oc_e538754f8719ac2d379f81638344144b';

const CircuitBreakerNotifyGroupId = 'oc_7be780ff6b14ccfc8aeb692a4686a03d';

// 发版平台消息卡片接受类型
export enum ReleasePlatformMessageReceiveType {
  Group = 'group',
  Single = 'single',
}

// 发版平台消息卡片消息来源
export enum ReleasePlatformMessageSource {
  CheckItem = 'check_item',
  AutoDrive = 'auto_drive',
  StageFlow = 'stage_flow',
  InformationSync = 'information_sync',
}

@Injectable()
export default class ReleasePlatformMessageService {
  @Inject(VersionProcessInfoTable)
  private versionProcessInfoModel: ModelType<VersionProcessInfoTable>;
  @Inject()
  private businessConfigService: BusinessConfigService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private bits: BitsService;
  @Inject()
  private meego: MeegoService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private releasePlatformUtils: ReleasePlatformUtilService;
  @Inject()
  private paConfig: AirplaneConfigService;
  @Inject()
  private businessConfig: BusinessConfigService;
  @Inject()
  private paMessageService: MessageService;
  @Inject()
  private env: BytedEnv;
  @Inject()
  private versionMeegoProductDao: VersionMeegoProductDao;
  @Inject()
  private releasePlatformMsgRecordDao: ReleasePlatformMsgRecordDao;
  @Inject()
  private tcc: TccClients;

  /**
   * @deprecated 请使用 sendVersionMessageV2 发送消息
   */
  async sendVersionMessage(
    groupType: ReleasePlatformMessageGroupType,
    card: LarkCard,
    versionInfo: VersionProcessInfo,
    receivers?: (User | undefined)[],
    msgSource?: ReleasePlatformMessageSource, // 消息来源
    sourceName?: string, // 来源类型
  ): Promise<string> {
    if (process.env.NODE_ENV === 'development') {
      const res: SendLarkMessageResponse = await this.lark.sendCardMessage(UserIdType.chatId, TestChatId, card);
      return res.data?.message_id ?? '';
    }
    if (
      versionInfo.app_id === AppSettingId.LV_WIN ||
      versionInfo.app_id === AppSettingId.LV_MAC ||
      versionInfo.app_id === AppSettingId.CC_WIN ||
      versionInfo.app_id === AppSettingId.CC_MAC
    ) {
      // pc卡片转发到小剪
      const url = 'https://robot.cn.goofy.app/PaperAirPlaneSendMsg';
      const res = await axios.post(url, {
        card_content: JSON.stringify(card),
      });
      this.logger.info(`[sendVersionMessage] 发送卡片到小剪成功，message_id: ${JSON.stringify(res.data)}`);
    }
    const chatId = await this.getVersionMessageGroupId(groupType, versionInfo);
    const testChatIdArray = [TestChatId, 'oc_12fc6c40b14553e8f38d9fc65911129f', PPETestChatId];
    if (receivers && !testChatIdArray.includes(chatId)) {
      await Promise.all(
        chunk(
          receivers.map(it => it?.open_id ?? it?.user_id ?? ''),
          20,
        ).map(users => this.lark.addUserToChatGroup(chatId, UserIdType.openId, users)),
      );
    }
    const res: SendLarkMessageResponse = await this.lark.sendCardMessage(UserIdType.chatId, chatId, card);
    const messageId = res.data?.message_id ?? '';
    teaCollect(TeaEvent.PUSH_VERSION_MSG, {
      appId: versionInfo.app_id.toString(),
      platform: this.getPlatformByAppId(versionInfo.app_id),
      version: versionInfo.version,
      msg_receive_type: ReleasePlatformMessageReceiveType.Group,
      msg_source: msgSource ?? '',
      source_name: sourceName ?? '',
    });
    // 发版平台消息推送入库
    await this.releasePlatformMsgRecordDao.createRecord({
      app_id: versionInfo.app_id.toString(),
      version: versionInfo.version,
      urgent: false,
      message_id: messageId,
      receiver_ids: [chatId],
      mention_ids: receivers?.map(user => user?.open_id ?? user?.user_id ?? '') ?? [],
      msg_receive_type: ReleasePlatformMessageReceiveType.Group.toString(),
      receiver_type: UserIdType.openId.toString(),
    } as ReleasePlatformMsgRecord);
    return messageId;
  }

  async sendVersionMessageToGroup(
    messageName: string,
    messageCategory: VersionTransactionType,
    messageStrategy: MsgStrategy,
    card: LarkCard,
    versionInfo: VersionProcessInfo,
    description: string,
    releasePlatformGroupType: ReleasePlatformMessageGroupType, // 发群消息所需：版本群的类型
    receivers?: (User | undefined)[], // 发群消息所需：需要拉的人
    msgSource?: ReleasePlatformMessageSource, // 消息来源
    sourceName?: string, // 来源类型
  ): Promise<string> {
    const msgObj = {
      name: messageName, // 消息分类描述, 例如"封版提醒上车消息"
      type: MsgType.GroupChat, // 消息类型：私聊还是群聊
      category: MsgCategory.Release, // 消息一级业务分类,尽量使用枚举
      subCategory: messageCategory, // 消息二级业务分类,尽量使用枚举
      strategy: messageStrategy, // 消息触发策略
      msgContent: card, // 消息内容
      extra: {
        description,
        version: versionInfo.version,
        appId: versionInfo.app_id,
      },
    } as MsgTemplate;
    if (process.env.NODE_ENV === 'development') {
      const res: SendLarkMessageResponse = await this.paMessageService.sendNormalMsg(
        msgObj,
        UserIdType.chatId,
        TestChatId,
      );
      return res.data?.message_id ?? '';
    }
    // // Dreamina信息单独抄送一份到调试群
    // if (versionInfo.app_id === AppSettingId.DREAMINA_ANDROID || versionInfo.app_id === AppSettingId.DREAMINA_IOS) {
    //   await this.paMessageService.sendNormalMsg(msgObj, UserIdType.chatId, DreaminaTestGroup);
    // }
    if (
      versionInfo.app_id === AppSettingId.LV_WIN ||
      versionInfo.app_id === AppSettingId.LV_MAC ||
      versionInfo.app_id === AppSettingId.CC_WIN ||
      versionInfo.app_id === AppSettingId.CC_MAC
    ) {
      if (!card) {
        this.logger.error(`no message ==> ${card}`);
      }
      // pc卡片转发到小剪
      const url = 'https://robot.cn.goofy.app/PaperAirPlaneSendMsg';
      const res = await axios.post(url, {
        card_content: JSON.stringify(card),
      });
      this.logger.info(`[sendVersionMessage] 发送卡片到小剪成功，message_id: ${JSON.stringify(res.data)}`);
    }
    const chatId = await this.getVersionMessageGroupId(releasePlatformGroupType, versionInfo);
    if (receivers && chatId !== TestChatId && chatId !== PPETestChatId) {
      await Promise.all(
        chunk(
          receivers.map(it => it?.open_id ?? it?.user_id ?? ''),
          20,
        ).map(users => this.lark.addUserToChatGroup(chatId, UserIdType.openId, users)),
      );
    }
    const res: SendLarkMessageResponse = await this.paMessageService.sendNormalMsg(msgObj, UserIdType.chatId, chatId);
    const messageId = res.data?.message_id ?? '';
    // 发版平台消息推送上报
    teaCollect(TeaEvent.PUSH_VERSION_MSG, {
      appId: versionInfo.app_id.toString(),
      platform: this.getPlatformByAppId(versionInfo.app_id),
      version: versionInfo.version,
      msg_receive_type: ReleasePlatformMessageReceiveType.Group,
      msg_source: msgSource ?? '',
      source_name: sourceName ?? '',
    });
    // 发版平台消息推送入库
    await this.releasePlatformMsgRecordDao.createRecord({
      app_id: versionInfo.app_id.toString(),
      version: versionInfo.version,
      urgent: false,
      message_id: messageId,
      receiver_ids: [chatId],
      mention_ids: receivers?.map(user => user?.open_id ?? user?.user_id ?? '') ?? [],
      msg_receive_type: ReleasePlatformMessageReceiveType.Group.toString(),
      receiver_type: UserIdType.openId.toString(),
    } as ReleasePlatformMsgRecord);
    return messageId;
  }

  async getVersionMessageGroupId(groupType: ReleasePlatformMessageGroupType, versionInfo: VersionProcessInfo) {
    let chatId = TestChatId;
    if (groupType === ReleasePlatformMessageGroupType.CircuitBreakerNotifyGroup) {
      chatId = CircuitBreakerNotifyGroupId;
      if (
        versionInfo.app_id === AppSettingId.LV_WIN ||
        versionInfo.app_id === AppSettingId.LV_MAC ||
        versionInfo.app_id === AppSettingId.CC_WIN ||
        versionInfo.app_id === AppSettingId.CC_MAC
      ) {
        if (this.env.isPPE()) {
          chatId = PPETestChatId; // pc端固定为测试群
        } else {
          chatId = PCGrayGroupChatId;
        }
      }
      return chatId;
    }
    if (
      isRetouchApp(versionInfo.app_id) &&
      groupType !== ReleasePlatformMessageGroupType.RetouchSystemTestBugGroup &&
      groupType !== ReleasePlatformMessageGroupType.RetouchSystemTestAllGroup &&
      groupType !== ReleasePlatformMessageGroupType.RetouchRDQAGroup &&
      groupType !== ReleasePlatformMessageGroupType.RetouchUserStoryGroup &&
      groupType !== ReleasePlatformMessageGroupType.RetouchPublishGroup &&
      groupType !== ReleasePlatformMessageGroupType.HypicPublishGroup &&
      groupType !== ReleasePlatformMessageGroupType.RetouchCoreGroup &&
      groupType !== ReleasePlatformMessageGroupType.BussinessCoreGroup
    ) {
      chatId = 'oc_12fc6c40b14553e8f38d9fc65911129f';
      return chatId;
    }
    if (isPippitApp(versionInfo.app_id) || isTinycutApp(versionInfo.app_id)) {
      const defaultChatId = await this.getProductDefaultChatId(versionInfo.app_id, groupType, versionInfo);
      chatId = defaultChatId ?? chatId;
      return chatId;
    }
    if (groupType === ReleasePlatformMessageGroupType.BmReleaseGroup) {
      if (!versionInfo.bmReleaseGroupChatId) {
        if (
          versionInfo.app_id === AppSettingId.LV_WIN ||
          versionInfo.app_id === AppSettingId.LV_MAC ||
          versionInfo.app_id === AppSettingId.CC_WIN ||
          versionInfo.app_id === AppSettingId.CC_MAC
        ) {
          versionInfo.bmReleaseGroupChatId = await this.getPCBMMeegoChatId(versionInfo);
        } else if (
          versionInfo.app_id === AppSettingId.DREAMINA_ANDROID ||
          versionInfo.app_id === AppSettingId.DREAMINA_IOS
        ) {
          return (await this.getDreaminaChatId(groupType, versionInfo)) ?? DreaminaTestGroup;
        } else {
          const appInfo = await this.businessConfigService.appID2AppInfo(versionInfo.app_id);
          if (!appInfo) {
            return '';
          }
          const originVersion =
            versionInfo.app_id === AppSettingId.CC_IOS || versionInfo.app_id === AppSettingId.CC_ANDROID
              ? cc2lvVersion(versionInfo.version)
              : versionInfo.version;
          versionInfo.bmReleaseGroupChatId = await this.releasePlatformUtils.getOrCreateBMGroupChat(
            originVersion,
            appInfo.platform,
          );
        }
        await this.updateByCriteria(
          {
            version: versionInfo.version,
            app_id: versionInfo.app_id,
          },
          versionInfo,
        );
      }
      chatId = versionInfo.bmReleaseGroupChatId ?? TestChatId;
    }
    if (groupType === ReleasePlatformMessageGroupType.VersionReleaseGroup) {
      if (
        versionInfo.app_id === AppSettingId.LV_WIN ||
        versionInfo.app_id === AppSettingId.LV_MAC ||
        versionInfo.app_id === AppSettingId.CC_WIN ||
        versionInfo.app_id === AppSettingId.CC_MAC
      ) {
        if (!versionInfo.versionReleaseGroupChatId) {
          // 暂时这么做，兼容原有写法
          versionInfo.versionReleaseGroupChatId = PCReleaseGroupChatId;
        }
      } else if (
        versionInfo.app_id === AppSettingId.DREAMINA_ANDROID ||
        versionInfo.app_id === AppSettingId.DREAMINA_IOS
      ) {
        return (await this.getDreaminaChatId(groupType, versionInfo)) ?? DreaminaTestGroup;
      } else {
        if (!versionInfo.versionReleaseGroupChatId) {
          const appInfo = await this.businessConfigService.appID2AppInfo(versionInfo.app_id);
          if (!appInfo) {
            return '';
          }
          const originVersion =
            versionInfo.app_id === AppSettingId.CC_IOS || versionInfo.app_id === AppSettingId.CC_ANDROID
              ? cc2lvVersion(versionInfo.version)
              : versionInfo.version;
          const result = await this.bits.getVersionGroup(`LV-${appInfo.platform}`, originVersion);
          if (result) {
            const chatInfo = await this.lark.larkChatI2OpenChatId(result.group_number);
            versionInfo.versionReleaseGroupChatId = chatInfo;
            await this.updateByCriteria(
              {
                version: versionInfo.version,
                app_id: versionInfo.app_id,
              },
              versionInfo,
            );
          }
        }
      }
      chatId = versionInfo.versionReleaseGroupChatId ?? TestChatId;
    }
    if (groupType === ReleasePlatformMessageGroupType.BussinessCoreGroup) {
      if (
        versionInfo.app_id === AppSettingId.LV_WIN ||
        versionInfo.app_id === AppSettingId.LV_MAC ||
        versionInfo.app_id === AppSettingId.CC_WIN ||
        versionInfo.app_id === AppSettingId.CC_MAC
      ) {
        chatId = PCReleaseGroupChatId;
      } else {
        const appInfo = await this.businessConfig.appID2AppInfo(versionInfo.app_id);
        const chatInfo = (await this.paConfig.queryConfigItem(
          appInfo?.business_id?.toString() ?? '',
          VersionConfigKeys.coreGroupChatId,
        )) as ChatInfo;
        chatId = chatInfo.chat_id ?? '';
      }
    }
    // 即梦通过这里获取群组id
    if (versionInfo.app_id === AppSettingId.DREAMINA_ANDROID || versionInfo.app_id === AppSettingId.DREAMINA_IOS) {
      chatId = (await this.getDreaminaChatId(groupType, versionInfo)) ?? chatId;
      return chatId;
    }
    if (
      versionInfo.app_id === AppSettingId.RETOUCH_IOS ||
      versionInfo.app_id === AppSettingId.RETOUCH_ANDROID ||
      versionInfo.app_id === AppSettingId.HYPIC_IOS ||
      versionInfo.app_id === AppSettingId.HYPIC_ANDROID
    ) {
      const retouchChatId = this.getRetouchChatId(groupType);
      chatId = retouchChatId ?? chatId;
    }
    if (groupType === ReleasePlatformMessageGroupType.PCTestQAGroup) {
      chatId = PCTestQAGroupChatId;
    }
    if (this.env.isPPE()) {
      if (
        versionInfo.app_id === AppSettingId.LV_WIN ||
        versionInfo.app_id === AppSettingId.LV_MAC ||
        versionInfo.app_id === AppSettingId.CC_WIN ||
        versionInfo.app_id === AppSettingId.CC_MAC
      ) {
        chatId = PPETestChatId; // pc端固定为测试群
      }
    }
    return chatId;
  }

  getRetouchChatId(groupType: ReleasePlatformMessageGroupType) {
    switch (groupType) {
      case ReleasePlatformMessageGroupType.RetouchSystemTestBugGroup:
        return RetouchSystemTestBugGroup;
      case ReleasePlatformMessageGroupType.RetouchSystemTestAllGroup:
        return RetouchSystemTestAllGroup;
      case ReleasePlatformMessageGroupType.RetouchRDQAGroup:
        return RetouchRDQAGroup;
      case ReleasePlatformMessageGroupType.RetouchUserStoryGroup:
        return RetouchUserStoryGroup;
      case ReleasePlatformMessageGroupType.RetouchPublishGroup:
        return RetouchPublishGroup;
      case ReleasePlatformMessageGroupType.HypicPublishGroup:
        return HypicPublishGroup;
      case ReleasePlatformMessageGroupType.RetouchCoreGroup:
        return RetouchCoreGroup;
      default:
        return undefined;
    }
  }

  async getDreaminaChatId(groupType: ReleasePlatformMessageGroupType, versionInfo: VersionProcessInfo) {
    switch (groupType) {
      case ReleasePlatformMessageGroupType.DreaminaTestGroup:
        return DreaminaTestGroup;
      case ReleasePlatformMessageGroupType.DreaminaCoreGroup:
      case ReleasePlatformMessageGroupType.BussinessCoreGroup:
        return DreaminaCoreGroup;
      case ReleasePlatformMessageGroupType.DreaminaIntegrationGroup:
      case ReleasePlatformMessageGroupType.BmReleaseGroup:
      case ReleasePlatformMessageGroupType.VersionReleaseGroup:
        let androidMeegoChatId = '';
        let iosMeegoChatId = '';
        const versionMeego = await this.versionMeegoProductDao.findVersionMeego(581595, versionInfo.version);
        androidMeegoChatId = versionMeego?.android_meego_chat_id ?? '';
        iosMeegoChatId = versionMeego?.ios_meego_chat_id ?? '';
        if (androidMeegoChatId !== '' && iosMeegoChatId !== '') {
          this.logger.info(
            `[GetDreaminaChatId] Hit Record，AndroidMeegoChatId ${androidMeegoChatId} IOSMeegoChatId ${iosMeegoChatId}`,
          );
          return this.getPlatformByAppId(versionInfo.app_id) === PlatformType.Android
            ? androidMeegoChatId
            : iosMeegoChatId;
        }
        this.logger.info(`[GetDreaminaChatId] Miss Record`);
        return await this.addBotToIntegrationGroup(
          581595,
          Number(versionMeego?.android_meego_id ?? versionMeego?.ios_meego_id),
          versionInfo.version,
        );
      default:
        return DreaminaTestGroup;
    }
  }

  async getProductDefaultChatId(
    appId: number,
    groupType: ReleasePlatformMessageGroupType,
    versionInfo: VersionProcessInfo,
  ) {
    const productChatIdsConfigKey = `${appId}_chatIds_config`;
    const chatIdsConfig = await this.tcc.keys.get(productChatIdsConfigKey);
    const getMeegoGroup = async (businessId: number) => {
      let androidMeegoChatId = '';
      let iosMeegoChatId = '';
      const versionMeego = await this.versionMeegoProductDao.findVersionMeego(businessId, versionInfo.version);
      androidMeegoChatId = versionMeego?.android_meego_chat_id ?? '';
      iosMeegoChatId = versionMeego?.ios_meego_chat_id ?? '';
      if (androidMeegoChatId !== '' && iosMeegoChatId !== '') {
        this.logger.info(
          `[GetChatId] ${businessId} Hit Record，AndroidMeegoChatId ${androidMeegoChatId} IOSMeegoChatId ${iosMeegoChatId}`,
        );
        return this.getPlatformByAppId(versionInfo.app_id) === PlatformType.Android
          ? androidMeegoChatId
          : iosMeegoChatId;
      }
      this.logger.info(`[GetChatId] ${businessId} Miss Record`);
      return await this.addBotToIntegrationGroup(
        businessId,
        Number(versionMeego?.android_meego_id ?? versionMeego?.ios_meego_id),
        versionInfo.version,
      );
    };
    if (chatIdsConfig) {
      const configResult = JSON.parse(chatIdsConfig) as {
        [key: string]: productChatIdConfig;
      };
      const businessId = BitsAppIdToAid(appId);
      const testGroupConfig = configResult[ReleasePlatformMessageGroupType.DefaultTestGroup.toString()];
      switch (groupType) {
        case ReleasePlatformMessageGroupType.DefaultTestGroup:
          return testGroupConfig?.groupId ?? TestChatId;
        case ReleasePlatformMessageGroupType.DefaultBMReleaseGroup:
        case ReleasePlatformMessageGroupType.BmReleaseGroup:
          const bmReleaseGroupConfig = configResult[ReleasePlatformMessageGroupType.DefaultBMReleaseGroup.toString()];
          return (
            (bmReleaseGroupConfig?.isDynamic ? await getMeegoGroup(businessId) : bmReleaseGroupConfig?.groupId) ??
            testGroupConfig?.groupId ??
            TestChatId
          );
        case ReleasePlatformMessageGroupType.DefaultIntegrationGroup:
          const integrationGroupConfig =
            configResult[ReleasePlatformMessageGroupType.DefaultIntegrationGroup.toString()];
          return (
            (integrationGroupConfig?.isDynamic ? await getMeegoGroup(businessId) : integrationGroupConfig?.groupId) ??
            testGroupConfig?.groupId ??
            TestChatId
          );
        case ReleasePlatformMessageGroupType.DefaultVersionReleaseGroup:
        case ReleasePlatformMessageGroupType.VersionReleaseGroup:
          const versionReleaseGroupConfig =
            configResult[ReleasePlatformMessageGroupType.DefaultVersionReleaseGroup.toString()];
          return (
            (versionReleaseGroupConfig?.isDynamic
              ? await getMeegoGroup(businessId)
              : versionReleaseGroupConfig?.groupId) ??
            testGroupConfig?.groupId ??
            TestChatId
          );
        case ReleasePlatformMessageGroupType.DefaultPublishGroup:
          return (
            configResult[ReleasePlatformMessageGroupType.DefaultPublishGroup.toString()]?.groupId ??
            testGroupConfig?.groupId ??
            TestChatId
          );
        case ReleasePlatformMessageGroupType.DefaultBusinessCoreGroup:
        case ReleasePlatformMessageGroupType.BussinessCoreGroup:
          return (
            configResult[ReleasePlatformMessageGroupType.DefaultBusinessCoreGroup.toString()]?.groupId ??
            testGroupConfig?.groupId ??
            TestChatId
          );
        default:
          return DreaminaTestGroup;
      }
    }
  }

  async addBotToIntegrationGroup(businessId: number, meegoId: number, version: string) {
    const result = await this.meego.addBotToMeegoChat(meegoId, 'cli_9c8628b7b1f1d102', 'story');
    const chatId = result?.chatId ?? '';
    await this.versionMeegoProductDao.updateMeegoInfo(
      businessId,
      version,
      meegoId.toString(),
      meegoId.toString(),
      chatId,
      chatId,
    );
    // 机器人拉群为异步操作，主动等待
    await new Promise(resolve => setTimeout(resolve, 5000)).then(() => {
      console.info(`${businessId} ${version} ${meegoId} pull bot into group`);
    });
    return chatId;
  }

  async testSendVersionMessage(
    groupType: ReleasePlatformMessageGroupType,
    card: LarkCard,
    versionInfo: VersionProcessInfo,
    receivers?: (User | undefined)[],
  ): Promise<string> {
    const chat = await this.getPCBMMeegoChatId(versionInfo);
    const chatId = PPETestChatId;
    if (
      versionInfo.app_id === AppSettingId.LV_WIN ||
      versionInfo.app_id === AppSettingId.LV_MAC ||
      versionInfo.app_id === AppSettingId.CC_WIN ||
      versionInfo.app_id === AppSettingId.CC_MAC
    ) {
      // pc卡片转发到小剪
      const url = 'https://robot.cn.goofy.app/PaperAirPlaneSendMsg';
      const res = await axios.post(url, {
        card_content: JSON.stringify(card),
      });
      this.logger.info(`[sendVersionMessage] 发送卡片到小剪成功，message_id: ${JSON.stringify(res.data)}`);
    }
    const res: SendLarkMessageResponse = await this.lark.sendCardMessage(UserIdType.chatId, chatId, card);
    return res.data?.message_id ?? '';
  }
  async updateByCriteria(criteria: Partial<VersionProcessInfo>, updateData: VersionProcessInfo) {
    const omitData = omit(updateData, ['_id']);
    await this.versionProcessInfoModel.updateOne(criteria, omitData);
  }

  async getPCBMMeegoChatId(verionInfo: VersionProcessInfo) {
    // 去除version中的'.'
    let finalVersion = verionInfo.version;
    if (verionInfo.app_id === AppSettingId.CC_MAC || verionInfo.app_id === AppSettingId.CC_WIN) {
      finalVersion = cc2lvVersion(verionInfo.version);
    }
    const version = finalVersion.replace(/\./g, '');
    const keyWord = `${version}&`;
    const pcBMMeego = await this.meego.queryAllWorkItems('story', '剪映专业版BM');
    // 数组中找出包含关键词的第一个元素
    const result = pcBMMeego.find(item => item.name.includes(keyWord));
    if (!result) {
      this.logger.error(`[getPCBMMeegoChatId] 没有找到剪映专业版BM`);
      return undefined;
    }
    const chatId = await this.meego.addBotToMeegoChat(result.id, 'cli_9c8628b7b1f1d102');
    this.logger.info(`[getPCBMMeegoChatId] 剪映专业版BM的群聊ID ${chatId?.chatId}`);
    if (chatId) {
      return chatId.chatId;
    } else {
      this.logger.error(`[getPCBMMeegoChatId] 没有找到剪映专业版BM的群聊`);
      return undefined;
    }
  }

  // NOTE 多端接入配置
  getPlatformByAppId(appId: AppSettingId): PlatformType {
    switch (appId) {
      case AppSettingId.LV_ANDROID:
      case AppSettingId.CC_ANDROID:
      case AppSettingId.RETOUCH_ANDROID:
      case AppSettingId.HYPIC_ANDROID:
      case AppSettingId.DREAMINA_ANDROID:
      case AppSettingId.PIPPIT_ANDROID:
      case AppSettingId.TINYCUT_ANDROID:
        return PlatformType.Android;
      case AppSettingId.LV_IOS:
      case AppSettingId.CC_IOS:
      case AppSettingId.RETOUCH_IOS:
      case AppSettingId.HYPIC_IOS:
      case AppSettingId.DREAMINA_IOS:
      case AppSettingId.PIPPIT_IOS:
      case AppSettingId.TINYCUT_IOS:
        return PlatformType.iOS;
      case AppSettingId.LV_WIN:
      case AppSettingId.CC_WIN:
        return PlatformType.Windows;
      case AppSettingId.LV_MAC:
      case AppSettingId.CC_MAC:
        return PlatformType.Mac;
      default:
        return PlatformType.init;
    }
  }
}
