import { Inject, Injectable } from '@gulux/gulux';
import MeegoService, { faceuProjectKey } from './third/meego';
import { WorkItemInfo } from '@shared/meego/WorkItemResult';
import { FieldValue, RoleFiledValue } from '@shared/meego/MeegoCommon';

interface StageInfo {
  stage: string; // 阶段
  escape_rate: string; // 逃逸率
  issues_count: string; // 问题个数
  resolve_rate: string; // 解决率
  resolve_rate_of_total: string; // 解决占比
  accuracy: string; // 准确率
  accuracy_rate_of_total: string; // 准确占比
  level_distribution: string; // 问题等级分布
  repeat_rate: string; // 重复率
  repeat_rate_of_total: string; // 重复问题占比
  serious_issue_of_total: string; // 严重问题（p0+p1）召回率占比
  mttr: string; // p0问题mttr
}

interface ToolsInfo {
  tools_name: string;
  issues_count: string;
  resolve_rate: string;
  resolve_rate_of_total: string;
  accuracy: string;
  accuracy_rate_of_total: string;
  level_distribution: string;
  mttr: string;
}

@Injectable()
export default class DashboardService {
  @Inject()
  private meegoService: MeegoService;
  private meegos: WorkItemInfo[];
  private validMeegos: WorkItemInfo[];
  private toolsMeegos: WorkItemInfo[];
  private onlineMeegos: WorkItemInfo[];
  private legacyMeegos: WorkItemInfo[];
  private p0Meegos: WorkItemInfo[];
  private incidentMeegos: WorkItemInfo[];
  private isAndroid: boolean;

  toPercentage(value: number) {
    return `${(value * 100).toFixed(2)}%`;
  }

  resolveIssueCount(issues: WorkItemInfo[]): number {
    if (issues === undefined) {
      return 0;
    }
    const resolve_issues = issues.filter(
      workitem =>
        workitem !== undefined &&
        (workitem.work_item_status.state_key === 'CLOSED' || workitem.work_item_status.state_key === 'RESOLVED'),
    );
    return resolve_issues.length;
  }

  seriousIssueCount(issues: WorkItemInfo[]): number {
    if (issues === undefined) {
      return 0;
    }
    const resolve_issues = issues.filter(
      workitem =>
        workitem !== undefined &&
        (workitem.work_item_status.state_key === 'CLOSED' || workitem.work_item_status.state_key === 'RESOLVED'),
    );
    const p0 = resolve_issues.filter(meegoInfo =>
      meegoInfo.fields.some(
        field =>
          field !== undefined && field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P0',
      ),
    );
    const p1 = resolve_issues.filter(meegoInfo =>
      meegoInfo.fields.some(
        field =>
          field !== undefined && field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P1',
      ),
    );
    return p0.length + p1.length;
  }

  issuePriority(issues: WorkItemInfo[]): string {
    if (issues === undefined && typeof issues !== 'function') {
      return `p0: 0, p1: 0, p2: 0`;
    }
    const p0 = issues.filter(meegoInfo =>
      meegoInfo.fields.some(
        field =>
          field !== undefined && field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P0',
      ),
    );
    const p1 = issues.filter(meegoInfo =>
      meegoInfo.fields.some(
        field =>
          field !== undefined && field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P1',
      ),
    );
    const p2 = issues.filter(meegoInfo =>
      meegoInfo.fields.some(
        field =>
          field !== undefined && field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P2',
      ),
    );
    return `p0: ${p0.length}, p1: ${p1.length}, p2: ${p2.length}`;
  }

  async queryVersionAllIssue(
    discoveryVersions: string[],
    filterOptions: string,
  ): Promise<{
    totalCount: number;
    validCount: number;
    grayCount: number;
    onlineCount: number;
    p0Count: number;
    incidentCount: number;
    escapeCount: number;
  }> {
    console.log('start querryAllIssue', typeof discoveryVersions, discoveryVersions);
    const versions = [];
    for (const version of discoveryVersions) {
      console.log('input version', version);
      if (version.includes('Android')) {
        this.isAndroid = true;
      } else {
        this.isAndroid = false;
      }
      const versionID = await this.meegoService.queryVersionId(faceuProjectKey, version);
      versions.push(versionID);
    }
    console.log('versions', versions);
    const result = await this.meegoService.issueForQualitySearch(
      faceuProjectKey,
      ['0', '1', '2', '3'],
      versions,
      filterOptions,
    );

    this.meegos = result.filter(
      meegoInfo =>
        !(
          meegoInfo.fields.some(
            field =>
              field.field_key === 'bug_channel' &&
              (field.field_value as FieldValue)?.label === 'TikTok质量小助手-单issue稳定性',
          ) ||
          meegoInfo.fields.some(
            field => field.field_key === 'bug_channel' && (field.field_value as FieldValue)?.label === 'Rig助手',
          ) ||
          meegoInfo.fields.some(
            field =>
              field.field_key === 'bug_channel' && (field.field_value as FieldValue)?.label === '实验巡检-大盘稳定性',
          )
        ),
    );
    this.toolsMeegos = result.filter(item => !this.meegos.includes(item));
    if (this.isAndroid) {
      return {
        totalCount: this.meegos.length,
        validCount: this.queryVersionAllValidIssue(),
        grayCount: this.queryAndroidGrayIssue(),
        onlineCount: this.queryVersionOnlineIssue(this.isAndroid),
        p0Count: this.queryOnlineP0Issue(),
        incidentCount: this.queryVersionIncidentIssue(),
        escapeCount: this.queryEscapeIssue(),
      };
    }
    return {
      totalCount: this.meegos.length,
      validCount: this.queryVersionAllValidIssue(),
      onlineCount: this.queryVersionOnlineIssue(this.isAndroid),
      grayCount: 0,
      p0Count: this.queryOnlineP0Issue(),
      incidentCount: this.queryVersionIncidentIssue(),
      escapeCount: this.queryEscapeIssue(),
    };
  }

  // 有效问题
  queryVersionAllValidIssue(): number {
    const legacyMeegos = this.meegos.filter(meegoInfo =>
      meegoInfo.fields.some(
        field => field.field_key === 'field_fbc20d' && (field.field_value as FieldValue)?.label === '历史遗留',
      ),
    );
    this.legacyMeegos = legacyMeegos;
    console.log('legacyMeegos', this.legacyMeegos);
    const result = this.meegos.filter(
      meegoInfo =>
        !(
          meegoInfo.fields.some(
            field => field.field_key === 'field_6a1f6a' && (field.field_value as FieldValue)?.label === '确认是无效Bug',
          ) ||
          meegoInfo.fields.some(
            field => field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P3',
          )
        ),
    );
    this.validMeegos = result.filter(item => !legacyMeegos.includes(item));
    return result.length;
  }

  queryAndroidGrayIssue(): number {
    const result = this.validMeegos.filter(meegoInfo =>
      meegoInfo.fields.some(
        field => field.field_key === 'issue_stage' && (field.field_value as FieldValue)?.label === '灰度阶段',
      ),
    );
    return result.length;
  }

  // 线上问题
  queryVersionOnlineIssue(isAndroid: boolean): number {
    if (isAndroid) {
      const result = this.validMeegos.filter(meegoInfo =>
        meegoInfo.fields.some(
          field => field.field_key === 'issue_stage' && (field.field_value as FieldValue)?.label === '线上阶段',
        ),
      );
      this.onlineMeegos = result;
      return result.length;
    } else {
      const result = this.validMeegos.filter(meegoInfo =>
        meegoInfo.fields.some(
          field =>
            field.field_key === 'issue_stage' &&
            ((field.field_value as FieldValue)?.label === '线上阶段' ||
              (field.field_value as FieldValue)?.label === '小流量阶段'),
        ),
      );
      this.onlineMeegos = result;
      console.log('onlineMeegos', this.onlineMeegos);
      return result.length;
    }
  }

  repeatIssue(issues: WorkItemInfo[]): number {
    const repeatMeegos = issues?.filter(meegoInfo =>
      meegoInfo.fields.some(
        field => field.field_key === 'field_100b1e' && (field.field_value as FieldValue)?.label === '重复提交',
      ),
    );
    return repeatMeegos?.length ?? 0;
  }

  // 线上问题
  queryToolsData(): ToolsInfo[] {
    function validMeegos(meegos: WorkItemInfo[]) {
      return meegos.filter(
        meegoInfo =>
          !(
            meegoInfo.fields.some(
              field =>
                field.field_key === 'field_6a1f6a' && (field.field_value as FieldValue)?.label === '确认是无效Bug',
            ) ||
            meegoInfo.fields.some(
              field => field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P3',
            )
          ),
      );
    }
    const allTTMeegos = this.toolsMeegos.filter(
      meegoInfo =>
        meegoInfo.fields !== undefined &&
        meegoInfo.fields.some(
          field =>
            field.field_key === 'bug_channel' &&
            (field.field_value as FieldValue)?.label === 'TikTok质量小助手-单issue稳定性',
        ),
    );
    const allRigMeegos = this.toolsMeegos.filter(
      meegoInfo =>
        meegoInfo.fields !== undefined &&
        meegoInfo.fields.some(
          field => field.field_key === 'bug_channel' && (field.field_value as FieldValue)?.label === 'Rig助手',
        ),
    );
    const validTTMeegos = validMeegos(allTTMeegos);
    const validRigMeegos = validMeegos(allRigMeegos);
    const allLibraMeegos = this.toolsMeegos.filter(
      meegoInfo =>
        !(meegoInfo.fields === undefined) &&
        meegoInfo.fields.some(
          field =>
            field.field_key === 'bug_channel' && (field.field_value as FieldValue)?.label === '实验巡检-大盘稳定性',
        ),
    );

    const validLibraMeegos = validMeegos(allLibraMeegos);

    const valid_meegos_count = validMeegos(this.toolsMeegos).length;
    const valid_tt_meegos_count = validTTMeegos.length;
    const all_tt_meegos_count = allTTMeegos.length;
    return [
      {
        tools_name: 'TikTok质量小助手',
        accuracy_rate_of_total: this.toPercentage(valid_tt_meegos_count / valid_meegos_count),
        resolve_rate: this.toPercentage(this.resolveIssueCount(validTTMeegos) / valid_tt_meegos_count),
        resolve_rate_of_total: this.toPercentage(
          this.resolveIssueCount(validTTMeegos) / this.resolveIssueCount(validTTMeegos),
        ),
        accuracy: this.toPercentage(valid_tt_meegos_count / all_tt_meegos_count),
        level_distribution: this.issuePriority(validTTMeegos),
        issues_count: `全部问题:${all_tt_meegos_count}, 有效问题:${valid_tt_meegos_count}, 已解决问题数:${this.resolveIssueCount(validTTMeegos)}`,
        mttr: this.caculateMTTR(validTTMeegos),
      },
      {
        tools_name: 'rig',
        accuracy_rate_of_total: this.toPercentage(validRigMeegos.length / valid_meegos_count),
        resolve_rate: this.toPercentage(this.resolveIssueCount(validRigMeegos) / validRigMeegos.length),
        resolve_rate_of_total: this.toPercentage(
          this.resolveIssueCount(validRigMeegos) / this.resolveIssueCount(validRigMeegos),
        ),
        accuracy: this.toPercentage(validRigMeegos.length / allRigMeegos.length),
        level_distribution: this.issuePriority(validRigMeegos),
        issues_count: `全部问题:${allRigMeegos.length}, 有效问题:${validRigMeegos.length}, 已解决问题数:${this.resolveIssueCount(validRigMeegos)}`,
        mttr: this.caculateMTTR(validRigMeegos),
      },
      {
        tools_name: '实验巡检',
        accuracy: this.toPercentage(validLibraMeegos.length / allLibraMeegos.length),
        resolve_rate: this.toPercentage(this.resolveIssueCount(validLibraMeegos) / validLibraMeegos.length),
        resolve_rate_of_total: this.toPercentage(
          this.resolveIssueCount(validLibraMeegos) / this.resolveIssueCount(validLibraMeegos),
        ),
        accuracy_rate_of_total: this.toPercentage(validLibraMeegos.length / valid_meegos_count),
        level_distribution: this.issuePriority(validLibraMeegos),
        issues_count: `全部问题：${allLibraMeegos.length}，有效问题：${validLibraMeegos.length}, 已解决问题数:${this.resolveIssueCount(validLibraMeegos)}`,
        mttr: this.caculateMTTR(validLibraMeegos),
      },
    ];
  }

  // 线上p0问题
  queryOnlineP0Issue(): number {
    const result = this.onlineMeegos.filter(meegoInfo =>
      meegoInfo.fields.some(
        field => field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P0',
      ),
    );
    this.p0Meegos = result;
    return result.length;
  }

  queryAllP0Issue(): number {
    const result = this.validMeegos.filter(meegoInfo =>
      meegoInfo.fields.some(
        field => field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P0',
      ),
    );
    this.p0Meegos = result;
    return result.length;
  }

  // 线上事故
  queryVersionIncidentIssue(): number {
    const result = this.p0Meegos.filter(meegoInfo =>
      meegoInfo.fields.some(
        field => field.field_key === 'tags' && (field.field_value as FieldValue)?.label === '线上事故',
      ),
    );
    this.incidentMeegos = result;
    return result.length;
  }

  // 线上逃逸问题
  queryEscapeIssue(): number {
    const validTTMeegos = this.onlineMeegos.filter(
      meegoInfo =>
        !(meegoInfo.fields === undefined) &&
        meegoInfo.fields.some(
          field =>
            field.field_key === 'bug_channel' &&
            (field.field_value as FieldValue)?.label === 'TikTok质量小助手-单issue稳定性',
        ),
    );
    console.log('逃逸问题数：', this.onlineMeegos.length, validTTMeegos.length);
    return this.onlineMeegos.length - validTTMeegos.length;
  }

  queryIncidentMTTR(): string {
    return this.caculateMTTR(this.incidentMeegos);
  }

  caculateMTTR(meegos: WorkItemInfo[]): string {
    if (meegos === undefined || meegos.length === 0) {
      return '0';
    }
    function calculateAverage(numbers: number[]) {
      if (numbers.length === 0) {
        return '0';
      } // 防止除以零
      const sum = numbers.reduce((acc, curr) => acc + curr, 0); // 计算总和
      if (sum === 0) {
        return '+∞';
      }
      return (sum / numbers.length / 3600000).toFixed(2); // 计算平均值
    }

    const resolveTimes: number[] = [];
    const p0Meegos = meegos.filter(meegoInfo =>
      meegoInfo.fields.some(
        field => field.field_key === 'priority' && (field.field_value as FieldValue)?.label === 'P0',
      ),
    );
    console.log('p0Meegos', p0Meegos);
    for (const meego of p0Meegos) {
      const openWorkItem = meego.work_item_status.history?.find(item => item.state_key === 'OPEN');
      if (meego.work_item_status.state_key === 'CLOSED') {
        const resolveWorkItem = meego.work_item_status.history?.find(item => item.state_key === 'RESOLVED');
        if (resolveWorkItem !== undefined && openWorkItem !== undefined) {
          if (resolveWorkItem.updated_at !== undefined && openWorkItem.updated_at !== undefined) {
            const resolve_update_at = resolveWorkItem.updated_at;
            const open_update_at = openWorkItem.updated_at;
            resolveTimes.push(resolve_update_at - open_update_at);
          }
        }
      } else if (meego.work_item_status.state_key === 'RESOLVED') {
        if (
          meego.work_item_status.updated_at !== undefined &&
          openWorkItem !== undefined &&
          openWorkItem.updated_at !== undefined
        ) {
          resolveTimes.push(meego.work_item_status.updated_at - openWorkItem.updated_at);
        }
      }
    }
    return calculateAverage(resolveTimes);
  }

  queryMeegoData(): StageInfo[] {
    const groupedByStage: Record<string, WorkItemInfo[]> = {};
    for (const item of this.validMeegos) {
      for (const field of item.fields) {
        if (field.field_key === 'issue_stage') {
          const { label } = field.field_value as FieldValue;
          if (!groupedByStage[label]) {
            groupedByStage[label] = [];
          }
          groupedByStage[label].push(item);
        }
      }
    }

    const allIssueGroupedByStage: Record<string, WorkItemInfo[]> = {};
    // 按照 issue_stage 的 label 进行分组
    for (const item of this.meegos) {
      for (const field of item.fields) {
        if (field.field_key === 'issue_stage') {
          const { label } = field.field_value as FieldValue;
          if (allIssueGroupedByStage[label] === undefined) {
            allIssueGroupedByStage[label] = [];
          }
          // 将当前项添加到相应的 label 数组中
          allIssueGroupedByStage[label].push(item);
        }
      }
    }

    function validIssueCount(state: string): number {
      if (groupedByStage[state] === undefined) {
        return 0;
      }
      return groupedByStage[state].length;
    }

    function allIssueCount(state: string): number {
      if (allIssueGroupedByStage[state] === undefined) {
        return 0;
      }
      return allIssueGroupedByStage[state].length;
    }
    const valid_meegos_count = this.validMeegos.length;
    console.log(
      '自动化测试：',
      groupedByStage['自动化测试'] ?? [],
      groupedByStage['新功能阶段'] ?? [],
      groupedByStage['系统阶段'] ?? [],
    );
    const seriousResolveIssueCount = this.seriousIssueCount(this.validMeegos);
    if (this.isAndroid) {
      const result: StageInfo[] = [
        {
          stage: '自动化测试',
          accuracy_rate_of_total: this.toPercentage(
            (
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []) as WorkItemInfo[]
            ).length / valid_meegos_count,
          ),
          serious_issue_of_total: this.toPercentage(
            this.seriousIssueCount(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) / seriousResolveIssueCount,
          ),
          escape_rate: this.toPercentage(
            (this.resolveIssueCount(this.validMeegos) -
              this.resolveIssueCount(
                (groupedByStage['自动化测试'] ?? [])
                  .concat(groupedByStage['新功能阶段'] ?? [])
                  .concat(groupedByStage['集成阶段'] ?? []),
              )) /
              this.resolveIssueCount(this.validMeegos),
          ),
          issues_count: `总问题数:${allIssueCount('新功能阶段') + allIssueCount('集成阶段') + allIssueCount('自动化测试')}, 有效问题数:${validIssueCount('新功能阶段') + validIssueCount('集成阶段') + validIssueCount('自动化测试')}, 已解决问题数:${this.resolveIssueCount((groupedByStage['自动化测试'] ?? []).concat(groupedByStage['新功能阶段'] ?? []).concat(groupedByStage['集成阶段'] ?? []))}, 已解决p0+p1:${this.seriousIssueCount((groupedByStage['自动化测试'] ?? []).concat(groupedByStage['新功能阶段'] ?? []).concat(groupedByStage['集成阶段'] ?? []))}`,
          resolve_rate: this.toPercentage(
            this.resolveIssueCount(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) /
              (validIssueCount('新功能阶段') + validIssueCount('集成阶段') + validIssueCount('自动化测试')),
          ),
          resolve_rate_of_total: this.toPercentage(
            this.resolveIssueCount(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) / this.resolveIssueCount(this.validMeegos),
          ),
          accuracy: this.toPercentage(
            (validIssueCount('新功能阶段') + validIssueCount('集成阶段') + validIssueCount('自动化测试')) /
              (allIssueCount('新功能阶段') + allIssueCount('集成阶段') + allIssueCount('自动化测试')),
          ),
          level_distribution: this.issuePriority(
            (groupedByStage['自动化测试'] ?? [])
              .concat(groupedByStage['新功能阶段'] ?? [])
              .concat(groupedByStage['集成阶段'] ?? []),
          ),
          repeat_rate: this.toPercentage(
            this.repeatIssue(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) /
              (validIssueCount('新功能阶段') + validIssueCount('集成阶段') + validIssueCount('自动化测试')),
          ),
          repeat_rate_of_total: this.toPercentage(
            this.repeatIssue(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) / valid_meegos_count,
          ),
          mttr: this.caculateMTTR(
            (groupedByStage['自动化测试'] ?? [])
              .concat(groupedByStage['新功能阶段'] ?? [])
              .concat(groupedByStage['集成阶段'] ?? []),
          ),
        },
        {
          stage: '灰度阶段',
          accuracy_rate_of_total: this.toPercentage(
            (groupedByStage['灰度阶段'] as WorkItemInfo[])?.length / valid_meegos_count,
          ),
          serious_issue_of_total: this.toPercentage(
            this.seriousIssueCount(groupedByStage['灰度阶段']) / seriousResolveIssueCount,
          ),
          escape_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          issues_count: `总问题数:${allIssueCount('灰度阶段')}, 有效问题数:${validIssueCount('灰度阶段')}, 已解决问题数:${this.resolveIssueCount(groupedByStage['灰度阶段'])}, 已解决p0+p1:${this.seriousIssueCount(groupedByStage['灰度阶段'])}`,
          resolve_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['灰度阶段']) / validIssueCount('灰度阶段'),
          ),
          resolve_rate_of_total: this.toPercentage(
            this.resolveIssueCount(groupedByStage['灰度阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          accuracy: this.toPercentage(validIssueCount('灰度阶段') / allIssueCount('灰度阶段')),
          level_distribution: this.issuePriority(groupedByStage['灰度阶段']),
          repeat_rate: this.toPercentage(this.repeatIssue(groupedByStage['灰度阶段']) / validIssueCount('灰度阶段')),
          repeat_rate_of_total: this.toPercentage(this.repeatIssue(groupedByStage['灰度阶段']) / valid_meegos_count),
          mttr: this.caculateMTTR(groupedByStage['灰度阶段']),
        },
        {
          stage: '线上阶段',
          accuracy_rate_of_total: this.toPercentage(
            (groupedByStage['线上阶段'] as WorkItemInfo[])?.length / valid_meegos_count,
          ),
          serious_issue_of_total: this.toPercentage(
            this.seriousIssueCount(groupedByStage['线上阶段']) / seriousResolveIssueCount,
          ),
          escape_rate: '-',
          issues_count: `总问题数:${allIssueCount('线上阶段')}, 有效问题数:${validIssueCount('线上阶段')}, 已解决问题数:${this.resolveIssueCount(groupedByStage['线上阶段'])}, 已解决p0+p1:${this.seriousIssueCount(groupedByStage['线上阶段'])}`,
          resolve_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / validIssueCount('线上阶段'),
          ),
          resolve_rate_of_total: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          accuracy: this.toPercentage(validIssueCount('线上阶段') / allIssueCount('线上阶段')),
          level_distribution: this.issuePriority(groupedByStage['线上阶段']),
          repeat_rate: this.toPercentage(this.repeatIssue(groupedByStage['线上阶段']) / validIssueCount('线上阶段')),
          repeat_rate_of_total: this.toPercentage(this.repeatIssue(groupedByStage['线上阶段']) / valid_meegos_count),
          mttr: this.caculateMTTR(groupedByStage['线上阶段']),
        },
        {
          stage: '整体',
          escape_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          serious_issue_of_total: '',
          issues_count: `总问题数:${this.meegos.length}, 有效问题数:${this.validMeegos?.length}, 已解决问题数:${this.resolveIssueCount(this.validMeegos)}, 已解决p0+p1:${seriousResolveIssueCount}, 历史遗留：${this.legacyMeegos.length}`,
          resolve_rate: this.toPercentage(this.resolveIssueCount(this.validMeegos) / this.validMeegos?.length),
          accuracy: this.toPercentage(this.validMeegos?.length / this.meegos?.length),
          resolve_rate_of_total: '-',
          accuracy_rate_of_total: '-',
          level_distribution: this.issuePriority(this.validMeegos),
          repeat_rate: this.toPercentage(this.repeatIssue(this.validMeegos) / valid_meegos_count),
          repeat_rate_of_total: '-',
          mttr: this.caculateMTTR(this.validMeegos),
        },
      ];
      return result;
    } else {
      return [
        {
          stage: '自动化测试',
          accuracy_rate_of_total: this.toPercentage(
            (
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []) as WorkItemInfo[]
            ).length / valid_meegos_count,
          ),
          serious_issue_of_total: this.toPercentage(
            this.seriousIssueCount(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []) as WorkItemInfo[],
            ) / seriousResolveIssueCount,
          ),
          escape_rate: this.toPercentage(
            (this.resolveIssueCount(this.validMeegos) -
              this.resolveIssueCount(
                (groupedByStage['自动化测试'] ?? [])
                  .concat(groupedByStage['新功能阶段'] ?? [])
                  .concat(groupedByStage['集成阶段'] ?? []),
              )) /
              this.resolveIssueCount(this.validMeegos),
          ),
          issues_count: `总问题数:${allIssueCount('新功能阶段') + allIssueCount('自动化测试') + allIssueCount('集成阶段')}, 有效问题数:${validIssueCount('新功能阶段') + validIssueCount('自动化测试') + validIssueCount('集成阶段')}, 已解决问题数:${this.resolveIssueCount((groupedByStage['自动化测试'] ?? []).concat(groupedByStage['新功能阶段'] ?? []).concat(groupedByStage['集成阶段'] ?? []))}, 已解决p0+p1:${this.seriousIssueCount((groupedByStage['自动化测试'] ?? []).concat(groupedByStage['新功能阶段'] ?? []).concat(groupedByStage['集成阶段'] ?? []))}`,
          resolve_rate: this.toPercentage(
            this.resolveIssueCount(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) /
              (validIssueCount('新功能阶段') + validIssueCount('集成阶段') + validIssueCount('自动化测试')),
          ),
          resolve_rate_of_total: this.toPercentage(
            this.resolveIssueCount(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) / this.resolveIssueCount(this.validMeegos),
          ),
          accuracy: this.toPercentage(
            (validIssueCount('新功能阶段') + validIssueCount('自动化测试') + validIssueCount('集成阶段')) /
              (allIssueCount('新功能阶段') + allIssueCount('自动化测试') + allIssueCount('集成阶段')),
          ),
          level_distribution: this.issuePriority(
            (groupedByStage['自动化测试'] ?? [])
              .concat(groupedByStage['新功能阶段'] ?? [])
              .concat(groupedByStage['集成阶段'] ?? []),
          ),
          repeat_rate: this.toPercentage(
            this.repeatIssue(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) /
              (validIssueCount('新功能阶段') + validIssueCount('自动化测试') + validIssueCount('集成阶段')),
          ),
          repeat_rate_of_total: this.toPercentage(
            this.repeatIssue(
              (groupedByStage['自动化测试'] ?? [])
                .concat(groupedByStage['新功能阶段'] ?? [])
                .concat(groupedByStage['集成阶段'] ?? []),
            ) / valid_meegos_count,
          ),
          mttr: this.caculateMTTR(
            (groupedByStage['自动化测试'] ?? [])
              .concat(groupedByStage['新功能阶段'] ?? [])
              .concat(groupedByStage['集成阶段'] ?? []),
          ),
        },
        {
          stage: '灰度阶段',
          accuracy_rate_of_total: this.toPercentage(
            (groupedByStage['灰度阶段'] as WorkItemInfo[])?.length / valid_meegos_count,
          ),
          serious_issue_of_total: this.toPercentage(
            this.seriousIssueCount(groupedByStage['灰度阶段']) / seriousResolveIssueCount,
          ),
          escape_rate: this.toPercentage(
            (this.resolveIssueCount(groupedByStage['小流量阶段']) +
              this.resolveIssueCount(groupedByStage['线上阶段'])) /
              this.resolveIssueCount(this.validMeegos),
          ),
          issues_count: `总问题数:${allIssueCount('灰度阶段')}, 有效问题数:${validIssueCount('灰度阶段')}, 已解决问题数:${this.resolveIssueCount(groupedByStage['灰度阶段'])}, 已解决p0+p1:${this.seriousIssueCount(groupedByStage['灰度阶段'])}`,
          resolve_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['灰度阶段']) / validIssueCount('灰度阶段'),
          ),
          resolve_rate_of_total: this.toPercentage(
            this.resolveIssueCount(groupedByStage['灰度阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          accuracy: this.toPercentage(validIssueCount('灰度阶段') / allIssueCount('灰度阶段')),
          level_distribution: this.issuePriority(groupedByStage['灰度阶段']),
          repeat_rate: this.toPercentage(this.repeatIssue(groupedByStage['灰度阶段']) / validIssueCount('灰度阶段')),
          repeat_rate_of_total: this.toPercentage(this.repeatIssue(groupedByStage['灰度阶段']) / valid_meegos_count),
          mttr: this.caculateMTTR(groupedByStage['灰度阶段']),
        },
        {
          stage: '小流量阶段',
          accuracy_rate_of_total: this.toPercentage(
            ((groupedByStage['小流量阶段'] as WorkItemInfo[]) ?? []).length / valid_meegos_count,
          ),
          serious_issue_of_total: this.toPercentage(
            this.seriousIssueCount(groupedByStage['小流量阶段']) / seriousResolveIssueCount,
          ),
          escape_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          issues_count: `总问题数:${allIssueCount('小流量阶段')}, 有效问题数:${validIssueCount('小流量阶段')}, 已解决问题数:${this.resolveIssueCount(groupedByStage['小流量阶段'])}, 已解决p0+p1:${this.seriousIssueCount(groupedByStage['小流量阶段'])}`,
          resolve_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['小流量阶段']) / validIssueCount('小流量阶段'),
          ),
          resolve_rate_of_total: this.toPercentage(
            this.resolveIssueCount(groupedByStage['小流量阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          accuracy: this.toPercentage(validIssueCount('小流量阶段') / allIssueCount('小流量阶段')),
          level_distribution: this.issuePriority(groupedByStage['小流量阶段']),
          repeat_rate: this.toPercentage(
            this.repeatIssue(groupedByStage['小流量阶段']) / validIssueCount('小流量阶段'),
          ),
          repeat_rate_of_total: this.toPercentage(this.repeatIssue(groupedByStage['小流量阶段']) / valid_meegos_count),
          mttr: this.caculateMTTR(groupedByStage['小流量阶段']),
        },
        {
          stage: '线上阶段',
          accuracy_rate_of_total: this.toPercentage(
            (groupedByStage['线上阶段'] as WorkItemInfo[])?.length / valid_meegos_count,
          ),
          serious_issue_of_total: this.toPercentage(
            this.seriousIssueCount(groupedByStage['线上阶段']) / seriousResolveIssueCount,
          ),
          escape_rate: '-',
          issues_count: `总问题数:${allIssueCount('线上阶段')}, 有效问题数:${validIssueCount('线上阶段')}, 已解决问题数:${this.resolveIssueCount(groupedByStage['线上阶段'])}, 已解决p0+p1:${this.seriousIssueCount(groupedByStage['线上阶段'])}`,
          resolve_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / validIssueCount('线上阶段'),
          ),
          resolve_rate_of_total: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          accuracy: this.toPercentage(validIssueCount('线上阶段') / allIssueCount('线上阶段')),
          level_distribution: this.issuePriority(groupedByStage['线上阶段']),
          repeat_rate: this.toPercentage(this.repeatIssue(groupedByStage['线上阶段']) / validIssueCount('线上阶段')),
          repeat_rate_of_total: this.toPercentage(this.repeatIssue(groupedByStage['线上阶段']) / valid_meegos_count),
          mttr: this.caculateMTTR(groupedByStage['线上阶段']),
        },
        {
          stage: '整体',
          accuracy_rate_of_total: '-',
          escape_rate: this.toPercentage(
            this.resolveIssueCount(groupedByStage['线上阶段']) / this.resolveIssueCount(this.validMeegos),
          ),
          serious_issue_of_total: '',
          issues_count: `总问题数:${this.meegos.length}, 有效问题数:${this.validMeegos.length}, 已解决问题数:${this.resolveIssueCount(this.validMeegos)}, 已解决p0+p1:${seriousResolveIssueCount}, 历史遗留:${this.legacyMeegos.length}`,
          resolve_rate: this.toPercentage(this.resolveIssueCount(this.validMeegos) / this.validMeegos.length),
          resolve_rate_of_total: '-',
          accuracy: this.toPercentage(this.validMeegos.length / this.meegos.length),
          level_distribution: this.issuePriority(this.validMeegos),
          repeat_rate: this.toPercentage(this.repeatIssue(this.validMeegos) / valid_meegos_count),
          repeat_rate_of_total: '-',
          mttr: this.caculateMTTR(this.validMeegos),
        },
      ];
    }
  }
}
