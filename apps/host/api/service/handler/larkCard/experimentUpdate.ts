import {
  CallBackToast,
  Card,
  CardButtonType,
  CardCallback,
  CardElementTagV2,
  CardTextTag,
} from '@pa/shared/dist/src/lark/larkCard';
import { HandleStatus, IndicatorType } from '@shared/experiment/experimentInfo';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { Inject, Injectable } from '@gulux/gulux';
import ExperimentSource from '../../dao/ExperimentSource';
import LarkService from '@pa/backend/dist/src/third/lark';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';

@Injectable()
export default class ExperimentUpdateHandler {
  @Inject()
  private lark: LarkService;

  @Inject()
  private experimentSourceDao: ExperimentSource;

  @Inject()
  private rpcProxy: RpcProxyManager;

  async handlerExperimentUpdateCallback(callbackData: CardCallback): Promise<Card | undefined> {
    const { id, isAd } = callbackData.action.value;
    const card = await this.experimentSourceDao.createErrorCard(id, isAd === '1');
    if (card) {
      const chat = await this.experimentSourceDao.getChatId(id);
      if (chat.chatId) {
        await this.lark.sendCardMessage(UserIdType.chatId, chat.chatId, card);
      }
      if (chat.oncallId) {
        const openChatId = await this.lark.chatId2OpenChatId(chat.oncallId);
        await this.lark.sendCardMessage(UserIdType.chatId, openChatId, card);
      }
    }
    return undefined;
  }

  async handlerExperimentUpdateStatusCallback(callbackData: CardCallback): Promise<Card | undefined> {
    const { option } = callbackData.action;
    const { id, appId, indicatorType } = callbackData.action.value;
    await this.experimentSourceDao.updateExperimentStatus(
      id,
      Number(appId),
      indicatorType as IndicatorType,
      option as HandleStatus,
    );
    return;
  }
  async handlerExperimentUpdateRemarkCallback(callbackData: CardCallback): Promise<Card | undefined> {
    const { option } = callbackData.action;
    const { id, appId, indicatorType } = callbackData.action.value;
    await this.experimentSourceDao.updateExperimentRemark(
      id,
      Number(appId),
      indicatorType as IndicatorType,
      option as string,
    );
    return;
  }

  async handleLibraLaunchGrayNotifyNotSendAnymoreCallback(callbackData: CardCallback): Promise<Card | undefined> {
    console.log('handleLibraLaunchGrayNotifyNotSendAnymoreCallback');
    const { libraInfo } = callbackData.action.value as unknown as { libraInfo: any };
    if (libraInfo !== undefined && libraInfo !== null) {
      const overseas = libraInfo?.flightInfo?.region === 1 || libraInfo?.flightInfo?.region === 2;
      await this.rpcProxy.getQuality(overseas).forbidLibraLaunchGrayNotify(libraInfo);
    }
    return;
  }

  async handleLibra100PercentGrayNotifyNotSendAnymoreCallback(callbackData: CardCallback): Promise<Card | undefined> {
    console.log('handleLibra100PercentGrayNotifyNotSendAnymoreCallback');
    const { libraInfo } = callbackData.action.value as unknown as { libraInfo: any };
    if (libraInfo !== undefined && libraInfo !== null) {
      const overseas = libraInfo?.flightInfo?.region === 1 || libraInfo?.flightInfo?.region === 2;
      await this.rpcProxy.getQuality(overseas).forbidLibra100PercentGrayNotify(libraInfo);
    }
    return;
  }

  async handleLibraCloseGrayNotifyNotSendAnymoreCallback(callbackData: CardCallback): Promise<CallBackToast> {
    console.log('handleLibraCloseGrayNotifyNotSendAnymoreCallback');
    const { libraInfo } = callbackData.action.value as unknown as { libraInfo: any };

    try {
      if (libraInfo !== undefined && libraInfo !== null) {
        await this.rpcProxy.getQuality(true).forbidLibraCloseGrayNotify(libraInfo);
      }
      return {
        toast: {
          type: 'success',
          content: '设置成功',
        },
      } as CallBackToast;
    } catch (e) {
      return {
        toast: {
          type: 'error',
          content: '设置失败',
        },
      } as CallBackToast;
    }
  }

  async handleLibraCloseGray(callbackData: CardCallback): Promise<CallBackToast> {
    console.log('handleLibraCloseGray');
    try {
      const { info } = callbackData.action.value as unknown as { info: any };
      const messageId = callbackData.open_message_id;
      const overseas = info?.libraInfo?.flightInfo?.region === 1 || info?.libraInfo?.flightInfo?.region === 2;
      const res: any = await this.rpcProxy.getQuality(overseas).closeLibra(info, messageId);
      console.log(`实验关闭结果:${JSON.stringify(res)}`);
      if (res.code === 0) {
        return {
          toast: {
            type: 'success',
            content: '实验关闭成功',
          },
        } as CallBackToast;
      } else if (res.code === 202) {
        return {
          toast: {
            type: 'error',
            content: '实验已经关闭',
          },
        } as CallBackToast;
      } else {
        return {
          toast: {
            type: 'error',
            content: '实验关闭失败',
          },
        } as CallBackToast;
      }
    } catch (error) {
      console.log(`实验关闭异常:${error}`);
      return {
        toast: {
          type: 'error',
          content: '实验关闭失败',
        },
      } as CallBackToast;
    }
  }

  async handleLibraLaunchReleaseNotifyNotSendAnymoreCallback(callbackData: CardCallback): Promise<Card | undefined> {
    console.log('handleLibraLaunchReleaseNotifyNotSendAnymoreCallback');
    const { libraInfo } = callbackData.action.value as unknown as { libraInfo: any };
    if (libraInfo !== undefined && libraInfo !== null) {
      const overseas = libraInfo?.flightInfo?.region === 1 || libraInfo?.flightInfo?.region === 2;
      await this.rpcProxy.getQuality(overseas).forbidLibraLaunchReleaseNotify(libraInfo);
    }
    return;
  }
}
