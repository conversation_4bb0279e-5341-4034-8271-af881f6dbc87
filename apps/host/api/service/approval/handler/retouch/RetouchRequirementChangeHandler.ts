import { useInject } from '@edenx/runtime/bff';
import { Logger } from '@gulux/gulux';
import { dayjs } from '@pa/shared/dist/src/utils/dayjs';
import { LarkClient } from '@gulux/gulux/lark';
import { ApprovalTaskEvent, ApprovalTaskStatus } from '@pa/shared/dist/src/lark/approval';
import { ApprovalDBService } from '../../ApprovalDBService';
import { BaseApprovalHandler } from '../BaseApprovalHandler';
import ApprovalService from '../../ApprovalService';
import {
  ApprovalDetail,
  ApprovalHistoryEvent,
  ApprovalOrderStatus,
  ApprovalType,
} from '@shared/approval/ApprovalOrder';
import { ApprovalInfoTable } from '../../../../model/approval/ApprovalInfoModel';
import { teaCollect, TeaEvent } from '../../../../tea';
import {
  RequiredTrasactionCreateParams,
  VersionTransactionBinding,
  VersionTransactionType,
} from '@shared/releasePlatform/versionTransaction';
import { batchGetUserInfoByOpenIds } from '@api/index';
import { uniq } from 'lodash';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import LarkService from '@pa/backend/dist/src/third/lark';

export class RetouchRequirementChangeHandler extends BaseApprovalHandler {
  name = 'RetouchRequirementChangeHandler';
  approvalCode = 'E978B6C9-CA0D-4D7C-B06A-BA750EAECE89';

  async handleTask(event: ApprovalTaskEvent): Promise<boolean> {
    const approvalDBService = useInject(ApprovalDBService);
    const logger = useInject(Logger);
    logger.info(`[${this.name}] handleTask => ${JSON.stringify(event)}`);
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: event.instance_code,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }
    const instance = rsp?.data;
    // 查找审批节点
    const selfCheckTask = instance?.task_list?.find(it => it?.node_id === '78da6d83ec693538f55c9a6d8be13ef6'); // 自检节点
    const firstAssessmentTaskKeyName = '00b4e9a0047c3b5dffb8452972b3878a'; // 需求质量影响面判断
    const secondAssessmentTaskKeyName = '43d3875b5529f4ebb41b958677a4b023'; // 版本节奏影响判断
    const thirdAssessmentTaskKeyName = '767fe65d6e6b4a48239df06123b59e39'; // 受影响的各方影响判断
    const finalAssessmentTaskKeyName = '2a3e69d94694ca1528dccf3e480c42fe'; // 插入必要性判断
    const firstAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === firstAssessmentTaskKeyName);
    const secondAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === secondAssessmentTaskKeyName);
    const thirdAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === thirdAssessmentTaskKeyName);
    const finalAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === finalAssessmentTaskKeyName);

    const approval = await approvalDBService.findOneByTask(event);
    const approvalService = useInject(ApprovalService);
    if (approval) {
      if (approval.approvalStatus === ApprovalOrderStatus.STOP) {
        logger.info(`[${this.name}] approval => ${JSON.stringify(approval)}, do not to handle.`);
        return false;
      }
      if (event.status === ApprovalTaskStatus.APPROVED) {
        // 自检查节点通过，发第一批推送
        if (event.task_id === selfCheckTask?.id) {
          await approvalService.retouchRequirementInsertCommonAssessment(approval, firstAssessmentTaskKeyName);
          await approvalService.retouchApprovalInvolveBusinessLineAssessment(approval);
        }
        // 需求质量影响面判断审核通过，发第二批推送
        if (
          firstAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          firstAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchRequirementInsertCommonAssessment(approval, secondAssessmentTaskKeyName);
          await approvalService.retouchApprovalInvolveBusinessLineAssessment(approval);
        }
        // 版本节奏影响评估审批通过，发第三批推送
        if (
          secondAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          secondAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchRequirementInsertCommonAssessment(approval, thirdAssessmentTaskKeyName);
          // 受影响业务方加签
          await approvalService.retouchApprovalInvolveBusinessLineAction(
            approval,
            thirdAssessmentTaskList?.[0]?.id ?? '',
          );
        }
        // 其他业务线影响评估审批通过，发第四批推送
        if (
          thirdAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          thirdAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchRequirementInsertCommonAssessment(approval, finalAssessmentTaskKeyName);
          // 发送之前阶段的信息汇总
          const commentSummary = await approvalService.buildRetouchCommonSummary(
            event.instance_code,
            new Map<string, string[]>([
              [firstAssessmentTaskKeyName, firstAssessmentTaskList?.map(it => it.id) ?? []],
              [secondAssessmentTaskKeyName, secondAssessmentTaskList?.map(it => it.id) ?? []],
              [thirdAssessmentTaskKeyName, thirdAssessmentTaskList?.map(it => it.id) ?? []],
            ]),
          );
          if (commentSummary) {
            await approvalService.retouchRequirementInsertSummary(approval, commentSummary);
          }
        }
      } else if (event.status === ApprovalTaskStatus.REJECTED) {
        // 审批拒绝
      }
      return true;
    } else {
      logger.info(`[${this.name}] handle not find approval => ${JSON.stringify(event)}`);
    }
    return false;
  }

  override async handleApproved(approval?: ApprovalInfoTable) {
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.InsertProductRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_APPROVED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const logger = useInject(Logger);
    logger.info(`[RetouchRequirementChangeHandler] handleApproved approval:${JSON.stringify(approval)} `);
    const approvalService = useInject(ApprovalService);
    // 异常需求插入的信息通知
    const result = await approvalService.requirementChangeApproved(approval);
    logger.info(`[RetouchRequirementChangeHandler] handleApproved result:${JSON.stringify(result)} `);
    // 审批单落库
    const approvalDBService = useInject(ApprovalDBService);
    const instanceCode = approval?.instanceCode;
    if (instanceCode) {
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Completed);
    }
    return result;
  }

  override async handleRejected(approval?: ApprovalInfoTable) {
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.InsertProductRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_REJECTED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const logger = useInject(Logger);
    logger.info(`[RetouchRequirementChangeHandler] handleRejected approval:${JSON.stringify(approval)} `);
    // TODO 异常需求插入的信息通知
    const approvalService = useInject(ApprovalService);
    const result = await approvalService.requirementChangeRejected(approval);
    logger.info(`[RetouchRequirementChangeHandler] handleRejected result:${JSON.stringify(result)} `);
    // 审批单落库
    const approvalDBService = useInject(ApprovalDBService);
    const instanceCode = approval?.instanceCode;
    if (instanceCode) {
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Rejected);
    }
  }

  override constructVersionTrasactionInfo(approval?: ApprovalInfoTable): RequiredTrasactionCreateParams | undefined {
    if (!approval) {
      return undefined;
    }
    const bindings: VersionTransactionBinding[] =
      approval.requirementChangeInfo?.appIds?.map(appid => ({
        app_id: appid,
        version: approval.version,
      })) ?? [];
    const transactionInfo = {
      type: VersionTransactionType.RequirementInsert,
      bindings,
      extra: {
        approvalDetail: approval as ApprovalDetail,
      },
    } as RequiredTrasactionCreateParams;
    return transactionInfo;
  }
}
