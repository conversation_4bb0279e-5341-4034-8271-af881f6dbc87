import React, { useState } from 'react';
import { Button, Modal, Input, Space, Icon, Typography, Toast } from '@douyinfe/semi-ui';
import { createLibraNewInfo } from '@api/libra';
import { IconAlertTriangle, IconEditStroked, IconPlus } from '@douyinfe/semi-icons';
import { libraRegionByUrl } from '@shared/libra/libraManageUtils';
import { LibraRegion } from '@shared/libra/commonLibra';

interface AddMissingExperimentButtonProps {
  onSuccess?: () => void;
}

const AddMissingExperimentButton: React.FC<AddMissingExperimentButtonProps> = ({ onSuccess }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [url, setUrl] = useState('');
  const [urlError, setUrlError] = useState('');

  // URL正则表达式匹配
  const urlPatterns = [
    /^https:\/\/data\.bytedance\.net\/libra\/flight\/([^\/]+)/,
    /^https:\/\/libra-sg\.tiktok-row\.net\/libra\/flight\/([^\/]+)/,
  ];

  const validateUrl = (inputUrl: string): boolean => urlPatterns.some(pattern => pattern.test(inputUrl));

  const extractFlightId = (inputUrl: string): number | null => {
    for (const pattern of urlPatterns) {
      const match = inputUrl.match(pattern);
      if (match && match[1]) {
        return parseInt(match[1], 10);
      }
    }
    return null;
  };

  const extractRegion = (inputUrl: string): LibraRegion => libraRegionByUrl(inputUrl);

  const handleOpen = () => {
    setVisible(true);
    setUrl('');
    setUrlError('');
  };

  const handleClose = () => {
    setVisible(false);
    setUrl('');
    setUrlError('');
  };

  const handleConfirm = async () => {
    if (!url.trim()) {
      setUrlError('请输入实验URL链接');
      return;
    }

    if (!validateUrl(url)) {
      setUrlError('请输入有效的实验URL链接格式');
      return;
    }

    const flightId = extractFlightId(url);
    if (!flightId) {
      setUrlError('无法从URL中提取实验ID');
      return;
    }

    setLoading(true);
    try {
      const region = extractRegion(url);

      const result = await createLibraNewInfo({
        data: {
          region,
          app: 0, // 直接传入 0，该接口内部会根据 Flight（libra api）信息自动补充补全一下 libraAppId
          flightId,
        },
      });

      if (result.code === 0) {
        Toast.success('实验记录新增成功！');
        handleClose();
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error(result.message || '新增实验记录失败');
      }
    } catch (error) {
      Toast.error(`新增实验记录失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button type="primary" icon={<IconPlus />} onClick={handleOpen}>
        补录实验
      </Button>
      <Modal
        title="补录实验"
        visible={visible}
        onCancel={handleClose}
        confirmLoading={loading}
        onOk={handleConfirm}
        okText="确定"
        cancelText="取消"
        width={500}
      >
        <Space vertical align="start" style={{ width: '100%' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Icon svg={<IconAlertTriangle />} type="warning" style={{ color: '#faad14' }} />
            <Typography.Text strong>【谨慎】建议仅管理员使用</Typography.Text>
          </div>
          <div>请输入实验URL链接：</div>
          <Input
            placeholder="https://data.bytedance.net/libra/flight/123456/"
            value={url}
            onChange={value => {
              setUrl(value);
              setUrlError('');
            }}
            validateStatus={urlError ? 'error' : undefined}
          />
          {urlError && <div style={{ color: 'red', fontSize: 12 }}>{urlError}</div>}
          <div style={{ fontSize: 12, color: '#666' }}>
            支持格式：
            <br />• https://data.bytedance.net/libra/flight/{'{flightId}'}/ <br />•
            https://libra-sg.tiktok-row.net/libra/flight/{'{flightId}'}/
          </div>
        </Space>
      </Modal>
    </>
  );
};

export default AddMissingExperimentButton;
