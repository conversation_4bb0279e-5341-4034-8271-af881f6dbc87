import { CallBackToast, Card, CardCallback, CardCallbackType } from '@pa/shared/dist/src/lark/larkCard';
import { Inject, Injectable } from '@gulux/gulux';
import MajorIssueCheckHandler from './handler/larkCard/majorIssueCheck';
import ExperimentUpdateHandler from './handler/larkCard/experimentUpdate';
import CreateMrHandler from './handler/larkCard/createMr';
import CreateC<PERSON>ryPickMrsHandler from './handler/larkCard/createCherryPickMrs';
import BusCardCallback from './handler/larkCard/busCardCallback';
import UpdateChecklistInfoHandler from './handler/larkCard/updateChecklistInfo';
import FastMergeService from './bus/fastMerge';
import BatchMrService from './bus/BatchMr';
import PackageBalanceService from './packageBalanceService';
import VersionReleaseService from './releasePlatform/versionReleaseService';
import VersionAutoStageService from './releasePlatform/versionAutoStageService';
import MetricsAlarmService from './MetricsAlarmService';
import FeatureMonitorService from './featureMonitorService';
import { VersionStageCheckListService } from './releasePlatform/versionStageCheckListService';
import TestFlightStageService from './releasePlatform/stageServices/testFlightStageService';
import CheckItemAgentFactory from './releasePlatform/checkItemAgentFactory';
import BugResolveRatioCheckItemAgent from './releasePlatform/checkItemAgents/bugResolveRatioCheckItemAgent';
import FunctionalBugCheckItemAgent from './releasePlatform/checkItemAgents/functionalBugCheckItemAgent';
import QaTestService from './releasePlatform/qaTestService';
import { useInject } from '@edenx/runtime/bff';
import { ApprovalCardDispatchService } from './approval/ApprovalCardDispatchService';
import { ReleasePackageSubmitInfoService } from './releasePlatform/releasePackageSubmitService';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import AcceptanceFeatureNotificationService from './acceptanceCenter/acceptanceFeatureNotificationService';
import CircuitBreakerService from './circuitBreaker/circuitBreakerService';

/**
 * 两种更新卡片样式的方法
 * 1. 立即更新 立即更新需要3秒内处理完并返回结果
 * 2. 延迟更新
 * 返回 Card 组件则为立即更新，
 * 返回 undefined 为延迟更新。
 */

@Injectable()
export default class CardCallbackHandlerService {
  @Inject()
  private createMrHandler: CreateMrHandler;

  @Inject()
  private majorIssueCheckHandler: MajorIssueCheckHandler;

  @Inject()
  private experimentUpdateHandler: ExperimentUpdateHandler;

  @Inject()
  private createCherryPickMrsHandler: CreateCherryPickMrsHandler;

  @Inject()
  private busCardCallback: BusCardCallback;

  @Inject()
  private updateChecklistInfoHandler: UpdateChecklistInfoHandler;

  @Inject()
  private fastMergeService: FastMergeService;

  @Inject()
  private batchMrService: BatchMrService;

  @Inject()
  private packageBalanceService: PackageBalanceService;

  @Inject()
  private versionReleaseService: VersionReleaseService;

  @Inject()
  private versionAutoStageService: VersionAutoStageService;
  @Inject()
  private metricsAlarmService: MetricsAlarmService;
  @Inject()
  private featureMonitorService: FeatureMonitorService;
  @Inject()
  private versionCheckListService: VersionStageCheckListService;
  @Inject()
  private testFlightService: TestFlightStageService;
  private checkItemAgentFactory: CheckItemAgentFactory;
  @Inject()
  private bugResolveAgent: BugResolveRatioCheckItemAgent;
  @Inject()
  private functionalBugAgent: FunctionalBugCheckItemAgent;
  @Inject()
  private qaTestService: QaTestService;
  @Inject()
  private approvalCardDispatchService: ApprovalCardDispatchService;
  @Inject()
  private submitService: ReleasePackageSubmitInfoService;
  @Inject()
  private acceptanceFeatureNotificationService: AcceptanceFeatureNotificationService;
  @Inject()
  private circuitBreakerService: CircuitBreakerService;

  async handle(type: CardCallbackType, data: CardCallback): Promise<Card | CallBackToast | undefined> {
    switch (type) {
      case CardCallbackType.CreateMR:
        return this.createMrHandler.handlerCreateMrCallback(data);
      case CardCallbackType.MajorIssueCheck:
        return this.majorIssueCheckHandler.handlerMajorIssueCheckCallback(data);
      case CardCallbackType.ExperimentUpdate:
        return this.experimentUpdateHandler.handlerExperimentUpdateCallback(data);
      case CardCallbackType.ExperimentUpdateStatus:
        return this.experimentUpdateHandler.handlerExperimentUpdateStatusCallback(data);
      case CardCallbackType.ExperimentUpdateRemark:
        return this.experimentUpdateHandler.handlerExperimentUpdateRemarkCallback(data);
      case CardCallbackType.LibraLaunchGrayNotifyNotSendAnymore:
        return this.experimentUpdateHandler.handleLibraLaunchGrayNotifyNotSendAnymoreCallback(data);
      case CardCallbackType.Libra100PercentGrayNotifyNotSendAnymore:
        return this.experimentUpdateHandler.handleLibra100PercentGrayNotifyNotSendAnymoreCallback(data);
      case CardCallbackType.LibraCloseGrayNotifyNotSendAnymore:
        return this.experimentUpdateHandler.handleLibraCloseGrayNotifyNotSendAnymoreCallback(data);
      case CardCallbackType.LibraCloseGray:
        return this.experimentUpdateHandler.handleLibraCloseGray(data);
      case CardCallbackType.LibraLaunchReleaseNotifyNotSendAnymore:
        return this.experimentUpdateHandler.handleLibraLaunchReleaseNotifyNotSendAnymoreCallback(data);
      case CardCallbackType.CreateCherryPickMR:
        return this.createCherryPickMrsHandler.handlerCreateCherryPickMrsCallback(data);
      case CardCallbackType.JoinIntegrationGroup:
        return this.busCardCallback.handleJoinIntegrationGroup(data);
      case CardCallbackType.GetOnBus:
        return this.busCardCallback.handleGetOnBus(data);
      case CardCallbackType.MarkGetOnBus:
        return this.busCardCallback.handleMarkGetOnBus(data);
      case CardCallbackType.GetOffBus:
        return this.busCardCallback.handleGetOffBus(data);
      case CardCallbackType.NotifyBusList:
        return this.busCardCallback.handleNotifyBusList(data);
      case CardCallbackType.PushBus:
        return this.busCardCallback.handlePushBus(data);
      case CardCallbackType.DelayCR:
        return this.busCardCallback.handleDelayCR(data);
      case CardCallbackType.UpdateCheckListInfo:
        return this.updateChecklistInfoHandler.handlerUpdateCheckListInfoCallback(data);
      case CardCallbackType.CancelFastMerge:
        return this.fastMergeService.cancelFastMerge(data);
      case CardCallbackType.RenewFastMerge:
        return this.fastMergeService.renewalFastMergeTimeLimit(data);
      case CardCallbackType.BatchSubMrConflictResolved:
        return this.batchMrService.subMrConflictResolvedCardHandler(data);
      case CardCallbackType.WithdrawBanlance:
        return this.packageBalanceService.approveBalanceCallBack(data);
      case CardCallbackType.VersionAutoStageChangeStatus:
        return this.versionAutoStageService.changeCheckItemStatus(data);
      case CardCallbackType.VersionRejectLarkGroup:
        return this.versionReleaseService.createVersionRejectGroup(data);
      case CardCallbackType.MetricsAlarm:
        return this.metricsAlarmService.handle(data);
      case CardCallbackType.FinishMeegoNode:
        return this.featureMonitorService.finishNode(data);
      case CardCallbackType.GrayDelayEvaluate:
        return this.versionReleaseService.grayPackageDelayEvaluate(data);
      case CardCallbackType.BusinessSingleBugExemptApproved:
        return this.bugResolveAgent.businessSingleBugExemptApproved(data);
      case CardCallbackType.BusinessSingleBugExemptReject:
        return this.bugResolveAgent.businessSingleBugExemptReject(data);
      case CardCallbackType.BugCheckItemExemptApprove:
        return this.functionalBugAgent.bugExemptApprove(data);
      case CardCallbackType.BugResolveRateExemptApprove:
        return this.bugResolveAgent.businessBugResolveRateExemptApprove(data);
      case CardCallbackType.QATestStart:
        return this.versionReleaseService.startQAUserStoryTest(data);
      case CardCallbackType.QualityCommonDispatchCN:
      case CardCallbackType.QualityCommonDispatchSG:
        useInject(RpcProxyManager)
          .getQuality(type === CardCallbackType.QualityCommonDispatchSG)
          .dispatchQualityCardAction(data);
        break;
      case CardCallbackType.IosFullReleaseAutoApprove:
        return this.versionReleaseService.fullReleaseCheckApprove(data);
      case CardCallbackType.ApprovalService:
        return this.approvalCardDispatchService.dispatch(data);
      case CardCallbackType.SilentReleaseNoti:
        return this.versionReleaseService.silentReleaseNotify(data);
      case CardCallbackType.AcceptDailyCardNotSendAnymore:
        return this.acceptanceFeatureNotificationService.handleAcceptDailyCardNotSendAnymoreCallback(data);
      case CardCallbackType.SubmitBlockUrgentComplete:
        return this.versionReleaseService.versionSubmitBlockUrgentComplete(data);
      case CardCallbackType.CircuitBreakerHandle:
        this.circuitBreakerService.circuitBreakerCardStatusChange(data);
      case CardCallbackType.ApprovalAddMoreBusinessLine:
        return this.approvalCardDispatchService.addMoreBusinessLine(data);
      default:
        return undefined;
    }
  }
}
