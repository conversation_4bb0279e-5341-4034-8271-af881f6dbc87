import { Inject, Injectable } from '@gulux/gulux';
import { TccClients } from '@gulux/gulux/tcc';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { PaAlarmService } from '@pa/backend/dist/src/utils/alarm';
import { LibraControlSetting } from '@pa/shared/dist/src/libra/LibraAttributionModel';

interface StarlingKey {
  STARLING_AK: string;
  STARLING_SK: string;
}

interface NewDownloadConfig {
  enable: boolean;
  enableSplitDownload: boolean; // 开启分片下载
}

/**
 * TCC配置服务
 - 线上：https://cloud.bytedance.net/tcc/namespace/lv.paperairplane.settings?by_key=false&condition=name&configId=&dir_path=%2Fdefault&env=prod&filter_no_tag=false&keyword=&order=&pn=1&region=CN&rn=20&scope=all&x-resource-account=public
 - BOE：https://cloud-boe.bytedance.net/tcc/namespace/lv.paperairplane.settings?by_key=false&condition=name&configId=&dir_path=%2Fdefault&env=prod&filter_no_tag=false&keyword=&order=&pn=1&region=China-BOE&rn=20&scope=all&tab=&x-resource-account=boe
 */
@Injectable()
export default class TccService {
  @Inject()
  private tccClients: TccClients;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private alarm: PaAlarmService;

  getBitsKey() {
    return '95ec6a2104f2a8153bc907f9578dcc13';
  }

  getCozeKey() {
    return 'B9-hVyzCUATXiuOO7n1ABbH62XQdLACroUZYx6V5fbg';
  }

  async getCozePrivateKey() {
    const key = await this.tccClients.keys.get('coze_private_key');
    return key;
  }

  getStarlingKey(): StarlingKey {
    return {
      STARLING_AK: '7bf5ff08fafcee04a93d1bc094c56dd3',
      STARLING_SK: 'dc869f278fd27b433310f0feac6ab2de',
    };
  }

  getGitlabKey() {
    return '********************';
  }

  getSettingKey() {
    return '****************************************************************';
  }

  async getTccStr(key: string): Promise<string | undefined> {
    return this.tccClients.keys.get(key);
  }

  async getTccModel<T = string>(key: string): Promise<T | undefined> {
    const value = await this.tccClients.keys.get(key);
    try {
      return JSON.parse(value) as T;
    } catch (e) {
      this.alarm.reportError(`TCC配置解析失败 key:${key} value:${value}`);
      return undefined;
    }
  }

  async isNewDownloadWay() {
    try {
      const config = await this.getTccModel<NewDownloadConfig>('multi_language_download_config');
      return config?.enable ?? true;
    } catch (e) {
      return true;
    }
  }

  async isDownloadSplit() {
    try {
      const config = await this.getTccModel<NewDownloadConfig>('multi_language_download_config');
      return config?.enableSplitDownload ?? true;
    } catch (e) {
      return true;
    }
  }

  async enableBusCodeReview() {
    try {
      const config = await this.getTccModel<{ enableBusCodeReview: boolean }>('mr_code_review_config');
      return config?.enableBusCodeReview ?? true;
    } catch (e) {
      if (e instanceof Error) {
        this.logger.error(e);
      }
      return true;
    }
  }

  getLarkKey() {
    return '1DteJjYy8YHhJRAewHaeU0nx32eBqsTq';
  }

  getMeegokKey() {
    return 'FCEA49A9A18772168AAF72890E5094F6';
  }

  getCodebaseKey() {
    return '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
  }
  // 这个是ci_ttandroid的token
  getCiTTAndroidCodebaseKey() {
    return '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
  }

  getMultiLanguageCodebaseKey() {
    return '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
  }

  getZhongkuiToken() {
    return '15230e5dad94ffaefa7b526c0c1e89e5';
  }

  /**
   * 慎用个人token，可能过期
   */
  getMyJwtToken() {
    return '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
  }

  async getLibraControlSetting(type: string) {
    const settings = JSON.parse(await this.tccClients.keys.get(`libra_control_setting`)) as LibraControlSetting[];
    if (settings.length > 0) {
      for (const se of settings) {
        if (se.type === 'all' && se.enable) {
          return se.enable;
        }
        if (se.type.includes(type)) {
          return se.enable;
        }
      }
    }
    return false;
  }
}
