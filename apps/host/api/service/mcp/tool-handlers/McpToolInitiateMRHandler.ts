import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { BusinessAppInfo } from '@pa/shared/src/appSettings/appSettings';
import { PlatformType } from '@pa/shared/dist/src/core';
import { MrType } from '@shared/bits/mrInfo';
import repos from '@shared/gitlab/repos';
import { McpToolBaseHandler, McpToolDefinition, McpToolRsp } from '@shared/mcp/mcp-info';
import BitsConfigV2Service, { MrRepos } from 'api/service/bitsConfigV2';
import BitsService from 'api/service/third/bits';
import GitLabService from 'api/service/third/gitlab';
import MeegoService from 'api/service/third/meego';
import { extractMeegoId } from 'api/service/utils/MeegoUtils';

interface InitiateMRArgs {
  platform: 'Android' | 'iOS' | 'Multi';
  sourceBranch: string;
  targetBranch: string;
  userName: string;
  appName: string;
  meegoUrl: string;
  email: string;
}

@Injectable()
export default class McpToolInitiateMRHandler implements McpToolBaseHandler {
  @Inject()
  private logger: BytedLogger;

  @Inject()
  private bitsConfigV2: BitsConfigV2Service;

  @Inject()
  private gitlabService: GitLabService;

  @Inject()
  private bussinessConfigService: BusinessConfigService;

  @Inject()
  private bits: BitsService;

  @Inject()
  private meegoService: MeegoService;

  toolDefinition(): McpToolDefinition[] {
    return [
      {
        name: 'initiate_mr',
        description: '在bits中发起MR',
        inputSchema: {
          type: 'object',
          properties: {
            sourceBranch: {
              type: 'string',
              description: '源分支名称',
            },
            targetBranch: {
              type: 'string',
              description: '目标分支名称',
            },
            appName: {
              type: 'string',
              description: '应用名称',
            },
            meegoUrl: {
              type: 'string',
              description: 'meego链接',
            },
          },
          required: ['sourceBranch', 'targetBranch', 'appName', 'meegoUrl'],
        },
        annotations: {
          title: '发起MR',
          readOnlyHint: false,
          destructiveHint: false,
          idempotentHint: false,
          openWorldHint: true,
        },
      },
    ];
  }

  async toolHandler(toolName: string, args: InitiateMRArgs): Promise<McpToolRsp> {
    const { appName } = args;
    const platform = 'iOS';
    this.logger.info(`发起MR, appName: ${appName}, platform: ${platform}`);
    if (toolName !== 'initiate_mr') {
      return {
        content: [
          {
            type: 'text',
            text: `未找到对应的 tool handler: ${toolName}`,
          },
        ],
      };
    }

    const appInfoList = await this.bussinessConfigService.getAppList();
    const appInfo = appInfoList.find(it => {
      let match = true;
      if (appName) {
        match = match && it.app_name === appName;
      }
      if (platform) {
        match = match && it.platform === platform;
      }
      return match;
    });
    if (!appInfo) {
      return {
        content: [
          {
            type: 'text',
            text: '未查询到对应的app信息',
          },
        ],
      } as McpToolRsp;
    }

    try {
      let allRepo = [this.getMainRepo(appInfo)];
      // 如果中间层有sourceBranch，则加入中间层仓库
      const middleRes = await this.gitlabService.getBranchInfo(repos.middleLayerRepo.projectId, args.sourceBranch);
      if (middleRes && middleRes.data) {
        allRepo = [
          repos.middleLayerRepo,
          repos.androidMainRepo,
          repos.iosMainRepo,
          repos.androidTTPMainRepo,
          repos.iosTTPMainRepo,
          repos.pcMainRepo,
          repos.pcTTPMainRepo,
        ];
      }

      const mrRepos: MrRepos[] = [];

      for (const repo of allRepo) {
        mrRepos.push({
          projectId: repo.projectId,
          sourcesBranch: args.sourceBranch,
          targetBranch: args.targetBranch,
          isHost:
            repo === repos.androidMainRepo ||
            repo === repos.iosMainRepo ||
            repo === repos.pcMainRepo ||
            repo === repos.iosTTPMainRepo ||
            repo === repos.pcTTPMainRepo ||
            repo === repos.androidTTPMainRepo,
          platform: repo.platform,
        });
      }
      const { mrType, mrTitle } = await this.getMRTypeFromMeegoUrl(args.meegoUrl);
      const meegoId = extractMeegoId(args.meegoUrl);
      if (!meegoId) {
        this.logger.error(`解析meego ID错误, 请检查复制的meego链接: ${args.meegoUrl}`);
        return {
          content: [
            {
              type: 'text',
              text: `解析meego ID错误, 请检查复制的meego链接: ${args.meegoUrl}`,
            },
          ],
        };
      }
      if (mrTitle === '') {
        this.logger.error(`解析meego标题错误, 请检查复制的meego链接: ${args.meegoUrl}`);
        return {
          content: [
            {
              type: 'text',
              text: `解析meego标题错误, 请检查复制的meego链接: ${args.meegoUrl}`,
            },
          ],
        };
      }
      const email = args.email;
      let author = '';
      if (email && email !== '') {
        author = email.split('@')[0];
      } else {
        author = await this.getRecentCommitAuthor(mrRepos[0], args.sourceBranch);
      }
      const mrConfig = await this.bitsConfigV2.buildMrConfig({
        title: mrTitle,
        type: mrType,
        customFields: {
          range_of_influence: '纸飞机后台发起MR，和对应操作人进行确认',
          how_to_test_issue: '纸飞机后台发起MR，和对应操作人进行确认',
          the_test_results: 1,
        },
        targetVersion: '',
        author: author,
        repos: mrRepos,
        wip: false,
      });

      this.logger.info(`发起MR，MR配置: ${JSON.stringify(mrConfig)}`);

      const createResult = await this.bits.createMr(mrConfig, author);

      if (createResult && createResult.data && createResult.data.success) {
        // bind meego
        await this.bits.bindMeegoToMR(
          createResult.data.mr_id,
          meegoId,
          mrType === MrType.bug ? 'bug' : 'issue',
          'faceu',
          author,
        );
        let mrLink = '';
        if (appInfo.platform === PlatformType.Android) {
          mrLink = `https://bits.bytedance.net/devops/1499128834/code/detail/${createResult.data.mr_id}`;
        } else {
          mrLink = `https://bits.bytedance.net/devops/1486844930/code/detail/${createResult.data.mr_id}`;
        }
        return {
          content: [
            {
              type: 'text',
              text: `MR已成功发起，mr链接: ${mrLink}`,
            },
          ],
        };
      }

      return {
        content: [
          {
            type: 'text',
            text: `MR发起失败，请联系zhengbolun<EMAIL>`,
          },
        ],
      };
    } catch (error: any) {
      this.logger.error(`发起MR失败: ${error}`);
      return {
        content: [
          {
            type: 'text',
            text: `发起MR失败: ${error.message || error}`,
          },
        ],
      };
    }
  }

  private async getRecentCommitAuthor(repo: MrRepos, branch: string) {
    const IGNORE_AUTHOR = ['BitsAdmin', 'ci_paper_airplane', 'root'];
    const res = await this.bits.searchBranch(repo.projectId, branch);
    if (res && res.length > 0) {
      for (const item of res) {
        if (item.username && !IGNORE_AUTHOR.includes(item.username)) {
          return item.username;
        }
      }
    }
    return '';
  }

  private async getMRTypeFromMeegoUrl(meegoUrl: string) {
    const meegoId = extractMeegoId(meegoUrl);
    if (!meegoId) {
      this.logger.error(`解析meego Id错误, 请检查复制的meego链接: ${meegoUrl}`);
      return {
        mrType: MrType.bug,
        mrTitle: '',
      };
    }
    const workItem = await this.meegoService.queryMeegoWorkItemInfo('faceu', 'story', [Number(meegoId)]);
    if (workItem && workItem.data && Array.isArray(workItem.data) && workItem.data.length > 0) {
      return {
        mrType: MrType.feature,
        mrTitle: workItem.data[0].name,
      };
    } else {
      const bugItem = await this.meegoService.queryMeegoWorkItemInfo('faceu', 'issue', [Number(meegoId)]);
      if (bugItem && bugItem.data && Array.isArray(bugItem.data) && bugItem.data.length > 0) {
        return {
          mrType: MrType.bug,
          mrTitle: bugItem.data[0].name,
        };
      }
    }
    return {
      mrType: MrType.bug,
      mrTitle: '',
    };
  }

  private getMainRepo(appInfo: BusinessAppInfo) {
    if (appInfo.platform === PlatformType.Android) {
      if (appInfo.app_id === 177502) {
        return repos.androidMainRepo;
      }
      return repos.androidTTPMainRepo;
    }
    if (appInfo.platform === PlatformType.iOS) {
      if (appInfo.app_id === 177501) {
        return repos.iosMainRepo;
      }
    }
    return repos.iosTTPMainRepo;
  }

  private getPlatformType(platform: 'Android' | 'iOS' | 'Multi'): PlatformType {
    switch (platform.toLowerCase()) {
      case 'android':
        return PlatformType.Android;
      case 'ios':
        return PlatformType.iOS;
      case 'multi':
        return PlatformType.Multi;
      default:
        throw new Error(`不支持的平台类型: ${platform}`);
    }
  }
}
