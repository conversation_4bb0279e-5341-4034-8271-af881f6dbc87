import React, { useState } from 'react';
import { Card, Row, Col, Typography, Input, Button, Toast } from '@douyinfe/semi-ui';
import { IconDelete, IconCopy } from '@douyinfe/semi-icons';
import { testLibraDesignDocCheck, testParseLibraDesignDoc } from '@api/debug';
import { updateLibraFlightType, deleteLibraFlightType } from '@api/libra';

const { Title } = Typography;

interface FlightTypeTestProps {
  flightId: string;
  setFlightId: (value: string) => void;
  responseData: any;
  setResponseData: (value: any) => void;
}

const ResponseDataSection: React.FC<{
  responseData: any;
  setResponseData: (data: any) => void;
  title?: string;
}> = ({ responseData, setResponseData, title = '响应数据' }) => {
  if (!responseData) {
    return null;
  }

  return (
    <Card title={title} style={{ marginTop: 24 }} bodyStyle={{ padding: 20 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
        <Typography.Text strong>数据详情：</Typography.Text>
        <div style={{ display: 'flex', gap: 8 }}>
          <Button
            size="small"
            onClick={() => {
              navigator.clipboard.writeText(JSON.stringify(responseData, null, 4));
              Toast.success('已复制到剪贴板');
            }}
            type="tertiary"
            icon={<IconCopy />}
            aria-label="复制结果"
          />
          <Button
            size="small"
            onClick={() => {
              setResponseData(null);
              Toast.success('已清空结果');
            }}
            type="tertiary"
            icon={<IconDelete />}
            aria-label="清空结果"
          />
        </div>
      </div>
      <pre
        style={{
          backgroundColor: '#f5f5f5',
          padding: 12,
          borderRadius: 4,
          fontSize: 12,
          lineHeight: 1.5,
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all',
          maxHeight: 400,
          overflow: 'auto',
          margin: 0,
        }}
      >
        {JSON.stringify(responseData, null, 4)}
      </pre>
    </Card>
  );
};

const FlightTypeTestSection: React.FC<FlightTypeTestProps> = ({
  flightId,
  setFlightId,
  responseData,
  setResponseData,
}) => {
  const [loading, setLoading] = useState(false);

  const handleUpdateFlightType = async () => {
    if (!flightId.trim()) {
      Toast.error('请输入实验ID');
      return;
    }

    const flightIdNumber = parseInt(flightId, 10);
    if (isNaN(flightIdNumber)) {
      Toast.error('实验ID必须是数字');
      return;
    }

    setLoading(true);
    try {
      const result = await updateLibraFlightType({
        data: {
          flightId: flightIdNumber,
          forceUpdate: true,
        },
      });
      setResponseData(result);

      if (result.code === 0) {
        Toast.success('实验类型更新成功');
      } else {
        Toast.error(`实验类型更新失败: ${result.msg || '未知错误'}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      setResponseData({ error: errorMessage });
      Toast.error(`实验类型更新失败: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFlightType = async () => {
    if (!flightId.trim()) {
      Toast.error('请输入实验ID');
      return;
    }

    const flightIdNumber = parseInt(flightId, 10);
    if (isNaN(flightIdNumber)) {
      Toast.error('实验ID必须是数字');
      return;
    }

    setLoading(true);
    try {
      const result = await deleteLibraFlightType({
        data: {
          flightId: flightIdNumber,
        },
      });
      setResponseData(result);

      if (result.code === 0) {
        Toast.success('实验类型删除成功');
      } else {
        const errorMsg = result.msg || '删除失败';
        Toast.error(`实验类型删除失败: ${errorMsg}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      setResponseData({ error: errorMessage });
      Toast.error(`实验类型删除失败: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
      <div style={{ marginBottom: 8 }}>
        <Typography.Text strong>更新或删除实验类型</Typography.Text>
      </div>
      <Input
        placeholder="请输入实验ID"
        value={flightId}
        onChange={setFlightId}
        style={{ width: '100%' }}
        showClear={true}
      />
      <div style={{ display: 'flex', gap: 8 }}>
        <Button type="primary" onClick={handleUpdateFlightType} loading={loading} style={{ flex: 1 }}>
          更新实验类型
        </Button>
        <Button type="danger" onClick={handleDeleteFlightType} loading={loading} style={{ flex: 1 }}>
          删除实验类型
        </Button>
      </div>
    </div>
  );
};

const LibraDebugPage: React.FC = () => {
  const [docUrl, setDocUrl] = useState('https://bytedance.larkoffice.com/docx/AmWDdLRz8opBQTx4IoZcfl1nnvh');
  const [flightId, setFlightId] = useState('');
  const [loading, setLoading] = useState(false);
  const [responseData, setResponseData] = useState<any>(null);

  const handleClear = () => {
    setDocUrl('');
    setFlightId('');
    setResponseData(null);
  };

  const handleDesignDocParse = async () => {
    if (!docUrl.trim()) {
      Toast.error('请输入文档URL');
      return;
    }

    let flightIdNumber: number | undefined;
    if (flightId.trim()) {
      flightIdNumber = parseInt(flightId, 10);
      if (isNaN(flightIdNumber)) {
        Toast.error('实验ID必须是数字');
        return;
      }
    }

    setLoading(true);
    try {
      const params: { docUrl: string; flightId?: number } = { docUrl };
      if (flightIdNumber !== undefined) {
        params.flightId = flightIdNumber;
      }
      const result = await testParseLibraDesignDoc({
        data: params,
      });

      setResponseData(result);
      if (result.code === 0) {
        Toast.success('文档解析完成');
      } else {
        const errorMsg = result.msg ?? '未知错误';
        Toast.error(`文档解析失败: ${errorMsg}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '网络错误';
      setResponseData({ error: errorMessage });
      Toast.error(`文档解析失败: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: 24, backgroundColor: '#f5f7fa', minHeight: '100vh' }}>
      <Title heading={3} style={{ marginBottom: 24 }}>
        Libra 调试中心
      </Title>

      <Row gutter={24}>
        {/* 左侧卡片区域 */}
        <Col span={12}>
          <Card title="实验设计文档解析" style={{ marginBottom: 24 }} bodyStyle={{ padding: 20 }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 12, marginBottom: 16 }}>
              <Input
                placeholder="请输入实验设计文档URL"
                value={docUrl}
                onChange={setDocUrl}
                style={{ width: '100%' }}
                showClear={true}
                onClear={handleClear}
              />
              <Input
                placeholder="请输入实验ID"
                value={flightId}
                onChange={setFlightId}
                style={{ width: '100%' }}
                showClear={true}
              />
            </div>
            <div style={{ display: 'flex', gap: 12 }}>
              <Button type="primary" onClick={handleDesignDocParse} loading={loading}>
                解析文档
              </Button>
            </div>
          </Card>

          <Card title="接口测试" style={{ marginBottom: 24 }} bodyStyle={{ padding: 20 }}>
            <FlightTypeTestSection
              flightId={flightId}
              setFlightId={setFlightId}
              responseData={responseData}
              setResponseData={setResponseData}
            />
          </Card>

          <Card title="调试工具" bodyStyle={{ padding: 20 }}>
            {/* 请在此处填充调试工具内容 */}
          </Card>
        </Col>

        {/* 右侧卡片区域 */}
        <Col span={12}>
          <Card title="实验数据" style={{ marginBottom: 24 }} bodyStyle={{ padding: 20 }}>
            {/* 请在此处填充实验数据内容 */}
          </Card>

          <Card title="操作日志" bodyStyle={{ padding: 20 }}>
            {/* 请在此处填充操作日志内容 */}
          </Card>
        </Col>
      </Row>

      {/* 独立的响应数据展示区域 */}
      <ResponseDataSection responseData={responseData} setResponseData={setResponseData} />
    </div>
  );
};

export default LibraDebugPage;
