import { Inject, Injectable } from '@gulux/gulux';
import LarkCardService from '../larkCard';
import {
  Card,
  CardElement,
  CardElementTag,
  CardTemplate,
  CardTextTag,
  CardButtonType,
} from '@pa/shared/dist/src/lark/larkCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { ReleasePlatformUtilService } from './releasePlatformUtil';
import MeegoService from '../third/meego';
import { VersionTransaction } from '@shared/releasePlatform/versionTransaction';
import { formatTimestamp2Month, VersionProcessInfo } from '@shared/releasePlatform/versionStage';
import { AppId2Name } from '@shared/experiment/experimentInfo';
import { ApprovalBusinessConfigs } from '@shared/approval/ApprovalOrder';
import { MAIN_HOST_HTTPS, PlatformType, User } from '@pa/shared/dist/src/core';
import { batchGetUserInfoByOpenIds } from '@api/index';
import { getStageInfo } from '@shared/releasePlatform/releasePlatformUtils';

@Injectable()
export default class VersionTransactionCard {
  @Inject()
  private larkCard: LarkCardService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private releasePlatformUtil: ReleasePlatformUtilService;
  @Inject()
  private meego: MeegoService;

  buildStageDelayNotiCard(versionInfo: VersionProcessInfo, title: string, description: string) {
    // const title = `灰度延期风险信息补充`;
    // const description = `[${AppId2Name[versionInfo.app_id]}-${versionInfo.version}-${versionInfo.version}] ${delayStage.display_name} 阶段原计划${formatTimestamp2Month(delayStage.start_time * 1000)}开始，目前还未开始，已Delay，请及时更新相关信息`;
    const baseCard = this.larkCard.buildBaseCard({
      title,
      template: CardTemplate.red,
      config: { enable_forward: false },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: description,
      },
    } as CardElement);
    elements.push({
      tag: CardElementTag.action,
      actions: [
        {
          tag: CardElementTag.button,
          text: {
            tag: CardTextTag.plain_text,
            content: '立即处理',
          },
          type: CardButtonType.primary_filled,
          url: `${MAIN_HOST_HTTPS}/release/list?show_detail=true&appid=${versionInfo.app_id}&version=${versionInfo.version}&open_info_sync=true`,
        },
      ],
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async addDelayCardParams(baseCard: Card, transaction: VersionTransaction) {
    const elements: CardElement[] = [];
    const userInfo = transaction.extra?.responsible_people_userids;
    if (userInfo?.length) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `责任人：${userInfo.map(it => `<at email=${it.email}></at>`)}`,
        },
      } as CardElement);
    }
    const team_names = transaction.extra?.responsible_teams?.map(item => ApprovalBusinessConfigs[item].name);

    if (team_names?.length) {
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `责任团队：${team_names?.join(',') ?? ''}`,
        },
      } as CardElement);
    }
    const date = formatTimestamp2Month((transaction.extra?.expected_resolution_time ?? 0) * 1000);
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `预期解决时间：${date}`,
      },
    } as CardElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `归因：${transaction.extra?.attributions?.join(',')}`,
      },
    } as CardElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `原因：${transaction.extra?.reason}`,
      },
    } as CardElement);

    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `解决方案：${transaction.extra?.solution}`,
      },
    } as CardElement);
    baseCard.elements = elements;
    return baseCard;
  }

  async buildGreyDelayCard(transaction: VersionTransaction, versionInfo: VersionProcessInfo) {
    const { bindings } = transaction;
    const appName = AppId2Name[bindings[0].app_id];
    const stages = versionInfo.version_stages;
    const greyStagesNames = [
      'gray',
      'testFlight',
      'dreamina_adr_gray',
      'dreamina_ios_testFlight',
      'retouch_ios_testFlight',
      'retouch_adr_gray',
      'first_gray_win',
      'second_gray_win',
      'default_adr_gray',
      'default_ios_testFlight',
    ];
    const greyStages = stages.filter(item => greyStagesNames.includes(item.stage_name));
    const msgStage = greyStages.filter(stage =>
      stage.sub_stages.find(item => item.stage_name === transaction.stage_name),
    );
    if (msgStage === undefined || !msgStage) {
      this.logger.error(`找不到当前阶段用于发送消息 ${transaction}`);
      return null;
    }

    const displayName = msgStage.map(stage => stage.display_name).join(' & ');
    const baseCard = this.larkCard.buildBaseCard({
      title: `${appName} ${bindings[0].version} ${displayName} 延期异常同步`,
      template: CardTemplate.red,
    });

    return await this.addDelayCardParams(baseCard, transaction);
  }

  async buildSubmitDelayCard(transaction: VersionTransaction, versionInfo: VersionProcessInfo) {
    const { bindings } = transaction;
    const appName = AppId2Name[bindings[0].app_id];
    const baseCard = this.larkCard.buildBaseCard({
      title: `${appName} ${bindings[0].version} 提审延期异常同步`,
      template: CardTemplate.red,
    });

    return await this.addDelayCardParams(baseCard, transaction);
  }

  async buildFullDelayCard(transaction: VersionTransaction, versionInfo: VersionProcessInfo) {
    const { bindings } = transaction;
    const appName = AppId2Name[bindings[0].app_id];
    const baseCard = this.larkCard.buildBaseCard({
      title: `${appName} ${bindings[0].version} 版本全量延期异常同步`,
      template: CardTemplate.red,
    });

    return await this.addDelayCardParams(baseCard, transaction);
  }

  async buildNormalVersionTransactionCard(title: string, content: string, atUsers: User[]) {
    const baseCard = this.larkCard.buildBaseCard({
      title,
      template: CardTemplate.red,
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content,
      },
    } as CardElement);
    if (atUsers.length) {
      const atUserString = atUsers.map(user => `<at email=${user.email}></at>`).join('');
      elements.push({
        tag: CardElementTag.div,
        text: {
          tag: CardTextTag.lark_md,
          content: `周知：${atUserString}`,
        },
      } as CardElement);
    }
    baseCard.elements = elements;
    return baseCard;
  }
}
