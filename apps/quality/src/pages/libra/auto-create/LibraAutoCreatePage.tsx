import React, { useEffect, useState } from 'react';
import { <PERSON>ton, Spin, Typography, Notification } from '@douyinfe/semi-ui';
import { createLibraReleaseByGray } from '@api/libra';
import { useLocation } from 'react-router-dom';
import { Token } from '@shared/libra/NonOpen';
import GetLibraTokens from '@/pages/libra/list/component/GetLibraTokens';
import UserSettingModule from '@/model/userSettingModel';
import { useModel } from '@edenx/runtime/model';
import { libraRegionFromAppId } from '@shared/libra/NonOpenCommon';
import { CreateLibraResult, LibraRegion } from '@shared/libra/commonLibra';
import { Modal } from 'antd';
import { libraDetailUrl } from '@shared/libra/libraManageUtils';

const { Text } = Typography;

const LibraAutoCreatePage: React.FC = () => {
  const searchParams = new URLSearchParams(window.location.search);
  const libraAppId = searchParams.get('libraAppId'); // 兼容两种大小写
  const flightId = searchParams.get('flightId');
  const [loading, setLoading] = useState(false);
  const [tokens, setTokens] = useState<Token[]>([]);
  const [tokenLoading, setTokenLoading] = useState(true);
  const [userSettingState] = useModel(UserSettingModule);

  const handleCreate = async () => {
    if (!libraAppId || !flightId) {
      Modal.error({
        title: '参数错误',
        content: '缺少必要参数: flightId 或 appId',
        centered: true,
      });
      return;
    }

    setLoading(true);
    try {
      console.log('Tokens:', tokens);
      const response = await createLibraReleaseByGray({
        data: {
          // 添加data层
          tokens,
          appId: Number(libraAppId),
          flightId: Number(flightId),
        },
      });

      if (response.code === 0) {
        const info = (response.data !== undefined ? (response.data as CreateLibraResult) : undefined)?.experiments?.[0];
        if (info !== undefined) {
          const url = libraDetailUrl(libraRegionFromAppId(info.app_id), info.id);
          Modal.success({
            title: '操作成功',
            content: (
              <a href={url} target="_blank" rel="noopener noreferrer">
                点击查看实验详情
              </a>
            ),
            centered: true,
          });
        }
      } else {
        if (response.message.includes('<!doctype html>')) {
          const parser = new DOMParser();
          const doc = parser.parseFromString(response.message, 'text/html');
          response.message = doc.querySelector('p')?.textContent || response.message;
        }
        Modal.error({
          title: '创建失败',
          content: response.message,
          centered: true,
        });
      }
    } catch (error) {
      Modal.error({
        title: '创建异常',
        content: '创建过程中发生错误',
        centered: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('Tokens changed:', tokens);
    console.log('Flight ID:', flightId);
    console.log('App ID:', libraAppId);
    if (tokens.length > 0 && flightId && libraAppId) {
      handleCreate();
    }
  }, [tokens]);

  return (
    <>
      <GetLibraTokens
        changeTokens={(newTokens: Token[]) => {
          setTokens([...newTokens]);
          setTokenLoading(false);
        }}
        tokenType={libraAppId === '147' ? 'cn' : 'sg'}
        email={userSettingState.info.email}
      />
      {tokenLoading ? (
        <div style={{ height: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Spin size="large" tip="正在获取Token..." />
        </div>
      ) : (
        <div style={{ padding: 24, textAlign: 'center' }}>
          <Spin spinning={loading} tip="实验自动创建中..." size="large">
            {!loading && (
              <Button type="primary" onClick={handleCreate}>
                重新创建
              </Button>
            )}
          </Spin>
        </div>
      )}
    </>
  );
};

export default LibraAutoCreatePage;
