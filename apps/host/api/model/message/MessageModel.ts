import { Database, getModelForClass, modelOptions, Prop, Severity } from '@gulux/gulux/typegoose';
import { VscodeMessageInfo } from '@shared/message/VscodeMessageInfo';

@Database('main')
@modelOptions({ options: { customName: 'MessageInfo', allowMixed: Severity.ALLOW } })
export class MessageTable implements VscodeMessageInfo {
  @Prop({ required: true })
  messages: string[];

  @Prop()
  email?: string[];

  @Prop({ required: true, index: true })
  expireAt: number;

  @Prop({ required: true, default: { sendCount: 0, sendEmail: [], sendMac: [] } })
  sendInfo: {
    sendCount: number;
    sendEmail: string[]; // 用来防止重复发送个人信息
    sendMac: string[]; // 用来防止重复发送群发信息
  };

  @Prop({ required: true, index: true })
  createdAt: number;

  @Prop({ required: true })
  updatedAt: number;

  @Prop({ required: true, default: false })
  isBroadcast: boolean; //是否为群发
}

export const MessageModel = getModelForClass(MessageTable);
