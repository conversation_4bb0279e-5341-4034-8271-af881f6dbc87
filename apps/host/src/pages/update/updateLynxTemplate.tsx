import BranchSelector from '@/component/BranchSelector';
import UserSelector from '@/component/UserSelector';
import { NetworkCode, NetworkResult, User, PlatformType } from '@pa/shared/dist/src/core';
import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Modal, Radio, RadioChangeEvent, Space, Table } from 'antd';
import axios from 'axios';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { ProColumns } from '@ant-design/pro-table';
import { blockConfig } from '@shared/bits/lynxInfo';
import { string } from 'zod';
import { uniq } from 'lodash';
import { platform } from '@byted/inspirecloud-api';

const { TextArea } = Input;
// 获取当前页面的 URL
const urlParams = new URLSearchParams(window.location.search);

const lynxTemplatesQuery = urlParams.get('lynxTemplates');

const queryItems = {
  lynxTemplates: lynxTemplatesQuery ? (JSON.parse(window.atob(lynxTemplatesQuery)) as string[]) : [''],
};

const EditableTable: React.FC<EditableTableProps> = ({ initialData, onSave }) => {
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState(initialData);
  const [editingKey, setEditingKey] = useState('');
  const [count, setCount] = useState(initialData.length + 1); // 用于新行的 key 值

  useEffect(() => {
    setDataSource(initialData);
  }, [initialData]);

  const handleAdd = () => {
    const newData = {
      key: count.toString(),
      channel: '',
      latestVersion: '',
    };
    setDataSource([...dataSource, newData]);
    setCount(count + 1);
    setEditingKey(newData.key); // 自动进入编辑模式
    form.setFieldsValue({
      channel: '',
      latestVersion: '',
    });
  };

  const isEditing = (record: tempRecord): boolean => record.key === editingKey;

  const handleEdit = (key: string) => {
    setEditingKey(key);
    const record = dataSource.find(item => item.key === key);
    if (record) {
      form.setFieldsValue({ key: string });
    }
  };

  const handleCancel = () => {
    setEditingKey('');
  };

  const handleSave = async () => {
    try {
      const row = await form.validateFields();
      const newData = [...dataSource];
      const index = newData.findIndex(item => editingKey === item.key);
      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, { ...item, ...row[editingKey] });
        setDataSource(newData);
        setEditingKey('');
      } else {
        newData.push(row);
        setDataSource(newData);
        setEditingKey('');
      }
      console.log('Updated Data:', newData);
      onSave(newData);
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  const handleDelete = (key: string) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    setEditingKey('');
    onSave(newData); // 确保每次操作之后保存数据
  };

  const columns = [
    {
      title: 'channel',
      dataIndex: 'channel',
      editable: true,
    },
    {
      title: '最低生效版本',
      dataIndex: 'latestVersion',
      editable: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      // @ts-ignore
      render: (_, record: tempRecord) => {
        if (!record) {
          return null;
        }
        const editable = isEditing(record);
        return editable ? (
          <span>
            <Space>
              <Button onClick={handleSave} type="link">
                Save
              </Button>
              <Button onClick={handleCancel} type="link">
                Cancel
              </Button>
            </Space>
          </span>
        ) : (
          <span>
            <Button disabled={editingKey !== ''} onClick={() => handleEdit(record.key)} type="link">
              编辑
            </Button>
            <Button disabled={editingKey !== ''} onClick={() => handleDelete(record.key)} type="link">
              删除
            </Button>
          </span>
        );
      },
    },
  ];

  const mergedColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: tempRecord) => ({
        record,
        inputType: col.dataIndex === 'age' ? 'number' : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  const EditableCell: React.FC<EditableCellProps> = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    const inputNode = inputType === 'number' ? <Input type="number" /> : <Input />;
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item
            name={[record.key, dataIndex]}
            style={{ margin: 0 }}
            rules={[
              {
                required: true,
                message: `Please Input ${title}!`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  return (
    <Form form={form} component={false}>
      <Space style={{ marginBottom: 16, marginTop: 10 }}>
        <Button onClick={handleAdd} type="primary" disabled={editingKey !== ''}>
          新增 channel
        </Button>
      </Space>
      <Table
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        bordered
        dataSource={dataSource}
        columns={mergedColumns}
        rowClassName="editable-row"
        pagination={false}
      />
    </Form>
  );
};

const UpdateLynxTemplate: React.FC = () => {
  const [buttonState, setButtonState] = useState<boolean>(false);
  const [inputStateList, setInputStateList] = useState<string[]>(queryItems.lynxTemplates);
  const [branch, setBranch] = useState<string>('develop');
  const [qa, setQa] = useState<User>();
  const [testRange, setTestRange] = useState<string>();
  // const [templateUrl, setTemplateUrl] = useState<string>("https://lf9-cdn-tos.bytegecko.com/obj/ies.fe.gecko/334d97647687bde560308beb2c8a45bc")
  const [isOversea, setIsOversea] = useState<boolean>(false);
  const [isAndroid, setSelectAndroid] = useState<PlatformType>(PlatformType.Android);
  const [isPackageIDModel, setIsPackageIDModel] = useState<boolean>(true); // 仅 iOS 使用，更新模式是否为指定 packageid 模式
  const [isDreamina, setIsDreamina] = useState<boolean>(false); // 是否即梦
  const [apps, setApps] = useState<string[]>(['剪映', 'CapCut_TTP', '即梦', 'Pippit', 'TinyCut']);
  const [selectedApp, setSelectedApp] = useState<string>('lv'); // 当前选择的 App (lv cc jm)
  const [showBlacklistModal, setShowBlacklistModal] = useState<boolean>(false);
  const [blacklist, setBlacklist] = useState<blockConfig[]>([]);
  const [previewBlackList, setPreviewBlackList] = useState<tempRecord[]>([]);

  const loadBlackListInfo = async () => {
    const formData = new FormData();
    formData.append(
      'data',
      JSON.stringify({
        product: selectedApp,
      }),
    );
    const ret = await fetch('/api/query_black_list', {
      method: 'POST',
      body: formData,
    });
    const blackListRet = (await ret.json()) as NetworkResult<blockConfig[]>;
    console.log(`blackList:`, blackListRet.data);
    setBlacklist(blackListRet.data!);
    return blackListRet.data!;
  };

  useEffect(() => {
    loadBlackListInfo();
  }, [selectedApp]);

  const onAppSelected = (e: RadioChangeEvent) => {
    console.log(`radio checked:${e.target.value}`);
    const selected = e.target.value;
    switch (selected) {
      case '剪映':
        setIsOversea(false);
        setIsDreamina(false);
        setSelectedApp('lv');
        break;
      case 'CapCut':
        setIsOversea(true);
        setIsDreamina(false);
        setSelectedApp('cc');
        break;
      case 'CapCut_TTP':
        setIsOversea(true);
        setIsDreamina(false);
        setSelectedApp('cc_ttp');
        break;
      case '即梦':
        setIsDreamina(true);
        setIsOversea(false);
        setSelectedApp('jm');
        setIsPackageIDModel(false);
        break;
      case '醒图':
        setIsDreamina(false);
        setIsOversea(false);
        setIsPackageIDModel(false);
        setSelectedApp('xt');
        break;
      case 'Hypic':
        setIsDreamina(false);
        setIsOversea(true);
        setIsPackageIDModel(false);
        setSelectedApp('hp');
        break;
      case 'CCUS':
        setIsDreamina(false);
        setIsOversea(true);
        setIsPackageIDModel(false);
        setSelectedApp('ccus');
        break;
      case 'Pippit':
        setIsDreamina(false);
        setIsOversea(false);
        setIsPackageIDModel(false);
        setSelectedApp('Pippit');
        break;
      case 'TinyCut':
        setIsDreamina(false);
        setIsOversea(false);
        setIsPackageIDModel(false);
        setSelectedApp('TinyCut');
        break;
      default:
        break;
    }
  };

  const onPlatformSelected = (e: RadioChangeEvent) => {
    console.log(`radio checked:${e.target.value}`);
    const selected = e.target.value;
    if (selected === 'Android') {
      setSelectAndroid(PlatformType.Android);
      setApps(
        uniq(
          apps
            .filter(item => item !== '醒图' && item !== 'Hypic' && item !== 'CCUS' && item !== 'CapCut')
            .concat('Pippit'),
        ),
      );
      setIsPackageIDModel(true);
    } else {
      setSelectAndroid(PlatformType.iOS);
      if (selectedApp !== 'lv' && selectedApp !== 'cc' && selectedApp !== 'cc_ttp') {
        setIsPackageIDModel(false);
      }
      // iOS 支持即梦更新
      setApps(prevApps => [...prevApps, '醒图', 'Hypic', 'CCUS', 'CapCut']);
    }
  };

  const onUpdateModelSelected = (e: RadioChangeEvent) => {
    console.log(`radio checked:${e.target.value}`);
    const selected = e.target.value;
    if (selected === '指定 PackageID') {
      setIsPackageIDModel(true);
    } else {
      setIsPackageIDModel(false);
    }
  };

  const addTemplateInput = () => {
    const newState = [...inputStateList];
    newState.push('');
    setInputStateList(newState);
  };

  const removerTemplateInput = (index: number) => {
    const newState = [...inputStateList];
    newState.splice(index, 1);
    setInputStateList(newState);
  };

  const addTemplateUrl = (value: string, index: number) => {
    inputStateList[index] = value;
    setInputStateList([...inputStateList]);
  };

  const addPackageId = (value: string, index: number) => {
    inputStateList[index] = value;
    setInputStateList([...inputStateList]);
  };

  const dowloadUrl = async function (allInputList: string[]) {
    setButtonState(true);
    const formData = new FormData();
    for (const url of allInputList) {
      console.log(`下载url${url}开始`);
      const response = await axios.get(url, {
        responseType: 'blob',
      });
      console.log(`下载url${url}结束`);
      const p = url.split('/');
      const fileName = p[p.length - 1];
      const file = new File([response.data], `${fileName}.zip`, { type: 'application/zip' });
      formData.append('file', file);
    }
    formData.append(
      'data',
      JSON.stringify({
        isOversea,
        qa,
        testRange,
        branch,
        platform: isAndroid,
        selectedApp,
      }),
    );
    return formData;
  };

  const dowloadPackages = function (allInputList: string[]) {
    setButtonState(true);
    const formData = new FormData();
    formData.append(
      'data',
      JSON.stringify({
        isOversea,
        qa,
        testRange,
        branch,
        platform: isAndroid,
        selectedApp,
      }),
    );
    return formData;
  };

  const submitUpdate = async () => {
    const allInputList = inputStateList.filter(value => value !== '');
    // 限制 CCUS 合入分支
    if (selectedApp === 'ccus' && (branch === 'develop' || branch.startsWith('release/'))) {
      message.error('CCUS 更新不允许合入剪映主干分支，请参考注意事项信息', 5);
      return;
    }

    // 限制 CapCut 合入 CCUS 分支
    if (selectedApp !== 'ccus' && branch.startsWith('ccus/')) {
      message.error('非 CCUS 更新不允许合入 CCUS 分支，请参考注意事项信息', 5);
      return;
    }

    if (selectedApp !== 'Pippit' && branch.startsWith('commercepro/')) {
      message.error('非 Pippit 更新不允许合入 commerpro 分支(分支名规范为commercepro/开头)，请参考注意事项信息', 5);
      return;
    }
    const isPippit = selectedApp === 'Pippit';
    const isTinyCut = selectedApp === 'TinyCut';
    if (
      branch !== '' &&
      (!isPackageIDModel || (allInputList.length > 0 && qa) || isDreamina || isPippit || isTinyCut)
    ) {
      let formData, result;
      if (isAndroid === PlatformType.Android && !isDreamina && !isPippit) {
        formData = await dowloadUrl(allInputList);
        result = await fetch('/api/update_lynx_template', {
          method: 'POST',
          body: formData,
        });
      } else if (selectedApp === 'xt' || selectedApp === 'hp') {
        // 醒图多仓 mr 创建逻辑
        formData = await dowloadUrl(allInputList);
        result = await fetch('/api/create_rtsdk_mr', {
          method: 'POST',
          body: formData,
        });
      } else {
        formData = dowloadPackages(allInputList);
        result = await fetch('/api/update_lynx_packageids', {
          method: 'POST',
          body: formData,
        });
      }
      const ret = await result.json();
      console.log(`post result:`, ret);
      if (result.status === 200 && ret.code === NetworkCode.Success) {
        message.success(`提交成功 ${ret.data}`, 15);
        if (isAndroid !== PlatformType.Android || isDreamina || isPippit) {
          const lynxFormData = new FormData();
          lynxFormData.append(
            'data',
            JSON.stringify({
              sourceBranch: ret.sourceBranch,
              packageIds: allInputList,
              creator: ret.creator,
              isOversea,
              branch,
              isPackageIDModel,
              isDreamina,
              product: selectedApp,
              blackList: blacklist,
              isAndroid,
            }),
          );
          const lynxResult = await fetch('/api/trigger_job_lynx_template', {
            method: 'POST',
            body: lynxFormData,
          });
          const lynxRet = (await lynxResult.json()) as NetworkResult<string>;
          console.log(`post result:`, lynxRet);
        }
      } else {
        message.error(`MR提交失败, ${JSON.stringify(ret)}`, 15);
      }
      setButtonState(false);
    } else if (branch === '') {
      message.error('请选择分支', 5);
    } else if (allInputList.length === 0) {
      message.error('请输入模板下载地址', 5);
    } else if (!qa) {
      message.error('请选择QA', 5);
    }
  };

  const handleOpenUpdateBlackListModal = async () => {
    const data = await loadBlackListInfo();
    let count = 0;
    const newRecords = data.map(item => {
      count++;

      return {
        key: count.toString(),
        channel: item.channel,
        latestVersion: item.latestVersion,
      };
    });

    setPreviewBlackList(newRecords);
    console.log(`previewBlackList:`, previewBlackList);
    setShowBlacklistModal(true);
  };

  const handleUpdateModalSave = (newBlackList: tempRecord[]): void => {
    const copiedBlackList = JSON.parse(JSON.stringify(newBlackList));
    // @ts-ignore
    copiedBlackList.forEach(item => {
      delete item.key;
    });
    setBlacklist(copiedBlackList);
  };

  const handleOK = async () => {
    const formData = new FormData();
    formData.append(
      'data',
      JSON.stringify({
        product: selectedApp,
        blackList: blacklist,
      }),
    );
    setShowBlacklistModal(false);
    const ret = await fetch('/api/update_black_list', {
      method: 'POST',
      body: formData,
    });
  };

  const getProjectID = () => {
    if (isAndroid === PlatformType.Android) {
      return selectedApp === 'cc_ttp' ? 798270 : 40279;
    } else if (selectedApp === 'xt' || selectedApp === 'hp') {
      return 64697;
    } else if (selectedApp === 'cc' || selectedApp === 'cc_ttp') {
      return 798331;
    }
    return 39995;
  };

  let templatelist: JSX.Element | null = (
    <Form.Item label="packageids" required>
      {inputStateList.map((_, index) => (
        <Space key={index}>
          <TextArea
            placeholder="请输入packageid"
            size="large"
            autoSize={{ minRows: 1, maxRows: 5 }}
            value={inputStateList[index]}
            onChange={e => addPackageId(e.target.value, index)}
            allowClear
            style={{ width: 400 }}
          />

          <Button
            type="primary"
            size="small"
            shape="circle"
            icon={inputStateList.length - 1 === index ? <PlusOutlined /> : <MinusOutlined />}
            onClick={() => {
              console.log(inputStateList.length, index);
              if (inputStateList.length - 1 === index) {
                addTemplateInput();
              } else {
                removerTemplateInput(index);
              }
            }}
          />
        </Space>
      ))}
    </Form.Item>
  );

  if (isAndroid === PlatformType.Android) {
    templatelist = (
      <Form.Item label="模版文件列表" required>
        {inputStateList.map((_, index) => (
          <Space key={index}>
            <TextArea
              placeholder="请输入模板资源地址"
              size="large"
              autoSize={{ minRows: 1, maxRows: 5 }}
              value={inputStateList[index]}
              onChange={e => addTemplateUrl(e.target.value, index)}
              allowClear
              style={{ width: 400 }}
            />

            <Button
              type="primary"
              size="small"
              shape="circle"
              icon={inputStateList.length - 1 === index ? <PlusOutlined /> : <MinusOutlined />}
              onClick={() => {
                console.log(inputStateList.length, index);
                if (inputStateList.length - 1 === index) {
                  addTemplateInput();
                } else {
                  removerTemplateInput(index);
                }
              }}
            />
          </Space>
        ))}
      </Form.Item>
    );
  }

  let qaItem: JSX.Element | null = (
    <div>
      <Form.Item label="QA" required>
        <UserSelector onSelect={setQa} placeholder="请选择QA" />
      </Form.Item>
      <Form.Item label="测试范围">
        <TextArea
          onChange={e => setTestRange(e.target.value)}
          autoSize={{ minRows: 2, maxRows: 5 }}
          placeholder="请输入测试范围"
          style={{ marginBottom: 5 }}
        />
      </Form.Item>
      <Form.Item label="安卓更新注意事项:">
        <span>
          新增的内置模板提交MR后请确认模板的Schema是否需要拼接“with_asset=1”参数（仅Android），该参数会影响资源加载顺序：
          <br />
          不拼接：本地没有模板时，先尝试拉取Gecko，拉取成功使用Gecko模板，拉取失败使用内置模板
          <br />
          拼接（推荐）：本地没有模板时，直接使用内置模板，并异步拉取Gecko
          <br />
        </span>
      </Form.Item>
    </div>
  );
  if (!isPackageIDModel || (selectedApp !== 'cc' && selectedApp !== 'lv' && selectedApp !== 'cc_ttp')) {
    // 全局更新模式不支持 QA 选择，展示更新黑名单入口
    qaItem = null;
    if (isAndroid === PlatformType.Android) {
      templatelist = <></>;
    } else {
      templatelist = (
        <div>
          <Form.Item label="全局更新注意事项:">
            1. 操作指南：
            <a href="https://bytedance.larkoffice.com/docx/OSSudkIUyo8WpmxxVGtcsYplnNd">
              https://bytedance.larkoffice.com/docx/OSSudkIUyo8WpmxxVGtcsYplnNd
            </a>
            <br />
            2. CCUS 资源与 CapCut 隔离，发起 CCUS 资源更新时请确认更新的资源归属与分支， 不允许将 CCUS 资源更新到
            develop 及 release 分支
          </Form.Item>
          <Form.Item label="更新黑名单配置">
            <Button
              loading={buttonState}
              type="primary"
              onClick={() => handleOpenUpdateBlackListModal()}
              style={{ marginLeft: 10 }}
            >
              更新Lynx 黑名单(仅 iOS 全局更新生效)
            </Button>
          </Form.Item>
        </div>
      );
    }
  }

  let updateModelItem = null;
  // 仅 iOS 剪映 cc 支持双更新模式
  const iOSPackageUpdateProduct = ['lv', 'cc', 'cc_ttp'];
  if (isAndroid === PlatformType.iOS && iOSPackageUpdateProduct.includes(selectedApp)) {
    // 即梦 iOS 不支持按 packageID 更新
    updateModelItem = (
      <Form.Item label="更新模式" required>
        <Radio.Group defaultValue="指定 PackageID" buttonStyle="solid" onChange={onUpdateModelSelected}>
          <Radio.Button value="指定 PackageID">指定 PackageID</Radio.Button>
          <Radio.Button value="全局更新">全局更新</Radio.Button>
        </Radio.Group>
      </Form.Item>
    );
  }

  const columns: ProColumns<blockConfig>[] = [
    {
      title: 'channel',
      width: '10%',
      dataIndex: 'channel',
      valueType: 'text',
    },
    {
      title: '最低生效版本',
      width: '10%',
      dataIndex: 'latestVersion',
    },
  ];

  return (
    <Form labelCol={{ span: 8 }} wrapperCol={{ span: 20 }} style={{ maxWidth: 1000 }}>
      <Form.Item label="App" required>
        <Radio.Group defaultValue="剪映" buttonStyle="solid" onChange={onAppSelected}>
          {apps.map(app => (
            <Radio.Button key={app} value={app}>
              {app}
            </Radio.Button>
          ))}
        </Radio.Group>
      </Form.Item>
      <Form.Item label="平台" required>
        <Radio.Group defaultValue="Android" buttonStyle="solid" onChange={onPlatformSelected}>
          <Radio.Button value="Android">Android</Radio.Button>
          <Radio.Button value="iOS">iOS</Radio.Button>
        </Radio.Group>
      </Form.Item>
      {updateModelItem}
      <Form.Item label="更新分支" required>
        <BranchSelector
          selectedValue={branch}
          projectId={getProjectID()}
          placeholder="请选择需要更新的目标分支"
          onSelect={setBranch}
        />
      </Form.Item>
      {templatelist}
      {qaItem}
      <Form.Item wrapperCol={{ offset: 20 }} style={{ marginTop: 10 }}>
        <Button loading={buttonState} type="primary" onClick={submitUpdate}>
          更新Lynx 内置模板
        </Button>
      </Form.Item>
      <Modal
        title="更新 Lynx 黑名单配置"
        open={showBlacklistModal}
        width={'80%'}
        onCancel={() => setShowBlacklistModal(false)}
        onOk={() => handleOK()}
      >
        <EditableTable initialData={previewBlackList} onSave={handleUpdateModalSave} />
      </Modal>
    </Form>
  );
};

export default UpdateLynxTemplate;

interface EditableTableProps {
  initialData: tempRecord[]; // Specify a more specific type instead of any if possible
  onSave: (data: tempRecord[]) => void;
}

interface tempRecord {
  key: string;
  channel: string;
  latestVersion: string;
}

interface EditableCellProps {
  editing: boolean;
  dataIndex: string;
  title: string;
  inputType: 'text' | 'number' | 'checkbox'; // 这里可以根据你的需求调整为更具体的类型
  record: tempRecord; // 这里应替换为 `record` 对象的实际类型
  index: number;
  children: React.ReactNode;
  // 使用 TypeScript utility type `Record<K, T>` 可以为剩余的 props 提供类型
  [key: string]: any; // 这里可以更具体地描述除已列举属性外的可能的其他属性
}
