import { Inject, Injectable } from '@gulux/gulux';
import BaseCheckItemAgent, { TCCStageCheckItem } from './baseCheckItemAgent';
import {
  CheckItemStatus,
  ItemType,
  VersionStageCheckItem,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { VersionProcessInfo, VersionStageInfo } from '@shared/releasePlatform/versionStage';
import VersionStageCheckListDao from '../../dao/releasePlatform/VersionStageCheckListDao';
import VersionProcessInfoDao from '../../dao/releasePlatform/VersionProcessInfoDao';
import { PcCrashItemInfo } from '@shared/releasePlatform/versionStageCheckItemInfo';
import { User } from '@pa/shared/dist/src/core'; // Import User type if needed for owner
import { PcCrashRecordService } from '../PcCrashRecordService'; // Import PcCrashRecordService
import { PcCrashRecordModelTable } from '../../../model/releasePlatform/PcCrashRecordModel'; // Import PcCrashRecordModelTable for type casting

@Injectable()
export default class PcCrashCheckItemAgent extends BaseCheckItemAgent {
  @Inject()
  private versionStageCheckListDao: VersionStageCheckListDao;
  @Inject()
  private versionProcessDao: VersionProcessInfoDao;
  @Inject()
  private pcCrashRecordService: PcCrashRecordService; // Inject PcCrashRecordService

  itemType(): ItemType {
    return ItemType.PcCrashItem;
  }

  async produceCheckItems(
    version: string,
    stage: string,
    appId: number,
    item: TCCStageCheckItem, // This is the TCC configuration item, not a crash item itself
    versionInfo: VersionProcessInfo,
    stageInfo: VersionStageInfo,
    existedChecklist: VersionStageCheckList | null,
  ): Promise<VersionStageCheckItem[]> {
    this.logger.info(`Producing crash check items for appId: ${appId}, version: ${version}, stage: ${stage}`);

    // Fetch all relevant crash data based on version, stage, appId
    const allCrashDataFromDB = await this.fetchCrashData(appId, version, stage);

    const newCheckItems: VersionStageCheckItem[] = [];

    // Assuming fetchCrashData returns an array of crash incidents
    // Each incident should have a unique identifier, e.g., issue_id from PcCrashRecordModelTable
    for (const crashIncident of allCrashDataFromDB) {
      const operator = await this.lark.getUserInfoByEmail(`${crashIncident.owner}@bytedance.com`);

      // Create new item info
      const currentCrashItemInfo: PcCrashItemInfo = {
        crash_id: crashIncident.issue_id,
        oncallLink: crashIncident.chat_invite_link || '',
        priority: crashIncident.priority || '',
        assignee: operator ?? ({} as User),
        status: crashIncident.state || '',
        name: crashIncident.crash_module || String(crashIncident.issue_id) || 'Unknown Crash',
        rawCrashData: crashIncident,
        created_at: crashIncident.start_time || new Date(),
        stack_info: crashIncident.stack_info || '',
      };

      const ownerUser = undefined;

      const checkItem: VersionStageCheckItem = {
        check_item_id: `${appId}_${version}_${stage}_crash_${crashIncident.issue_id}`,
        status: CheckItemStatus.TBD, // Placeholder: Implement actual status mapping from crashIncident.status
        description: `Crash: ${currentCrashItemInfo.name}`.substring(0, 100),
        owner: ownerUser, // Assign owner if available
        item_type: this.itemType(),
        item_info: currentCrashItemInfo,
        department: '', // Placeholder, ensure it's a string
        extraData: {}, // Ensure it matches the defined type in VersionStageCheckItem
      };
      newCheckItems.push(checkItem);
    }

    this.logger.info(`Produced ${newCheckItems.length} crash check items.`);
    return newCheckItems;
  }

  // Fetching data from pcCrashRecord
  private async fetchCrashData(appId: number, version: string, stage: string): Promise<PcCrashRecordModelTable[]> {
    this.logger.info(`Fetching crash data for appId: ${appId}, version: ${version}, stage: ${stage}`);
    const filter = {
      version: version,
      appid: appId,
      state: { $ne: 'done' },
      priority: { $ne: 'P2' },
    };
    try {
      const crashRecords = await this.pcCrashRecordService.getAllPcCrashRecords(filter);
      this.logger.info(`Fetched ${crashRecords.length} crash records.`);
      return crashRecords;
    } catch (error) {
      this.logger.error('Error fetching crash data from PcCrashRecordService', { error, appId, version, stage });
      return []; // Return empty array on error to prevent downstream issues
    }
  }
}
