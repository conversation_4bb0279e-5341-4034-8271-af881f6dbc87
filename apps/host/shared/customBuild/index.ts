import { BuildResultArtifact } from '@shared/customBuild/buildResult';
import { User, PlatformType } from '@pa/shared/dist/src/core';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';

export interface CustomBuildRepoComponent {
  componentId: number;
  componentName: string;
  repoGroupName: string;
}

export interface LatestBuildInfo {
  type: CustomBuildType;
  version: string;
  versionCode: string;
  packageTime: string;
  hasFixVersion?: boolean;
  record?: CustomBuildRecord;
}

export interface IntegrationProductHistory {
  lvRecord?: CustomBuildRecord;
  ccRecord?: CustomBuildRecord;
}

export interface GrayProductHistory {
  grayCount: number;
  versionCode: string;
  downloadLink: string;
}

export interface LatestPackageResult {
  version: string;
  count: number;
  list: LatestBuildInfo[];
  hasFixVersion?: boolean;
}

export interface ProductManageResp {
  total: number;
  list: LatestPackageResult[];
}

export interface CustomBuildRecord {
  lvBranch: string;
  buildId: string;
  buildType: CustomBuildType | CustomBuildType[];
  commit?: string;
  jobId?: number;
  jobInfo?: any;
  username: string;
  userInfo?: User;
  buildResult: CustomBuildResultType;
  artifacts?: BuildResultArtifact[];
  buildParams: CustomBuildParam;
  extra?: any;
  createdAt?: string;
  testJob?: TestJob;
  appId?: AppSettingId | null;
  tfLink?: string;
  detailVersionCode?: string;
  tfFiveVersion?: number;
  pcDownloadLink?: string;
  pcVersionCode?: string;
  tfTaskFlowId?: number;
  isManualBuild?: boolean;
  platform?: PlatformType;
  mrLink?: string;
  airPlaneTaskName?: string;
  version?: string;
  needSendGroup?: boolean;
}

export interface TFVersionCode {
  appId: number;
  version: string;
  tfFiveVersion: number;
  buildResult: CustomBuildResultType;
}

export interface PCAndiOSTFBuildRecord {
  appId: AppSettingId | null;
  tfLink?: string;
  tfVersionCode?: string;
  pcDownloadLink?: string;
  pcVersionCode?: string;
  version: string;
  buildResult: CustomBuildResultType;
  username: string;
  branch: string;
  buildType: CustomBuildType | CustomBuildType[];
  tfTaskFlowId?: number;
}

export interface BuildProduct {
  type: BuildProductType;
  url: string;
}

export enum BuildProductType {
  apk = 'apk',
  mapping = 'mapping',
  resMapping = 'resMapping',
  text = 'text',
}

export enum CustomBuildResultType {
  triggerError = 'triggerError',
  building = 'building',
  success = 'success',
  failed = 'failed',
  cancel = 'cancel',
}

export interface CustomBuildParam {
  lvBranch: string;
  repos: CustomBuildRepo[];
  type: CustomBuildType | CustomBuildType[]; // []
  arch: PlatformType;
  buildTarget?: string;
  lvVersionName?: string;
  lvVersionCode?: string;
  veVersion?: string;
  effectVersion?: string;
  lynxVersion?: string;
  cloudVersion?: string;
  is32?: boolean;
  isDebug?: boolean;
  isOutTest?: boolean;
  isOversea?: boolean;
  isLynxDebug?: boolean;
  isOutBuild?: boolean;
  isHyperTest?: boolean;
  isRheaPro?: boolean;
  isRhea3?: boolean;
  isByteInsight?: boolean;
  isCoverage?: boolean;
  isAnyWhereDorOpen?: boolean;
  isDataAutomation?: boolean;
  isOpenNetWorkTrustUser?: boolean;
  isDexVmpOpen?: boolean;
  devVmpVersion?: string;
  isPublishCache?: boolean;
  buildTemplate?: string;
  isPublishGradleCache?: boolean;

  isForceAllSource?: boolean;
  isDebugFix?: boolean;

  isCheckoutMiddleLayerByVe?: boolean; // 当Repo中存在中间层时，该参数失效
  isCloseLibDevelop?: boolean;
  isCloseApkOpt?: boolean;
  extra?: any;
  commitId?: string;
  isUserStory?: boolean;
  isTFInUserStory?: boolean;
  useAndroidTTPWrapper?: boolean;
  mavenType?: string;
}

export enum CustomBuildType {
  VE = 'VE', // VE侧构建
  MR_MERGED = 'MR_MERGED', // MR合入针对主仓构建
  RD = 'RD', // 纸飞机前端构建
  RELEASE = 'RELEASE', // 拉取Release后构建
  OUT = 'OUT', // 三方平台触发构建
  QA_INTEGRATION_TESTING = 'QA_INTEGRATION_TESTING', // QA 的集成测试出包
  QA_INTEGRATION_TESTING_CD = 'QA_INTEGRATION_TESTING_CD', // QA 的集成测试CD出包
  E_TEST = 'E_TEST',
  SMOKE_TEST = 'SMOKE_TEST',
  PUBLISH_CACHE = 'PUBLISH_CACHE',
  PUBLISH_MODULE = 'PUBLISH_MODULE',
  UG_TEST = 'UG_TEST',
  PRECISE_TEST = 'PRECISE_TEST', // 覆盖率出包
  OUT_BUILD = 'OUT_BUILD', // 外发包构建
  NOTICE = 'PRECISE_TEST', // 覆盖率出包
  USER_STORY = 'USER_STORY', // 用户故事出包
  USER_STORY_TF = 'USER_STORY_TF', // 用户故事TF出包
  GRAY = 'GRAY', // 灰度，不带调试页的TF包
  TF_TEST = 'TF_TEST', // 带调试页TF包
  FORMAL = 'FORMAL', // 正式包
  USER_STORY_SCHEDULE = 'USER_STORY_SCHEDULE', // 定时构建
  INTEGRATION_SCHEDULE = 'INTEGRATION_SCHEDULE', // 定时构建
  PC_INTEGRATION_TEST = 'PC_INTEGRATION_TEST', // PC集成测试包
  PC_SYSTEM_TEST = 'PC_SYSTEM_TEST', // pc系统测试包
  DEBUG_NOT_LYRA = 'DEBUG', // 不带中间层的debug包
}

export interface CustomBuildRepo {
  projectId: number;
  branch: string;
  openComponent: number[];
}

export interface CustomBuildBitsRepoInfo {
  projectId: number;
  branch: string;
  repoUrl: string;
  openComponent: string[];
}

export interface TestJob {
  id: number;
  uaJobId: number;
}
