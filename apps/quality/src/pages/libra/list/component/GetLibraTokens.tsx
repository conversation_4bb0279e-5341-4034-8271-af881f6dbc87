import { useState, useEffect } from 'react';
import { Modal, Notification, Typography } from '@douyinfe/semi-ui';
import { getSGToken, getCNToken } from '@shared/libra/LibraCreate';
import { Button } from 'antd';

interface TokenFetcherProps {
  changeTokens: (tokens: any[]) => void;
  tokenType: 'cn' | 'sg' | 'cnAndSg';
  email: string;
}

const GetLibraTokens = ({ changeTokens, tokenType, email }: TokenFetcherProps) => {
  const [tokenLoading, setTokenLoading] = useState(false);
  const [tokenError, setTokenError] = useState({
    cn: false,
    sg: false,
  });
  const [retryCount, setRetryCount] = useState(0);

  const fetchToken = (region: 'cn' | 'sg', callback: (token: any) => void) => {
    const fetcher = region === 'cn' ? getCNToken : getSGToken;
    fetcher(email, token => {
      if (token?.token) {
        callback(token);
        setTokenError(prev => ({ ...prev, [region]: false }));
      } else {
        setTokenError(prev => ({ ...prev, [region]: true }));
      }
      console.log('token', token);
      setTokenLoading(false);
    });
  };
  const retryFetch = () => {
    setRetryCount(prev => prev + 1);
    setTokenLoading(true);
    setTokenError({ cn: false, sg: false });
  };

  const getToken = () => {
    setTokenLoading(true);

    if (tokenType === 'cn') {
      fetchToken('cn', token => {
        changeTokens([token]);
      });
    } else if (tokenType === 'sg') {
      fetchToken('sg', token => {
        changeTokens([token]);
      });
    } else if (tokenType === 'cnAndSg') {
      const tokens: any[] = [];
      fetchToken('cn', cnToken => {
        tokens.push(cnToken);
      });
      fetchToken('sg', sgToken => {
        tokens.push(sgToken);
      });
      changeTokens(tokens);
    }
  };
  useEffect(() => {
    getToken();
  }, [tokenType, email, retryCount]);
  useEffect(() => {
    if (tokenType === 'cn') {
      setTokenError({ cn: true, sg: false });
    } else if (tokenType === 'sg') {
      setTokenError({ cn: false, sg: true });
    } else if (tokenType === 'cnAndSg') {
      setTokenError({ cn: true, sg: true });
    }
  }, []);
  const hasError = tokenError.cn || tokenError.sg;

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (hasError) {
      intervalId = setInterval(() => {
        getToken();
      }, 2000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [hasError, getToken]);
  const showCnError = tokenError.cn && (tokenType === 'cn' || tokenType === 'cnAndSg');
  const showSgError = tokenError.sg && (tokenType === 'sg' || tokenType === 'cnAndSg');

  return (
    <Modal
      visible={tokenLoading || hasError}
      title={hasError ? '获取Token失败' : '正在获取Token...'}
      closable={false}
      footer={null}
      centered
      fullScreen
    >
      <div style={{ textAlign: 'center', padding: '20px' }}>
        {hasError ? (
          <>
            {showCnError && (
              <>
                <div
                  style={{
                    fontSize: '16px',
                    backgroundColor: '#FFF3BF', // 更深的告警黄色
                    padding: '12px',
                    borderRadius: '8px',
                    margin: '8px 0',
                    border: '1px solid #FFD666', // 加深的黄色边框
                    boxShadow: '0 1px 2px 0 rgba(0,0,0,0.1)', // 轻微阴影增强立体感
                  }}
                >
                  <p>Libra国内token过期，请在Libra重新登陆。</p>
                  <Typography.Text
                    link={{ href: 'https://data.bytedance.net/libra/new-flight/coding', target: '_blank' }}
                  >
                    点我快速登录
                  </Typography.Text>
                </div>
              </>
            )}
            {showSgError && (
              <>
                <div
                  style={{
                    fontSize: '16px',
                    backgroundColor: '#FFF3BF', // 更深的告警黄色
                    padding: '12px',
                    borderRadius: '8px',
                    margin: '8px 0',
                    border: '1px solid #FFD666', // 加深的黄色边框
                    boxShadow: '0 1px 2px 0 rgba(0,0,0,0.1)', // 轻微阴影增强立体感
                  }}
                >
                  <p>Libra海外token过期，请在Libra重新登陆。</p>
                  <Typography.Text
                    link={{ href: 'https://libra-sg.tiktok-row.net/libra/new-flight/coding', target: '_blank' }}
                  >
                    点我快速登录
                  </Typography.Text>
                </div>
              </>
            )}
            <div style={{ marginTop: '16px' }}>
              <Button type="primary" onClick={retryFetch}>
                重试获取Token
              </Button>
            </div>
          </>
        ) : (
          <p>正在获取Token，请稍候...</p>
        )}
      </div>
    </Modal>
  );
};

export default GetLibraTokens;
