import { Api, Data, Get, Params, Post } from '@edenx/runtime/bff';
import { z } from 'zod';
import { VscodeService } from '../service/vscode';
import { MessageService } from '../service/message/MessageService';

interface Info {
  info: string;
  id: number;
}

interface VersionInfo {
  version_name: string;
  version_description: string;
  infos?: Info[];
}

interface GetVersionInfoParams {
  version: VersionInfo[];
  AppName: string;
}

export const getVersionInfo = Api(
  Get('/open/getVersionInfo'),
  Params(
    z.object({
      app_name: z.string().optional(),
      user_email: z.string().optional(),
    }),
  ),
  async ({ params }): Promise<GetVersionInfoParams[]> => {
    const { app_name: appName, user_email: userEmail } = params;
    const vscodeService = new VscodeService();

    try {
      // 获取剪映版本信息
      const LvResult = await vscodeService.getLvVersionInfo();
      // 获取CapCut版本信息
      const capCutResult = await vscodeService.getCapCutVersionInfo();
      return [
        {
          AppName: LvResult.data.appName,
          version: LvResult.data.versions,
        },
        {
          AppName: capCutResult.data.appName,
          version: capCutResult.data.versions,
        },
      ];
    } catch (error) {
      console.error('Failed to fetch version info:', error);
      return [
        {
          AppName: '剪映',
          version: [],
        },
        {
          AppName: 'CapCut',
          version: [],
        },
      ];
    }
  },
);

// 消息相关接口
export const saveMessage = Api(
  Post('/open/saveMessage'),
  Data(
    z.object({
      messages: z.array(z.string()).min(1),
      email: z.array(z.string().email()).optional(),
      expireInHours: z.number().min(1),
    }),
  ),
  async ({
    data,
  }): Promise<{
    code: number;
    data: {
      id: string;
      messages: string[];
      email?: string[];
      expireAt: number;
      createdAt: number;
    };
  }> => {
    const messageService = new MessageService();
    try {
      const message = await messageService.saveMessage(data);
      return {
        code: 0,
        data: {
          id: message._id!,
          messages: message.messages,
          email: message.email,
          expireAt: message.expireAt,
          createdAt: message.createdAt,
        },
      };
    } catch (error) {
      console.error('保存消息失败:', error);
      throw new Error('保存消息失败');
    }
  },
);

export const getMessages = Api(
  Post('/open/getMessages'),
  Data(
    z.object({
      latestGetMessageCreateTime: z.number().optional(),
      email: z.string().optional(),
      mac: z.string().optional(),
    }),
  ),
  async ({ data }) => {
    const messageService = new MessageService();
    try {
      if (!data.mac && !data.email) {
        return {
          code: -1,
          message: 'mac和email不能同时为空',
        };
      }
      const latestGetMessageCreateTime = data.latestGetMessageCreateTime ? data.latestGetMessageCreateTime : 0;

      const result = await messageService.getMessages({
        latestGetMessageCreateTime,
        email: data.email,
        mac: data.mac,
      });

      return {
        code: 0,
        data: {
          messages: result.messages,
          latestCreateTime: result.latestCreateTime,
        },
      };
    } catch (error) {
      console.error('获取消息失败:', error);
      throw new Error('获取消息失败');
    }
  },
);
