import { Inject, Injectable } from '@gulux/gulux';
import {
  Feedback,
  FeedbackCallbackInfo,
  FeedbackPriority,
  FollowUpStatus,
  UpdateRecord,
} from '@shared/releasePlatform/feedbackFollwUp';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import UpdateRecordDao from '../dao/releasePlatform/FeedbackUpdateRecordDao';
import FeedbackDao from '../dao/releasePlatform/FeedbackDao';
import { VersionProcessInfo, VersionStageStatus } from '@shared/releasePlatform/versionStage';
import { BmType } from '@shared/bits/bmInfo';
import { useInject } from '@edenx/runtime/bff';
import { ExperienceFeedbackAnalysisService } from '@gulux-bam/ies_efficiency_experience_feedback_analysis';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import VersionStageCheckListDao, {
  VersionStageCheckListItemQuery,
} from '../dao/releasePlatform/VersionStageCheckListDao';
import { CheckItemStatus } from '@shared/releasePlatform/versionStageInfoCheckList';
import { CemAnalysisService } from '@gulux-bam/ies_efficiency_cem_analysis';
import { experience_external_namespace } from '@gulux-bam/ies_efficiency_experience_feedback_analysis/lib/typings/apps/experience_feedback_analysis/idl/experience_external';
import MeegoService from '../third/meego';
import { WorkItemInfo } from '@shared/meego/WorkItemResult';
import { FieldValue, FieldValuePair } from '@shared/meego/MeegoCommon';
import { FilterQuery } from '@byted/bytedmongoose';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';
import { FeedbackFollowCheckItemAgent } from './checkItemAgents/feedbackFollowCheckItemAgent';
import {
  ManuallyCheckItemInfo,
  ManuallyCheckItemStatus,
  FeedbackMetricsItemInfo,
} from '@shared/releasePlatform/versionStageCheckItemInfo';
import XGetFeedbackInfoWithFiltersReqOptions = experience_external_namespace.XGetFeedbackInfoWithFiltersReqOptions;
import XGetFeedbackInfoByIssueCodesResp = experience_external_namespace.XGetFeedbackInfoByIssueCodesResp;
export const LABEL_LV_APP = [''];
export const LABEL_LV_PC = [
  '245015',
  '245016',
  '243234',
  '249423',
  '241157',
  '248145',
  '248144',
  '252818',
  '250838',
  '250839',
  '216225',
  '216227',
  '216230',
  '216232',
  '252816',
  '216233',
];
export const LABEL_RETOUCH_APP = [''];

interface FeedbackStatusHandle {
  field: string;
  isHandled: boolean;
}

@Injectable()
export class FeedbackService {
  @Inject()
  private feedbackDao: FeedbackDao;

  @Inject()
  private updateRecordDao: UpdateRecordDao;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;

  @Inject()
  private checklistDao: VersionStageCheckListDao;

  @Inject()
  private meegoService: MeegoService;

  convertFeedbackInfoToCallback(info: any): FeedbackCallbackInfo {
    try {
      return {
        feedbackID: info.feedbackID,
        feedbackChannel: info.feedbackChannel,
        ticketID: info.ticketID,
        contentTitle: info.contentTitle,
        eredarCreateTimestampMs: parseInt(info.eredarCreateTimestampMs, 10),
        uid: Number(info.uid),
        did: Number(info.did),
        device_os_general: info.deviceOsGeneral, // 映射设备平台
        update_version_code: info.updateVersionCode,
        labelIds: info.label_ids,
        labelNames: info.label_names,
        aid: info.aid,
      };
    } catch (error) {
      this.logger.error('转换反馈信息失败', { error, info });
      throw error;
    }
  }

  async judgePriority(callbackInfo: FeedbackCallbackInfo, versionInfo: VersionProcessInfo): Promise<string> {
    const topLabels = await this.analyzeTopFeedbackLabels();
    const { topFive, topFiveToTen, topTenToTwenty } = topLabels;
    const label = callbackInfo.labelNames?.at(-2) ?? ''; // 二级标签
    if (!callbackInfo.labelNames?.at(-2)) {
      return FeedbackPriority.P2;
    }
    if (topFive.includes(label)) {
      return FeedbackPriority.P0;
    }
    if (topFiveToTen.includes(label)) {
      return FeedbackPriority.P1;
    }
    if (topTenToTwenty.includes(label)) {
      return FeedbackPriority.P2;
    }
    return FeedbackPriority.P2;
  }

  async generateDefaultFeedback(
    callbackInfo: FeedbackCallbackInfo,
    versionInfo: VersionProcessInfo,
  ): Promise<Omit<Feedback, 'feedbackCallbackInfo'>> {
    const qaBM = versionInfo.bmInfo[BmType.qa];
    const priority = await this.judgePriority(callbackInfo, versionInfo);
    return {
      feedbackID:
        (callbackInfo.feedbackChannel ?? 'voc_no_channel') + (callbackInfo.feedbackID ?? 'voc_no_feedback_id'),
      version: versionInfo.version,
      appId: versionInfo.app_id,
      followUpStatus: FollowUpStatus.INITIAL,
      responsiblePerson: [qaBM],
      handler: [qaBM],
      customLabels: [],
      oncallGroup: '',
      priority,
    };
  }

  async analyzeTopFeedbackLabels() {
    try {
      const feedbacks = await this.getAllFeedbacks();
      const labelCountMap = new Map<string, number>();
      feedbacks.forEach(feedback => {
        const labelNames = feedback.feedbackCallbackInfo?.labelNames;
        if (Array.isArray(labelNames) && labelNames.length >= 2) {
          const targetLabel = labelNames[labelNames.length - 2];
          if (targetLabel) {
            labelCountMap.set(targetLabel, (labelCountMap.get(targetLabel) || 0) + 1);
          }
        }
      });
      const topFive = Array.from(labelCountMap.entries())
        .map(([label]) => label) // 只保留标签名称
        .slice(0, 5); // 直接取前5个
      const topFiveToTen = Array.from(labelCountMap.entries())
        .map(([label]) => label) // 只保留标签名称
        .slice(5, 10);
      const topTenToTwenty = Array.from(labelCountMap.entries())
        .map(([label]) => label) // 只保留标签名称
        .slice(10, 20);

      return {
        topFive,
        topFiveToTen,
        topTenToTwenty,
      };
    } catch (error) {
      console.error('分析反馈标签时出错:', error);
      throw error;
    }
  }

  async processFeedbackCallback(
    callbackInfo: FeedbackCallbackInfo,
    versionInfo: VersionProcessInfo,
  ): Promise<Feedback> {
    try {
      if (callbackInfo.feedbackID) {
        const existing = await this.feedbackDao.findByFeedbackId(
          callbackInfo.feedbackChannel + callbackInfo.feedbackID,
        );
        if (existing) {
          return existing;
        }
      }

      const newFeedback = await this.createFeedback({
        ...(await this.generateDefaultFeedback(callbackInfo, versionInfo)),
        feedbackCallbackInfo: callbackInfo,
      });

      // 初始化状态但不等待完成
      const fullFeedbackId =
        (callbackInfo.feedbackChannel ?? 'voc_no_channel') + (callbackInfo.feedbackID ?? 'voc_no_feedback_id');
      this.initializeFeedbackStatus(fullFeedbackId).catch(e =>
        this.logger.error('初始化反馈状态失败', { error: e, feedbackId: fullFeedbackId }),
      );

      this.logger.info('[processFeedbackCallback] success', {
        feedbackID: newFeedback.feedbackCallbackInfo.feedbackChannel + newFeedback.feedbackID,
      });
      return newFeedback;
    } catch (e) {
      this.logger.error('[processFeedbackCallback] fail', { error: e, callbackInfo });
      throw new Error('反馈处理失败，请检查日志');
    }
  }

  async initializeFeedbackStatus(feedbackId?: string) {
    const cemService = useInject(CemAnalysisService);
    const commonPlatformInfo = {
      busiDomain: {
        project: 'cap_cut',
        firstLevel: [],
        secondLevel: [],
        domain: 'cap_cut',
      },
      timeLoadLocation: '+08:00',
      language: 'zh',
    };

    // 初始化状态为未处理
    const result = await cemService.OpenTagLabelToFeedback({
      commonPlatformInfo,
      feedbackID: feedbackId,
      labelField: 'open_capcut_follow_status_list',
      labelValue: JSON.stringify([FollowUpStatus.INITIAL]),
    });
    if (!result.BaseResp) {
      this.logger.error(`initializeFeedbackStatus 更新反馈标签结果 跟进状态 result: ${result}`);
    } else {
      this.logger.info(
        `initializeFeedbackStatus 更新反馈标签结果 跟进状态 code: ${result.BaseResp.StatusCode}  feedbackID: ${feedbackId}`,
      );
    }
    return result;
  }

  async batchProcessFeedbackCallback(
    callbackInfo: FeedbackCallbackInfo[],
    versionInfo: VersionProcessInfo,
  ): Promise<Feedback[]> {
    const processedFeedback: Feedback[] = [];
    const initializationPromises: Promise<any>[] = [];

    for (const info of callbackInfo) {
      try {
        const fullFeedbackId = (info.feedbackChannel ?? 'voc_no_channel') + (info.feedbackID ?? 'voc_no_feedback_id');
        const existingIndex = processedFeedback.findIndex(f => f.feedbackID === fullFeedbackId);
        if (existingIndex >= 0) {
          continue;
        }
        if (info.feedbackID) {
          const existing = await this.feedbackDao.findByFeedbackId(fullFeedbackId);
          if (existing) {
            await this.feedbackDao.updateCallbackInfo(fullFeedbackId, info);
            // @ts-ignore
            const { _doc } = existing;
            processedFeedback.push({ ..._doc, feedbackCallbackInfo: info });
            continue;
          }
        }

        const newFeedback = await this.createFeedback({
          ...(await this.generateDefaultFeedback(info, versionInfo)),
          feedbackCallbackInfo: info,
        });

        // 将初始化操作加入到Promise数组中，但不等待
        initializationPromises.push(this.initializeFeedbackStatus(info.feedbackID));

        this.logger.info('[processFeedbackCallback] success', {
          feedbackID: newFeedback.feedbackCallbackInfo.feedbackChannel + newFeedback.feedbackID,
        });
        processedFeedback.push(newFeedback);
      } catch (e) {
        this.logger.error('[processFeedbackCallback] fail', { error: e, callbackInfo });
        // throw new Error('反馈处理失败，请检查日志');
      }
    }

    // 等待所有初始化操作完成
    if (initializationPromises.length > 0) {
      try {
        this.logger.info(
          `[batchProcessFeedbackCallback] Waiting for ${initializationPromises.length} initialization promises to complete`,
        );
        await Promise.all(initializationPromises);
        this.logger.info('[batchProcessFeedbackCallback] All initialization promises completed successfully');
      } catch (error) {
        this.logger.error('[batchProcessFeedbackCallback] Error while waiting for initialization promises', { error });
        // 继续返回已处理的反馈，即使部分初始化失败
      }
    }

    return processedFeedback;
  }

  async getAllFeedbacks(filters: FilterQuery<Feedback> = {}): Promise<Feedback[]> {
    const appId = (filters as Feedback)?.appId;
    // 仅需反馈平台跟进的反馈，作为全部基准。如需获取真实全部反馈，可直接调用getRealAllFeedbacks
    let labels = [];
    switch (appId) {
      case AppSettingId.LV_ANDROID:
      case AppSettingId.LV_IOS:
        labels = LABEL_LV_APP;
      // break
      case AppSettingId.LV_MAC:
      case AppSettingId.LV_WIN:
        labels = LABEL_LV_PC;
        break;
      case AppSettingId.RETOUCH_ANDROID:
      case AppSettingId.RETOUCH_IOS:
        labels = LABEL_RETOUCH_APP;
      // break
      default:
        return this.getRealAllFeedbacks({ ...filters });
    }
    return this.getRealAllFeedbacks({ ...filters, 'feedbackCallbackInfo.labelIds.0': { $in: labels } });
  }

  async getRealAllFeedbacks(filters: FilterQuery<Feedback> = {}): Promise<Feedback[]> {
    try {
      return this.feedbackDao.findAll(filters);
    } catch (e) {
      this.logger.error('获取全量反馈失败', { error: e });
      throw new Error('获取反馈失败，请稍后重试');
    }
  }

  // 创建反馈并记录初始状态
  async createFeedback(feedback: Omit<Feedback, 'followUpStatus'>) {
    const newFeedback = await this.feedbackDao.create({
      ...feedback,
      followUpStatus: FollowUpStatus.INITIAL,
    });

    this.logger.info(`Created feedback ${newFeedback.feedbackID}`);
    return newFeedback;
  }

  // 新增批量更新所有反馈的方法
  async updateAllFeedbacks(feedbacks: Feedback[], operator?: string): Promise<boolean> {
    try {
      if (!feedbacks || feedbacks.length === 0) {
        this.logger.warn('没有提供要更新的反馈数据');
        return false;
      }

      // 记录操作日志
      this.logger.info(`开始批量更新 ${feedbacks.length} 条反馈数据`);
      const processedIds = new Set<string>(); // 用于跟踪已处理的feedbackID
      const updateRecords: UpdateRecord[] = []; // 收集所有更新记录

      // 首先获取原始数据，以便比较变更
      const feedbackIds = feedbacks.map(f => f.feedbackID);
      const originalFeedbacks = await this.feedbackDao.findAll({
        feedbackID: { $in: feedbackIds } as any,
      });
      const originalMap = new Map<string, Feedback>();
      originalFeedbacks.forEach(feedback => {
        originalMap.set(feedback.feedbackID, feedback);
      });

      for (const feedback of feedbacks) {
        const fullFeedbackId = feedback.feedbackID;
        if (processedIds.has(fullFeedbackId)) {
          continue;
        }

        processedIds.add(fullFeedbackId); // 记录已处理的ID

        // 尝试查找原始反馈以比较变更
        const originalFeedback = originalMap.get(fullFeedbackId);
        if (originalFeedback) {
          // 生成更新记录
          const records = this.generateUpdateRecords(originalFeedback, feedback, operator || 'system');
          updateRecords.push(...records);
        }

        await this.updateFeedbackLabels(feedback); // 调用voc接口
      }

      // 调用DAO层的批量更新方法
      const result = await this.feedbackDao.updateMany(feedbacks);

      // 保存所有更新记录
      if (updateRecords.length > 0) {
        await this.updateRecordDao.batchLogUpdates(updateRecords);
      }

      if (result) {
        this.logger.info(`批量更新反馈数据成功: ${feedbacks.length} 条记录, 记录变更: ${updateRecords.length} 条`);
      } else {
        this.logger.error('批量更新反馈数据失败');
      }

      return result;
    } catch (error) {
      this.logger.error('批量更新反馈数据出错', { error });
      return false;
    }
  }

  /**
   * 生成更新记录
   * @param original 原始反馈对象
   * @param updated 更新后的反馈对象
   * @param operator 操作人
   * @returns 更新记录数组
   */
  private generateUpdateRecords(original: Feedback, updated: Feedback, operator: string): UpdateRecord[] {
    const records: UpdateRecord[] = [];
    const timestamp = new Date().toISOString();

    // 检查跟进状态变更
    if (original.followUpStatus !== updated.followUpStatus) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'followUpStatus',
        beforeContent: String(original.followUpStatus),
        afterContent: String(updated.followUpStatus),
        operator,
        updateTime: timestamp,
      });
    }

    // 检查优先级变更
    if (original.priority !== updated.priority) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'priority',
        beforeContent: original.priority || '',
        afterContent: updated.priority || '',
        operator,
        updateTime: timestamp,
      });
    }

    // 检查自定义标签变更
    if (JSON.stringify(original.customLabels) !== JSON.stringify(updated.customLabels)) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'customLabels',
        beforeContent: JSON.stringify(original.customLabels),
        afterContent: JSON.stringify(updated.customLabels),
        operator,
        updateTime: timestamp,
      });
    }

    // 检查备注变更
    if (original.remarks !== updated.remarks) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'remarks',
        beforeContent: original.remarks || '',
        afterContent: updated.remarks || '',
        operator,
        updateTime: timestamp,
      });
    }

    // 检查Bug单变更
    if (JSON.stringify(original.bugTickets) !== JSON.stringify(updated.bugTickets)) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'bugTickets',
        beforeContent: JSON.stringify(original.bugTickets || []),
        afterContent: JSON.stringify(updated.bugTickets || []),
        operator,
        updateTime: timestamp,
      });
    }

    // 检查负责人变更
    if (JSON.stringify(original.responsiblePerson) !== JSON.stringify(updated.responsiblePerson)) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'responsiblePerson',
        beforeContent: JSON.stringify(original.responsiblePerson),
        afterContent: JSON.stringify(updated.responsiblePerson),
        operator,
        updateTime: timestamp,
      });
    }

    // 检查处理人变更
    if (JSON.stringify(original.handler) !== JSON.stringify(updated.handler)) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'handler',
        beforeContent: JSON.stringify(original.handler),
        afterContent: JSON.stringify(updated.handler),
        operator,
        updateTime: timestamp,
      });
    }

    // 检查自动归因变更
    if (JSON.stringify(original.autoReason) !== JSON.stringify(updated.autoReason)) {
      records.push({
        feedbackID: updated.feedbackID,
        updateField: 'autoReason',
        beforeContent: JSON.stringify(original.autoReason || {}),
        afterContent: JSON.stringify(updated.autoReason || {}),
        operator,
        updateTime: timestamp,
      });
    }

    return records;
  }

  private getAppConfig(appId: number): { aid: string; deviceOsGeneral?: string[] } {
    switch (appId) {
      case 177501: // 剪映iOS
        return { aid: '1775', deviceOsGeneral: ['ios'] };
      case 177502: // 剪映Android
        return { aid: '1775', deviceOsGeneral: ['android'] };
      case 251501: // 醒图iOS
        return { aid: '2515', deviceOsGeneral: ['ios'] };
      case 251502: // 醒图Android
        return { aid: '2515', deviceOsGeneral: ['android'] };
      case 2020092383: // 剪映windows
        return { aid: '3704', deviceOsGeneral: ['windows', 'macos'] };
      case 2020092892: // 剪映 mac
        return { aid: '3704', deviceOsGeneral: ['windows', 'macos'] };
      default:
        return { aid: appId.toString().slice(0, 4) };
    }
  }

  async getFeedbackDataOfVoc(appId: number, appVersion: string): Promise<XGetFeedbackInfoByIssueCodesResp> {
    const config = this.getAppConfig(appId);
    const endTime = new Date().getTime().toString();
    const startTime = (new Date().getTime() - 1800 * 1000).toString();

    const rpc = useInject(ExperienceFeedbackAnalysisService);
    const rpcRequestBody = {
      filters: [
        {
          type: 1,
          filter: [
            {
              key: 'app_version',
              op: 1,
              value: [appVersion],
            },
            {
              key: 'aid',
              op: 1,
              value: [config.aid],
            },
            {
              key: 'device_os_general',
              op: 1,
              value: config.deviceOsGeneral,
            },
          ],
        },
      ],
      pagination: {
        page: 1,
        pageSize: 80,
      },
      period: {
        datetime: {
          startMs: startTime,
          endMs: endTime,
        },
        interval: '',
      },
      sortList: [
        {
          orderBy: '_id',
          orderType: 'desc',
        },
      ],
      Base: {
        LogID: '',
        Caller: '',
        Addr: '',
        Client: '',
        TrafficEnv: {
          Open: false,
          Env: '',
        },
        Extra: {
          '': '',
        },
      },
    };

    try {
      const res: XGetFeedbackInfoByIssueCodesResp = await rpc.XGetFeedbackInfoWithFilters(
        rpcRequestBody as XGetFeedbackInfoWithFiltersReqOptions,
      );
      if (res.BaseResp) {
        this.logger.info(
          `获取反馈数据成功: ${res.BaseResp.StatusMessage} ${res.BaseResp.StatusCode} ${res.BaseResp.Extra} ${appVersion} ${config.aid} ${config.deviceOsGeneral}`,
        );
      }
      return res;
    } catch (error) {
      this.logger.error(`获取反馈数据失败: ${error}`, {
        appId,
        appVersion,
        timeRange: `${new Date(startTime).toISOString()} - ${new Date(endTime).toISOString()}`,
      });
      throw error;
    }
  }

  async getOnProgressTestFlightVersions(app_id: number) {
    const onProgressVersions = await this.versionProcessInfoDao.findOnProgressVersions(app_id);
    const versions = [];
    if (!onProgressVersions) {
      return;
    }
    for (const version of onProgressVersions) {
      const testFlight = version.version_stages.filter(stage => stage.display_name.includes('灰度'));
      let onProgress = false;
      for (const parentTestFlight of testFlight) {
        const subStages = parentTestFlight?.sub_stages;
        if (!subStages) {
          continue;
        }
        const onProgressSubStages = subStages.filter(stage => stage.status === VersionStageStatus.OnProgress);

        if (onProgressSubStages.length > 0) {
          onProgress = true;
        }
      }
      if (onProgress) {
        versions.push(version);
      }
    }
    return {
      app_id,
      versions,
    };
  }

  async getOnProgressTestFlightInfo() {
    // const allAppIds = [2020092383, 2020092892];
    const allAppIds = [177501, 177502, 2020092383, 2020092892, 251501, 251502, 244127338754, 225469550850];
    const onProgressInfo = [];
    for (const appid of allAppIds) {
      const versions = await this.getOnProgressTestFlightVersions(appid);
      if (versions) {
        onProgressInfo.push(versions);
      }
    }
    return onProgressInfo;
  }

  getHandleInfo(status: FollowUpStatus): FeedbackStatusHandle {
    const statusMap: Record<FollowUpStatus, FeedbackStatusHandle> = {
      [FollowUpStatus.USER_NOT_CONTACTED]: { field: 'userNotContacted', isHandled: false },
      [FollowUpStatus.QA_IN_PROGRESS]: { field: 'qaInProgress', isHandled: false },
      [FollowUpStatus.LOCAL_REPRODUCTION]: { field: 'localReproduction', isHandled: false },
      [FollowUpStatus.RD_IN_PROGRESS]: { field: 'rdInProgress', isHandled: false },
      [FollowUpStatus.USER_OCCASIONAL]: { field: 'userOccasional', isHandled: true },
      [FollowUpStatus.AUTO_RECOVERY]: { field: 'autoRecovery', isHandled: true },
      [FollowUpStatus.NOT_A_BUG]: { field: 'notABug', isHandled: true },
      [FollowUpStatus.CONTACT_ERROR_NO_REPRODUCTION]: { field: 'contactErrorNoReproduction', isHandled: true },
      [FollowUpStatus.NO_REPRODUCTION]: { field: 'noReproduction', isHandled: true },
      [FollowUpStatus.NEW_BUG_REPORTED]: { field: 'newBugReported', isHandled: true },
      [FollowUpStatus.HISTORICAL_BUG_REPUSHED]: { field: 'historicalBugRepushed', isHandled: true },
      [FollowUpStatus.KNOWN_ISSUE_OPTIMIZATION_REQUIRED]: { field: 'knownIssueOptimizationRequired', isHandled: true },
      [FollowUpStatus.KNOWN_ISSUE_FIXED]: { field: 'knownIssueFixed', isHandled: true },
      [FollowUpStatus.KNOWN_ISSUE_UNFIXED]: { field: 'knownIssueUnfixed', isHandled: true },
      [FollowUpStatus.NON_AGGREGATED]: { field: 'nonAggregated', isHandled: true },
      [FollowUpStatus.INITIAL]: { field: 'initial', isHandled: false },
    };
    return statusMap[status];
  }

  async getFeedbackHandleInfo(app_id: number, version: string, oldFeedbacks: Feedback[]) {
    const oldFeedbackMap = new Map<string, Feedback>();
    oldFeedbacks.forEach(feedback => {
      const uniqueKey = `${feedback.feedbackID}`;
      oldFeedbackMap.set(uniqueKey, feedback);
    });

    // 统计各状态数量
    const statistics: FeedbackMetricsItemInfo = {
      total: oldFeedbacks.length,
      processed: 0, // 已处理总数
      notHandled: 0, // 未处理总数
      noLabel: 0, // 无标签
      userNotContacted: 0,
      qaInProgress: 0,
      localReproduction: 0,
      rdInProgress: 0,
      userOccasional: 0,
      autoRecovery: 0,
      notABug: 0,
      contactErrorNoReproduction: 0,
      newBugReported: 0,
      historicalBugRepushed: 0,
      knownIssueOptimizationRequired: 0,
      knownIssueFixed: 0,
      knownIssueUnfixed: 0,
      nonAggregated: 0,
    };

    oldFeedbacks.forEach(feedback => {
      // 检查是否有跟进状态
      if (!feedback.followUpStatus) {
        statistics.noLabel++;
        statistics.notHandled++;
        return;
      }

      const status = feedback.followUpStatus;

      // 如果是初始状态，只计入未处理数量
      if (status === FollowUpStatus.INITIAL) {
        statistics.noLabel++;
        statistics.notHandled++;
        return;
      }

      const statusInfo = this.getHandleInfo(status);

      // 查找状态对应的统计信息
      if (statusInfo) {
        // 更新对应状态的计数
        statistics[statusInfo.field]++;

        // 更新已处理/未处理计数
        if (statusInfo.isHandled) {
          statistics.processed++;
        } else {
          statistics.notHandled++;
        }
      } else {
        // 未知状态，计入无标签
        this.logger.info(`[statistics] unknown fb label: ${JSON.stringify(statusInfo)}`);
        statistics.noLabel++;
        statistics.notHandled++;
      }
    });

    return statistics;
  }

  async updateFeedbackCheckItemStatus(app_id: number, version: string, status: CheckItemStatus) {
    const grey_stages = [
      'final_gray',
      'sub_gray',
      'sub_testflight',
      'final_testflight',
      'testflight_pre_check',
      'sub_testflight@1',
      'final_testflight@3',
      'sub_testflight@2',
    ];
    const res = [];
    for (const grey_stage of grey_stages) {
      const checklist = await this.checklistDao.find({
        stage: grey_stage,
        app_id,
        version,
      } as VersionStageCheckListItemQuery);
      if (checklist) {
        for (const item of checklist.check_items) {
          if (item.check_item_id.includes('feedback')) {
            item.status = status;
          }
        }
        res.push(checklist);
        await this.checklistDao.update(
          { version, app_id, stage: grey_stage } as VersionStageCheckListItemQuery,
          checklist,
        );
      }
    }
    return res;
  }

  async updateSpecificFeedbackCheckItemStatus(
    app_id: number,
    version: string,
    status: CheckItemStatus,
    metric: string,
    stage: string,
  ) {
    const res = [];
    const agent = useInject(FeedbackFollowCheckItemAgent);
    const checklist = await this.checklistDao.find({
      stage,
      app_id,
      version,
    } as VersionStageCheckListItemQuery);
    if (checklist) {
      for (const item of checklist.check_items) {
        if (
          item.check_item_id.includes(agent.itemNamePrefix(app_id, version, stage)) &&
          item.description.includes(metric)
        ) {
          item.status = status;
        }
      }
      res.push(checklist);
      await this.checklistDao.update({ version, app_id, stage } as VersionStageCheckListItemQuery, checklist);
    }
    return res;
  }

  async updateFeedbackLabels(feedback: Feedback): Promise<boolean> {
    const cemService = useInject(CemAnalysisService);

    if (!feedback || !feedback.feedbackID) {
      this.logger.error('更新反馈标签失败: 无效的反馈对象');
      return false;
    }

    const commonPlatformInfo = {
      busiDomain: {
        project: 'cap_cut',
        firstLevel: [],
        secondLevel: [],
        domain: 'cap_cut',
      },
      timeLoadLocation: '+08:00',
      language: 'zh',
    };

    // 在内部catch一下 线下测试不会报error
    try {
      // 更新跟进状态
      if (feedback.followUpStatus) {
        const result = await cemService.OpenTagLabelToFeedback({
          commonPlatformInfo,
          feedbackID: feedback.feedbackCallbackInfo.feedbackID,
          labelField: 'open_capcut_follow_status_list',
          labelValue: JSON.stringify([feedback.followUpStatus]),
        });
        if (!result.BaseResp) {
          this.logger.error(`更新反馈标签结果 跟进状态 result: ${result}`);
        } else {
          this.logger.info(
            `更新反馈标签结果 跟进状态 code: ${result.BaseResp.StatusCode}  feedbackID: ${feedback.feedbackID}`,
          );
        }
      }

      // 更新自定义标签
      if (feedback.customLabels && feedback.customLabels.length > 0) {
        const result = await cemService.OpenTagLabelToFeedback({
          commonPlatformInfo,
          feedbackID: feedback.feedbackCallbackInfo.feedbackID,
          labelField: 'open_capcut_custome_labels',
          labelValue: JSON.stringify(feedback.customLabels),
        });
        if (!result.BaseResp) {
          this.logger.error(`更新反馈标签结果 跟进状态 result: ${result}`);
        } else {
          this.logger.info(
            `更新反馈标签结果 跟进状态 code: ${result.BaseResp.StatusCode}  feedbackID: ${feedback.feedbackID}`,
          );
        }
      }

      // 更新优先级
      if (feedback.priority) {
        const result = await cemService.OpenTagLabelToFeedback({
          commonPlatformInfo,
          feedbackID: feedback.feedbackCallbackInfo.feedbackID,
          labelField: 'open_capcut_priority',
          labelValue: feedback.priority,
        });
        if (!result.BaseResp) {
          this.logger.error(`更新反馈标签结果 跟进状态 result: ${result}`);
        } else {
          this.logger.info(
            `更新反馈标签结果 跟进状态 code: ${result.BaseResp.StatusCode}  feedbackID: ${feedback.feedbackID}`,
          );
        }
      }

      return true;
    } catch (error) {
      console.log(error);
      this.logger.error(`更新反馈标签失败: ${error}`, { feedbackId: feedback.feedbackID });
      return false;
    }
  }

  async batchUpdateFeedbackLabels(feedbacks: Feedback[]): Promise<string[]> {
    const successfulIDs: string[] = [];

    if (!feedbacks || feedbacks.length === 0) {
      return successfulIDs;
    }

    for (const feedback of feedbacks) {
      try {
        const success = await this.updateFeedbackLabels(feedback);
        if (success) {
          successfulIDs.push(feedback.feedbackID);
        }
      } catch (error) {
        this.logger.error(`批量更新反馈标签失败: ${error}`, { feedbackId: feedback.feedbackID });
      }
    }

    return successfulIDs;
  }

  async deleteAllFeedbacks(filters: Partial<Feedback> = {}): Promise<{ deletedCount: number }> {
    try {
      this.logger.info(`开始删除反馈数据`, { filters });
      const result = await this.feedbackDao.deleteAll(filters);
      this.logger.info(`成功删除反馈数据`, { deletedCount: result.deletedCount });
      return result;
    } catch (error) {
      this.logger.error(`删除反馈数据失败: ${error}`, { filters });
      throw new Error('删除反馈数据失败，请检查日志');
    }
  }

  async getMeegoVersionId(app_id: number, version: string): Promise<number> {
    const MEEGO_FIELD_KEY_QUERY: Record<number, (v: string) => string> = {
      177501: v => `剪映-iOS-${v}`,
      177502: v => `剪映-Android-${v}`,
      251501: v => `醒图-iOS-${v}`,
      251502: v => `醒图-Android-${v}`,
      244127338754: v => `即梦-Android-${v}`,
      225469550850: v => `即梦-iOS-${v}`,
    };
    const query_conf = MEEGO_FIELD_KEY_QUERY[app_id];
    if (query_conf) {
      const meego_field_key_query = query_conf(version);
      const versionId = await this.meegoService.queryVersionId('faceu', meego_field_key_query);
      return versionId;
    }
    return -1;
  }

  async getHighPriorityMeegoItemInfo(app_id: number, version: string) {
    const meegoService = useInject(MeegoService);
    const versionId = await this.getMeegoVersionId(app_id, version);
    let meegoWorkItemInfo: WorkItemInfo[] = [];
    if (versionId !== -1) {
      meegoWorkItemInfo = await meegoService.queryUserFeedbackBug(versionId);
    }

    // 分别提取P0和P1的item
    const p0Items = meegoWorkItemInfo.filter(item => {
      const priorityField = item.fields.find((field: FieldValuePair) => field.field_key === 'priority');
      if (!priorityField) {
        return false;
      }
      const fieldValue = priorityField.field_value as FieldValue;
      const priority = fieldValue.label;
      return priority === 'P0';
    });

    const p1Items = meegoWorkItemInfo.filter(item => {
      const priorityField = item.fields.find((field: FieldValuePair) => field.field_key === 'priority');
      if (!priorityField) {
        return false;
      }
      const fieldValue = priorityField.field_value as FieldValue;
      const priority = fieldValue.label;
      return priority === 'P1';
    });

    // 统计P0的解决状态
    const p0Stats = {
      total: p0Items.length,
      completed: p0Items.filter(
        item => item.work_item_status.state_key === 'CLOSED' || item.work_item_status.state_key === 'REJECT',
      ).length,
      open: p0Items.filter(item => item.work_item_status.state_key === 'OPEN').length,
    };

    // 统计P1的解决状态
    const p1Stats = {
      total: p1Items.length,
      completed: p1Items.filter(
        item => item.work_item_status.state_key === 'CLOSED' || item.work_item_status.state_key === 'REJECT',
      ).length,
      open: p1Items.filter(item => item.work_item_status.state_key === 'OPEN').length,
    };

    return {
      p0Stats,
      p1Stats,
    };
  }
}
