import { Inject, Injectable } from '@gulux/gulux';
import BitsService from './third/bits';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LarkCardService from './larkCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import UserService from '@pa/backend/dist/src/service/user';
import VersionProcessInfoDao from './dao/releasePlatform/VersionProcessInfoDao';
import CustomBuildDao from './dao/releasePlatform/CustomBuildDao';
import CustomBuildService from './customBuild';
import { AppSetting, AppSettingId, AppSettingSymbol } from '@pa/shared/dist/src/appSettings/appSettings';
import { VersionProcessInfo, VersionStageStatus } from '@shared/releasePlatform/versionStage';
import { getFormalPackage, getFullReleaseRecord, getStageInfo } from '@shared/releasePlatform/releasePlatformUtils';
import { PlatformType } from '@pa/shared/dist/src/core';
import {
  CustomBuildParam,
  CustomBuildRecord,
  CustomBuildType,
  IntegrationProductHistory,
  LatestBuildInfo,
  LatestPackageResult,
  ProductManageResp,
} from '@shared/customBuild';
import {
  PCSubGrayExtraData,
  SmallFlowExtraData,
  SubGrayExtraData,
  SubTestFlightExtraData,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import ComponentModelService from './model/customBuildModel';
import { BitsAppIdToAid } from '@shared/bits';
import { ReleaseRecord } from '@shared/bits/release';
import timeUtil from '../utils/timeUtil';
import versionUtils from '../utils/versionUtils';
import GitLabService from './third/gitlab';
import OnCallService from './third/oncall';
import { PCPackageInfo } from '@shared/releasePlatform/QATestConfig';

@Injectable()
export default class ScheduleBuildService {
  @Inject()
  private bits: BitsService;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private larkCard: LarkCardService;

  @Inject()
  private lark: LarkService;

  @Inject()
  private user: UserService;

  @Inject()
  private customBuildService: CustomBuildService;

  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;

  @Inject()
  private customBuildDao: CustomBuildDao;

  @Inject(AppSettingSymbol)
  private appSetting: AppSetting;

  @Inject()
  private componentModelService: ComponentModelService;

  @Inject()
  private gitlab: GitLabService;

  @Inject()
  private oncall: OnCallService;

  async integrationTestScheduleBuildWithoutConfigPage() {
    const currentTime = Date.now();
    const isOffDay = await this.oncall.isOffDay(currentTime);
    if (isOffDay) {
      return;
    }
    this.logger.info(`integrationTestScheduleBuild 开始执行集成测试定时构建, appId: ${this.appSetting.id}`);
    const { appSetting } = this;
    const onProgressVersion = await this.versionProcessInfoDao.findOnProgressVersions(appSetting.id);
    if (!onProgressVersion) {
      this.logger.error(`integrationTestScheduleBuild 未找到正在进行中的版本, appId: ${appSetting.id}`);
      return;
    }
    const integrationTestVersion = onProgressVersion.filter(it =>
      it.version_stages.find(
        stage => stage.display_name.includes('封版') && stage.status === VersionStageStatus.Complete,
      ),
    );
    if (integrationTestVersion.length === 0) {
      this.logger.info(`integrationTestScheduleBuild 未找到正在进行中的集成测试版本, appId: ${appSetting.id}`);
      return;
    }
    if (appSetting.id === AppSettingId.LV_IOS || appSetting.id === AppSettingId.CC_IOS) {
      for (const versionInfo of integrationTestVersion) {
        let lockPackageStage = getStageInfo(versionInfo.version_stages, 'smallFlow');
        if (!lockPackageStage) {
          this.logger.error(
            `integrationTestScheduleBuild 未找到小流量版本, appId: ${appSetting.id}, version: ${versionInfo.version}`,
          );
          lockPackageStage = getStageInfo(versionInfo.version_stages, 'user_story');
          if (!lockPackageStage) {
            this.logger.error(
              `integrationTestScheduleBuild 未找到用户故事版本, appId: ${appSetting.id}, version: ${versionInfo.version}`,
            );
            continue;
          }
        }
        this.logger.info(
          `integrationTestScheduleBuild 开始执行集成测试定时构建, appId: ${appSetting.id}, version: ${versionInfo.version}`,
        );
        if (lockPackageStage.status === VersionStageStatus.NotStart) {
          this.logger.info(
            `integrationTestScheduleBuild 未封包，开始集成测试定时构建, appId: ${appSetting.id}, version: ${versionInfo.version}`,
          );
          // 不带调试页
          const branch =
            versionInfo.app_id === AppSettingId.LV_IOS
              ? `release/${versionInfo.version}`
              : `overseas/release/${versionInfo.version}`;
          const result = await this.customBuildService.manualBuildTFPackage(
            'zhengbolun.patlon',
            appSetting.id,
            branch,
            PlatformType.iOS,
            [CustomBuildType.GRAY, CustomBuildType.INTEGRATION_SCHEDULE],
            versionInfo.version,
            true,
            undefined,
            undefined,
            appSetting.id === AppSettingId.LV_IOS ? '不带调试页TF包' : '不带调试页TF包（TTP机器构建）',
          );
        }
      }
    }
  }

  async integrationTestScheduleBuild() {
    this.logger.info(`integrationTestScheduleBuild 开始执行集成测试定时构建, appId: ${this.appSetting.id}`);
    const currentTime = Date.now();
    const isOffDay = await this.oncall.isOffDay(currentTime);
    if (isOffDay) {
      return;
    }
    const { appSetting } = this;
    const onProgressVersion = await this.versionProcessInfoDao.findOnProgressVersions(appSetting.id);
    if (!onProgressVersion) {
      this.logger.error(`integrationTestScheduleBuild 未找到正在进行中的版本, appId: ${appSetting.id}`);
      return;
    }
    const integrationTestVersion = onProgressVersion.filter(it =>
      it.version_stages.find(
        stage => stage.display_name.includes('封版') && stage.status === VersionStageStatus.Complete,
      ),
    );
    if (integrationTestVersion.length === 0) {
      this.logger.info(`integrationTestScheduleBuild 未找到正在进行中的集成测试版本, appId: ${appSetting.id}`);
      return;
    }
    if (appSetting.id === AppSettingId.LV_IOS || appSetting.id === AppSettingId.CC_IOS) {
      for (const versionInfo of integrationTestVersion) {
        let lockPackageStage = getStageInfo(versionInfo.version_stages, 'smallFlow');
        if (!lockPackageStage) {
          this.logger.error(
            `integrationTestScheduleBuild 未找到小流量版本, appId: ${appSetting.id}, version: ${versionInfo.version}`,
          );
          lockPackageStage = getStageInfo(versionInfo.version_stages, 'user_story');
          if (!lockPackageStage) {
            this.logger.error(
              `integrationTestScheduleBuild 未找到用户故事版本, appId: ${appSetting.id}, version: ${versionInfo.version}`,
            );
            continue;
          }
        }
        this.logger.info(
          `integrationTestScheduleBuild 开始执行集成测试定时构建, appId: ${appSetting.id}, version: ${versionInfo.version}`,
        );
        if (lockPackageStage.status === VersionStageStatus.NotStart) {
          this.logger.info(
            `integrationTestScheduleBuild 未封包，开始集成测试定时构建, appId: ${appSetting.id}, version: ${versionInfo.version}`,
          );
          // 带调试页
          const branch =
            versionInfo.app_id === AppSettingId.LV_IOS
              ? `release/${versionInfo.version}`
              : `overseas/release/${versionInfo.version}`;
          const result = await this.customBuildService.manualBuildTFPackage(
            'zhengbolun.patlon',
            appSetting.id,
            branch,
            PlatformType.iOS,
            [CustomBuildType.TF_TEST, CustomBuildType.INTEGRATION_SCHEDULE],
            versionInfo.version,
            true,
            undefined,
            undefined,
            appSetting.id === AppSettingId.LV_IOS ? '带调试页TF包' : '带调试页TF包（TTP机器构建）',
          );
        }
      }
    }
  }

  async userStoryTestScheduleBuild() {
    const currentTime = Date.now();
    const isOffDay = await this.oncall.isOffDay(currentTime);
    if (isOffDay) {
      return;
    }
    const { appSetting } = this;
    const onProgressVersion = await this.versionProcessInfoDao.findOnProgressVersions(appSetting.id);
    if (!onProgressVersion) {
      this.logger.error(`integrationTestScheduleBuild 未找到正在进行中的版本, appId: ${appSetting.id}`);
      return;
    }
    let userStoryStageName = 'user_story';
    if (appSetting.id === AppSettingId.LV_IOS || appSetting.id === AppSettingId.CC_IOS) {
      userStoryStageName = 'user_story';
    } else if (appSetting.id === AppSettingId.LV_ANDROID || appSetting.id === AppSettingId.CC_ANDROID) {
      userStoryStageName = 'full_release_precheck';
    }
    const filterStageList = onProgressVersion.filter(it =>
      it.version_stages.find(
        stage => stage.stage_name.includes(userStoryStageName) && stage.status === VersionStageStatus.OnProgress,
      ),
    );
    if (filterStageList.length === 0) {
      this.logger.info(`userStoryTestScheduleBuild 未找到正在进行中的用户故事测试版本, appId: ${appSetting.id}`);
      return;
    }
    const versionInfo = filterStageList[0];
    let branch = `release/${versionInfo.version}`;
    if (versionInfo.app_id === AppSettingId.CC_IOS || versionInfo.app_id === AppSettingId.CC_ANDROID) {
      branch = `overseas/release/${versionInfo.version}`;
    }
    if (await this.isSameCommitBuild(versionInfo, branch)) {
      return;
    }
    if (appSetting.platform === PlatformType.Android) {
      const res = await this.customBuildService.newCustomBuild(
        {
          type: CustomBuildType.FORMAL,
          lvBranch: branch,
          arch: PlatformType.Android,
          repos: [],
        } as CustomBuildParam,
        CustomBuildType.USER_STORY_SCHEDULE,
        PlatformType.Android,
        appSetting.id,
        'zhengbolun.patlon',
        true,
      );
    } else if (appSetting.platform === PlatformType.iOS) {
      const result = await this.customBuildService.manualBuildTFPackage(
        'zhengbolun.patlon',
        appSetting.id,
        branch,
        PlatformType.iOS,
        [CustomBuildType.TF_TEST, CustomBuildType.USER_STORY_SCHEDULE],
        versionInfo.version,
        true,
        undefined,
        undefined,
        appSetting.id === AppSettingId.LV_IOS ? '带调试页TF包' : '带调试页TF包（TTP机器构建）',
      );
      // 不带调试页
      const result2 = await this.customBuildService.manualBuildTFPackage(
        'zhengbolun.patlon',
        appSetting.id,
        branch,
        PlatformType.iOS,
        [CustomBuildType.GRAY, CustomBuildType.USER_STORY_SCHEDULE],
        versionInfo.version,
        true,
        undefined,
        undefined,
        appSetting.id === AppSettingId.LV_IOS ? '不带调试页TF包' : '不带调试页TF包（TTP机器构建）',
      );
    }
  }

  async isSameCommitBuild(versionInfo: VersionProcessInfo, branch: string) {
    const platform =
      versionInfo.app_id === AppSettingId.LV_IOS || versionInfo.app_id === AppSettingId.CC_IOS
        ? PlatformType.iOS
        : PlatformType.Android;
    const isOversea = versionInfo.app_id !== AppSettingId.LV_IOS && versionInfo.app_id !== AppSettingId.LV_ANDROID;
    const projectId = platform === PlatformType.iOS ? 39995 : 40279;
    this.logger.info(
      `isSameCommitBuild, projectId: ${projectId}, branch: ${branch}, appId: ${versionInfo.app_id}, isOversea: ${isOversea}, platform: ${platform}, version: ${versionInfo.version}`,
    );
    const buildRecord = await this.customBuildDao.getLatestRecord(
      versionInfo.app_id,
      platform,
      versionInfo.version,
      isOversea,
      [CustomBuildType.USER_STORY_SCHEDULE],
      false,
    );
    this.logger.info(`isSameCommitBuild, buildRecord: ${JSON.stringify(buildRecord)}`);
    if (!buildRecord) {
      return false;
    }
    const branchInfo = await this.gitlab.getBranchInfo(projectId, branch);
    const latestCommitId = branchInfo?.data?.commit?.id;
    this.logger.info(`isSameCommitBuild, latestCommitId: ${latestCommitId}`);
    this.logger.info(`isSameCommitBuild, result: ${latestCommitId === buildRecord.commit}`);
    if (latestCommitId === buildRecord.commit) {
      return true;
    }
    return false;
  }

  async fetchVersionBuildRecord(
    appId: AppSettingId,
    page: number,
    pageSize: number,
    version: string[],
    productType?: CustomBuildType,
  ) {
    const { appSetting } = this;
    let versionInfos = null;
    if (version.length > 0) {
      versionInfos = await this.versionProcessInfoDao.findVersionProcessInfoByPage(page, pageSize, [appId], version);
    } else {
      versionInfos = await this.versionProcessInfoDao.findNomalVersionProcessInfoByPage(page, pageSize, [appId]);
    }
    if (!versionInfos) {
      return;
    }
    const listQuery: { [key: string]: any } = {
      app_id: { $in: [appId] },
      releaseVersionType: { $ne: 'fixVersion' },
    };
    if (version.length > 0) {
      listQuery.version = {
        $in: version,
      };
    }
    const result: LatestPackageResult[] = [];
    const count = await this.versionProcessInfoDao.count(listQuery);
    for (const versionInfo of versionInfos) {
      const buildInfoArray: LatestBuildInfo[] = [];
      if (
        versionInfo.app_id === AppSettingId.LV_IOS ||
        versionInfo.app_id === AppSettingId.CC_IOS ||
        versionInfo.app_id === AppSettingId.LV_ANDROID ||
        versionInfo.app_id === AppSettingId.CC_ANDROID
      ) {
        const platfrom =
          versionInfo.app_id === AppSettingId.LV_IOS || versionInfo.app_id === AppSettingId.CC_IOS
            ? PlatformType.iOS
            : PlatformType.Android;
        const isOversea = versionInfo.app_id !== AppSettingId.LV_IOS && versionInfo.app_id !== AppSettingId.LV_ANDROID;
        const integrationPackage = await this.customBuildDao.getLatestRecord(
          versionInfo.app_id,
          platfrom,
          versionInfo.version,
          isOversea,
          [CustomBuildType.MR_MERGED],
        );
        const currentTime = new Date().getTime() / 1000;
        let timeStr = timeUtil.secondFormat2(currentTime);
        if (integrationPackage && integrationPackage.createdAt) {
          const dateObject: Date = new Date(integrationPackage.createdAt);
          const timestamp: number = dateObject.getTime() / 1000;
          timeStr = timeUtil.secondFormat2(timestamp);
        }
        if (integrationPackage) {
          buildInfoArray.push({
            type: CustomBuildType.MR_MERGED,
            version: versionInfo.version,
            versionCode: '',
            packageTime: timeStr,
            record: integrationPackage,
          });
        }
      }
      const grayPackage = this.getGrayPackageInfo(appId, versionInfo);
      buildInfoArray.push(...grayPackage);
      const formalPackage = this.getFormalPackageInfo(appId, versionInfo);
      if (formalPackage) {
        buildInfoArray.push(formalPackage);
      }
      if (
        versionInfo.app_id === AppSettingId.LV_WIN ||
        versionInfo.app_id === AppSettingId.LV_MAC ||
        versionInfo.app_id === AppSettingId.CC_WIN ||
        versionInfo.app_id === AppSettingId.CC_MAC
      ) {
        const pcSystemTestPackage = this.getPCSystemTestPackageInfo(appId, versionInfo);
        buildInfoArray.push(...pcSystemTestPackage);
      }
      const versionBuildInfo = {
        count: buildInfoArray.length,
        list: buildInfoArray,
        version: versionInfo.version,
      } as LatestPackageResult;
      if (formalPackage && formalPackage.hasFixVersion) {
        versionBuildInfo.hasFixVersion = true;
      }
      if (productType) {
        versionBuildInfo.list = buildInfoArray.filter(it => it.type === productType);
      }
      result.push(versionBuildInfo);
    }
    return {
      total: count,
      list: result,
    } as ProductManageResp;
  }

  matchVersionNumber(url: string): string | null {
    // 定义正则表达式，使用 | 来匹配 Jianying 或 Capcut
    const regex = /(Jianying|CapCut)_(\d+(_\d+)+)_(jianyingpro|capcutpc)/;
    // 使用正则表达式进行匹配
    const match = url.match(regex);
    // 如果匹配成功，返回捕获组中的版本号；否则返回 null
    return match ? match[2] : null;
  }

  getPCSystemTestPackageInfo(appId: AppSettingId, versionInfo: VersionProcessInfo) {
    const buildInfoArray: LatestBuildInfo[] = [];
    const pcSystemTestStage = getStageInfo(versionInfo.version_stages, 'pc_sub_system_test_4');
    if (!pcSystemTestStage) {
      this.logger.error(`fetchVersionBuildRecord 未找到PC系统测试版本, appId: ${appId}`);
      return buildInfoArray;
    }
    const extraData = pcSystemTestStage.extra_data as PCPackageInfo;
    if (!extraData) {
      return buildInfoArray;
    }
    if (extraData.packages && extraData.packages.length > 0) {
      const packageInfo = extraData.packages[extraData.packages.length - 1];
      buildInfoArray.push({
        type: CustomBuildType.PC_SYSTEM_TEST,
        version: versionInfo.version,
        versionCode: this.matchVersionNumber(packageInfo.link) ?? '',
        packageTime: '',
      });
    }
    return buildInfoArray;
  }

  getGrayPackageInfo(appId: AppSettingId, versionInfo: VersionProcessInfo) {
    const buildInfoArray: LatestBuildInfo[] = [];
    if (appId === AppSettingId.LV_IOS || appId === AppSettingId.CC_IOS) {
      const grayStage = getStageInfo(versionInfo.version_stages, 'testFlight');
      if (!grayStage) {
        this.logger.error(`fetchVersionBuildRecord 未找到灰度版本, appId: ${appId}`);
        return buildInfoArray;
      }
      // 倒序遍历subStage
      for (let i = grayStage.sub_stages.length - 1; i >= 0; i--) {
        const subStage = grayStage.sub_stages[i];
        const extraData = subStage.extra_data as SubTestFlightExtraData;
        if (!extraData) {
          continue;
        }
        if (extraData.versionCode && extraData.versionCode !== '') {
          const timeStr = timeUtil.secondFormat2(extraData.packageTime ?? extraData.releaseTime);
          buildInfoArray.push({
            type: CustomBuildType.GRAY,
            version: versionInfo.version,
            versionCode: extraData.versionCode,
            packageTime: timeStr,
          });
          break;
        }
      }
    } else if (appId === AppSettingId.LV_ANDROID || appId === AppSettingId.CC_ANDROID) {
      const grayStage = getStageInfo(versionInfo.version_stages, 'gray');
      if (!grayStage) {
        this.logger.error(`fetchVersionBuildRecord 未找到灰度版本, appId: ${appId}`);
        return buildInfoArray;
      }
      for (let i = grayStage.sub_stages.length - 1; i >= 0; i--) {
        const subStage = grayStage.sub_stages[i];
        const extraData = subStage.extra_data as SubGrayExtraData;
        if (!extraData) {
          continue;
        }
        if (extraData.versionCode && extraData.versionCode !== '') {
          const timeStr = timeUtil.secondFormat2(extraData.packageTime ?? extraData.releaseTime);
          buildInfoArray.push({
            type: CustomBuildType.GRAY,
            version: versionInfo.version,
            versionCode: extraData.versionCode,
            packageTime: timeStr,
          });
          break;
        }
      }
    } else if (appId === AppSettingId.RETOUCH_ANDROID || appId === AppSettingId.HYPIC_ANDROID) {
      const grayStage = getStageInfo(versionInfo.version_stages, 'retouch_adr_gray');
      if (!grayStage) {
        this.logger.error(`fetchVersionBuildRecord 未找到灰度版本, appId: ${appId}`);
        return buildInfoArray;
      }
      for (let i = grayStage.sub_stages.length - 1; i >= 0; i--) {
        const subStage = grayStage.sub_stages[i];
        const extraData = subStage.extra_data as SubGrayExtraData;
        if (!extraData) {
          continue;
        }
        if (extraData.versionCode && extraData.versionCode !== '') {
          const timeStr = timeUtil.secondFormat2(extraData.packageTime ?? extraData.releaseTime);
          buildInfoArray.push({
            type: CustomBuildType.GRAY,
            version: versionInfo.version,
            versionCode: extraData.versionCode,
            packageTime: timeStr,
          });
          break;
        }
      }
    } else if (appId === AppSettingId.RETOUCH_IOS || appId === AppSettingId.HYPIC_IOS) {
      const grayStage = getStageInfo(versionInfo.version_stages, 'retouch_ios_testFlight');
      if (!grayStage) {
        this.logger.error(`fetchVersionBuildRecord 未找到灰度版本, appId: ${appId}`);
        return buildInfoArray;
      }
      for (let i = grayStage.sub_stages.length - 1; i >= 0; i--) {
        const subStage = grayStage.sub_stages[i];
        const extraData = subStage.extra_data as SubGrayExtraData;
        if (!extraData) {
          continue;
        }
        if (extraData.versionCode && extraData.versionCode !== '') {
          const timeStr = timeUtil.secondFormat2(extraData.packageTime ?? extraData.releaseTime);
          buildInfoArray.push({
            type: CustomBuildType.GRAY,
            version: versionInfo.version,
            versionCode: extraData.versionCode,
            packageTime: timeStr,
          });
          break;
        }
      }
    } else if (
      appId === AppSettingId.LV_WIN ||
      appId === AppSettingId.LV_MAC ||
      appId === AppSettingId.CC_WIN ||
      appId === AppSettingId.CC_MAC
    ) {
      const grayStage = getStageInfo(versionInfo.version_stages, 'gray_win');
      const pcSubStages = grayStage?.sub_stages ?? [];
      if (!grayStage) {
        const firstGrayStage = getStageInfo(versionInfo.version_stages, 'first_gray_win');
        const secondGrayStage = getStageInfo(versionInfo.version_stages, 'second_gray_win');
        if (!firstGrayStage || !secondGrayStage) {
          this.logger.error(`fetchVersionBuildRecord 未找到灰度版本, appId: ${appId}, version: ${versionInfo.version}`);
          return buildInfoArray;
        }
        pcSubStages.push(...firstGrayStage.sub_stages);
        pcSubStages.push(...secondGrayStage.sub_stages);
      }
      for (let i = pcSubStages.length - 1; i >= 0; i--) {
        const subStage = pcSubStages[i];
        const extraData = subStage.extra_data as SubGrayExtraData;
        if (!extraData) {
          continue;
        }
        if (extraData.versionCode && extraData.versionCode !== '') {
          const timeStr = timeUtil.secondFormat2(extraData.packageTime ?? extraData.releaseTime);
          buildInfoArray.push({
            type: CustomBuildType.GRAY,
            version: versionInfo.version,
            versionCode: extraData.versionCode,
            packageTime: timeStr,
          });
          break;
        }
      }
    }
    return buildInfoArray;
  }

  getFormalPackageTime(record: ReleaseRecord | undefined) {
    if (!record) {
      return 0;
    }
    const { release_artifacts } = record;
    const packageTime = 0;
    if (release_artifacts && release_artifacts.length > 0) {
      return release_artifacts[0].artifact.build_time ?? 0;
    }
    return 0;
  }

  getFormalPackageInfo(appId: AppSettingId, versionInfo: VersionProcessInfo) {
    let buildInfo: LatestBuildInfo | undefined;
    if (
      versionInfo.app_id === AppSettingId.LV_IOS ||
      versionInfo.app_id === AppSettingId.CC_IOS ||
      versionInfo.app_id === AppSettingId.LV_ANDROID ||
      versionInfo.app_id === AppSettingId.CC_ANDROID
    ) {
      const fullReleaseRecord = getFormalPackage(versionInfo);
      if (fullReleaseRecord && fullReleaseRecord.length > 0) {
        const timestamp = this.getFormalPackageTime(fullReleaseRecord[0]);
        const timeStr = timeUtil.secondFormat2(timestamp);
        const record = {
          type: CustomBuildType.FORMAL,
          version: versionInfo.version,
          versionCode: fullReleaseRecord[0].update_version,
          packageTime: timeStr,
        } as LatestBuildInfo;
        if (fullReleaseRecord.length > 1) {
          // 如果多个正式包的版本号不一样，则需要展示构建历史
          if (fullReleaseRecord[0].update_version !== fullReleaseRecord[1].update_version) {
            record.hasFixVersion = true;
          }
        }
        buildInfo = record;
      }
    }

    return buildInfo;
  }

  async getAndroidArtifact(appId: AppSettingId, updateVersion: string, releaseEnv: string) {
    const aid = BitsAppIdToAid(appId);
    const res = await this.bits.getAndroidArtifact(aid, updateVersion, releaseEnv);
    if (!res) {
      return undefined;
    }
    const sortedRes = res.value.sort((a, b) => b.build_time - a.build_time);
    if (sortedRes && sortedRes.length > 0) {
      return sortedRes[0];
    }
    return undefined;
  }

  getAnotherAppId(appId: AppSettingId) {
    if (appId === AppSettingId.LV_IOS) {
      return AppSettingId.CC_IOS;
    } else if (appId === AppSettingId.CC_IOS) {
      return AppSettingId.LV_IOS;
    } else if (appId === AppSettingId.LV_ANDROID) {
      return AppSettingId.CC_ANDROID;
    } else if (appId === AppSettingId.CC_ANDROID) {
      return AppSettingId.LV_ANDROID;
    }
    return AppSettingId.LV_IOS;
  }

  async getIntegrationBuildRecord(query: any, page: number, pageSize: number, appId: number) {
    const res = await this.list(query, page, pageSize);
    const integrationPackageResult: IntegrationProductHistory[] = [];
    if (res) {
      // 根据commit获取对应另一端的记录
      for (const record of res) {
        const anotherAppId = this.getAnotherAppId(appId);
        const anotherAppRecord = await this.customBuildDao.getAnotherAppBuildRecord(anotherAppId, record.commit ?? '');
        const temp: IntegrationProductHistory = {
          lvRecord:
            record.appId === AppSettingId.LV_IOS || record.appId === AppSettingId.LV_ANDROID
              ? record
              : anotherAppRecord,
          ccRecord:
            record.appId === AppSettingId.CC_IOS || record.appId === AppSettingId.CC_ANDROID
              ? record
              : anotherAppRecord,
        };
        integrationPackageResult.push(temp);
      }
    }
    return {
      list: integrationPackageResult,
    };
  }

  async list(query: any, page: number, pageSize: number) {
    const res = await this.customBuildDao.list(query, page, pageSize);
    if (res && res.length > 0) {
      return res as CustomBuildRecord[];
    }
  }

  async count(query: any): Promise<number> {
    return await this.customBuildDao.count(query);
  }
}
