import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { FilterRule, Flight } from '@shared/libra/flight';
import { getAppIdByLibraAppId } from '@shared/libra/libraManageUtils';
import { LibraAppIds, LibraRegion } from '@shared/libra/commonLibra';
import { FlightType, LibraMeegoTeamSimpleInfo, LibraNewInfo } from '@shared/libra/LibraNewInfo';
import { LibraNewInfoListService } from './LibraNewInfoListService';
import LibraAPIService from './LibraAPIService';
import { LibraControlReq, LibraControlTakeOverType } from '@shared/libra/libraControl';
import MeegoRawService from '../third/meego';
import { current_region, Region } from '../../utils/region';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';

export enum PCCheckLevel {
  ERROR,
  WARNING,
}

export enum PCCheckType {
  RULE, // 基本规则
  NOT_MATCH_DOC, // 和实验文档不一致
}

export interface PCCheckErrorItem {
  name: string;
  require?: string;
  actual?: string;
  level: PCCheckLevel;
  type: PCCheckType;
}

@Injectable()
export class LibraPCCheckService {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private libraApi: LibraAPIService;
  @Inject()
  private libraNewInfoListService: LibraNewInfoListService;
  @Inject()
  private meegoService: MeegoRawService;
  @Inject()
  private rpcProxy: RpcProxyManager;

  check_rule_config = {
    exclude_string_lower_check: ['biz_scene', 'pf', 'user.is_creator', 'channel'],
  };

  isStringArray = (arr: unknown): arr is string[] => Array.isArray(arr) && arr.every(item => typeof item === 'string');
  isNumberArray = (arr: unknown): arr is number[] => Array.isArray(arr) && arr.every(item => typeof item === 'number');

  async checkPCLibra(req: LibraControlReq) {
    // 基础校验
    if (!req.flight) {
      return {
        take_over: false,
        take_over_reason: 'app_id或flight_id不能为空',
      };
    }
    const isSG = req.region === 'sg';
    const flightId = Number(req.flight_id);
    const libraAppId = Number(req.app_id);

    // 查询 LibraNewInfo 是否存在对应的记录
    let libraNewInfo: LibraNewInfo | null | undefined;
    const findOneQuery = {
      'flightInfo.id': flightId,
      libraAppId,
    };
    if (current_region() === Region.CN) {
      // CN 环境 RPC 转发一下
      const res = await this.rpcProxy.getQuality(true).libraNewInfoFindOne(findOneQuery);
      if (res) {
        libraNewInfo = res as LibraNewInfo;
      }
    } else {
      libraNewInfo = await this.libraNewInfoListService.findOne(findOneQuery);
    }

    // 通过 API 查询 Libra 信息
    const flight = await this.libraApi.queryFlight(isSG ? LibraRegion.SG : LibraRegion.CN, Number(flightId));
    if (!flight) {
      // 如果通过 API 查询不到最新的 flight 信息，则返回失败
      return { code: -1, msg: `实验信息请求失败，flight_id: ${flightId}` };
    }

    // 没有 LibraNewInfo 就创建一个
    if (!libraNewInfo) {
      if (current_region() === Region.CN) {
        // CN 环境 RPC 转发一下
        const res = await this.rpcProxy
          .getQuality(true)
          .createLibraNewInfo(isSG ? LibraRegion.SG : LibraRegion.CN, flight.app_id, flight.id);
        if (res) {
          libraNewInfo = res as LibraNewInfo;
        }
      } else {
        // 没有 libraNewInfo，就顺便创建一个
        libraNewInfo = await this.libraNewInfoListService.createLibraNewInfo(
          isSG ? LibraRegion.SG : LibraRegion.CN,
          flight.app_id,
          flight.id,
        );
      }
    }
    const checkResult = await this.checkLibraBasic(flight, libraNewInfo?.meegoTeamInfo);
    const checkCode = checkResult?.code ?? -1000;
    const checkMsg = checkResult?.msg ?? '';
    const basicCheckResult = checkResult?.checkResult ?? [];

    // 1. 基础检查
    let reason = '';
    if (basicCheckResult.length > 0) {
      reason += '\n';
      reason += `[基础检查]：${basicCheckResult.map(e => e.name).join('; ')}`;
    }
    if (reason.length > 0) {
      return {
        take_over: true,
        take_over_reason: `【实验检查错误（基础信息）】${reason}`,
        take_over_type: LibraControlTakeOverType.PCLibraCheckFailed,
      };
    }
    return {
      take_over: false,
      take_over_reason: '',
    };
  }

  toBase256(n: number): number[] {
    const bytes: number[] = [];
    let num = n;

    if (num < 0) {
      throw new Error('输入必须为非负数');
    }

    do {
      // 取余并转为数值
      const byte = Number(num % 256);
      bytes.unshift(byte); // 逆序排列，确保高位在前
      // 整数除法，向下取整
      num = Math.floor(num / 256);
    } while (num > 0);

    return bytes;
  }

  getPCVersionCode(filterRule: FilterRule[]) {
    let versionCode = 0;
    for (const { conditions } of filterRule) {
      const conditionItems = conditions.map(it => it.condition);
      for (const item of conditionItems) {
        if (item.key === '_version_code') {
          versionCode = item.value as number;
        }
      }
    }
    return versionCode;
  }

  async checkLibraBasic(flight: Flight, meegoTeamInfos?: LibraMeegoTeamSimpleInfo[]) {
    const checkResult: PCCheckErrorItem[] = [];
    const filter_rule: FilterRule[] = flight.filter_rule ?? [];
    const teamIds: number[] = meegoTeamInfos?.map(team => team.teamId) || []; // 筛选出所有的 teamId
    const mainTeamId = meegoTeamInfos?.find(team => team.isMainTeam)?.teamId || 0; // 筛选出主责团队的 teamId
    const appId = getAppIdByLibraAppId(flight.app_id); // 产品 ID
    const isSG =
      appId === LibraAppIds.cc ||
      appId === LibraAppIds.cc_web ||
      appId === LibraAppIds.cc_pc ||
      appId === LibraAppIds.hypic;
    // 1. 无需文档就能做的做的判断，主要检查一些异常case
    /* const vxRep = /^[v|V]\d$/
    // 1.1 分组名称检测
    const versionCheckFailedList = flight.versions?.filter(version=> {
        return vxRep.test(version.name) && !version.description // 分组名称为v0/v1等且没有描述时
    }).filter(version => {
        return version.name.toLowerCase() !== 'v0'
    })
    if (versionCheckFailedList.length > 0) {
        checkResult.push({
            name: '分组名称不要用v0/v1等无意义的词, 否则请带上描述信息',
            level: Level.WARNING,
            type: Type.RULE,
        } as AbCheckErrorItem)
    }*/
    filter_rule.forEach(rule => {
      rule.conditions?.forEach(c => {
        // 1.2. 文本类字段加上lower，忽略大小写
        const exclude_string_lower_check = this.check_rule_config?.exclude_string_lower_check ?? [];
        if (
          !exclude_string_lower_check.includes(c.condition.key) &&
          c.condition.op === '==' &&
          c.condition.type === 'string' &&
          c.condition.transformer !== 'lower'
        ) {
          checkResult.push({
            name: `文本类字段[${c.condition.key}]请加上lower，忽略大小写`,
            level: PCCheckLevel.ERROR,
            type: PCCheckType.RULE,
          } as PCCheckErrorItem);
        }
        // 1.3. 国家字段请使用priority_region，不要使用region
        if (c.condition.key === 'region' && flight.type !== FlightType.Server) {
          checkResult.push({
            name: '国家字段请使用priority_region，不要使用region',
            level: PCCheckLevel.ERROR,
            type: PCCheckType.RULE,
          } as PCCheckErrorItem);
        }
        // 1.4. 客户端实验存在：device_platform，且至少有选择 windows，mac其中一个
        if (flight.type !== FlightType.Server && c.condition.key === 'device_platform') {
          if (this.isStringArray(c.condition.value)) {
            const hasWindows = c.condition.value.some(v => v.toString().toLowerCase().includes('windows'));
            const hasMac = c.condition.value.some(v => v.toString().toLowerCase().includes('mac'));
            if (!hasWindows && !hasMac) {
              checkResult.push({
                name: '客户端实验存在：device_platform，至少要选择 windows，mac其中一个',
                level: PCCheckLevel.ERROR,
                type: PCCheckType.RULE,
              } as PCCheckErrorItem);
            }
          }
        }
      });
    });
    // 1.5. 普通客户端实验需要填写PSM
    if (FlightType.ClientNormal === flight.type) {
      // 普通客户端实验
      if (!flight?.specified_psms?.includes('toutiao.settings.settings')) {
        checkResult.push({
          name: '普通客户端实验需要填写PSM(toutiao.settings.settings)',
          level: PCCheckLevel.ERROR,
          type: PCCheckType.RULE,
        } as PCCheckErrorItem);
      }
    }
    // - Settings SDK实验：不填，校验
    if (flight.type === FlightType.SettingsClientSDK) {
      if (flight?.specified_psms?.length !== 0) {
        checkResult.push({
          name: 'Settings SDK实验不需要填写PSM',
          level: PCCheckLevel.ERROR,
          type: PCCheckType.RULE,
        } as PCCheckErrorItem);
      }
    }
    // 1.6. 海外实验中应用功能模块需要选择Global
    if (appId === LibraAppIds.cc_pc && !flight.product?.includes('Global')) {
      checkResult.push({
        name: '海外实验中应用功能模块需要选择Global',
        level: PCCheckLevel.ERROR,
        type: PCCheckType.RULE,
      } as PCCheckErrorItem);
    }

    if (flight.type !== FlightType.Server) {
      // pc版本号校验
      const pcVersionCode = this.getPCVersionCode(filter_rule);
      if (pcVersionCode === 0) {
        checkResult.push({
          name: 'pc版本号校验失败,要使用version_code',
          level: PCCheckLevel.ERROR,
          type: PCCheckType.RULE,
        } as PCCheckErrorItem);
      }
      const byteArray = this.toBase256(pcVersionCode);
      // 最后一位必须为0
      if (byteArray[byteArray.length - 1] !== 0) {
        checkResult.push({
          name: 'pc版本号校验失败,版本号转换后不符合x.x.0的规则',
          level: PCCheckLevel.ERROR,
          type: PCCheckType.RULE,
        } as PCCheckErrorItem);
      }

      const strVersion = byteArray.join('');

      // 灰度分群检测
      if (flight.name.includes('灰度')) {
        if (flight.user_tag_is_black === 1) {
          checkResult.push({
            name: '灰度实验灰度分群不应勾选黑名单',
            level: PCCheckLevel.ERROR,
            type: PCCheckType.RULE,
          } as PCCheckErrorItem);
        }
      } else {
        if (flight.user_tag_is_black === 0) {
          checkResult.push({
            name: '非灰度实验灰度分群应勾选黑名单',
            level: PCCheckLevel.ERROR,
            type: PCCheckType.RULE,
          } as PCCheckErrorItem);
        }
      }

      for (const cohort of flight.cohorts) {
        if (!cohort.cohort.cohort_name.includes(strVersion)) {
          checkResult.push({
            name: '存在与版本号不匹配的灰度分群',
            level: PCCheckLevel.ERROR,
            type: PCCheckType.RULE,
          } as PCCheckErrorItem);
        }
      }
    }

    // 指标配置校验, 检查不同的实验组 ：要求key配置相同，仅value不同
    const v1 = Object.keys(JSON.parse(flight.versions[0].config));
    if (flight.type !== FlightType.Server) {
      for (const v of flight.versions) {
        const config = JSON.parse(v.config);
        for (const key in config) {
          if (!v1.includes(key)) {
            checkResult.push({
              name: '指标配置不一致, 要求key配置相同，仅value不同',
              level: PCCheckLevel.ERROR,
              type: PCCheckType.RULE,
            } as PCCheckErrorItem);
            break;
          }
        }
      }
    }

    return { code: 0, msg: 'success', checkResult };
  }
}
