import { BuildMasterInfo, PCInfo } from '@shared/process/versionProcess';
import { VersionStageCheckList } from '@shared/releasePlatform/versionStageInfoCheckList';
import { BmType } from '@shared/bits/bmInfo';
import { CustomBuildRecord } from '@shared/customBuild';
import { FilterRuleDesc } from '@shared/utils/conditionFilter';
import { PlatformType, User } from '@pa/shared/dist/src/core';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';

export enum VersionStageStatus {
  OnProgress = 'OnProgress',
  Complete = 'Complete',
  NotStart = 'Not_Start',
  Fail = 'Fail',
}
export enum VersionProcessStatus {
  OnProgress = 'OnProgress',
  End = 'End',
  Fail = 'Fail',
}

export interface VersionStageMessageCount {
  warning: number;
  approval: number;
}

export enum CheckVersionStatusType {
  AllVersions = 0, // 检查所有版本
  CurrentVersion = 1, // 检查当前版本
}

export enum VersionMrStandard {
  NoLimit,
  SP0P1P2p3,
  SP0P1,
  SP0,
  Prohibited,
}

export interface VersionPhase {
  stage_name: string; // 阶段名称
  display_name: string; // 阶段展示的名称
  bits_calendar_segment: string; // 阶段开始所对应的bits空间中日历的segment名
  start_hour: string; // 阶段开始的具体时间，如：10/19
  start_time_offset?: number; // 阶段开始的时间偏移量，有的阶段时间与bits日历无法完全对应，使用偏移量计算，单位s
  end_calendar_segment?: string; // 阶段结束所对应的bits空间中日历的segment名
  end_hour?: string; // 阶段结束的具体时间，如：10/19
  duration?: number; // 阶段持续时间，如果没有定义end_calendar_segment，就会使用duration计算结束时间，计算逻辑：如果有预计开始时间，结束时间=预计开始时间+duration，如果没有，结束时间=实际开始时间+duration，阶段实际开始时进行计算
  sub_stages: VersionPhase[]; // 子阶段信息
  need_approve_stages?: string[]; // 阶段开始所依赖的阶段，这些阶段如果没完成，阶段不会开始
  mannully_start?: boolean; // 阶段是否不自动开始，由其他逻辑触发开始，比如QA确认等
  extra_data?: any; // 阶段的扩展信息，如灰度版本号
  ignore_checklist?: boolean; // 阶段完成是否不需要checklist不通过
  mr_standard: number; // 可以合入的MR级别
  status_rout: string[]; // 版本节点页面路由（暂时没用上）
  approval_config: []; // 审批配置（暂时没用）
  not_start_desc?: string; // 阶段未开始时的提示
}

export interface VersionTimelineConfig {
  version_stages: VersionPhase[];
}

export interface VersionStageInfo {
  stage_name: string;
  display_name: string;
  start_time: number; // 预期开始时间
  real_start_time: number; // 实际开始时间
  end_time: number; // 预期结束时间
  real_end_time: number; // 实际结束时间
  status: VersionStageStatus; // "OnProgress" | "Delay" | "Complete" ｜ "Not_Start"
  sub_stages: VersionStageInfo[]; // 子轴节点
  status_route?: string[];
  extra_data?: any;
  message_count_map: Record<string, number>;
  is_delay: boolean;
  is_skip?: boolean;
  parent_stage_name?: string;
  mr_standard: number;
  version_phase?: VersionPhase;
}
export interface VersionProcessInfo {
  version: string;
  app_id: number;
  version_stages: VersionStageInfo[];
  // @ts-ignore
  bmInfo: Record<number, BuildMasterInfo>;
  pcInfo?: PCInfo;
  meegoId: number;
  status: VersionProcessStatus;
  bmReleaseGroupChatId?: string; // bm发版群
  versionReleaseGroupChatId?: string; // 版本大群
  version_reject_group_chat_id?: string;
  is_inhouse?: number;
  overseas_branch_checkouted?: boolean;
  releaseVersionType?: ReleaseVersionType;
  backupTime?: number; // 版本更新时间
  versionReviewDoc?: string; // 复盘文档Url
  versionReviewDocOfRD?: string; // 复盘文档Url RD侧
  versionReviewDocofQA?: string; // 复盘文档Url QA侧
  versionExceptionReviewDoc?: string; // 异常情况记录文档Url
  is_crucial?: boolean; // 是否为关键版本
}

export interface SubIntegrationExtraData {
  build_id: string;
  build_record?: CustomBuildRecord;
  cc_build_id: string;
  cc_build_record?: CustomBuildRecord;
}

export interface iOSUserStoryExtraData {
  app_store_version: string; // 正式包版本号
  tf_debug_version: string; // 带调试页tf包版本号
}

export interface VersionStageChecklistStatus {
  versionInfo: VersionProcessInfo;
  stageCheckStatus: { stageInfo: VersionStageInfo; checklist: VersionStageCheckList }[];
}

export enum VersionSkipEvaluateStatus {
  NotStart = 0, // 未发起评估
  OnProgress = 1, // 评估中
  Complete = 2, // 评估通过，跳版
}

export interface VersionSkipInfo {
  version: string;
  app_id: number;
  reason_desc: string;
  responsible_peoples: User[];
  responsible_teams: string[];
  attributions: string[];
  skip_status: VersionSkipEvaluateStatus;
  skip_evaluate_chat?: string;
}

export const currentStage = (info: VersionProcessInfo) => {
  let currentIndex = 0;
  for (const versionStage of info.version_stages) {
    if (versionStage.status === VersionStageStatus.OnProgress) {
      let subStageIndex = 0;
      for (const subStage of versionStage.sub_stages) {
        if (subStage.status === VersionStageStatus.OnProgress) {
          return {
            currentIndex,
            subStageIndex,
            currentStage: versionStage,
            currentSubStage: subStage,
          };
        }
        subStageIndex++;
      }
      return {
        currentIndex,
        subStageIndex: undefined,
        currentStage: versionStage,
        currentSubStage: undefined,
      };
    }
    currentIndex++;
  }
  return undefined;
};

export const latestFinishedStage = (info: VersionProcessInfo) => {
  for (let i = info.version_stages.length - 1; i >= 0; i--) {
    if (info.version_stages[i].status === VersionStageStatus.Complete) {
      for (let j = info.version_stages[i].sub_stages.length - 1; j >= 0; j--) {
        if (info.version_stages[i].sub_stages[j].status === VersionStageStatus.Complete) {
          return {
            currentIndex: i,
            subStageIndex: j,
            currentStage: info.version_stages[i],
            currentSubStage: info.version_stages[i].sub_stages[j],
          };
        }
      }
      return {
        i,
        subStageIndex: undefined,
        currentStage: info.version_stages[i],
        currentSubStage: undefined,
      };
    }
  }
  return undefined;
};

export const filterOnprogressStages = (stages: VersionStageInfo[]): VersionStageInfo[] => {
  const res: VersionStageInfo[] = [];
  stages.forEach(it => {
    if (it.status !== VersionStageStatus.OnProgress) {
      return;
    }
    if (it.sub_stages.length === 0) {
      res.push(it);
    } else {
      res.push(...filterOnprogressStages(it.sub_stages));
    }
  });
  return res;
};

export const getOnProgressStages = (info: VersionProcessInfo) => filterOnprogressStages(info.version_stages);

export const competeStage = (stage: VersionStageInfo) => {
  stage.status = VersionStageStatus.Complete;
  stage.real_end_time = Math.floor(new Date().getTime() / 1000);
  stage.sub_stages.forEach(it => competeStage(it));
};

export const turnStageToSpecificSubStage = (stage: VersionStageInfo, subStageName: string) => {
  const subStageInfo = stage.sub_stages.find(it => it.stage_name === subStageName);
  if (!subStageInfo) {
    return;
  }
  for (const subStage of stage.sub_stages) {
    if (subStage.stage_name === subStageName) {
      if (subStage.status === VersionStageStatus.NotStart) {
        const curTime = Date.now() / 1000;
        subStage.status = VersionStageStatus.OnProgress;
        subStage.real_start_time = curTime;
        if (subStage.start_time === 0 && subStage.end_time === 0) {
          subStage.end_time = curTime + (subStage.version_phase?.duration ?? 0);
        }
        subStage.status = VersionStageStatus.OnProgress;
      }
      break;
    } else {
      competeStage(subStage);
    }
  }
};

export const isToday = (timestamp: number): boolean => {
  const today = new Date();
  const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

  const targetDate = new Date(timestamp);

  return targetDate >= startOfToday && targetDate < endOfToday;
};

export const formatTimestamp2Month = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    hour: 'numeric',
    minute: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Asia/Shanghai',
  });
};
export const formatTimestampWithYear = (timestamp: number | undefined): string => {
  if (!timestamp) {
    return '';
  }
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: 'numeric',
    minute: 'numeric',
  });
};

export const formatTimestamp2Hour = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', { hour: 'numeric', minute: 'numeric', timeZone: 'Asia/Shanghai' });
};

export const formatTimestampWithoutHour = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric', timeZone: 'Asia/Shanghai' });
};

export const isAfterFifthGray = (versionInfo: VersionProcessInfo, stageInfo: VersionStageInfo): boolean => {
  if (versionInfo.app_id !== 177502 && versionInfo.app_id !== 300602) {
    return false;
  }
  const splitName = stageInfo.stage_name.split('@');
  if (splitName.length === 2 && Number(splitName[1]) > 5) {
    return true;
  }
  return false;
};

export const isRetouchApp = (appId: number): boolean =>
  appId === 251501 || appId === 251502 || appId === 2020093988 || appId === 2020093924;

export const isDreaminaApp = (appId: number): boolean => appId === 244127338754 || appId === 225469550850;

export const isPippitApp = (appId: number): boolean =>
  appId === AppSettingId.PIPPIT_ANDROID || appId === AppSettingId.PIPPIT_IOS;

export const isTinycutApp = (appId: number): boolean =>
  appId === AppSettingId.TINYCUT_ANDROID || appId === AppSettingId.TINYCUT_IOS;

// NOTE 多端接入配置

export const isPCApp = (appId: number): boolean =>
  appId === 2020092383 || appId === 2020092892 || appId === 35928901 || appId === 35928902;

export const releasePlatformManagerEmails = () => [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  // 以下PC端固定BM管理员
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  // Dreamina临时管理员
  '<EMAIL>',
  // 剪映BM
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const versionManager = (versionInfo: VersionProcessInfo) => {
  const managerEmails = releasePlatformManagerEmails();
  if (versionInfo.bmInfo) {
    managerEmails.push(versionInfo.bmInfo[BmType.rd]?.email ?? 'rd');
    managerEmails.push(versionInfo.bmInfo[BmType.qa]?.email ?? 'qa');
  }
  return managerEmails;
};

// 参数介绍 https://bytedance.larkoffice.com/wiki/HClNwcwZiifbookC82fcJUaLnEe
export enum ReleasePlatformUrlSearchParams {
  Version = 'version',
  AppId = 'appid',
  MainStage = 'main_stage',
  SubStage = 'sub_stage',
  StageInfoOpen = 'stage_info_open',
  StageInfoRoute = 'stage_info_route',
  VersionInfoSheet = 'version_info_sheet',
  TeaMetricSheet = 'tea_metric_sheet',
  StabilityMetrixSheet = 'stability_metrix_sheet',
  BugExemptSheet = 'bug_exempt_sheet',
  BugExemptBusiness = 'bug_exempt_business',
  BusinessBugResloveExemptSheet = 'business_bug_reslove_exempt_sheet',
  ManualAssessmentSheet = 'manual_assessment_sheet',
  CheckItemId = 'check_item_id',
  ShowDetail = 'show_detail',
  FocusCheckItem = 'focus_check_item',
  QATestProgress = 'qa_test_progress',
  QAUserStoryVersion = 'qa_user_story_version',
  TicketApprovalDetail = 'ticket_approval_detail',
  ReleaseSubmitInfo = 'release_submit_info',
  ShowNotHandledFeedback = 'show_not_handled_feedback',
  SelectedFeedback = 'selected_feedback',
  FeedbackFilterOptions = 'feedback_filter_options',
  LibraGrayExemptSheet = 'libra_gray_exempt_sheet',
}

export enum VersionType {
  SmallFlow = '小流量',
  FixVersion = '小版本',
  RegularVersion = '常规版本',
}

export interface VersionProcessOverview {
  appId: number;
  product: string;
  version: string;
  platform: PlatformType;
  versionType: VersionType;
  versionStatus: string;
  codeFrozenTime: number;
  publishStage: string;
  nextStage: string;
  fullReleaseTime: string;
  grayCount: number;
  fixVersionCount: number;
  hotfixCount: number;
  RDBM: BuildMasterInfo;
  QABM: BuildMasterInfo;
  currentExceptionInfo: string;
  detailMessage: string;
  exceptionWarning?: string;
  historyExceptionInfo?: string[];
  detaileMessageUpdateTime?: number;
  isInhouse?: number;
  grayCircuitBreakerCount?: number;
  affectGrayCount?: number;
}

export interface VersionProcessOverviewFilter {
  // product: string[];
  // version: string[];
  // platform: PlatformType[];
  // versionType: VersionType[];
  // versionStatus: string[];
  userEmail: string;
  filterMap: Map<string, FilterRuleDesc>;
}
export enum ReleaseStage {
  FixVersion = 'fixVersion', // 小版本阶段
  Submit = 'submit', // ios全量阶段
  LvAdrSubmit = 'adr_lv_submit', // 剪映Android全量阶段
  CCAdrSubmit = 'adr_cc_submit', // CapCut Android全量阶段
  retouchAdrSubmit = 'retouch_adr_submit', //  hypic、Retouch Android全量阶段
  retouchIosSubmit = 'retouch_ios_submit', // Retouch iOS全量阶段
  hypicIosSubmit = 'hypic_ios_submit', // hypic iOS全量阶段
}

export enum ReleaseVersionType {
  Normal = 'normal', // 常规版本
  fixVersion = 'fixVersion', // 小版本
}

export class FixVersionExtraData {
  version: string;
}
