import { ApprovalHand<PERSON> } from '../ApprovalHandler';
import {
  ApprovalInstanceEvent,
  ApprovalTaskEvent,
  ApprovalTaskStatus,
  LarkApprovalStatus,
} from '@pa/shared/dist/src/lark/approval';
import { ApprovalDBService } from '../../ApprovalDBService';
import {
  ApprovalDetail,
  ApprovalHistoryEvent,
  ApprovalOrderStatus,
  ApprovalType,
} from '@shared/approval/ApprovalOrder';
import VersionTransactionDao from '../../../dao/releasePlatform/VersionTransactionDao';
import { useInject } from '@edenx/runtime/bff';
import { Logger } from '@gulux/gulux';
import { ApprovalInfoTable } from '../../../../model/approval/ApprovalInfoModel';
import {
  RequiredTrasactionCreateParams,
  VersionTransactionBinding,
  VersionTransactionType,
} from '@shared/releasePlatform/versionTransaction';
import { teaCollect, TeaEvent } from '../../../../tea';
import ApprovalService from '../../ApprovalService';
import { dayjs } from '@pa/shared/dist/src/utils/dayjs';
import { LarkClient } from '@gulux/gulux/lark';

export class RetouchTicketApprovalHandler extends ApprovalHandler {
  name = 'RetouchTicketApprovalHandler';
  approvalCode = '054352F0-AD67-4DF3-B9BD-C959D6F24837';

  override async handle(event: ApprovalInstanceEvent) {
    const approvalDBService = useInject(ApprovalDBService);
    const logger = useInject(Logger);
    logger.info(`[RetouchTicketApprovalHandler] handle => ${JSON.stringify(event)}`);
    const approval = await approvalDBService.findOne(event);
    if (approval) {
      logger.info(`[RetouchTicketApprovalHandler] findOne approval => ${JSON.stringify(approval)}`);
      if (approval.approvalStatus === ApprovalOrderStatus.STOP) {
        logger.info(`[${this.name}] approval => ${JSON.stringify(approval)}, do not to handle.`);
        return false;
      }
      if (event.status === LarkApprovalStatus.APPROVED) {
        // 审批通过
        await this.handleApproved(approval);
        const transactionInfo = await this.constructVersionTransactionInfo(approval);
        if (transactionInfo) {
          const transactionDao = useInject(VersionTransactionDao);
          await transactionDao.createVersionTransaction(transactionInfo);
        }
      } else if (event.status === LarkApprovalStatus.REJECTED) {
        // 审批拒绝
        await this.handleRejected(approval);
      }
      return true;
    } else {
      logger.info(`[RetouchTicketApprovalHandler] handle not find approval => ${JSON.stringify(event)}`);
    }
    return false;
  }

  async handleTask(event: ApprovalTaskEvent): Promise<boolean> {
    const approvalDBService = useInject(ApprovalDBService);
    const logger = useInject(Logger);
    logger.info(`[${this.name}] handleTask => ${JSON.stringify(event)}`);
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: event.instance_code,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }
    const instance = rsp?.data;
    // 查找审批节点
    const selfCheckTask = instance?.task_list?.find(it => it?.node_id === 'fad9ecca88c8afaf3d02d148c2efef46');
    const firstAssessmentTaskKeyName = '0a15fe21862b5dedfa330ccd9440a5b1'; // 需求质量影响面判断
    const secondAssessmentTaskKeyName = '7d0a7af62a2d2e3000d7dc74e8794c27'; // 未达标项解决预期时间合理性判断
    const finalAssessmentTaskKeyName = 'f8dbb3a422e97f4abf3b253ee988e144'; // 插入必要性判断
    const firstAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === firstAssessmentTaskKeyName);
    const secondAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === secondAssessmentTaskKeyName);
    const finalAssessmentTaskList = instance?.task_list?.filter(it => it?.node_id === finalAssessmentTaskKeyName);

    const approval = await approvalDBService.findOneByTask(event);
    const approvalService = useInject(ApprovalService);
    if (approval) {
      if (approval.approvalStatus === ApprovalOrderStatus.STOP) {
        logger.info(`[${this.name}] approval => ${JSON.stringify(approval)}, do not to handle.`);
        return false;
      }
      if (event.status === ApprovalTaskStatus.APPROVED) {
        // 自检查节点通过，发第一批推送
        if (event.task_id === selfCheckTask?.id) {
          await approvalService.retouchTicketApprovalCommonAssessment(approval, firstAssessmentTaskKeyName);
        }
        // 需求质量影响面判断审核通过，发第二批推送
        if (
          firstAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          firstAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchTicketApprovalCommonAssessment(approval, secondAssessmentTaskKeyName);
        }
        // 未达标项解决预期时间合理性审批通过，发第三批推送
        if (
          secondAssessmentTaskList?.map(it => it?.id)?.includes(event.task_id) &&
          secondAssessmentTaskList?.every(it => it?.status !== 'PENDING' && it?.status !== 'REJECTED')
        ) {
          await approvalService.retouchTicketApprovalCommonAssessment(approval, finalAssessmentTaskKeyName);
        }
      } else if (event.status === ApprovalTaskStatus.REJECTED) {
        // 审批拒绝
      }
      return true;
    } else {
      logger.info(`[${this.name}] handle not find approval => ${JSON.stringify(event)}`);
    }
    return false;
  }

  constructVersionTransactionInfo(approval?: ApprovalInfoTable): RequiredTrasactionCreateParams | undefined {
    if (!approval) {
      return undefined;
    }
    const bindings: VersionTransactionBinding[] = [
      {
        app_id: approval.app_id,
        version: approval.version,
      },
    ];
    return {
      type: VersionTransactionType.TicketApprove,
      bindings,
      extra: {
        approvalDetail: approval as ApprovalDetail,
      },
    } as RequiredTrasactionCreateParams;
  }

  private async handleApproved(approval?: ApprovalInfoTable) {
    const logger = useInject(Logger);
    logger.info(`[RetouchTicketApprovalHandler] handleApproved approval:${JSON.stringify(approval)} `);
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.TicketsRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_APPROVED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const approvalService = useInject(ApprovalService);
    const approvalDBService = useInject(ApprovalDBService);
    // TODO 审批通过后自动发放票据，先hold
    // const result = await approvalService.giveTicketToBits(approval);
    // TODO 方法票据的信息通知, 审批单落库
    // logger.info(`[RetouchTicketApprovalHandler] giveTicketToBits result:${JSON.stringify(result)} `);

    const instanceCode = approval?.instanceCode;
    if (instanceCode) {
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Completed);
    }
    return true;
  }

  private async handleRejected(approval?: ApprovalInfoTable) {
    const logger = useInject(Logger);
    logger.info(`[RetouchTicketApprovalHandler] handleRejected approval:${JSON.stringify(approval)} `);
    teaCollect(TeaEvent.APPROVAL, {
      approval_type: ApprovalType.TicketsRequirements,
      approval_event: ApprovalHistoryEvent.APPROVAL_REJECTED,
      cost: approval?.createTimeStamp ? dayjs().unix() - approval?.createTimeStamp : -1,
      version: approval?.version,
      isDev: process.env.NODE_ENV === 'development',
    });
    const approvalDBService = useInject(ApprovalDBService);
    // TODO 方法票据的信息通知
    const instanceCode = approval?.instanceCode;
    if (instanceCode) {
      await approvalDBService.updateApprovalStatus(instanceCode, ApprovalOrderStatus.Rejected);
    }
  }
}
