import { Inject, Injectable } from '@gulux/gulux';
import { LarkClient, LarkHttpResponseError } from '@gulux/gulux/lark';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { LarkResult } from '@pa/shared/dist/src/lark/larkResult';
import {
  RangeStyle,
  SheetItemElement,
  SheetMetaItem,
  SpreadSheetCreateRspItem,
  UpdateSheetPropertyData,
} from '@pa/shared/dist/src/lark/sheetItem';
import { Block, Descendant, DocMember, TextElement } from '@pa/shared/dist/src/lark/doc';
import axios from 'axios';
import { LarkResultCopyFile, LarkResultRootFolderMetaData } from '@pa/shared/dist/src/lark/larkResultData';
import { LarkFileType } from '@pa/shared/dist/src/lark/larkFileType';

@Injectable()
export default class LarkDocService {
  @Inject()
  private lark: LarkClient;

  @Inject()
  private logger: BytedLogger;

  /**
   * 获取空间节点信息
   */
  async getNodeInfo(wikiNodeId: string) {
    return await this.lark.wiki.space.getNode({
      params: {
        token: wikiNodeId,
      },
    });
  }

  /**
   * 获取 Wiki 的 token
   */
  async getWikiNodeToken(wikiNodeId: string) {
    const nodeRsp = await this.getNodeInfo(wikiNodeId);
    return nodeRsp?.data?.node?.obj_token;
  }

  /**
   * 提取路径最后一个部分（迁移自 CUBA）
   */
  extractPathLastPart(url: string) {
    try {
      if (url && url.startsWith('http')) {
        const path = new URL(url).pathname;
        const index = path.lastIndexOf('/');
        if (index !== -1) {
          return path.substring(index + 1);
        }
      }
    } catch (error) {
      console.log(error);
    }
    return undefined;
  }

  /**
   * 获取文档的 id（迁移自 CUBA）
   */
  async getDocumentIdByUrl(docUrl: string) {
    let documentId;
    let type = '';
    if (docUrl.includes('/docx/')) {
      // 个人空间文档，大概率解析失败
      documentId = this.extractPathLastPart(docUrl);
      type = 'doc';
    } else if (docUrl.includes('/wiki/')) {
      // wiki文档理论上没问题
      type = 'wiki';
      const wikiNodeId = this.extractPathLastPart(docUrl);
      if (wikiNodeId) {
        try {
          documentId = await this.getWikiNodeToken(wikiNodeId);
        } catch (e) {
          // 没权限等情况都可能出错
          console.error(e);
        }
      }
    }
    return { documentId, type };
  }

  /**
   * 获取文档基本信息（迁移自 CUBA）
   */
  async getDocumentBasicInfo(documentId: string) {
    const rsp = await this.lark.docx.document.get({
      path: {
        document_id: documentId,
      },
    });
    return rsp.data?.document;
  }

  /**
   * 检查文档权限（迁移自 CUBA）
   */
  async checkDocumentPermission(documentId: string) {
    let docName;
    try {
      docName = (await this.getDocumentBasicInfo(documentId))?.title;
    } catch (e) {
      if (e instanceof LarkHttpResponseError) {
        // e.response.status === 403
        // e.message Request failed with status code 403
      }
      return false;
    }
    return true;
  }

  /**
   * 获取文档的所有快（迁移自 CUBA）
   */
  async listAllBlocks(documentId: string) {
    let has_more = true;
    const items = [];
    let page_token = '';

    while (has_more) {
      const payload: any = {
        path: {
          document_id: documentId,
        },
      };
      if (page_token) {
        payload.params = {
          page_token,
        };
      }
      const rsp = await this.lark.docx.documentBlock.list(payload);
      items.push(...(rsp.data?.items ?? []));
      has_more = rsp.data?.has_more ?? false;
      page_token = rsp.data?.page_token ?? '';
    }
    return items;
  }

  /**
   * 给定锚点的type和文案，找到下面第一个符合要求的block（迁移自 CUBA）
   * block_type定义: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/document-docx/docx-v1/document-block-children/get
   */
  async findTargetBlocksFollowedAnchor(
    documentId: string,
    params: {
      anchor_block_type: 3 | 4 | 5 | 6 | 7 | (3 | 4 | 5 | 6 | 7)[]; // 3: heading1 4: heading2, 5:heading3
      anchor_text: string;
      targetblock_type: number;
    },
  ) {
    const blockType2Key: Record<number, string> = {
      // 2: 'text',
      3: 'heading1',
      4: 'heading2',
      5: 'heading3',
      6: 'heading4',
      7: 'heading5',
    };
    const targetBlockType = [params.anchor_block_type].flat();
    const allBlocks = await this.listAllBlocks(documentId);
    let index =
      allBlocks?.findIndex(
        (block: any) =>
          // 可能由于评论等导致分成多个blcok
          targetBlockType.includes(block.block_type) &&
          block[blockType2Key[block.block_type]]?.elements
            ?.map((element: TextElement) => element?.text_run?.content ?? '')
            ?.join('')
            ?.includes(params.anchor_text),
      ) ?? -1;

    if (index < 0) {
      return undefined;
    }

    const targetBlocks = [];
    while (++index < (allBlocks?.length ?? 0)) {
      if (allBlocks?.[index]?.block_type === params.targetblock_type) {
        targetBlocks.push(allBlocks?.[index]);
      }
    }
    return targetBlocks;
  }

  /**
   * 获取表格所有记录
   */
  async getTableAllRows(
    tableId: string,
    tableParam?: { viewId?: string; appToken?: string; wikiNodeId?: string },
    options?: { filter?: string; text_field_as_array?: boolean },
  ) {
    if (tableParam?.wikiNodeId) {
      const nodeRsp = await this.getNodeInfo(tableParam.wikiNodeId);
      tableParam.appToken = nodeRsp?.data?.node?.obj_token;
    }
    const payload: any = {
      path: {
        app_token: tableParam?.appToken ?? '',
        table_id: tableId,
      },
      params: {
        page_size: 500,
        text_field_as_array: options?.text_field_as_array ?? false,
      },
    };
    if (tableParam?.viewId) {
      payload.params['view_id'] = tableParam.viewId;
    }
    if (options?.filter) {
      payload.params.filter = options?.filter;
    }

    let result: any[] = [];
    let hasMore = true;
    let pageToken;
    while (hasMore) {
      if (pageToken) {
        payload.params['page_token'] = pageToken;
      }
      const allRowsRsp = await this.lark.bitable.appTableRecord.list(payload);
      hasMore = allRowsRsp.data?.has_more ?? false;
      pageToken = allRowsRsp.data?.page_token;
      result = result.concat(allRowsRsp.data?.items ?? []);
    }
    return result as { fields: any; record_id: string }[];
  }

  /**
   * 批量插入记录
   *
   * 返回数组：每批次更新的code和msg
   */
  async batchInsertRecords(
    tableId: string,
    records: any[],
    appToken?: string,
    params?: {
      user_id_type?: 'user_id' | 'union_id' | 'open_id';
      text_field_as_array?: boolean;
    },
  ) {
    const fields: { fields: any }[] = [];
    records.forEach(element => {
      fields.push({
        fields: element,
      });
    });
    console.log(`batchInsertRecords: insert records: length = ${fields.length}`);
    console.log(`batchInsertRecords: insert records: ${JSON.stringify(fields)}`);
    const n = Math.floor(fields.length / 500) + 1;
    let batchFields;
    const resultCodeList = [];
    for (let i = 0; i < n; i++) {
      batchFields = fields.slice(i * 500, Math.min(fields.length, (i + 1) * 500));
      console.log('batchInsertRecords: i = %d, length: %d', i, batchFields.length);
      const batchCreateRsp = await this.lark.bitable.appTableRecord.batchCreate({
        path: {
          app_token: appToken ?? '',
          table_id: tableId,
        },
        data: {
          records: batchFields,
        },
        params,
      });
      resultCodeList.push({ code: batchCreateRsp.code, msg: batchCreateRsp.msg });
      console.log(`batchInsertRecords: batchCreateRsp: i: ${i}${JSON.stringify(batchCreateRsp)}`);
    }
    return resultCodeList;
  }

  /**
   * 批量更新记录， 一次调用只能500条，分批更新
   * 返回数组：每批次更新的code和msg
   */
  async batchUpdate(
    tableId: string,
    records: {
      fields: any;
      record_id: string;
    }[],
    params?: {
      user_id_type?: 'user_id' | 'union_id' | 'open_id';
      text_field_as_array?: boolean;
    },
    appToken?: string | undefined,
  ) {
    const n = Math.floor(records.length / 500) + 1;
    let batchRecords;
    const resultCodeList = [];
    for (let i = 0; i < n; i++) {
      batchRecords = records.slice(i * 500, Math.min(records.length, (i + 1) * 500));
      console.log('batchUpdate: i = %d, length: %d', i, batchRecords.length);
      const batchUpdateRsp = await this.lark.bitable.appTableRecord.batchUpdate({
        data: {
          records: batchRecords,
        },
        path: {
          app_token: appToken ?? '',
          table_id: tableId,
        },
        params,
      });

      resultCodeList.push({ code: batchUpdateRsp.code, msg: batchUpdateRsp.msg });
      console.log(`batchCreateRsp: i: ${i}${JSON.stringify(batchUpdateRsp)}`);
    }
    return resultCodeList;
  }

  async batchDeleteRecords(tableId: string, recordIds: string[], appToken?: string) {
    const n = Math.floor(recordIds.length / 500) + 1;
    let batchRecords;
    const resultCodeList = [];
    for (let i = 0; i < n; i++) {
      batchRecords = recordIds.slice(i * 500, Math.min(recordIds.length, (i + 1) * 500));
      console.log('batchDeleteRecords: i = %d, length: %d', i, batchRecords.length);
      const batchDeleteRsp = await this.lark.bitable.appTableRecord.batchDelete({
        data: {
          records: recordIds,
        },
        path: {
          app_token: appToken ?? '',
          table_id: tableId,
        },
      });
      resultCodeList.push({ code: batchDeleteRsp.code, msg: batchDeleteRsp.msg });
      console.log(`batchDeleteRsp: i: ${i}${JSON.stringify(batchDeleteRsp)}`);
    }
    return resultCodeList;
  }

  /**
   * 创建电子表格
   * API: https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet/create
   */
  async createSpreadSheet(title: string, folder_token: string): Promise<SpreadSheetCreateRspItem> {
    const result = await this.lark.request<LarkResult<{ spreadsheet: SpreadSheetCreateRspItem }>>({
      url: `/open-apis/sheets/v3/spreadsheets`,
      method: 'POST',
      data: {
        title,
        folder_token,
      },
    });
    this.logger.info(
      `[createSpreadSheet] title: ${title}, folder_token: ${folder_token}, rsp: ${JSON.stringify(result)}`,
    );
    if (result.code === 0) {
      return result.data.spreadsheet;
    } else {
      throw new Error(
        `[createSpreadSheet] failed! title: ${title}, folder_token: ${folder_token}, code: ${result.code}`,
      );
    }
  }

  /**
   * 获取工作表
   * API: https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet-sheet/query
   */
  async querySheets(spreadsheet_token: string): Promise<SheetMetaItem[]> {
    const result = await this.lark.request<LarkResult<{ sheets: SheetMetaItem[] }>>({
      url: `/open-apis/sheets/v3/spreadsheets/${spreadsheet_token}/sheets/query`,
      method: 'GET',
    });
    this.logger.info(`[querySheets] spreadsheet_token: ${spreadsheet_token}, rsp: ${JSON.stringify(result)}`);
    if (result.code === 0) {
      return result.data.sheets;
    } else {
      throw new Error(`[querySheets] failed! spreadsheet_token: ${spreadsheet_token}, code: ${result.code}`);
    }
  }

  /**
   * 创建工作表
   * API：https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet-sheet/create
   */
  async createSheet(spreadsheet_token: string, title?: string, index?: number): Promise<SheetMetaItem> {
    const result = await this.lark.request<LarkResult<{ sheet: SheetMetaItem }>>({
      url: `/open-apis/sheets/v3/spreadsheets/${spreadsheet_token}/sheets`,
      method: 'POST',
      data: {
        title,
        index,
      },
    });
    this.logger.info(
      `[createSheet] title: ${title ?? 'unknown title'}, index: ${index ?? 'unknown index'} spreadsheet_token: ${spreadsheet_token}, rsp: ${JSON.stringify(result)}`,
    );
    if (result.code === 0) {
      return result.data.sheet;
    } else {
      throw new Error(
        `[createSheet] failed! title: ${title ?? 'unknown title'}, index: ${index ?? 'unknown index'} spreadsheet_token: ${spreadsheet_token}, code: ${result.code}`,
      );
    }
  }

  /**
   * 删除工作表
   * API：https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet-sheet/delete
   */
  async deleteSheet(spreadsheet_token: string, sheetId: string): Promise<SheetMetaItem> {
    const result = await this.lark.request<LarkResult<{ sheet: SheetMetaItem }>>({
      url: `/open-apis/sheets/v3/spreadsheets/${spreadsheet_token}/sheets/${sheetId}`,
      method: 'DELETE',
    });
    this.logger.info(
      `[deleteSheet] spreadsheet_token: ${spreadsheet_token}, sheetId: ${sheetId}, rsp: ${JSON.stringify(result)}`,
    );
    if (result.code === 0) {
      return result.data.sheet;
    } else {
      throw new Error(
        `[deleteSheet] failed! spreadsheet_token: ${spreadsheet_token}, sheetId: ${sheetId}, code: ${result.code}`,
      );
    }
  }

  getSheetColumnIndexLabel(columnCount: number): string {
    let label = '';
    let copyColumnCount = columnCount;
    while (copyColumnCount > 0) {
      const remainder = (copyColumnCount - 1) % 26;
      label = String.fromCharCode(65 + remainder) + label; // 65 是 'A' 的 ASCII 码
      copyColumnCount = Math.floor((copyColumnCount - 1) / 26);
    }
    return label;
  }

  /**
   * 向工作表写入数据
   * API: https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet-sheet-value/append
   */
  async writeToSheet(
    spreadsheet_token: string,
    sheet_id: string,
    values: SheetItemElement[][][],
    cusRange?: string,
  ): Promise<LarkResult<any>> {
    // 单元格数据结构：https://open.larkoffice.com/document/server-docs/docs/sheets-v3/spreadsheet-sheet-value/cell-data-structure
    // values 字段为三维数组，第一维为行，第二维为列
    // 获取行数
    const rowCount = values.length;
    // 获取每一行的列数（假设每一行的列数不一致，取最大值）
    const colCount = Math.max(...values.map(row => row.length));
    const columnIndex = this.getSheetColumnIndexLabel(colCount);
    const range = cusRange ?? `${sheet_id}!A1:${columnIndex}${rowCount}`;
    try {
      const result = await this.lark.request({
        url: `/open-apis/sheets/v3/spreadsheets/${spreadsheet_token}/sheets/${sheet_id}/values/${range}/append`,
        method: 'POST',
        data: {
          values,
        },
      });
      this.logger.info(
        // eslint-disable-next-line max-len
        `[writeToSheet] spreadsheet_token: ${spreadsheet_token}, sheet_id: ${sheet_id}, rsp: ${JSON.stringify(result)}`,
      );
      console.log(
        // eslint-disable-next-line max-len
        `[writeToSheet] spreadsheet_token: ${spreadsheet_token}, sheet_id: ${sheet_id}, rsp: ${JSON.stringify(result)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `[writeToSheet] spreadsheet_token: ${spreadsheet_token}, sheet_id: ${sheet_id}, error: ${JSON.stringify(
          error,
        )}`,
      );
      console.error(
        `[writeToSheet] spreadsheet_token: ${spreadsheet_token}, sheet_id: ${sheet_id}, error: ${JSON.stringify(
          error,
        )}`,
      );
      return {
        code: -1,
        msg: `writeToSheet failed! spreadsheet_token: ${spreadsheet_token}, sheet_id: ${sheet_id}`,
        data: {},
      };
    }
  }

  async transDocOwner(docId: string, newOwnerEmail: string) {
    const token = docId;
    await this.lark.request({
      url: `/open-apis/drive/v1/permissions/${token}/members/transfer_owner`,
      method: 'POST',
      params: {
        type: 'docx',
      },
      data: {
        member_type: 'email',
        member_id: newOwnerEmail,
      },
    });
  }

  async createDoc(title: string, ownerEmail?: string, folderToken?: string): Promise<any> {
    const result = await this.lark.request({
      url: `/open-apis/docx/v1/documents`,
      method: 'POST',
      data: {
        folder_token: folderToken,
        title,
      },
    });
    if (ownerEmail) {
      try {
        const token = result.data.document.document_id;
        await this.lark.request({
          url: `/open-apis/drive/v1/permissions/${token}/members/transfer_owner`,
          method: 'POST',
          params: {
            type: 'docx',
          },
          data: {
            member_type: 'email',
            member_id: ownerEmail,
          },
        });
      } catch (e) {
        console.log(e);
      }
    }
    return result;
  }

  async creatDocBlock(docId: string, blockId: string, block: Block[]): Promise<any> {
    const result = await this.lark.request({
      url: `/open-apis/docx/v1/documents/${docId}/blocks/${blockId}/children`,
      method: 'POST',
      data: {
        children: block,
      },
    });
    return result;
  }

  async creatDocDescendant(docId: string, blockId: string, descendant: Descendant): Promise<any> {
    const result = await this.lark.request({
      url: `open-apis/docx/v1/documents/${docId}/blocks/${blockId}/descendant`,
      method: 'POST',
      data: descendant,
    });
    return result;
  }

  /**
   * 向单个范围写入数据
   */
  async writeSheetValues(fileToken: string, range: string, data: any[][]): Promise<any> {
    const path = `/open-apis/sheets/v2/spreadsheets/${fileToken}/values`;
    const result = this.lark.request({
      url: path,
      method: 'PUT',
      data: {
        valueRange: {
          range,
          values: data,
        },
      },
    });
    this.logger.info(`writeSheetValues : ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * 向多个范围写入数据
   */
  async writeMultiSheetValues(fileToken: string, valueRanges: any[]): Promise<any> {
    const path = `/open-apis/sheets/v2/spreadsheets/${fileToken}/values_batch_update`;
    const result = await this.lark.request({
      url: path,
      method: 'POST',
      data: {
        valueRanges,
      },
    });
    this.logger.info(`writeMultiSheetValues : ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * 合并单元格
   */
  async mergeSheetCells(fileToken: string, range: string, mergeType = 'MERGE_ALL'): Promise<any> {
    const path = `/open-apis/sheets/v2/spreadsheets/${fileToken}/merge_cells`;
    const result = await this.lark.request({
      url: path,
      method: 'POST',
      data: {
        range,
        mergeType,
      },
    });
    this.logger.info(`mergeSheetCells : ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * 更新工作表属性
   */
  async updateSheetProperties(fileToken: string, properties: UpdateSheetPropertyData[]): Promise<any> {
    const path = `/open-apis/sheets/v2/spreadsheets/${fileToken}/sheets_batch_update`;
    const result = await this.lark.request({
      url: path,
      method: 'POST',
      data: {
        requests: properties,
      },
    });
    this.logger.info(`updateSheetProperties : ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * 更新行列
   */
  async updateSheetDimension(fileToken: string, dimensionUpdateInfo: any): Promise<any> {
    const path = `/open-apis/sheets/v2/spreadsheets/${fileToken}/dimension_range`;
    const result = await this.lark.request({
      url: path,
      method: 'PUT',
      data: dimensionUpdateInfo,
    });
    this.logger.info(`updateSheetDimension : ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * 批量修改单元格样式
   */
  async batchUpdateSheetCellStyle(fileToken: string, data: RangeStyle[]): Promise<any> {
    const path = `/open-apis/sheets/v2/spreadsheets/${fileToken}/styles_batch_update`;
    const result = await this.lark.request({
      url: path,
      method: 'PUT',
      data: {
        data,
      },
    });
    this.logger.info(`batchUpdateSheetCellStyle : ${JSON.stringify(result)}`);
    return result;
  }

  /**
   * doc获取的block_type为30的电子表格的token，类似J3pxsyvv2hxkiTtartxcptZQnFc_g2PQKf，包含了spreadsheet_token和sheet_id（迁移自 CUBA）
   * range: example g2PQKf!L2:L13, sheet_id!lefttop:rightbottom
   */
  async getSpreadSheetPlainText(spreadsheet_token: string, sheet_id: string, ranges: string[]) {
    const path = `/open-apis/sheets/v3/spreadsheets/${spreadsheet_token}/sheets/${sheet_id}/values/batch_get_plain`;
    return this.lark.request<{
      code: number;
      msg: string;
      data: { value_ranges: { range: string; values: string[][] }[] };
    }>({
      url: path,
      method: 'post',
      data: {
        ranges,
      },
    });
  }

  /**
   * 批量添加文档协作者
   */
  async batchAddDocCollaborators(fileToken: string, members: DocMember[]) {
    const path = `/open-apis/drive/v1/permissions/${fileToken}/members/batch_create?type=docx`;
    const result = await this.lark.request({
      url: path,
      method: 'POST',
      data: {
        members,
      },
    });
    return result;
  }

  /**
   * 更新云文档权限设置
   */
  async updateDocPermission(fileToken: string, data: any) {
    const path = `/open-apis/drive/v1/permissions/${fileToken}/public?type=docx`;
    const result = await this.lark.request({
      url: path,
      method: 'PATCH',
      data,
    });
    this.logger.info(`updateDocPermission : ${JSON.stringify(result)}`);
    return result;
  }

  async getCurrentBeforeLarkCalendarEventsList(calendarId: string) {
    // 获取含当前时间之后日程列表
    // 获取当前时间戳（毫秒）
    const currentTimeInMs = Date.now();
    // 将毫秒转换为秒
    const currentTimeInSeconds = Math.floor(currentTimeInMs / 1000);
    // 减去一天的秒数（86400 秒）
    const newTime = currentTimeInSeconds - 86400 * 30;
    const url = `https://open.feishu.cn/open-apis/calendar/v4/calendars/${calendarId}/events`;
    const body = {
      // 'anchor_time': theDayBeforeCurrentTime,
      start_time: newTime,
      end_time: currentTimeInSeconds,
      page_size: 1000,
    };

    const token = await this.getPartrolFeishuToken().then(res => res?.tenant_access_token);
    const headers = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    try {
      const response = await axios.get(url, {
        params: body,
        headers,
      });
      const res = response.data;
      if (res.code === 0) {
        return res;
      } else {
        const errorMsg = res.msg || '未知错误';
        this.logger.error(`获取日历日程列表失败，错误描述${errorMsg}`);
        return null;
      }
    } catch (error) {
      this.logger.error('请求日历日程列表时出错:', error);
      return null;
    }
  }

  async getPartrolFeishuToken() {
    const url = 'https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal';
    const body = {
      app_id: 'cli_9fea90334d3d900d',
      app_secret: 'h2NTKKCM0vmw2X1FjksCnhfGRsIYYKVI',
    };

    try {
      const response = await axios.post(url, body);
      const res = response.data;
      console.log(`token: ${res.app_access_token}, ${res.tenant_access_token}`);
      return {
        app_access_token: res.app_access_token,
        tenant_access_token: res.tenant_access_token,
      };
    } catch (error) {
      this.logger.error('获取飞书 token 时出错:', error);
      throw error;
    }
  }
  /**
   * 获取我的空间（root folder）元数据
   * https://open.larkoffice.com/document/server-docs/docs/drive-v1/folder/get-root-folder-meta
   */
  async getRootFolderMeta() {
    const path = `/open-apis/drive/explorer/v2/root_folder/meta`;
    const result = await this.lark.request<LarkResult<LarkResultRootFolderMetaData>>({
      url: path,
      method: 'GET',
    });
    return result;
  }

  /**
   * 复制文件
   * https://open.larkoffice.com/document/server-docs/docs/drive-v1/file/copy?appId=cli_9c8628b7b1f1d102
   */
  async copyFile(newFileName: string, fileToken: string, fileType: LarkFileType, targetFolderToken: string) {
    const path = `/open-apis/drive/v1/files/${fileToken}/copy`;
    const result = await this.lark.request<LarkResult<{ file: LarkResultCopyFile }>>({
      url: path,
      method: 'POST',
      data: {
        name: newFileName,
        type: fileType,
        folder_token: targetFolderToken,
      },
    });
    const file = result.data?.file;
    console.log(file);
    return result;
  }

  /**
   * 转移所有者
   * https://open.larkoffice.com/document/server-docs/docs/permission/permission-member/transfer_owner?appId=cli_9c8628b7b1f1d102
   */
  async transferOwner(
    fileToken: string,
    fileType: LarkFileType,
    memberType: 'email' | 'openid' | 'userid',
    memberId: string,
  ) {
    const path = `/open-apis/drive/v1/permissions/${fileToken}/members/transfer_owner`;
    return await this.lark.request({
      url: path,
      method: 'POST',
      params: {
        type: fileType,
      },
      data: {
        member_type: memberType,
        member_id: memberId,
      },
    });
  }
}
