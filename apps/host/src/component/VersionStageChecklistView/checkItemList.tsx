import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import {
  ReleasePlatformUrlSearchParams,
  versionManager,
  VersionProcessInfo,
  VersionStageInfo,
  VersionStageStatus,
} from '@shared/releasePlatform/versionStage';
import {
  CheckItemStatus,
  ItemType,
  SubGrayExtraData,
  VersionStageCheckItem,
  VersionStageUserCheckItem,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { useModel } from '@edenx/runtime/model';
import { ColumnProps } from '@douyinfe/semi-ui/lib/es/table';
import {
  Button,
  Card,
  Checkbox,
  Divider,
  Empty,
  Input,
  Modal,
  Pagination,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tag,
  TagInput,
  Toast,
  Typography,
  Tooltip,
  SideSheet,
  CheckboxGroup,
  Popover,
} from '@douyinfe/semi-ui';
import CheckItemTableContainer from '@/component/CheckItemTableContainer';
import {
  BugResolveRatioItemInfo,
  BugResolveRatioItemStatus,
  FunctionalBugItemInfo,
  LibraCheckItemInfo,
  ManuallyCheckItemInfo,
  ManuallyCheckItemStatus,
  ManuallyCheckItemType,
  StabilityMetricItemInfo,
  StabilityMetricsItemStatus,
  TotalBugResolveRatioItemInfo,
  PcCrashItemInfo,
  FeedbackMetricsItemInfo,
  FeedbackMetricsItemStatus,
} from '@shared/releasePlatform/versionStageCheckItemInfo';
import { User } from '@pa/shared/dist/src/core';
import UserCard from '@/component/UserCard';
import {
  applyBugBlockItemExemption,
  applyBusinessBugExemption,
  applyLibraBlockItemExemption,
  batchNotifyChecklist,
  fetchTotalQATestProgress,
  fetchVersionInfoByCriteria,
  findChecklist,
  notifyCheckItem,
  notifySpecificBusinessBugReslove,
  notifySpecificQATestProgress,
  pushBugItemExemptApprove,
  pushBusinessBugExemptApprove,
  turnVersionStage,
  updateBugItemExemptApproveStatus,
  updateBusinessBugExemptApproveStatus,
  updateSpecificCheckItem,
} from '@api/releasePlatform';
import { fetchLoginEmail, updateCheckListState } from '@api/versionProcess';
import BugExemptApplySideSheet from '@/component/BugExemptApplySideSheet';
import UserSettingModule from '@/model/userSettingModel';
import BusinessBugResolveRateExemptApplySheet from '@/component/BusinessBugResolveRateExemptApplySheet';
import { ManualCheckDetail } from '@/component/ManualCheckItemSideSheet';
import { SlardarMetricDetail } from '@/component/SlardarCheckItemSideSheet';
import { IllustrationNoContent, IllustrationNoContentDark } from '@douyinfe/semi-illustrations';
import SemiReactUserGroup from '@/component/UserGroup';
import { useSearchParams } from '@edenx/runtime/router';
import QATestProgressDetailRecordSheet from '@/component/QATestProgressDetail';
import CustomComponent from '@/component/TestPackageInfomation';
import { TestProgressInfo, TestProgressStatus, TotalQATestProgress } from '@shared/releasePlatform/QATestConfig';
import UserStoryVersionInfo from '@/component/TestPackageInfomation/userStoryVersionInfo';
import { AppSettingId, MAIN_HOST_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';
import PCTestPackageInfo from '@/component/TestPackageInfomation/pcTestPackageInfo';
import { getStageInfo } from '@shared/releasePlatform/releasePlatformUtils';
import { PCJumpDetail, UpdatePCMetric } from '@/component/ManualCheckItemSideSheet/PCManualCheckDetail';
import {
  IconCrossStroked,
  IconEdit,
  IconTick,
  IconAlertTriangle,
  IconImage,
  IconInfoCircle,
  IconHelpCircle,
  IconSetting,
  IconChevronUpDown,
  IconSort,
} from '@douyinfe/semi-icons';
import { buildUserSelector } from '@/component/UserSelect';
import { ProForm } from '@ant-design/pro-components';
import OperatorComfirmButton from '@/component/OperatorComfirmButton';
import { BmType } from '@shared/bits/bmInfo';
import {
  Feedback,
  FeedbackCallbackInfo,
  FeedbackMeegoInfo,
  FeedbackPriority,
  FollowUpStatus,
} from '@shared/releasePlatform/feedbackFollwUp';
import { CemOpenVoiceSearch } from '@ies/cem-open-sdk';
import {
  batchProcessFeedback,
  feedbacksTest,
  feedbacksTest2,
  feedbacksTest3,
  getAllFeedbacks,
  getCurrentFeedbacks,
  getFeedbackProcessInfo,
  getMeegoInfo,
  getRecentFeedbackRising,
  getVersionBugResolveInfo,
  trackFeedbackStatusChange,
  updateAllFeedbacks,
  updateSpecificFeedbackItemStatus,
} from '@api/releasePlatformFeedback';
import { BuildMasterInfo } from '@shared/process/versionProcess';
import LibraExemptApplySideSheet from '@/component/LibraExemptApplySideSheet';
import { SearchFeedbacksResp } from '@ies/cem-open-sdk/src/api/apps/cem_analysis/idl/feedback_analyze';
import { oldDefaultGatewayConfig } from '@bytecloud/common-lib/es/gateway/gateway-config.polyfill';
import { current } from 'immer';
import MicroFrontend from '@/pages/microfrontend';
import { noop, orderBy } from 'lodash';
import AppSettingModule from '@/model/appSettingModel';
import { CemOpenVoiceSearchFeedbackCallbackInfo } from '@ies/cem-open-sdk/src/open/CemOpenVoiceSearch/types';
import { CircuitBreakerDetail } from '@/component/CircuitBreakerApplySideSheet';
import { CircuitBreakerManualEntryModal } from '@/component/CircuitBreakerManualEntryModal';
// import { LABEL_LV_APP,LABEL_RETOUCH_APP,LABEL_LV_PC } from '../../../api/service/releasePlatform/FeedbackService';
const LABEL_LV_PC = [
  '245015',
  '245016',
  '243234',
  '249423',
  '241157',
  '248145',
  '248144',
  '252818',
  '250838',
  '250839',
  '216225',
  '216227',
  '216230',
  '216232',
  '252816',
  '216233',
];
const { Text, Title } = Typography;

const UserAvatar: React.FC<{ checkUser: User }> = ({ checkUser }) => (
  <UserCard
    email={checkUser?.email}
    simpleUserData={{
      avatarUrl: typeof checkUser?.avatar === 'string' ? checkUser?.avatar : checkUser?.avatar?.avatar_240,
      name: checkUser?.name,
    }}
    triggerType="hover"
  />
);
// BuildMasterInfo
const UserAvatarCopy: React.FC<{ checkUser: any }> = ({ checkUser }) => (
  <UserCard
    email={checkUser?.email}
    simpleUserData={{
      avatarUrl: checkUser?.avatarUrl,
      name: checkUser?.nameCN,
    }}
    triggerType="hover"
  />
);

// Bug解决率按钮
const BugReolveRatioOperation: React.FC<{
  itemInfo: BugResolveRatioItemInfo;
  versionInfo: VersionProcessInfo;
  businessName: string;
  operatorList: string[];
  loginUserEmail: string;
  checkItemId: string;
  stageName: string;
  updateCheckItem: (newItem: VersionStageCheckItem) => void;
}> = ({
  itemInfo,
  operatorList,
  loginUserEmail,
  versionInfo,
  checkItemId,
  businessName,
  stageName,
  updateCheckItem,
}) => {
  const [loading, setLoading] = useState(false);
  const [stageInfo, setStageInfo] = useState<VersionStageInfo | undefined>();
  const onConfirm = () => {
    setLoading(true);
    notifySpecificBusinessBugReslove({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: stageName,
        businessName,
      },
    }).then(res => {
      setLoading(false);
    });
  };
  useEffect(() => {
    setStageInfo(getStageInfo(versionInfo.version_stages, stageName));
  }, [versionInfo, stageName]);

  const onCancel = () => {
    Toast.warning('取消');
  };
  return (
    <div>
      <Space>
        <Popconfirm
          title="将在版本大群单独催促此业务线Owner"
          onConfirm={onConfirm}
          onCancel={onCancel}
          disabled={
            itemInfo.status === BugResolveRatioItemStatus.Exempted ||
            itemInfo.status === BugResolveRatioItemStatus.Pass ||
            stageInfo?.status !== VersionStageStatus.OnProgress
          }
        >
          <Button
            theme={'light'}
            type={'secondary'}
            disabled={
              itemInfo.status === BugResolveRatioItemStatus.Exempted ||
              itemInfo.status === BugResolveRatioItemStatus.Pass ||
              stageInfo?.status !== VersionStageStatus.OnProgress
            }
            loading={loading}
          >
            催促解决
          </Button>
        </Popconfirm>
        {stageInfo ? (
          <BusinessBugResolveRateExemptApplySheet
            versionInfo={versionInfo}
            bugResloveInfo={itemInfo}
            stageInfo={stageInfo}
            updateCheckItem={updateCheckItem}
          />
        ) : (
          <></>
        )}
      </Space>
    </div>
  );
};

export const SlardarCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  stageName: string;
  stageInfo?: VersionStageInfo;
}> = ({ versionInfo, stageInfo, checkItems, stageName }) => {
  const [operatorList, setOeratorList] = useState<string[]>([]);
  const [loginUser, setLoginUser] = useState<string>('');

  const [tableDataSource, setTableDataSource] = useState<VersionStageCheckItem[]>([]);
  const [pass, setPass] = useState<boolean>(false);
  const [jumpMetricUrl, setJumpMetricUrl] = useState<string>();

  const newUpdateCheckItem: (item: VersionStageCheckItem) => void = (item: VersionStageCheckItem) => {
    // 使用 map 来生成一个新的数组，而不是直接修改原数组
    const updatedCheckItems = checkItems.map(it => (it.check_item_id === item.check_item_id ? item : it));

    // 使用 setState 更新状态，以确保组件可以响应状态改变
    setTableDataSource(updatedCheckItems);

    // 检查是否所有项目都是 Exempt
    const allExempt = updatedCheckItems.every(i => i.status === CheckItemStatus.Exempt);

    // 根据检查结果更新 pass 状态
    setPass(allExempt);
  };

  useEffect(() => {
    setTableDataSource(checkItems);
    // 这里进行初始化时的全部 exempt 检查
    setPass(checkItems.every(i => i.status === CheckItemStatus.Exempt));
  }, [checkItems]);

  const getItemStatus = (item: VersionStageCheckItem) => {
    const itemInfo = item.item_info as StabilityMetricItemInfo;
    if (!itemInfo) {
      return StabilityMetricsItemStatus.TBD;
    }
    return itemInfo.status;
  };

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: '指标名',
      width: '7%',
      dataIndex: 'name',
      render: (_, row) => <Text>{row.description}</Text>,
    },
    {
      disable: true,
      title: '状态',
      width: '5%',
      dataIndex: 'status',
      filters: [
        {
          text: '待评估',
          value: StabilityMetricsItemStatus.TBD,
        },
        {
          text: '阻塞',
          value: StabilityMetricsItemStatus.Blocked,
        },
        {
          text: '通过',
          value: StabilityMetricsItemStatus.Pass,
        },
        {
          text: '豁免',
          value: StabilityMetricsItemStatus.Exempted,
        },
      ],
      onFilter: (value, record) => {
        if (record) {
          const info = record.item_info as StabilityMetricItemInfo;
          return info.status === value;
        }
        return false;
      },
      ellipsis: true,
      valueType: 'select',
      render: (val, item, index) => {
        const itemInfo = item.item_info as StabilityMetricItemInfo;
        let internalStatus: any = item.status;
        if (itemInfo && itemInfo.status) {
          internalStatus = itemInfo.status;
        }
        if (internalStatus === StabilityMetricsItemStatus.TBD) {
          return (
            <Tag size="large" color="amber">
              待评估
            </Tag>
          );
        }
        if (internalStatus === StabilityMetricsItemStatus.Blocked) {
          return (
            <Tag size="large" color="red">
              阻塞
            </Tag>
          );
        }
        if (internalStatus === StabilityMetricsItemStatus.Pass) {
          return (
            <Tag size="large" color="green">
              通过
            </Tag>
          );
        }
        if (internalStatus === StabilityMetricsItemStatus.Exempted) {
          return (
            <Tag size="large" color="light-blue">
              豁免
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: '准出值班人',
      width: '5%',
      dataIndex: 'operator',
      render: (val: any, item: VersionStageCheckItem) => {
        const info = item.item_info as StabilityMetricItemInfo;
        const duty = item.owner;
        if (duty === undefined) {
          return <></>;
        }
        // if (info && info.owners && info.owners.length > 0) {
        //   duty = info.owners[0];
        // }
        return <UserAvatar checkUser={duty} />;
      },
    },
    {
      title: '指标Owner',
      width: '5%',
      render: (val: any, item: VersionStageCheckItem) => {
        let owners: User[] = [];
        if (item.owner !== undefined) {
          owners = [item.owner];
        }
        const info = item.item_info as StabilityMetricItemInfo;
        const users = info.owners;
        if (users && users.length > 0) {
          owners = users;
        }
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <SemiReactUserGroup users={owners} triggerType={'hover'} />
            {/* {owners.map((user, index) => UserAvatar({ checkUser: user }))} */}
          </div>
        );
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem | undefined) => {
        let loading = false;
        return (
          <Space spacing={'tight'}>
            {item ? (
              <>
                <Popconfirm
                  disabled={
                    getItemStatus(item) !== StabilityMetricsItemStatus.TBD ||
                    stageInfo?.status !== VersionStageStatus.OnProgress
                  }
                  title="将在发版群催促值班人"
                  onConfirm={() => {
                    loading = true;
                    notifyCheckItem({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        checkItemId: item?.check_item_id ?? '',
                        stage: stageName,
                        itemType: ItemType.Slardar,
                      },
                    }).then(res => {
                      loading = false;
                    });
                  }}
                >
                  <Button
                    theme={'light'}
                    type={'secondary'}
                    loading={loading}
                    disabled={
                      getItemStatus(item) !== StabilityMetricsItemStatus.TBD ||
                      stageInfo?.status !== VersionStageStatus.OnProgress
                    }
                  >
                    催促解决
                  </Button>
                </Popconfirm>
                <SlardarMetricDetail
                  checkItem={item}
                  versionInfo={versionInfo}
                  stageName={stageName}
                  updateCheckItem={newUpdateCheckItem}
                />
              </>
            ) : (
              <></>
            )}
          </Space>
        );
      },
    },
  ];
  const rowKey = (record: VersionStageCheckItem | undefined) => record!.check_item_id;

  useEffect(() => {
    let metricUrl = `${MAIN_HOST_HTTPS}/quality/version/slardar`;
    metricUrl += `?appid=${versionInfo.app_id}`;
    const grayData = stageInfo?.extra_data as SubGrayExtraData;
    if (grayData) {
      metricUrl += `&selected=${grayData.versionCode}`;
    }
    setJumpMetricUrl(metricUrl);
  }, [versionInfo, stageInfo]);

  return (
    <CheckItemTableContainer
      checkItemTable={
        <Table
          columns={columns}
          rowKey={'check_item_id'}
          dataSource={tableDataSource}
          pagination={{
            pageSize: 5,
            total: tableDataSource.length,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      }
      itemTitle={'性能指标'}
      statusDescription={''}
      pass={pass}
      hideBatchNotify={false}
      hideStatus={false}
      batchNotify={async () => {
        await batchNotifyChecklist({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            itemType: ItemType.Slardar,
            stageName,
          },
        });
      }}
      batchNotifyConfirmTitle={'批量催促准出值班人'}
      batchNotifyConfirmText={'将在发版群发送催促准出的卡片'}
      versionInfo={versionInfo}
      stage={stageInfo}
      customizeButton={
        <>
          <Button onClick={() => window.open(jumpMetricUrl)}>查看全部指标</Button>
        </>
      }
    />
  );
};

// 通用项也用这个
export const ManualCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  title: string;
  stageName: string;
  stageInfo?: VersionStageInfo;
}> = ({ versionInfo, stageInfo, checkItems, title, stageName }) => {
  const [operatorList, setOeratorList] = useState<string[]>([]);
  const [loginUser, setLoginUser] = useState<string>('');
  const [tableDataSource, setTableDataSource] = useState<VersionStageCheckItem[]>([]);
  const [pass, setPass] = useState<boolean>(false);

  const newUpdateCheckItem: (item: VersionStageCheckItem) => void = (item: VersionStageCheckItem) => {
    // 使用 map 来生成一个新的数组，而不是直接修改原数组
    const updatedCheckItems = checkItems.map(it => (it.check_item_id === item.check_item_id ? item : it));

    // 使用 setState 更新状态，以确保组件可以响应状态改变
    setTableDataSource(updatedCheckItems);

    // 检查是否所有项目都是 Exempt
    const allExempt = updatedCheckItems.every(i => i.status === CheckItemStatus.Exempt);

    // 根据检查结果更新 pass 状态
    setPass(allExempt);
  };

  useEffect(() => {
    setTableDataSource(checkItems);
    // 这里进行初始化时的全部 exempt 检查
    setPass(checkItems.every(i => i.status === CheckItemStatus.Exempt));
  }, [checkItems]);

  const getItemStatus = (item: VersionStageCheckItem) => {
    const itemInfo = item.item_info as ManuallyCheckItemInfo;
    if (!itemInfo) {
      return ManuallyCheckItemStatus.TBD;
    }
    return itemInfo.status;
  };

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: '事项',
      width: '6%',
      dataIndex: 'name',
      render: (_, row) => <Text>{row.description}</Text>,
    },
    {
      disable: true,
      title: '状态',
      width: '5%',
      dataIndex: 'status',
      filters: [
        {
          text: '待评估',
          value: ManuallyCheckItemStatus.TBD,
        },
        {
          text: '阻塞',
          value: ManuallyCheckItemStatus.Blocked,
        },
        {
          text: '通过',
          value: ManuallyCheckItemStatus.Pass,
        },
        {
          text: '豁免',
          value: ManuallyCheckItemStatus.Exempted,
        },
      ],
      onFilter: (value, record) => {
        if (record) {
          const info = record.item_info as ManuallyCheckItemInfo;
          return info.status === value;
        }
        return false;
      },
      ellipsis: true,
      valueType: 'select',
      render: (val, item) => {
        const itemInfo = item.item_info as ManuallyCheckItemInfo;
        let internalStatus: any = item.status;
        if (itemInfo && itemInfo.status) {
          internalStatus = itemInfo.status;
        }
        if (internalStatus === ManuallyCheckItemStatus.TBD) {
          return (
            <Tag size="large" color="amber">
              待评估
            </Tag>
          );
        }
        if (internalStatus === ManuallyCheckItemStatus.Blocked) {
          return (
            <Tag size="large" color="red">
              阻塞
            </Tag>
          );
        }
        if (internalStatus === ManuallyCheckItemStatus.Pass) {
          return (
            <Tag size="large" color="green">
              通过
            </Tag>
          );
        }
        if (internalStatus === ManuallyCheckItemStatus.Exempted) {
          return (
            <Tag size="large" color="light-blue">
              豁免
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: '跟进人',
      width: '5%',
      dataIndex: 'operator',
      render: (val: any, item: VersionStageCheckItem) => {
        if (item.owner === undefined) {
          return <></>;
        }
        return <UserAvatar checkUser={item.owner} />;
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem | undefined) => {
        let loading = false;
        let isPC = false;
        if (item) {
          const mannuallyItemInfo = item.item_info as ManuallyCheckItemInfo;
          if (mannuallyItemInfo.type === ManuallyCheckItemType.PCMetrics) {
            isPC = true;
          }
        }
        return (
          <Space spacing={'tight'}>
            {item ? (
              <>
                <Popconfirm
                  title="将在发版群催促跟进人"
                  disabled={
                    getItemStatus(item) !== ManuallyCheckItemStatus.TBD ||
                    stageInfo?.status !== VersionStageStatus.OnProgress
                  }
                  onConfirm={() => {
                    loading = true;
                    notifyCheckItem({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        checkItemId: item?.check_item_id ?? '',
                        stage: stageName,
                        itemType: ItemType.QABM,
                      },
                    }).then(res => {
                      loading = false;
                    });
                  }}
                >
                  <Button
                    theme={'light'}
                    type={'secondary'}
                    loading={loading}
                    disabled={
                      getItemStatus(item) !== ManuallyCheckItemStatus.TBD ||
                      stageInfo?.status !== VersionStageStatus.OnProgress
                    }
                  >
                    催促评估
                  </Button>
                </Popconfirm>
                {isPC ? (
                  <PCJumpDetail versionInfo={versionInfo} checkItemId={item.check_item_id} />
                ) : (
                  <ManualCheckDetail
                    checkItem={item}
                    operatorList={operatorList}
                    loginUserEmail={loginUser}
                    updateCheckItem={newUpdateCheckItem}
                    stageName={stageName}
                    versionInfo={versionInfo}
                  />
                )}
                <UpdatePCMetric checkItem={item} versionInfo={versionInfo} updatePCCheckItem={newUpdateCheckItem} />
              </>
            ) : (
              <></>
            )}
          </Space>
        );
      },
    },
  ];
  const rowKey = (record: VersionStageCheckItem | undefined) => record!.check_item_id;
  return (
    <CheckItemTableContainer
      checkItemTable={
        <Table
          columns={columns}
          dataSource={tableDataSource}
          rowKey={'check_item_id'}
          pagination={{
            pageSize: 5,
            total: tableDataSource.length,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      }
      itemTitle={title}
      statusDescription={''}
      pass={pass}
      hideBatchNotify={false}
      hideStatus={false}
      batchNotify={async () => {
        await batchNotifyChecklist({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            itemType: ItemType.RDBM,
            stageName,
            isManual: true,
          },
        });
      }}
      batchNotifyConfirmTitle={'批量催促跟进人'}
      batchNotifyConfirmText={'将在发版群发送催促准出的卡片'}
      versionInfo={versionInfo}
      stage={stageInfo}
    />
  );
};

export const PcCrashCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  stageName: string;
  stageInfo?: VersionStageInfo;
  updateCheckItem: (checkItem: VersionStageCheckItem) => void;
}> = ({ versionInfo, checkItems, stageInfo, stageName, updateCheckItem }) => {
  const [curCrashItems, setCurCrashItems] = useState<VersionStageCheckItem[]>([]);
  useEffect(() => {
    setCurCrashItems(checkItems);
  }, [checkItems]);

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: 'Oncall群链接',
      dataIndex: 'item_info',
      key: 'oncallLink',
      width: '15%',
      render: (text: any, record: VersionStageCheckItem) => {
        if (record.item_type === ItemType.PcCrashItem) {
          const info = record.item_info as PcCrashItemInfo;
          return info.oncallLink ? (
            <Text link={{ href: info.oncallLink, target: '_blank' }}>{info.stack_info}</Text>
          ) : (
            '-'
          );
        }
        return null;
      },
    },
    {
      title: '优先级',
      dataIndex: 'item_info',
      key: 'priority',
      width: '5%',
      render: (text: any, record: VersionStageCheckItem) => {
        if (record.item_type === ItemType.PcCrashItem) {
          const info = record.item_info as PcCrashItemInfo;
          return info.priority || '-';
        }
        return null;
      },
    },
    {
      title: '崩溃次数',
      dataIndex: 'item_info',
      key: 'crashCount',
      width: '5%',
      render: (text: any, record: VersionStageCheckItem) => {
        if (record.item_type === ItemType.PcCrashItem) {
          const info = record.item_info as PcCrashItemInfo;
          return info.rawCrashData?.count ?? '-';
        }
        return null;
      },
    },
    {
      title: '影响用户数',
      dataIndex: 'item_info',
      key: 'affectedUsers',
      width: '5%',
      render: (text: any, record: VersionStageCheckItem) => {
        if (record.item_type === ItemType.PcCrashItem) {
          const info = record.item_info as PcCrashItemInfo;
          return info.rawCrashData?.user ?? '-';
        }
        return null;
      },
    },
    {
      title: '处理人',
      dataIndex: 'item_info',
      key: 'assignee',
      width: '5%',
      render: (text: any, record: VersionStageCheckItem) => {
        if (record.item_type === ItemType.PcCrashItem) {
          const info = record.item_info as PcCrashItemInfo;
          return info.assignee ? <UserAvatar checkUser={info.assignee} /> : '-';
        }
        return null;
      },
    },
    {
      title: '状态',
      dataIndex: 'item_info',
      key: 'status',
      width: '5%',
      render: (text: any, record: VersionStageCheckItem) => {
        if (record.item_type === ItemType.PcCrashItem) {
          const info = record.item_info as PcCrashItemInfo;
          return info.status || '-';
        }
        return null;
      },
    },
  ];

  return (
    <CheckItemTableContainer
      checkItemTable={
        <div style={{ overflow: 'auto', maxHeight: '500px' }}>
          {/* 添加带滚动条的div */}
          <Table
            columns={columns}
            dataSource={curCrashItems}
            rowKey={'check_item_id'}
            pagination={{
              pageSize: 50,
              total: curCrashItems.length,
            }}
            scroll={{
              x: '100%',
              scrollToFirstRowOnChange: true,
            }}
          />
        </div>
      }
      itemTitle={'Crash 列表'}
      statusDescription={''}
      pass={curCrashItems.every(item => item.status !== CheckItemStatus.Blocked)}
      hideBatchNotify={false} // Or true, depending on requirements
      hideStatus={false}
      batchNotify={async () => {
        // TODO: Implement batch notify for CrashItem if needed
        await batchNotifyChecklist({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            itemType: ItemType.PcCrashItem,
            stageName,
            isManual: true, // Or false, depending on agent logic
          },
        });
      }}
      batchNotifyConfirmTitle={'批量催促Crash处理人'}
      batchNotifyConfirmText={'将在发版群发送催促解决Crash的卡片'}
      versionInfo={versionInfo}
      stage={stageInfo}
    />
  );
};

export const MeegoCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  stageName: string;
  stageInfo?: VersionStageInfo;
  updateCheckItem: (checkItem: VersionStageCheckItem) => void;
}> = ({ versionInfo, checkItems, stageInfo, stageName, updateCheckItem }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [curBugItems, setCurBugItems] = useState<VersionStageCheckItem[]>([]);
  const [searchParams, setSearchParams] = useSearchParams();
  useEffect(() => {
    if (checkItems === curBugItems) {
      return;
    }
    if (searchParams.get(ReleasePlatformUrlSearchParams.BugExemptSheet)) {
      const bugExemptSheet = Number(searchParams.get(ReleasePlatformUrlSearchParams.BugExemptSheet));
      // 将checkItems中对应bugExemptSheet的排在第一位
      for (let index = 0; index < checkItems.length; index++) {
        const info = checkItems[index].item_info as FunctionalBugItemInfo;
        if (info.meego_id === bugExemptSheet) {
          const newCheckItems = [...checkItems];
          newCheckItems.splice(index, 1);
          newCheckItems.unshift(checkItems[index]);
          setCurBugItems(newCheckItems);
          break;
        }
      }
    } else {
      setCurBugItems(checkItems);
    }
  }, [checkItems]);

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: 'Meego链接',
      width: '25%',
      dataIndex: 'name',
      render: (_, row) => {
        const itemInfo = row.item_info as FunctionalBugItemInfo;
        return (
          <Text link={{ href: itemInfo.meego_url, target: '_blank' }}>
            {itemInfo.bug_name.length > 80 ? `${itemInfo.bug_name.substring(0, 100)}...` : itemInfo.bug_name}
          </Text>
        );
      },
    },
    {
      disable: true,
      title: '优先级',
      width: '5%',
      dataIndex: 'priority',
      render: (val, item) => {
        const itemInfo = item.item_info as FunctionalBugItemInfo;
        if (!itemInfo) {
          return <></>;
        }
        const internalStatus = itemInfo.priority;
        if (internalStatus === 'P0') {
          return (
            <Tag size="large" color="red">
              P0
            </Tag>
          );
        }
        if (internalStatus === 'P1') {
          return (
            <Tag size="large" color="pink">
              P1
            </Tag>
          );
        }
        if (internalStatus === 'S') {
          return (
            <Tag size="large" color="red">
              S
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: '经办人',
      width: '5%',
      dataIndex: 'operator',
      render: (val: any, item: VersionStageCheckItem) => {
        const itemInfo = item.item_info as FunctionalBugItemInfo;
        if (!itemInfo) {
          return <></>;
        }
        return <UserAvatar checkUser={itemInfo.operator} />;
      },
    },
    {
      disable: true,
      title: '报告人',
      width: '5%',
      dataIndex: 'reporter',
      render: (val: any, item: VersionStageCheckItem) => {
        const itemInfo = item.item_info as FunctionalBugItemInfo;
        if (!itemInfo) {
          return <></>;
        }
        return <UserAvatar checkUser={itemInfo.reporter} />;
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem | undefined) => {
        let loading = false;
        const bugInfo = item?.item_info as FunctionalBugItemInfo;
        return (
          <Space spacing={'tight'}>
            {bugInfo && stageInfo ? (
              <>
                <Popconfirm
                  title="将在发版群催促经办人"
                  disabled={stageInfo.status !== VersionStageStatus.OnProgress}
                  onConfirm={() => {
                    loading = true;
                    notifyCheckItem({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        checkItemId: item?.check_item_id ?? '',
                        stage: stageName,
                        itemType: ItemType.FunctionalBug,
                      },
                    }).then(res => {
                      loading = false;
                    });
                  }}
                >
                  <Button
                    theme={'light'}
                    type={'secondary'}
                    loading={loading}
                    disabled={stageInfo.status !== VersionStageStatus.OnProgress}
                  >
                    催促解决
                  </Button>
                </Popconfirm>
                <BugExemptApplySideSheet
                  bugInfo={bugInfo}
                  versionInfo={versionInfo}
                  stageInfo={stageInfo}
                  handleSubmit={async (
                    influenceStage: string,
                    influenceScope: string,
                    exemptReason: string,
                    improveMeasure: string,
                  ) => {
                    await applyBugBlockItemExemption({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        meegoId: bugInfo.meego_id,
                        influenceStage,
                        influenceScope,
                        exemptReason,
                        improveMeasure,
                        userEmail: userSettingState.info.email,
                      },
                    }).then(res => {
                      if (res) {
                        updateCheckItem(res);
                        const newBugInfo = res.item_info as FunctionalBugItemInfo;
                        window.open(
                          `https://applink.feishu.cn/client/chat/open?openChatId=${newBugInfo.exempt_chat_id ?? ''}`,
                        );
                      }
                    });
                  }}
                  pushButtonClick={async () => {
                    await pushBugItemExemptApprove({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        meegoIssueId: bugInfo.meego_id,
                        userEmail: userSettingState.info.email,
                      },
                    });
                  }}
                  approveButtonClick={async () => {
                    await updateBugItemExemptApproveStatus({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        meegoIssueId: bugInfo.meego_id,
                        userEmail: userSettingState.info.email,
                        approve: true,
                      },
                    }).then(res => {
                      if (res) {
                        updateCheckItem(res);
                      }
                    });
                  }}
                  rejectButtonClick={async () => {
                    await updateBugItemExemptApproveStatus({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        meegoIssueId: bugInfo.meego_id,
                        userEmail: userSettingState.info.email,
                        approve: false,
                      },
                    }).then(res => {
                      if (res) {
                        updateCheckItem(res);
                      }
                    });
                  }}
                />
              </>
            ) : (
              <></>
            )}
          </Space>
        );
      },
    },
  ];
  const rowKey = (record: VersionStageCheckItem | undefined) => record!.check_item_id;
  return (
    <CheckItemTableContainer
      checkItemTable={
        <Table
          columns={columns}
          rowKey={'check_item_id'}
          dataSource={curBugItems}
          pagination={{
            pageSize: 5,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      }
      itemTitle={'S级/P0/P1 缺陷'}
      statusDescription={''}
      pass={checkItems.filter(it => it.status !== CheckItemStatus.Exempt).length === 0}
      hideBatchNotify={false}
      hideStatus={false}
      batchNotify={async () => {
        await batchNotifyChecklist({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            itemType: ItemType.FunctionalBug,
            stageName,
          },
        });
      }}
      batchNotifyConfirmTitle={'批量催促经办人'}
      batchNotifyConfirmText={'将在发版群发送催促准出的卡片'}
      versionInfo={versionInfo}
      stage={stageInfo}
    />
  );
};

export const LibraGrayCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  stageName: string;
  stageInfo?: VersionStageInfo;
  updateCheckItem: (checkItem: VersionStageCheckItem) => void;
  title: string;
  libraItemType: ItemType;
}> = ({ versionInfo, checkItems, stageInfo, stageName, updateCheckItem, title, libraItemType }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [curLibraGrayItems, setCurLibraGrayItems] = useState<VersionStageCheckItem[]>([]);
  const [searchParams, setSearchParams] = useSearchParams();
  useEffect(() => {
    if (checkItems === curLibraGrayItems) {
      return;
    }
    if (searchParams.get(ReleasePlatformUrlSearchParams.LibraGrayExemptSheet)) {
      const libraGrayExemptSheet = Number(searchParams.get(ReleasePlatformUrlSearchParams.LibraGrayExemptSheet));
      // 将checkItems中对应bugExemptSheet的排在第一位
      for (let index = 0; index < checkItems.length; index++) {
        const info = checkItems[index].item_info as LibraCheckItemInfo;
        if (info.meego_id === libraGrayExemptSheet) {
          const newCheckItems = [...checkItems];
          newCheckItems.splice(index, 1);
          newCheckItems.unshift(checkItems[index]);
          setCurLibraGrayItems(newCheckItems);
          break;
        }
      }
    } else {
      setCurLibraGrayItems(checkItems);
    }
  }, [checkItems]);

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: 'Meego链接',
      width: '25%',
      dataIndex: 'name',
      render: (_, row) => {
        const itemInfo = row.item_info as LibraCheckItemInfo;
        if (!itemInfo || !itemInfo.meego_name || !itemInfo.meego_id) {
          return <></>;
        }
        return (
          <Text
            link={{ href: `https://meego.larkoffice.com/faceu/story/detail/${itemInfo.meego_id}`, target: '_blank' }}
          >
            {itemInfo.meego_name.length > 80 ? `${itemInfo.meego_name.substring(0, 100)}...` : itemInfo.meego_name}
          </Text>
        );
      },
    },
    {
      title: '实验链接',
      width: '25%',
      dataIndex: 'name',
      render: (_, row) => {
        const itemInfo = row.item_info as LibraCheckItemInfo;
        if (!itemInfo || !itemInfo.flight_id || !itemInfo.flight_name) {
          return <></>;
        }
        const { flight_info_list } = itemInfo;

        if (!flight_info_list || flight_info_list.length === 0) {
          if (!itemInfo.flight_id || !itemInfo.flight_name) {
            return <></>;
          }
          let libraUrl = `https://data.bytedance.net/libra/flight/${itemInfo.flight_id}`;
          if (versionInfo.app_id === AppSettingId.CC_IOS || versionInfo.app_id === AppSettingId.CC_ANDROID) {
            libraUrl = `https://libra-sg.tiktok-row.net/libra/flight/${itemInfo.flight_id}`;
          }
          return itemInfo.flight_id !== 0 ? (
            <Text link={{ href: libraUrl, target: '_blank' }}>
              {itemInfo.flight_name.length > 80 ? `${itemInfo.flight_name.substring(0, 100)}...` : itemInfo.flight_name}
            </Text>
          ) : (
            <Text>未开启灰度实验</Text>
          );
        } else {
          return (
            <Space vertical={true}>
              {' '}
              {flight_info_list.map(flight => {
                const url =
                  versionInfo.app_id === AppSettingId.CC_IOS || versionInfo.app_id === AppSettingId.CC_ANDROID
                    ? `https://libra-sg.tiktok-row.net/libra/flight/${flight.flightId}`
                    : `https://data.bytedance.net/libra/flight/${flight.flightId}`;

                return (
                  <Text key={flight.flightId} link={{ href: url, target: '_blank' }}>
                    {flight.flightName.length > 80 ? `${flight.flightName.substring(0, 100)}...` : flight.flightName}
                  </Text>
                );
              })}
            </Space>
          );
        }
      },
    },
    {
      disable: true,
      title: 'owner',
      width: '5%',
      dataIndex: 'owner',
      render: (val: any, item: VersionStageCheckItem) => {
        const itemInfo = item.item_info as LibraCheckItemInfo;
        if (!itemInfo || !item.owner) {
          return <></>;
        }
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <SemiReactUserGroup users={[item.owner]} triggerType={'hover'} />
          </div>
        );
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem | undefined) => {
        let loading = false;
        const libraInfo = item?.item_info as LibraCheckItemInfo;
        return (
          <Space spacing={'tight'}>
            {libraInfo && stageInfo ? (
              <>
                <Popconfirm
                  title="将在需求群催促技术owner"
                  disabled={stageInfo.status !== VersionStageStatus.OnProgress}
                  onConfirm={() => {
                    loading = true;
                    notifyCheckItem({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        checkItemId: item?.check_item_id ?? '',
                        stage: stageName,
                        itemType: item?.item_type ?? ItemType.LibraGrayCheckItem,
                      },
                    }).then(res => {
                      loading = false;
                    });
                  }}
                >
                  <Button
                    theme={'light'}
                    type={'secondary'}
                    loading={loading}
                    disabled={stageInfo.status !== VersionStageStatus.OnProgress}
                  >
                    催促解决
                  </Button>
                </Popconfirm>
                <LibraExemptApplySideSheet
                  itemInfo={libraInfo}
                  versionInfo={versionInfo}
                  stageInfo={stageInfo}
                  handleSubmit={async (
                    influenceStage: string,
                    influenceScope: string,
                    exemptReason: string,
                    improveMeasure: string,
                  ) => {
                    await applyLibraBlockItemExemption({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        meegoId: libraInfo.meego_id,
                        influenceStage,
                        influenceScope,
                        exemptReason,
                        improveMeasure,
                        userEmail: userSettingState.info.email,
                        itemType: libraItemType,
                      },
                    }).then(res => {
                      if (res) {
                        updateCheckItem(res);
                      }
                    });
                  }}
                  pushButtonClick={async () => {}}
                  approveButtonClick={async () => {}}
                  rejectButtonClick={async () => {}}
                />
              </>
            ) : (
              <></>
            )}
          </Space>
        );
      },
    },
  ];
  const rowKey = (record: VersionStageCheckItem | undefined) => record!.check_item_id;
  return (
    <CheckItemTableContainer
      checkItemTable={
        <Table
          columns={columns}
          rowKey={'check_item_id'}
          dataSource={curLibraGrayItems}
          pagination={{
            pageSize: 5,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      }
      itemTitle={title}
      statusDescription={''}
      pass={checkItems.filter(it => it.status !== CheckItemStatus.Exempt).length === 0}
      hideBatchNotify={false}
      hideStatus={false}
      batchNotify={async () => {
        await batchNotifyChecklist({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            itemType: libraItemType,
            stageName,
          },
        });
      }}
      batchNotifyConfirmTitle={'批量催促技术owners'}
      batchNotifyConfirmText={'将在发版群发送催促准出的卡片'}
      versionInfo={versionInfo}
      stage={stageInfo}
    />
  );
};

const BugResolveRatioSubTable: React.FC<{
  itemInfos: FunctionalBugItemInfo[];
  versionInfo: VersionProcessInfo;
  stageName: string;
  businessName: string;
  businessResolveInfo: BugResolveRatioItemInfo;
  updateCheckItem: (checkItem: VersionStageCheckItem) => void;
}> = ({ itemInfos, versionInfo, stageName, businessName, businessResolveInfo, updateCheckItem }) => {
  const [loginUser, setLoginUser] = useState<string>('');

  fetchLoginEmail().then(ret => {
    setLoginUser(ret.email);
  });
  const columns: ColumnProps<FunctionalBugItemInfo>[] = [
    {
      title: 'Meego链接',
      width: '10%',
      dataIndex: 'name',
      render: (_, row) => <Text link={{ href: row.meego_url, target: '_blank' }}>{row.bug_name}</Text>,
    },
    {
      disable: true,
      title: '优先级',
      width: '5%',
      dataIndex: 'priority',
      filters: [
        {
          text: 'P0',
          value: 'P0',
        },
        {
          text: 'P1',
          value: 'P1',
        },
        {
          text: 'P2',
          value: 'P2',
        },
        {
          text: 'P3',
          value: 'P3',
        },
      ],
      onFilter: (value, record) => {
        if (record) {
          return record.priority === value;
        }
        return false;
      },
      ellipsis: true,
      valueType: 'select',
      render: (val, item) => {
        const internalStatus = item.priority;
        if (internalStatus === 'P0') {
          return (
            <Tag size="large" color="red">
              P0
            </Tag>
          );
        }
        if (internalStatus === 'P1') {
          return (
            <Tag size="large" color="pink">
              P1
            </Tag>
          );
        }
        if (internalStatus === 'S') {
          return (
            <Tag size="large" color="red">
              S
            </Tag>
          );
        }
        if (internalStatus === 'P2') {
          return (
            <Tag size="large" color="red">
              P2
            </Tag>
          );
        }
        if (internalStatus === 'P3') {
          return (
            <Tag size="large" color="red">
              P3
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: '经办人',
      width: '5%',
      dataIndex: 'operator',
      sorter: (a, b) => ((a?.operator?.email ?? '') > (b?.operator?.email ?? '') ? 1 : -1),
      render: (val: any, item) => <UserAvatar checkUser={item.operator} />,
    },
    {
      disable: true,
      title: '报告人',
      width: '5%',
      dataIndex: 'reporter',
      render: (val: any, item) => <UserAvatar checkUser={item.reporter} />,
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item) => {
        let loading = false;
        const bugInfo = item as FunctionalBugItemInfo;
        const stageInfo = getStageInfo(versionInfo.version_stages, stageName);
        return (
          <Space spacing={'tight'}>
            {bugInfo && stageInfo ? (
              <>
                <Popconfirm
                  disabled={
                    businessResolveInfo.status === BugResolveRatioItemStatus.Pass ||
                    businessResolveInfo.status === BugResolveRatioItemStatus.Exempted ||
                    stageInfo.status !== VersionStageStatus.OnProgress
                  }
                  title="将在版本大群催促经办人"
                  onConfirm={() => {
                    loading = true;
                    notifySpecificBusinessBugReslove({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageName,
                        meegoId: item.meego_id,
                        businessName,
                      },
                    }).then(res => {
                      loading = false;
                    });
                  }}
                >
                  <Button
                    theme={'light'}
                    type={'secondary'}
                    loading={loading}
                    disabled={
                      businessResolveInfo.status === BugResolveRatioItemStatus.Pass ||
                      businessResolveInfo.status === BugResolveRatioItemStatus.Exempted ||
                      stageInfo.status !== VersionStageStatus.OnProgress
                    }
                  >
                    催促解决
                  </Button>
                </Popconfirm>
                <BugExemptApplySideSheet
                  bugInfo={bugInfo}
                  versionInfo={versionInfo}
                  stageInfo={stageInfo}
                  businessResolveInfo={businessResolveInfo}
                  handleSubmit={async (
                    influenceStage: string,
                    influenceScope: string,
                    exemptReason: string,
                    improveMeasure: string,
                  ) => {
                    await applyBusinessBugExemption({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        meegoId: bugInfo.meego_id,
                        businessName,
                        influenceStage,
                        influenceScope,
                        exemptReason,
                        improveMeasure,
                        userEmail: loginUser,
                      },
                    }).then(res => {
                      if (res) {
                        updateCheckItem(res);
                        window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${bugInfo.exempt_chat_id}`);
                      }
                    });
                  }}
                  pushButtonClick={async () => {
                    await pushBusinessBugExemptApprove({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        businessName,
                        meegoIssueId: bugInfo.meego_id,
                        userEmail: loginUser,
                      },
                    }).then(res => {
                      if (res) {
                      }
                    });
                  }}
                  approveButtonClick={async () => {
                    await updateBusinessBugExemptApproveStatus({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        businessName,
                        meegoIssueId: bugInfo.meego_id,
                        userEmail: loginUser,
                        approve: true,
                      },
                    }).then(res => {
                      if (res) {
                        updateCheckItem(res);
                      }
                    });
                  }}
                  rejectButtonClick={async () => {
                    await updateBusinessBugExemptApproveStatus({
                      data: {
                        appId: versionInfo.app_id,
                        version: versionInfo.version,
                        stage: stageInfo?.stage_name ?? '',
                        businessName,
                        meegoIssueId: bugInfo.meego_id,
                        userEmail: loginUser,
                        approve: false,
                      },
                    }).then(res => {
                      if (res) {
                        updateCheckItem(res);
                      }
                    });
                  }}
                />
              </>
            ) : (
              <></>
            )}
          </Space>
        );
      },
    },
  ];
  const rowKey = (record: FunctionalBugItemInfo | undefined) => record!.meego_id.toString();
  return (
    <Table
      columns={columns}
      rowKey={'meego_id'}
      dataSource={itemInfos}
      pagination={{
        pageSize: 5,
      }}
      scroll={{
        x: '100%',
        scrollToFirstRowOnChange: true,
      }}
    />
  );
};

export const BugResolveRatioCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  stageName: string;
  updateCheckItem: (checkItem: VersionStageCheckItem) => void;
  filterOpen: boolean;
}> = ({ versionInfo, checkItems, stageName, updateCheckItem, filterOpen }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [operatorList, setOeratorList] = useState<string[]>([]);
  const [checkItem, setCheckItem] = useState<VersionStageCheckItem | undefined>(undefined);
  const [tableDataSource, setTableDataSource] = useState<BugResolveRatioItemInfo[]>([]);

  useEffect(() => {
    const itemInfo = checkItems[0].item_info as TotalBugResolveRatioItemInfo;
    if (!itemInfo) {
      return;
    }
    setCheckItem(checkItems[0]);
  }, [checkItems]);

  useEffect(() => {
    const itemInfo = checkItem?.item_info as TotalBugResolveRatioItemInfo;
    if (!itemInfo) {
      return;
    }
    const allBusinessRationInfo = filterOpen
      ? itemInfo.item_infos.filter(it => it.owner.email === userSettingState.info.email)
      : itemInfo.item_infos;
    const sortedBusinessRationInfo: BugResolveRatioItemInfo[] = [];
    const completeBusinessRationInfo: BugResolveRatioItemInfo[] = [];
    // 抽出ratio < need_resolve_ratio的元素
    for (const businessRationInfo of allBusinessRationInfo) {
      if (businessRationInfo.ratio < businessRationInfo.need_reslove_ratio) {
        sortedBusinessRationInfo.push(businessRationInfo);
      } else {
        completeBusinessRationInfo.push(businessRationInfo);
      }
    }
    sortedBusinessRationInfo.push(...completeBusinessRationInfo);
    setTableDataSource(sortedBusinessRationInfo);
  }, [filterOpen, checkItem]);

  const columns: ColumnProps<BugResolveRatioItemInfo>[] = [
    {
      title: '业务线',
      width: '8%',
      dataIndex: 'name',
      render: (_, row) => (
        <Text link={{ href: `https://meego.larkoffice.com/faceu/issueView/${row.issue_view_id}`, target: '_blank' }}>
          {row.business_name}
        </Text>
      ),
    },
    {
      disable: true,
      title: '状态',
      width: '5%',
      dataIndex: 'status',
      render: (val, item) => {
        if (item.status === BugResolveRatioItemStatus.Blocked) {
          return (
            <Tag size="large" color="red">
              不达标
            </Tag>
          );
        }
        if (item.status === BugResolveRatioItemStatus.Pass) {
          return (
            <Tag size="large" color="green">
              达标
            </Tag>
          );
        }
        if (item.status === BugResolveRatioItemStatus.Assessment) {
          return (
            <Tag size="large" color="yellow">
              申请豁免中
            </Tag>
          );
        }
        if (item.status === BugResolveRatioItemStatus.Exempted) {
          return (
            <Tag size="large" color="yellow">
              已豁免
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: 'Owner',
      width: '5%',
      dataIndex: 'operator',
      render: (val: any, item) => <UserAvatar checkUser={item.owner} />,
    },
    {
      title: '解决率',
      width: '5%',
      render: (val: any, item) => <Text>{item.exempted_ratio}</Text>,
    },
    {
      title: '距离达标需要解决的bug数量',
      width: '8%',
      render: (val: any, item) => <Text>{item.need_reslove_number}</Text>,
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item) => (
        <Space spacing={'tight'}>
          {item ? (
            <BugReolveRatioOperation
              itemInfo={item}
              versionInfo={versionInfo}
              stageName={stageName}
              businessName={item.business_name}
              operatorList={operatorList}
              loginUserEmail={userSettingState.info.email}
              checkItemId={checkItem?.check_item_id ?? ''}
              updateCheckItem={updateCheckItem}
            />
          ) : (
            <></>
          )}
        </Space>
      ),
    },
  ];
  const rowKey = (record: BugResolveRatioItemInfo | undefined) => Number(record!.business_name);
  const expandRowRender = (record: BugResolveRatioItemInfo | undefined) => {
    if (!record || !record.bug_item_list) {
      return <></>;
    }
    return (
      <BugResolveRatioSubTable
        itemInfos={record.bug_item_list}
        versionInfo={versionInfo}
        stageName={stageName}
        businessName={record.business_name}
        businessResolveInfo={record}
        updateCheckItem={updateCheckItem}
      />
    );
  };

  const descriptionText = () => {
    const totalResolveInfo = checkItem?.item_info as TotalBugResolveRatioItemInfo;
    if (!totalResolveInfo) {
      return <></>;
    }
    const damagedGrayStage = getStageInfo(versionInfo.version_stages, 'sub_damaged_gray');
    let neededResolveRate = damagedGrayStage?.status === VersionStageStatus.NotStart ? 80 : 90;
    if (
      versionInfo.app_id === 251501 ||
      versionInfo.app_id === 251502 ||
      versionInfo.app_id === 2020093988 ||
      versionInfo.app_id === 2020093924
    ) {
      neededResolveRate = 85;
    }
    return (
      <>
        <Text>{`当前Bug解决率：${totalResolveInfo.ratio.toFixed(2)}%，豁免后：${totalResolveInfo.exempted_ratio.toFixed(2)}%`}</Text>
        <Text>{`，灰度准入`}</Text>
        {totalResolveInfo.exempted_ratio < neededResolveRate ? (
          <Text type="danger">{`不达标，还需解决${totalResolveInfo.need_reslove_number}个`}</Text>
        ) : (
          <Text type="success">达标</Text>
        )}
      </>
    );
  };

  return tableDataSource.length > 0 ? (
    <CheckItemTableContainer
      checkItemTable={
        <Table
          columns={columns}
          rowKey={'business_name'}
          dataSource={tableDataSource}
          expandedRowRender={expandRowRender}
          pagination={{
            pageSize: 5,
            total: tableDataSource.length,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      }
      itemTitle={'Bug解决率'}
      statusDescription={descriptionText()}
      pass={checkItem?.status === CheckItemStatus.Exempt}
      hideBatchNotify={false}
      hideStatus={false}
      batchNotify={async () => {
        await batchNotifyChecklist({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            itemType: ItemType.BugResolveRatio,
            stageName,
          },
        });
      }}
      batchNotifyConfirmTitle={'批量催促业务线POC'}
      batchNotifyConfirmText={'将在发版群发送催促准出的卡片'}
      versionInfo={versionInfo}
      stage={getStageInfo(versionInfo.version_stages, stageName)}
    />
  ) : (
    <>
      <Empty
        image={<IllustrationNoContent style={{ width: 200, height: 300 }} />}
        darkModeImage={<IllustrationNoContentDark style={{ width: 150, height: 150 }} />}
        title={'没有待办准出项'}
      />
    </>
  );
};

// 测试进度按钮
const QATestProgressOperation: React.FC<{
  itemInfo: TestProgressInfo;
  versionInfo: VersionProcessInfo;
  businessName: string;
  operatorList: string[];
  loginUserEmail: string;
  checkItemId: string;
  stageName: string;
  updateQATestInfo: (newItem: TotalQATestProgress) => void;
}> = ({
  itemInfo,
  operatorList,
  loginUserEmail,
  versionInfo,
  checkItemId,
  businessName,
  stageName,
  updateQATestInfo,
}) => {
  const [loading, setLoading] = useState(false);
  const [stageInfo, setStageInfo] = useState<VersionStageInfo | undefined>();
  const onConfirm = () => {
    setLoading(true);
    notifySpecificQATestProgress({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: stageName,
        businessName,
      },
    }).then(res => {
      setLoading(false);
    });
  };
  useEffect(() => {
    setStageInfo(getStageInfo(versionInfo.version_stages, stageName));
  }, [versionInfo, stageName]);

  const onCancel = () => {
    Toast.warning('取消');
  };
  return (
    <div>
      <Space>
        <Popconfirm
          title="将私发消息单独催促此业务线Owner"
          onConfirm={onConfirm}
          onCancel={onCancel}
          disabled={
            itemInfo.status === TestProgressStatus.Complete || stageInfo?.status !== VersionStageStatus.OnProgress
          }
        >
          <Button
            theme={'light'}
            type={'secondary'}
            disabled={
              itemInfo.status === TestProgressStatus.Complete || stageInfo?.status !== VersionStageStatus.OnProgress
            }
            loading={loading}
          >
            提醒处理
          </Button>
        </Popconfirm>
        {stageInfo ? (
          <QATestProgressDetailRecordSheet
            versionInfo={versionInfo}
            testInfo={itemInfo}
            stageInfo={stageInfo}
            updateQATestInfo={updateQATestInfo}
          />
        ) : (
          <></>
        )}
      </Space>
    </div>
  );
};
export const QATestProgressCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  stageName: string;
  updateCheckItem: (checkItem: VersionStageCheckItem) => void;
  filterOpen: boolean;
}> = ({ versionInfo, checkItems, stageName, updateCheckItem, filterOpen }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [operatorList, setOeratorList] = useState<string[]>([]);
  const [checkItem, setCheckItem] = useState<VersionStageCheckItem | undefined>(undefined);
  const [tableDataSource, setTableDataSource] = useState<TestProgressInfo[]>([]);
  const [pass, setPass] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const newUpdateTestInfo: (item: TotalQATestProgress) => void = (item: TotalQATestProgress) => {
    if (!item) {
      return;
    }
    if (searchParams.get(ReleasePlatformUrlSearchParams.QATestProgress)) {
      const businessName = searchParams.get(ReleasePlatformUrlSearchParams.QATestProgress) ?? '';
      for (let index = 0; index < item.business_test_info.length; index++) {
        const info = item.business_test_info[index];
        if (info.business === businessName) {
          const newItems = [...item.business_test_info];
          newItems.splice(index, 1);
          newItems.unshift(item.business_test_info[index]);
          setTableDataSource(newItems);
        }
      }
    } else {
      setTableDataSource(item.business_test_info);
    }
    // 检查是否所有项目都是 Exempt
    const allExempt = item.business_test_info.every(i => i.status === TestProgressStatus.Complete);

    // 根据检查结果更新 pass 状态
    setPass(allExempt);
  };

  useEffect(() => {
    const stageInfo = getStageInfo(versionInfo.version_stages, stageName);
    if (!stageInfo) {
      return;
    }
    fetchTotalQATestProgress({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: stageInfo.parent_stage_name ? stageInfo.parent_stage_name : stageInfo.stage_name,
        subStage: stageInfo.stage_name,
      },
    }).then(res => {
      if (!res || res.code !== 0 || res.data === undefined) {
        return;
      }
      newUpdateTestInfo(res.data);
    });
    setCheckItem(checkItems[0]);
  }, [checkItems]);

  useEffect(() => {
    const stageInfo = getStageInfo(versionInfo.version_stages, stageName);
    if (!stageInfo) {
      return;
    }
    fetchTotalQATestProgress({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: stageInfo.parent_stage_name ? stageInfo.parent_stage_name : stageInfo.stage_name,
        subStage: stageInfo.stage_name,
      },
    }).then(res => {
      if (!res || res.code !== 0 || !res.data) {
        return;
      }
      const itemInfo = res.data;
      const allBusinessProgressInfo = filterOpen
        ? itemInfo.business_test_info.filter(it => it.owner.email === userSettingState.info.email)
        : itemInfo.business_test_info;
      const sortedBusinessProgressInfo: TestProgressInfo[] = [];
      const completeBusinessProgressInfo: TestProgressInfo[] = [];
      // 抽出未完成的元素
      for (const businessProgressInfo of allBusinessProgressInfo) {
        if (businessProgressInfo.status !== TestProgressStatus.Complete) {
          sortedBusinessProgressInfo.push(businessProgressInfo);
        } else {
          completeBusinessProgressInfo.push(businessProgressInfo);
        }
      }
      sortedBusinessProgressInfo.push(...completeBusinessProgressInfo);
      if (searchParams.get(ReleasePlatformUrlSearchParams.QATestProgress)) {
        const businessName = searchParams.get(ReleasePlatformUrlSearchParams.QATestProgress) ?? '';
        for (let index = 0; index < itemInfo.business_test_info.length; index++) {
          const info = itemInfo.business_test_info[index];
          if (info.business === businessName) {
            const newItems = [...itemInfo.business_test_info];
            newItems.splice(index, 1);
            newItems.unshift(itemInfo.business_test_info[index]);
            setTableDataSource(newItems);
          }
        }
      } else {
        setTableDataSource(sortedBusinessProgressInfo);
      }
    });
  }, [filterOpen, checkItem]);

  const columns: ColumnProps<TestProgressInfo>[] = [
    {
      title: '业务线',
      width: '8%',
      dataIndex: 'name',
      render: (_, row) => <Text>{row.business}</Text>,
    },
    {
      disable: true,
      title: '状态',
      width: '5%',
      dataIndex: 'status',
      render: (val, item) => {
        if (item.status === TestProgressStatus.NotStart) {
          return (
            <Tag size="large" color="red">
              未开始
            </Tag>
          );
        }
        if (item.status === TestProgressStatus.OnProgress) {
          return (
            <Tag size="large" color="yellow">
              测试中
            </Tag>
          );
        }
        if (item.status === TestProgressStatus.Complete) {
          return (
            <Tag size="large" color="green">
              完成
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: 'Poc',
      width: '5%',
      dataIndex: 'operator',
      render: (val: any, item) => <UserAvatar checkUser={item.owner} />,
    },
    {
      title: '测试进度',
      width: '5%',
      render: (val: any, item) => <Text>{item.progress ?? 0}%</Text>,
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item) => (
        <Space spacing={'tight'}>
          {item ? (
            <QATestProgressOperation
              itemInfo={item}
              versionInfo={versionInfo}
              stageName={stageName}
              businessName={item.business}
              operatorList={operatorList}
              loginUserEmail={userSettingState.info.email}
              checkItemId={checkItem?.check_item_id ?? ''}
              updateQATestInfo={newUpdateTestInfo}
            />
          ) : (
            <></>
          )}
        </Space>
      ),
    },
  ];

  const getCustomizeButton = () => {
    if (versionInfo.app_id === 2020092383 || versionInfo.app_id === 35928901) {
      return <PCTestPackageInfo versionInfo={versionInfo} stageName={stageName} />;
    } else if (
      versionInfo.app_id === AppSettingId.RETOUCH_IOS ||
      versionInfo.app_id === AppSettingId.RETOUCH_ANDROID ||
      versionInfo.app_id === AppSettingId.HYPIC_IOS ||
      versionInfo.app_id === AppSettingId.HYPIC_ANDROID ||
      versionInfo.app_id === AppSettingId.DREAMINA_ANDROID ||
      versionInfo.app_id === AppSettingId.PIPPIT_ANDROID ||
      versionInfo.app_id === AppSettingId.TINYCUT_ANDROID
    ) {
      return <></>;
    } else {
      if (stageName === 'user_story' || stageName === 'dreamina_ios_user_story' || stageName === 'default_user_story') {
        return <UserStoryVersionInfo versionInfo={versionInfo} stageName={stageName} />;
      }
      return <CustomComponent versionInfo={versionInfo} stageName={stageName} />;
    }
  };

  return (
    <CheckItemTableContainer
      checkItemTable={
        <Table
          columns={columns}
          rowKey={'business'}
          dataSource={tableDataSource}
          pagination={{
            pageSize: 5,
            total: tableDataSource.length,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      }
      itemTitle={'测试进度'}
      pass={checkItem?.status === CheckItemStatus.Exempt}
      hideBatchNotify={false}
      hideStatus={false}
      batchNotify={async () => {
        await batchNotifyChecklist({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            itemType: ItemType.QATestProgress,
            stageName,
          },
        });
      }}
      batchNotifyConfirmTitle={'批量催促业务线POC'}
      batchNotifyConfirmText={'将给POC私发催促测试的卡片'}
      versionInfo={versionInfo}
      stage={getStageInfo(versionInfo.version_stages, stageName)}
      customizeButton={getCustomizeButton()}
    />
  );
};

export const BMOutputDataCheckList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  title: string;
  stageName: string;
  stageInfo?: VersionStageInfo;
  updateVersionInfo: (newInfo: VersionProcessInfo) => void;
}> = ({ versionInfo, stageInfo, checkItems, title, stageName, updateVersionInfo }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [versionOperator, setVersionOperator] = useState<string[]>([]);
  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [lv_android_qa, setLV_android_qa] = useState<any>([]);
  const [lv_ios_qa, setLV_ios_qa] = useState<any>([]);
  const [form] = ProForm.useForm();
  const [QaEmails, setQaEmails] = useState<any>([]);
  const [versionManagers, setVersionManagers] = useState<any>([]);
  const [appIdToQuery, setAppIdToQuery] = useState<number>(0);
  const [qa_check_item_id, setQa_check_item_id] = useState<string>('');
  const [reviewer_check_item_id, setReviewer_check_item_id] = useState<string>('');
  const [other_qa_check_item_id, setOther_qa_check_item_id] = useState<string>('');
  const [other_reviewer_check_item_id, setOther_reviewer_check_item_id] = useState<string>('');

  const [tableDataSource, setTableDataSource] = useState<VersionStageCheckItem[]>([]);
  const [pass, setPass] = useState<boolean>(false);
  useEffect(() => {
    if (!versionInfo) {
      return;
    }
    const emailsExtra = versionManager(versionInfo);
    setVersionManagers(emailsExtra);
    const AppIdToQuery = versionInfo.app_id === 177501 ? 177502 : 177501;
    setAppIdToQuery(AppIdToQuery);
    const operator = versionManager(versionInfo);
    setVersionOperator(operator);
    const get_QA_emails = async () => {
      const LV_and_Infos = await fetchVersionInfoByCriteria({
        data: {
          appId: 177502,
          version: versionInfo.version,
        },
      });
      const LV_ios_Infos = await fetchVersionInfoByCriteria({
        data: {
          appId: 177501,
          version: versionInfo.version,
        },
      });
      setLV_ios_qa(LV_ios_Infos?.bmInfo[BmType.qa]);
      setLV_android_qa(LV_and_Infos?.bmInfo[BmType.qa]);
    };
    get_QA_emails();
  }, [versionInfo]);
  useEffect(() => {
    findChecklist({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: 'BM_output_data',
      },
    }).then(checkList => {
      if (!checkList) {
        return;
      }
      setQa_check_item_id(checkList.check_items[0].check_item_id);
      setReviewer_check_item_id(checkList.check_items[1].check_item_id);
      setOther_qa_check_item_id(appIdToQuery + checkList.check_items[0].check_item_id.slice(6));
      setOther_reviewer_check_item_id(appIdToQuery + checkList.check_items[1].check_item_id.slice(6));
    });
  }, [appIdToQuery]);

  useEffect(() => {
    setQaEmails([lv_ios_qa, lv_android_qa]);
  }, [lv_android_qa, lv_ios_qa]);

  function asUser(obj: any): User {
    // 断言 obj 是 User 类型，并忽略额外的属性
    return {
      name: obj.name,
      email: obj.email,
      open_id: obj.open_id,
      user_id: obj.user_id,
      employee_no: obj.employee_no,
      avatar: obj.avatar,
    } as User;
  }

  const handleSubmit = async (item: VersionStageCheckItem) => {
    const values = form.getFieldsValue();
    const jsonStrings = values.name.map((i: { key: any }) => i.key);
    const jsonObjects = jsonStrings.map((jsonString: string) => JSON.parse(jsonString));
    const users: User[] = jsonObjects.map(asUser);
    const curReviewItem = {
      ...item,
      status: users ? CheckItemStatus.Exempt : CheckItemStatus.Blocked,
      extraData: {
        ...item.extraData,
        review_organizers: users,
      },
    };
    const curQaItem = {
      ...item,
      check_item_id: qa_check_item_id,
      status: users ? CheckItemStatus.Exempt : CheckItemStatus.Blocked,
      extraData: {
        ...item.extraData,
        review_organizers: users,
      },
    };
    const otherReviewItem = {
      ...item,
      check_item_id: other_reviewer_check_item_id,
      status: users ? CheckItemStatus.Exempt : CheckItemStatus.Blocked,
      extraData: {
        ...item.extraData,
        review_organizers: users,
      },
    };
    const otherQaItem = {
      ...item,
      check_item_id: other_qa_check_item_id,
      status: users ? CheckItemStatus.Exempt : CheckItemStatus.Blocked,
      extraData: {
        ...item.extraData,
        review_organizers: users,
      },
    };
    updateSpecificCheckItem({
      data: {
        appId: appIdToQuery,
        version: versionInfo.version,
        stage: 'BM_output_data',
        checkItemId: other_qa_check_item_id,
        updateData: otherQaItem,
      },
    });
    updateSpecificCheckItem({
      data: {
        appId: appIdToQuery,
        version: versionInfo.version,
        stage: 'BM_output_data',
        checkItemId: other_reviewer_check_item_id,
        updateData: otherReviewItem,
      },
    });
    updateSpecificCheckItem({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: 'BM_output_data',
        checkItemId: qa_check_item_id,
        updateData: curQaItem,
      },
    });
    await updateSpecificCheckItem({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: 'BM_output_data',
        checkItemId: item.check_item_id,
        updateData: curReviewItem,
      },
    }).then(res => {
      setSelectedUsers(users);
      setTableDataSource(prevDataSource =>
        prevDataSource.map(dataItem => {
          if (dataItem.check_item_id === item.check_item_id) {
            // 更新当前项的数据
            return {
              ...dataItem,
              extraData: {
                ...dataItem.extraData,
                review_organizers: users,
              },
            };
          }
          return dataItem;
        }),
      );
    });
  };
  useEffect(() => {
    setTableDataSource(checkItems.slice(1));
    setPass(tableDataSource.find(item => item.description === '复盘组织人')?.extraData.review_organizers?.length !== 0);
  }, [checkItems]);

  const columns = [
    {
      title: '类型',
      width: '6%',
      dataIndex: 'name',
      render: (_: any, row: VersionStageCheckItem) => <Text>{row?.description ?? ''}</Text>,
    },
    {
      disable: true,
      title: '人员',
      width: '50%',
      dataIndex: 'operator',
      render: (val: any, item: VersionStageCheckItem, index: number) => {
        if (item.owner === undefined) {
          return null;
        }
        const { review_organizers } = item.extraData;
        return (
          <div>
            {review_organizers &&
              review_organizers.map((reviewer: any, idx: number) => (
                <UserAvatar key={`review_organizer-${idx}`} checkUser={reviewer} />
              ))}
          </div>
        );
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem, index: number) => (
        <div>
          <Button
            onClick={() => {
              setModalVisible(true);
            }}
          >
            添加复盘组织人
          </Button>
          <Modal
            title="添加复盘组织人"
            visible={isModalVisible}
            width={'60%'}
            onCancel={() => {
              setModalVisible(false);
            }}
            footer={[
              <Button key="cancel" onClick={() => setModalVisible(false)}>
                取消
              </Button>,
              <Button
                key="submit"
                type="primary"
                onClick={() => {
                  setSelectedUsers(item?.extraData.review_organizers ?? []);
                  form.submit();
                  setModalVisible(false); // 关闭 Modal
                }}
              >
                提交
              </Button>,
            ]}
          >
            <ProForm
              form={form}
              onFinish={() => handleSubmit(item)}
              submitter={{
                render: () => null,
              }}
            >
              {buildUserSelector(form, 'name')}
            </ProForm>
          </Modal>
        </div>
      ),
    },
  ];
  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space wrap={true}>
            <Title heading={6} style={{ margin: '8px 0' }}>
              {title}
            </Title>
            <Divider layout={'vertical'} margin={'6px'} />
            {
              <>
                <Tag
                  color={pass ? 'light-green' : 'red'}
                  size={'large'}
                  shape={'square'}
                  type={'ghost'}
                  prefixIcon={pass ? <IconTick /> : <IconCrossStroked />}
                >
                  {pass ? '通过' : '不通过'}
                </Tag>
                <Divider layout={'vertical'} margin={'6px'} />
              </>
            }
            {
              <>
                <Text style={{ margin: '8px 0' }}>{'QA_BM: '}</Text>
                {QaEmails &&
                  QaEmails.map((qa: any, idx: number) => <UserAvatarCopy key={`QA_M-${idx}`} checkUser={qa} />)}
                <Divider layout={'vertical'} margin={'6px'} />
              </>
            }
            <div>{''}</div>
          </Space>
          <div>
            {' '}
            <OperatorComfirmButton
              operatorEmails={versionOperator}
              title={'确认完成'}
              theme={'solid'}
              type={'primary'}
              disable={
                !QaEmails.includes(userSettingState.info.email ?? 'user') &&
                !versionManagers.includes(userSettingState.info.email ?? 'user')
              }
              confirmTitle={'确认完成，流转此阶段'}
              onclick={async () => {
                const newVersionInfo = await turnVersionStage({
                  data: {
                    appId: versionInfo?.app_id ?? 0,
                    version: versionInfo?.version ?? '',
                    stageName:
                      (stageInfo?.status === VersionStageStatus.Complete
                        ? stageInfo?.parent_stage_name
                        : stageInfo?.stage_name) ?? '',
                    userEmail: userSettingState.info.email ?? '',
                  },
                });
                if (newVersionInfo) {
                  updateVersionInfo(newVersionInfo);
                }
              }}
            />
          </div>
        </div>
        <Divider margin={'6px'} />
        <Table
          columns={columns}
          dataSource={tableDataSource}
          rowKey={'check_item_id'}
          pagination={{
            pageSize: 5,
            total: tableDataSource.length,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      </Card>
    </div>
  );
};

function extractEmails(data: VersionStageCheckItem[]): string[] {
  const emails: string[] = [];

  data.forEach(item => {
    item.extraData.QA_BM?.forEach(qaBm => {
      emails.push(qaBm.email);
    });
    item.extraData.review_organizers?.forEach(reviewer => {
      emails.push(reviewer.email as string);
    });
  });

  return Array.from(new Set(emails));
}

export const TimeLineAnalysisCheckList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  title: string;
  stageName: string;
  stageInfo?: VersionStageInfo;
  updateVersionInfo: (newInfo: VersionProcessInfo) => void;
}> = ({ versionInfo, stageInfo, checkItems, title, stageName, updateVersionInfo }) => {
  const [userSettingState] = useModel(UserSettingModule);
  const [versionOperator, setVersionOperator] = useState<string[]>([]);
  const [permittedEmails, setPermittedEmails] = useState<string[]>([]);
  const [versionManagers, setVersionManagers] = useState<string[]>([]);
  const [reviewers, setReviewers] = useState<User[]>([]);
  useEffect(() => {
    if (!versionInfo) {
      return;
    }
    const operator = versionManager(versionInfo);
    setVersionOperator(operator);
    findChecklist({
      data: {
        appId: versionInfo.app_id,
        version: versionInfo.version,
        stage: 'BM_output_data',
      },
    }).then(checkList => {
      if (!checkList) {
        return;
      }
      const review = checkList.check_items[1].extraData.review_organizers;
      if (review) {
        setReviewers(review);
      }

      const emailsExtra = versionManager(versionInfo);
      setVersionManagers(emailsExtra);
      const emails = extractEmails(checkList.check_items);
      const manager = versionManager(versionInfo);
      emails.push(...manager);
      setPermittedEmails(emails);
    });
  }, [versionInfo]);

  const [tableDataSource, setTableDataSource] = useState<VersionStageCheckItem[]>([]);
  const [pass, setPass] = useState<boolean>(false);

  const handleCheckChange = async (item: VersionStageCheckItem) => {
    const curStatus = !item?.extraData.analysis_done;

    setTableDataSource(prevDataSource =>
      prevDataSource.map(dataItem => {
        if (dataItem.check_item_id === item.check_item_id) {
          // 立即更新状态
          return {
            ...dataItem,
            extraData: {
              ...dataItem.extraData,
              analysis_done: curStatus,
            },
          };
        }
        return dataItem;
      }),
    );

    // 异步更新后台数据
    try {
      const newItem = {
        ...item,
        status: curStatus ? CheckItemStatus.Exempt : CheckItemStatus.Blocked,
        extraData: {
          ...item.extraData,
          analysis_done: curStatus,
        },
      };
      const AppIdToQuery = versionInfo.app_id === 177501 ? 177502 : 177501;

      const otherItem = {
        ...item,
        check_item_id: AppIdToQuery + item.check_item_id.slice(6),
        status: curStatus ? CheckItemStatus.Exempt : CheckItemStatus.Blocked,
        extraData: {
          ...item.extraData,
          analysis_done: curStatus,
        },
      };
      const updatePromises = [
        updateSpecificCheckItem({
          data: {
            appId: AppIdToQuery,
            version: versionInfo.version,
            stage: 'timeline_analysis',
            checkItemId: AppIdToQuery + item.check_item_id.slice(6),
            updateData: otherItem,
          },
        }),
        updateSpecificCheckItem({
          data: {
            appId: versionInfo.app_id,
            version: versionInfo.version,
            stage: 'timeline_analysis',
            checkItemId: item.check_item_id,
            updateData: newItem,
          },
        }),
      ];
      await Promise.allSettled(updatePromises);
    } catch (error) {
      // 如果后台更新失败，将状态还原
      setTableDataSource(prevDataSource =>
        prevDataSource.map(dataItem => {
          if (dataItem.check_item_id === item.check_item_id) {
            // 还原状态
            return {
              ...dataItem,
              status: curStatus ? CheckItemStatus.Blocked : CheckItemStatus.Exempt,
              extraData: {
                ...dataItem.extraData,
                analysis_done: !curStatus,
              },
            };
          }
          return dataItem;
        }),
      );
      console.error('更新失败:', error);
    }
  };
  useEffect(() => {
    setTableDataSource(checkItems);
    setPass(checkItems.every(i => i.extraData.analysis_done));
  }, [checkItems]);

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: '事项',
      width: '30%',
      dataIndex: 'name',
      render: (_, row) => <Text strong={true}>{row.description}</Text>,
    },
    {
      title: '是否完成',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem, index: number) => (
        <Checkbox
          checked={item?.extraData.analysis_done}
          onChange={() => handleCheckChange(item)}
          disabled={
            !permittedEmails.includes(userSettingState.info.email ?? 'user') &&
            !versionManagers.includes(userSettingState.info.email ?? 'user')
          }
        />
      ),
    },
  ];
  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space wrap={true}>
            <Title heading={6} style={{ margin: '8px 0' }}>
              {title}
            </Title>
            <Divider layout={'vertical'} margin={'6px'} />
            {
              <>
                <Tag
                  color={pass ? 'light-green' : 'red'}
                  size={'large'}
                  shape={'square'}
                  type={'ghost'}
                  prefixIcon={pass ? <IconTick /> : <IconCrossStroked />}
                >
                  {pass ? '通过' : '不通过'}
                </Tag>
                <Divider layout={'vertical'} margin={'6px'} />
              </>
            }
            {
              <>
                <Text style={{ margin: '8px 0' }}>{'QA_BM: '}</Text>
                {reviewers &&
                  reviewers.map((qa: any, idx: number) => <UserAvatar key={`QA_M-${idx}`} checkUser={qa} />)}
                <Divider layout={'vertical'} margin={'6px'} />
              </>
            }
            <div>{''}</div>
          </Space>
          <div>
            {' '}
            <OperatorComfirmButton
              operatorEmails={versionOperator}
              title={'确认完成'}
              theme={'solid'}
              type={'primary'}
              disable={
                !permittedEmails.includes(userSettingState.info.email ?? 'user') &&
                !versionManagers.includes(userSettingState.info.email ?? 'user')
              }
              confirmTitle={'确认完成，流转此阶段'}
              onclick={async () => {
                const newVersionInfo = await turnVersionStage({
                  data: {
                    appId: versionInfo?.app_id ?? 0,
                    version: versionInfo?.version ?? '',
                    stageName:
                      (stageInfo?.status === VersionStageStatus.Complete
                        ? stageInfo?.parent_stage_name
                        : stageInfo?.stage_name) ?? '',
                    userEmail: userSettingState.info.email ?? '',
                  },
                });
                if (newVersionInfo) {
                  updateVersionInfo(newVersionInfo);
                }
              }}
            />
          </div>
        </div>
        <Divider margin={'6px'} />
        <Table
          columns={columns}
          dataSource={tableDataSource}
          rowKey={'check_item_id'}
          pagination={{
            pageSize: 5,
            total: tableDataSource.length,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      </Card>
    </div>
  );
};

export const FeedBackFollowCheckList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  title: string;
  stageName: string;
}> = ({ versionInfo, checkItems, title, stageName }) => {
  // console.log('versionInfo', versionInfo);
  const [userSettingState] = useModel(UserSettingModule);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [processedFeedback, setProcessedFeedback] = useState<Feedback[]>([]);
  const [searchParams] = useSearchParams();
  const [responsiblePeopleform] = ProForm.useForm();
  const [isResponsiblePeopleModalVisible, setResponsiblePeopleModalVisible] = useState(false);
  const [handlerform] = ProForm.useForm();
  const [currentFeedback, setCurrentFeedback] = useState<{ feedbackID: string; index: number } | null>(null);
  const [isHandlerModalVisible, setHandlerPeopleModalVisible] = useState(false);
  const [modifiedFeedbacks, setModifiedFeedbacks] = useState<Set<Feedback>>(new Set());
  const [isBugTicketModalVisible, setBugTicketModalVisible] = useState(false);
  const [currentBugTicketsUrls, setCurrentBugTicketsUrls] = useState<string[]>([]);
  const [isOncallGroupModalVisible, setOncallGroupModalVisible] = useState(false);
  const [currentOncallGroupUrl, setCurrentOncallGroupUrl] = useState<string>('');
  const [currentEditingRecord, setCurrentEditingRecord] = useState<Feedback | null>(null);
  // 添加自动保存状态
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);
  // 简化排序状态管理，使用单一状态
  const [sortParams, setSortParams] = useState<{ orderBy: string; orderType: 'desc' | 'asc' }[]>([]);
  const [refreshKey, setRefreshKey] = useState(0);

  const [feParams, setFeParams] = useState<{ [field: string]: any }>({});

  const addToModified = (feedback: Feedback) => {
    setModifiedFeedbacks(prev => new Set(prev).add(feedback));
  };

  useEffect(() => {
    // console.log('current params', sortParams);
  }, [sortParams]);

  // 添加自动保存处理函数
  const handleAutoSave = useCallback(async () => {
    if (modifiedFeedbacks.size === 0) {
      return;
    }

    setAutoSaveStatus('saving');
    const feedbacksArray = Array.from(modifiedFeedbacks);
    const toSaveFeedbacks = processedFeedback.filter(pf => feedbacksArray.some(af => af.feedbackID === pf.feedbackID));

    try {
      const result = await updateAllFeedbacks({
        data: {
          feedbacks: toSaveFeedbacks,
          userEmail: userSettingState.info.email,
        },
      });

      if (result.success) {
        setAutoSaveStatus('success');
        Toast.success(`已自动保存 ${toSaveFeedbacks.length} 条修改`);
        setModifiedFeedbacks(new Set());

        // 3秒后重置状态
        setTimeout(() => {
          setAutoSaveStatus('idle');
        }, 3000);
      } else {
        setAutoSaveStatus('error');
        Toast.error(result.message || '自动保存失败');
      }
    } catch (error) {
      setAutoSaveStatus('error');
      Toast.error(`自动保存失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [modifiedFeedbacks, processedFeedback]);

  // 添加监视modifiedFeedbacks变化的Effect，自动保存
  useEffect(() => {
    if (modifiedFeedbacks.size > 0) {
      // 防抖处理，1秒内没有新的修改才触发保存
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }

      autoSaveTimerRef.current = setTimeout(() => {
        handleAutoSave();
      }, 1000);
    }

    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
    };
  }, [modifiedFeedbacks, handleAutoSave]);

  // LV_ANDROID = 177502,
  //     LV_IOS = 177501,
  //     LV_WIN = 2020092383,
  //     LV_MAC = 2020092892,
  //     RETOUCH_IOS = 251501,
  //     RETOUCH_ANDROID = 251502,
  //     DREAMINA_ANDROID = 244127338754,
  //     DREAMINA_IOS = 225469550850,
  const APPID_CONFIG_MAP: Record<number, (info: VersionProcessInfo) => any> = {
    177501: info => ({
      aid: ['1775'],
      appVersion: [info.version],
      deviceOsGeneral: ['ios'],
      channelLabelIds: ['23'],
    }),
    177502: info => ({
      aid: ['1775'],
      appVersion: [info.version],
      deviceOsGeneral: ['android'],
      channelLabelIds: ['23'],
    }),
    2020092383: info => ({
      aid: ['3704'],
      appVersion: [info.version],
      deviceOsGeneral: ['windows', 'macos'],
      channelLabelIds: LABEL_LV_PC.map(x => '163@' + x),
    }),
    2020092892: info => ({
      aid: ['3704'],
      appVersion: [info.version],
      deviceOsGeneral: ['windows', 'macos'],
      channelLabelIds: LABEL_LV_PC.map(x => '163@' + x),
    }),
    251501: info => ({
      aid: ['2515'],
      appVersion: [info.version],
      deviceOsGeneral: ['ios'],
      channelLabelIds: ['23'],
    }),
    251502: info => ({
      aid: ['2515'],
      appVersion: [info.version],
      deviceOsGeneral: ['android'],
      channelLabelIds: ['23'],
    }),
    244127338754: info => ({
      aid: ['581595'],
      appVersion: [info.version],
      deviceOsGeneral: ['android'],
      channelLabelIds: ['23'],
    }),
    225469550850: info => ({
      aid: ['581595'],
      appVersion: [info.version],
      deviceOsGeneral: ['ios'],
      channelLabelIds: ['23'],
    }),
  };

  const generateFilterDefault = (info: VersionProcessInfo) => {
    const feedbackFilterOptions = searchParams.get(ReleasePlatformUrlSearchParams.FeedbackFilterOptions);
    if (feedbackFilterOptions) {
      try {
        const parsedFilters = JSON.parse(decodeURIComponent(feedbackFilterOptions));
        return parsedFilters;
      } catch (error) {
        console.error('Failed to parse filter options from URL', error);
      }
    }

    if (Object.keys(feParams).length > 0) {
      return feParams;
    }
    const number = info.version_stages.findIndex(item => item.display_name.includes('灰度'));
    const firstStage = info.version_stages[number].real_start_time * 1000;
    const nextStage = info.version_stages[number + 1].real_start_time * 1000;
    const timeRange = [String(firstStage), String(nextStage)];

    const configGenerator = APPID_CONFIG_MAP[info.app_id];

    const showParam = searchParams.get(ReleasePlatformUrlSearchParams.ShowNotHandledFeedback);
    const feedbackIds = searchParams.get(ReleasePlatformUrlSearchParams.SelectedFeedback);

    const defaultConfig: any = {
      eredarCreateTimeStampMs: {
        dateValue: timeRange,
      },
    };
    if (feedbackIds) {
      defaultConfig.feedbackID = feedbackIds.split(',');
    }
    if (showParam) {
      defaultConfig.openCapcutFollowStatusList = [
        FollowUpStatus.INITIAL,
        FollowUpStatus.USER_NOT_CONTACTED,
        FollowUpStatus.QA_IN_PROGRESS,
        FollowUpStatus.RD_IN_PROGRESS,
        FollowUpStatus.LOCAL_REPRODUCTION,
      ];
    }
    return configGenerator
      ? {
          ...configGenerator(info),
          ...defaultConfig,
        }
      : defaultConfig;
  };

  const copyShareLinkOfFilters = () => {
    const currentUrl = new URL(window.location.href);

    const defaultParms = generateFilterDefault(versionInfo);

    const filterParams = feParams || defaultParms || {};
    const filterParamsString = encodeURIComponent(JSON.stringify(filterParams));

    currentUrl.searchParams.set(ReleasePlatformUrlSearchParams.FeedbackFilterOptions, filterParamsString);
    currentUrl.searchParams.set(ReleasePlatformUrlSearchParams.ShowNotHandledFeedback, 'true');

    // 复制到剪贴板
    navigator.clipboard
      .writeText(currentUrl.toString())
      .then(() => {
        Toast.success('链接已复制到剪贴板');
      })
      .catch(() => {
        Toast.error('复制链接失败');
      });
  };

  const copyShareLink = (selectedKeys: string[]) => {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set(ReleasePlatformUrlSearchParams.ShowNotHandledFeedback, 'true');
    const selectedFeedbackIds = processedFeedback
      .filter(feedback => selectedKeys.includes(feedback.feedbackID))
      .map(feedback => feedback.feedbackCallbackInfo.feedbackID);

    // 复制到剪贴板
    navigator.clipboard
      .writeText(`${currentUrl.toString()}&selected_feedback=${selectedFeedbackIds.join(',')}`)
      .then(() => {
        Toast.success('链接已复制到剪贴板');
      })
      .catch(() => {
        Toast.error('复制链接失败');
      });
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: string[]) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  function asMaster(obj: any): BuildMasterInfo {
    // 断言 obj 是 User 类型，并忽略额外的属性
    return {
      type: BmType.da, // 随便写的
      email: obj.email,
      nameCN: obj.name,
      openId: obj.open_id,
      avatarUrl: obj.avatar,
    };
  }

  const handleResponsiblePeopleSubmit = async (feedbackID: string, curIndex: number) => {
    if (!currentFeedback) {
      return;
    }
    const values = responsiblePeopleform.getFieldsValue();
    const jsonStrings = values.name.map((i: { key: any }) => i.key);
    const jsonObjects = jsonStrings.map((jsonString: string) => JSON.parse(jsonString));
    const users: BuildMasterInfo[] = jsonObjects.map(asMaster);

    if (selectedRowKeys.includes(currentFeedback.feedbackID)) {
      setProcessedFeedback(prevFeedback =>
        prevFeedback.map((item, i) => {
          if (selectedRowKeys.includes(item.feedbackID)) {
            addToModified(item);
            return { ...item, responsiblePerson: users };
          }
          return item;
        }),
      );
    }
    setProcessedFeedback(prevFeedback =>
      prevFeedback.map((item, i) => {
        if (item.feedbackID === currentFeedback.feedbackID) {
          addToModified(item);
          return { ...item, responsiblePerson: users };
        }
        return item;
      }),
    );
  };

  const handleHandlerSubmit = async (feedbackID: string, curIndex: number) => {
    if (!currentFeedback) {
      return;
    }
    const values = handlerform.getFieldsValue();
    const jsonStrings = values.name.map((i: { key: any }) => i.key);
    const jsonObjects = jsonStrings.map((jsonString: string) => JSON.parse(jsonString));
    const users: BuildMasterInfo[] = jsonObjects.map(asMaster);
    if (selectedRowKeys.includes(currentFeedback.feedbackID)) {
      setProcessedFeedback(prevFeedback =>
        prevFeedback.map((item, i) => {
          if (selectedRowKeys.includes(item.feedbackID)) {
            addToModified(item);
            return { ...item, handler: users };
          }
          return item;
        }),
      );
    }
    setProcessedFeedback(prevFeedback =>
      prevFeedback.map((item, i) => {
        if (item.feedbackID === currentFeedback.feedbackID) {
          addToModified(item);
          return { ...item, handler: users };
        }
        return item;
      }),
    );
  };
  const handleSortClick = (tag: string) => (e: React.MouseEvent) => {
    // 阻止事件冒泡
    e.stopPropagation();

    // 循环切换排序状态：desc -> asc -> none -> desc
    if (sortParams.length === 0) {
      setSortParams([{ orderBy: tag, orderType: 'desc' }]);
    } else if (sortParams[0].orderType === 'desc' && sortParams[0].orderBy === tag) {
      setSortParams([{ orderBy: tag, orderType: 'asc' }]);
    } else {
      setSortParams([]);
    }
    // 强制刷新组件
    setRefreshKey(prev => prev + 1);
  };

  const columns = [
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>反馈链接</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('feedback_id')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: ['feedbackCallbackInfo', 'feedbackID'],
      width: 140,
      editable: false,
      render: (feedbackID: string, record: Feedback) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <a
            href={`https://feedback.bytedance.net/feedback/feedback_list_single/?ids=${feedbackID}`}
            target="_blank"
            style={{
              display: 'inline-block',
              maxWidth: '100%',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
            rel="noreferrer"
            onClick={e => e.stopPropagation()}
          >
            {`${feedbackID}`}
          </a>
          {record.feedbackCallbackInfo?.imageList && record.feedbackCallbackInfo.imageList.length > 0 && (
            <Tooltip content="包含图片附件的反馈">
              <IconImage
                style={{ color: 'var(--semi-color-primary)', fontSize: '14px' }}
                onClick={e => e.stopPropagation()}
              />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>一级标签</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('label_names')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: ['feedbackCallbackInfo', 'labelNames'],
      key: 'firstLabel',
      width: 130,
      editable: false,
      render: (labels: string[]) => labels?.at(-3) || '-',
    },
    {
      title: '二级标签',
      dataIndex: ['feedbackCallbackInfo', 'labelNames'],
      key: 'secondLabel',
      width: 120,
      editable: false,
      render: (labels: string[]) => labels?.at(-2) || '-',
    },
    {
      title: '三级标签',
      dataIndex: ['feedbackCallbackInfo', 'labelNames'],
      key: 'thirdLabel',
      width: 120,
      editable: false,
      render: (labels: string[]) => labels?.at(-1) || '-',
    },
    {
      title: '内容标题',
      dataIndex: ['feedbackCallbackInfo', 'contentTitle'],
      width: 200,
      editable: false,
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>反馈时间</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('eredar_create_timestamp_ms')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: ['feedbackCallbackInfo', 'eredarCreateTimestampMs'],
      width: 140,
      editable: false,
      render: (value: number) => (value ? new Date(value).toLocaleString() : ''),
    },
    {
      title: '版本号',
      dataIndex: 'version',
      width: 100,
      editable: true,
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>完整版本号</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('update_version_code')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: ['feedbackCallbackInfo', 'updateVersionCode'],
      width: 150,
      editable: true,
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>设备ID</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('did')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: ['feedbackCallbackInfo', 'did'],
      width: 200,
      editable: true,
    },
    {
      title: 'appID',
      dataIndex: 'appId',
      width: 120,
      editable: true,
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>跟进状态</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('open_capcut_follow_status_list')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: 'followUpStatus',
      width: 160,
      editable: true,
      render: (statuses: FollowUpStatus, record: Feedback, index: number) => {
        // 创建状态选项
        const statusOptions = Object.values(FollowUpStatus).map(statusItem => ({
          label: statusItem,
          value: statusItem,
        }));
        const status = statuses && statuses.length > 0 ? statuses : '';
        return (
          <Select
            style={{ width: '100%' }}
            placeholder="请选择跟进状态"
            optionList={statusOptions}
            value={status || []}
            onChange={value => {
              console.log('selected', selectedRowKeys);
              if (selectedRowKeys.includes(record.feedbackID)) {
                const oldFeedback = { ...record };
                setProcessedFeedback(prevFeedback =>
                  prevFeedback.map((item, i) => {
                    if (selectedRowKeys.includes(item.feedbackID)) {
                      const updatedFeedback = {
                        ...item,
                        followUpStatus: value as unknown as FollowUpStatus,
                      };
                      addToModified(item);
                      trackFeedbackStatusChange({
                        data: {
                          oldFeedback,
                          newFeedback: updatedFeedback,
                        },
                      });

                      return updatedFeedback;
                    }
                    return item;
                  }),
                );
              }
              setProcessedFeedback(prevFeedback =>
                prevFeedback.map((item, i) => {
                  if (item.feedbackID === record.feedbackID) {
                    const updatedFeedback = {
                      ...item,
                      followUpStatus: value as unknown as FollowUpStatus,
                    };
                    addToModified(item);
                    trackFeedbackStatusChange({
                      data: {
                        oldFeedback: item,
                        newFeedback: updatedFeedback,
                      },
                    });
                    return updatedFeedback;
                  }
                  return item;
                }),
              );
            }}
          />
        );
      },
    },
    {
      title: '负责人',
      dataIndex: 'responsiblePerson',
      width: 120,
      editable: true,
      render: (users: User[], record: Feedback, index: number) => (
        <div>
          {users && users.map((person, i) => <UserAvatarCopy key={i} checkUser={person} />)}
          <Button
            onClick={() => {
              setResponsiblePeopleModalVisible(true);
              setCurrentFeedback({ feedbackID: record.feedbackID, index });
            }}
          >
            修改负责人
          </Button>
        </div>
      ),
    },
    {
      title: '处理人',
      dataIndex: 'handler',
      width: 120,
      editable: true,
      render: (users: User[], record: Feedback, index: number) => (
        <div>
          {users && users.map((person, i) => <UserAvatarCopy key={i} checkUser={person} />)}
          <Button
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              setCurrentFeedback({ feedbackID: record.feedbackID, index });
              setHandlerPeopleModalVisible(true);
            }}
          >
            修改处理人
          </Button>
        </div>
      ),
    },
    {
      title: 'Bug单',
      dataIndex: 'bugTickets',
      width: 300,
      editable: true,
      render: (tickets: FeedbackMeegoInfo[], record: Feedback, index: number) => {
        const handleEditClick = (e: React.MouseEvent<HTMLElement>) => {
          e.stopPropagation();
          setCurrentEditingRecord(record);
          setCurrentBugTicketsUrls(record.bugTickets?.map(item => item.meegoUrl) || []);
          setBugTicketModalVisible(true);
        };
        const handleLinkClick = (e: React.MouseEvent<HTMLElement>) => {
          e.stopPropagation();
        };

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }} onClick={e => e.stopPropagation()}>
            <div style={{ flex: 1, display: 'flex', flexWrap: 'wrap', gap: '4px', overflow: 'hidden' }}>
              {record.bugTickets &&
                record.bugTickets.map((item, i) => (
                  <Text key={i} link={{ href: item.meegoUrl, target: '_blank' }} onClick={handleLinkClick}>
                    {item.meegoName}
                  </Text>
                ))}
            </div>
            <Button icon={<IconEdit />} size="small" onClick={handleEditClick} />
          </div>
        );
      },
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>自定义标签</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('open_capcut_custome_labels')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: 'customLabels',
      width: 240,
      editable: true,
      render: (labels: string[], record: Feedback, index: number) => (
        <TagInput
          style={{ width: '100%' }}
          placeholder="输入标签并按回车确认"
          value={labels || []}
          onChange={value => {
            if (selectedRowKeys.includes(record.feedbackID)) {
              setProcessedFeedback(prevFeedback =>
                prevFeedback.map((item, i) => {
                  if (selectedRowKeys.includes(item.feedbackID)) {
                    addToModified(item);
                    return { ...item, customLabels: value };
                  }
                  return item;
                }),
              );
            }
            setProcessedFeedback(prevFeedback =>
              prevFeedback.map((item, i) => {
                if (item.feedbackID === record.feedbackID) {
                  addToModified(item);
                  return { ...item, customLabels: value };
                }
                return item;
              }),
            );
          }}
          showClear
          validateStatus="default"
        />
      ),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      width: 200,
      editable: true,
      render: (remarks: string, record: Feedback, index: number) => (
        <Input
          placeholder="请输入备注"
          value={remarks || ''}
          onChange={value => {
            if (selectedRowKeys.includes(record.feedbackID)) {
              setProcessedFeedback(prevFeedback =>
                prevFeedback.map((item, i) => {
                  if (selectedRowKeys.includes(item.feedbackID)) {
                    addToModified(item);
                    return { ...item, remarks: value };
                  }
                  return item;
                }),
              );
            }
            setProcessedFeedback(prevFeedback =>
              prevFeedback.map((item, i) => {
                if (item.feedbackID === record.feedbackID) {
                  addToModified(item);
                  return { ...item, remarks: value };
                }
                return item;
              }),
            );
          }}
          showClear
        />
      ),
    },
    {
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <span>优先级</span>
          <Button
            size="small"
            type="tertiary"
            icon={<IconSort />}
            onClick={handleSortClick('open_capcut_priority')}
            style={{ padding: '4px' }}
          />
        </div>
      ),
      dataIndex: 'priority',
      width: 120,
      editable: true,
      render: (priority: string, record: Feedback, index: number) => {
        // 创建优先级选项
        const priorityOptions = Object.values(FeedbackPriority).map(p => ({
          label: p,
          value: p,
        }));

        return (
          <Select
            style={{ width: '100%' }}
            placeholder="请选择优先级"
            optionList={priorityOptions}
            value={priority}
            onChange={(value: any) => {
              if (selectedRowKeys.includes(record.feedbackID)) {
                setProcessedFeedback(prevFeedback =>
                  prevFeedback.map((item, i) => {
                    if (selectedRowKeys.includes(item.feedbackID)) {
                      addToModified(item);
                      return { ...item, priority: value as unknown as FeedbackPriority };
                    }
                    return item;
                  }),
                );
              }
              setProcessedFeedback(prevFeedback =>
                prevFeedback.map((item, i) => {
                  if (item.feedbackID === record.feedbackID) {
                    addToModified(item);
                    return { ...item, priority: value as unknown as FeedbackPriority };
                  }
                  return item;
                }),
              );
            }}
            showClear
          />
        );
      },
    },
    {
      title: '自动归因',
      dataIndex: 'autoReason',
      width: 150,
      editable: true,
      render: (reason: any, record: Feedback, index: number) => {
        const autoAttribution = reason ? `${JSON.stringify(reason).substring(0, 30)}...` : '';

        return (
          <Input
            placeholder="请输入自动归因"
            value={autoAttribution}
            onChange={value => {
              if (selectedRowKeys.includes(record.feedbackID)) {
                setProcessedFeedback(prevFeedback =>
                  prevFeedback.map((item, i) => {
                    if (selectedRowKeys.includes(item.feedbackID)) {
                      addToModified(item);
                      return { ...item, autoReason: value };
                    }
                    return item;
                  }),
                );
              }
              setProcessedFeedback(prevFeedback =>
                prevFeedback.map((item, i) => {
                  if (item.feedbackID === record.feedbackID) {
                    addToModified(item);
                    return { ...item, autoReason: value };
                  }
                  return item;
                }),
              );
            }}
            showClear
          />
        );
      },
    },
    {
      title: 'oncall群',
      dataIndex: 'bugTickets',
      width: 300,
      editable: true,
      render: (tickets: FeedbackMeegoInfo[], record: Feedback, index: number) => {
        const handleEditClick = (e: React.MouseEvent<HTMLElement>) => {
          e.stopPropagation();
          setCurrentEditingRecord(record);
          setCurrentOncallGroupUrl('');
          setOncallGroupModalVisible(true);
        };
        const handleLinkClick = (e: React.MouseEvent<HTMLElement>) => {
          e.stopPropagation();
        };

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }} onClick={e => e.stopPropagation()}>
            <div style={{ flex: 1, display: 'flex', flexWrap: 'wrap', gap: '4px', overflow: 'hidden' }}>
              {record.oncallGroup && (
                <Text link={{ href: record.oncallGroup, target: '_blank' }} onClick={handleLinkClick}>
                  {record.oncallGroup}
                </Text>
              )}
            </div>
            <Button icon={<IconEdit />} size="small" onClick={handleEditClick} />
          </div>
        );
      },
    },
  ];

  const CustomRenderVoiceList = ({
    hilbertFilters,
    listRequest,
    openVoiceDetailSideSheet,
    closeVoiceDetailSideSheet,
  }: {
    hilbertFilters: any;
    listRequest: (options: { offset: number; limit: number; hilbertFilters: any }) => Promise<SearchFeedbacksResp>;
    openVoiceDetailSideSheet: (feedback: any) => void;
    closeVoiceDetailSideSheet: () => void;
  }) => {
    const [voiceList, setVoiceList] = useState<any>();
    const [loading, setLoading] = useState(false);
    const [processing, setProcessing] = useState(false);
    const [fetchError, setFetchError] = useState<string | null>(null);
    const [processError, setProcessError] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [feedbackCount, setFeedbackCount] = useState(0);
    // 添加列显示控制状态
    const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>({
      反馈链接: true,
      一级标签: true,
      二级标签: true,
      三级标签: true,
      内容标题: true,
      反馈时间: true,
      版本号: true,
      AppID: true,
      完整版本号: true,
      设备id: true,
      跟进状态: true,
      负责人: true,
      处理人: true,
      Bug单: true,
      自定义标签: true,
      备注: true,
      优先级: true,
      自动归因: true,
      oncall群: true,
    });
    // const [filteredColumns, setFilteredColumns] = useState<any>([]);

    const fetchData = async (page: number, size: number) => {
      setLoading(true);
      setFetchError(null);
      try {
        const offset = (page - 1) * size;
        const response: any = await listRequest({
          offset,
          limit: size,
          hilbertFilters,
        });

        // console.log('获取到的反馈数据:', response);
        console.log('筛选条件： ', hilbertFilters);
        setFeedbackCount(response.count || 0);
        setVoiceList(response.details || []);
        return response.details || [];
      } catch (error) {
        const errorMessage = '获取反馈列表失败';
        setFetchError(errorMessage);
        Toast.error(errorMessage);
        console.error(errorMessage, error);
        return [];
      } finally {
        setLoading(false);
      }
    };

    // 处理页码或每页条数变化
    const handlePaginationChange = async (page: number, size: number) => {
      setCurrentPage(page);
      setPageSize(size);
      await fetchData(page, size);
    };

    const enhancedColumns = useMemo(
      () =>
        // `columns` is from the FeedBackFollowCheckList scope
        columns.map(column => ({
          ...column,
          onCell: (record: Feedback) => ({
            onClick: (e: React.MouseEvent) => {
              if (
                e.target instanceof HTMLElement &&
                (e.target.closest('.semi-modal') ||
                  e.target.closest('.user-select-modal') ||
                  e.target.closest('.bug-ticket-modal') ||
                  e.target.closest('.oncall-group-modal') ||
                  e.target.closest('button') ||
                  e.target.closest('.semi-select') ||
                  e.target.closest('.semi-input') ||
                  e.target.closest('.semi-tag-input') ||
                  e.target.closest('.user-selector-wrapper'))
              ) {
                // 如果点击的是Modal或其他UI控件，不触发打开详情
                return;
              }
              openVoiceDetailSideSheet(record.feedbackCallbackInfo);
            },
            style: { cursor: 'pointer' },
          }),
        })),
      [columns, openVoiceDetailSideSheet],
    );

    const filteredColumns = useMemo(
      () =>
        enhancedColumns.filter(column => {
          // 处理 title 是 React 组件的情况
          if (typeof column.title === 'object') {
            // 检查特殊列，如 feedback_id, label_names, did
            if (
              (column.dataIndex && Array.isArray(column.dataIndex) && column.dataIndex[1] === 'feedbackID') ||
              (column.dataIndex && Array.isArray(column.dataIndex) && column.dataIndex[1] === 'labelNames') ||
              column.dataIndex === 'did'
            ) {
              return true;
            }
            // 尝试从 title 中提取文本内容进行匹配
            const titleStr = column.title.props?.children?.[0]?.props?.children;
            return titleStr && visibleColumns[titleStr];
          }
          // 处理 title 是字符串的情况
          return visibleColumns[column.title as string];
        }),
      [enhancedColumns, visibleColumns],
    );

    useEffect(() => {
      fetchData(currentPage, pageSize);
    }, [hilbertFilters, currentPage, pageSize]);

    // 处理反馈数据
    useEffect(() => {
      if (!voiceList || voiceList.length === 0) {
        setProcessedFeedback([]);
        return;
      }

      setProcessing(true);
      setProcessError(null);
      batchProcessFeedback({
        data: {
          appId: versionInfo.app_id,
          version: versionInfo.version,
          callbackInfo: voiceList,
          versionInfo,
        },
      })
        .then(data => {
          if (data) {
            setProcessedFeedback(data);
          } else {
            setProcessedFeedback([]);
          }
        })
        .catch(error => {
          const errorMessage = '处理反馈数据失败';
          setProcessError(errorMessage);
          Toast.error(errorMessage);
          console.error(errorMessage, error);
          setProcessedFeedback([]);
        })
        .finally(() => {
          setProcessing(false);
        });
    }, [voiceList]);

    return (
      <div>
        {(loading || processing) && (
          <div
            style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', padding: '20px' }}
          >
            <Spin />
            <Text style={{ marginLeft: '8px' }}>{loading ? '正在获取数据...' : '正在处理数据...'}</Text>
          </div>
        )}
        {!loading && !processing && (fetchError || processError) && (
          <div
            style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', padding: '20px' }}
          >
            <IconAlertTriangle style={{ color: 'red', marginRight: '8px' }} />
            <Text style={{ color: 'red' }}>{fetchError || processError}</Text>
          </div>
        )}
        {!loading && !processing && !fetchError && !processError && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '16px', marginLeft: '16px' }}>
              <Popover
                content={
                  <div style={{ padding: '8px', width: '250px' }}>
                    <Typography.Title heading={6} style={{ marginBottom: '12px' }}>
                      显示列配置
                    </Typography.Title>
                    <CheckboxGroup
                      value={Object.entries(visibleColumns)
                        .filter(([_, visible]) => visible)
                        .map(([t]) => t)}
                      onChange={(value: string[]) => {
                        const newVisibleColumns = { ...visibleColumns };
                        Object.keys(newVisibleColumns).forEach(t => {
                          newVisibleColumns[t] = value.includes(t);
                        });
                        setVisibleColumns(newVisibleColumns);
                      }}
                      direction="vertical"
                    >
                      {Object.keys(visibleColumns).map(t => (
                        <Checkbox key={t} value={t}>
                          {t}
                        </Checkbox>
                      ))}
                    </CheckboxGroup>
                  </div>
                }
                trigger="click"
                position="right"
              >
                <Button icon={<IconSetting />} type="tertiary">
                  列显示设置
                </Button>
              </Popover>
              <Button
                icon={<IconSetting />}
                type="primary"
                onClick={() => {
                  copyShareLinkOfFilters();
                }}
              >
                分享筛选项
              </Button>
            </div>

            <Modal
              title="编辑Bug单链接(输入后按回车确认)"
              mask={false}
              visible={isBugTicketModalVisible}
              // @ts-ignore 去掉注释编译会报错，但是这行代码有用
              afterClose={e => {
                if (e) {
                  e.stopPropagation();
                }
              }}
              onCancel={e => {
                if (e) {
                  e.stopPropagation();
                }
                setBugTicketModalVisible(false);
                setCurrentEditingRecord(null);
                setCurrentBugTicketsUrls([]);
              }}
              onOk={async e => {
                if (e) {
                  e.stopPropagation();
                }
                if (currentEditingRecord) {
                  try {
                    // 如果数组为空，直接设置为空数组
                    if (currentBugTicketsUrls.length === 0) {
                      if (selectedRowKeys.includes(currentEditingRecord.feedbackID)) {
                        setProcessedFeedback(prevFeedback =>
                          prevFeedback.map(item => {
                            if (selectedRowKeys.includes(item.feedbackID)) {
                              addToModified(item);
                              return { ...item, bugTickets: [] };
                            }
                            return item;
                          }),
                        );
                      } else {
                        setProcessedFeedback(prevFeedback =>
                          prevFeedback.map(item => {
                            if (item.feedbackID === currentEditingRecord.feedbackID) {
                              addToModified(item);
                              return { ...item, bugTickets: [] };
                            }
                            return item;
                          }),
                        );
                      }
                      setBugTicketModalVisible(false);
                      setCurrentEditingRecord(null);
                      setCurrentBugTicketsUrls([]);
                      Toast.success('Bug单已清空');
                      return;
                    }

                    // 获取Meego信息
                    const result = await getMeegoInfo({
                      data: {
                        meegoUrl: currentBugTicketsUrls,
                      },
                    });
                    if (result && Array.isArray(result)) {
                      if (selectedRowKeys.includes(currentEditingRecord.feedbackID)) {
                        setProcessedFeedback(prevFeedback =>
                          prevFeedback.map(item => {
                            if (selectedRowKeys.includes(item.feedbackID)) {
                              addToModified(item);
                              return { ...item, bugTickets: result };
                            }
                            return item;
                          }),
                        );
                      } else {
                        setProcessedFeedback(prevFeedback =>
                          prevFeedback.map(item => {
                            if (item.feedbackID === currentEditingRecord.feedbackID) {
                              addToModified(item);
                              return { ...item, bugTickets: result };
                            }
                            return item;
                          }),
                        );
                      }
                      setBugTicketModalVisible(false);
                      setCurrentEditingRecord(null);
                      setCurrentBugTicketsUrls([]);
                      Toast.success('Bug单更新成功');
                    } else {
                      Toast.error('获取Bug信息失败，请重试');
                    }
                  } catch (error) {
                    // 捕获请求异常
                    console.error('获取Bug信息失败:', error);
                    Toast.error('获取Bug信息失败，请检查链接是否正确');
                  }
                }
              }}
              width={600}
              style={{ zIndex: 1050 }}
              maskStyle={{ zIndex: 1040 }}
              getPopupContainer={() => document.body}
              className="bug-ticket-modal"
            >
              <div onClick={e => e.stopPropagation()}>
                <TagInput
                  placeholder="输入Bug单链接并按回车"
                  value={currentBugTicketsUrls}
                  defaultValue={currentBugTicketsUrls}
                  onChange={value => setCurrentBugTicketsUrls(value)}
                  showClear
                  style={{ width: '100%' }}
                  validateStatus="default"
                />
                <Typography.Text type="tertiary">提示：每个链接按回车键确认，支持输入多个链接</Typography.Text>
              </div>
            </Modal>

            <Modal
              title="输入oncall群链接(输入后按回车确认)"
              mask={false}
              visible={isOncallGroupModalVisible}
              // @ts-ignore 去掉注释编译会报错，但是这行代码有用
              afterClose={e => {
                if (e) {
                  e.stopPropagation();
                }
              }}
              onCancel={e => {
                if (e) {
                  e.stopPropagation();
                }
                setOncallGroupModalVisible(false);
                setCurrentEditingRecord(null);
                setCurrentOncallGroupUrl('');
              }}
              onOk={async e => {
                if (e) {
                  e.stopPropagation();
                }
                if (currentEditingRecord && currentOncallGroupUrl) {
                  try {
                    if (selectedRowKeys.includes(currentEditingRecord.feedbackID)) {
                      setProcessedFeedback(prevFeedback =>
                        prevFeedback.map(item => {
                          if (selectedRowKeys.includes(item.feedbackID)) {
                            addToModified(item);
                            return { ...item, oncallGroup: currentOncallGroupUrl };
                          }
                          return item;
                        }),
                      );
                    } else {
                      setProcessedFeedback(prevFeedback =>
                        prevFeedback.map(item => {
                          if (item.feedbackID === currentEditingRecord.feedbackID) {
                            addToModified(item);
                            return { ...item, oncallGroup: currentOncallGroupUrl };
                          }
                          return item;
                        }),
                      );
                    }
                    setOncallGroupModalVisible(false);
                    setCurrentEditingRecord(null);
                    setCurrentOncallGroupUrl('');
                    Toast.success('oncall群链接更新成功');
                  } catch (error) {
                    // 捕获请求异常
                    console.error('失败:', error);
                    Toast.error('失败，请检查链接是否正确');
                  }
                }
              }}
              width={600}
              style={{ zIndex: 1050 }}
              maskStyle={{ zIndex: 1040 }}
              getPopupContainer={() => document.body}
              className="oncall-group-modal"
            >
              <div onClick={e => e.stopPropagation()}>
                <TagInput
                  placeholder="输入oncall群并按回车"
                  value={[currentOncallGroupUrl]}
                  defaultValue={[currentOncallGroupUrl]}
                  onChange={value => setCurrentOncallGroupUrl(value[0])}
                  showClear
                  max={1}
                  style={{ width: '100%' }}
                  validateStatus="default"
                />
              </div>
            </Modal>

            <Table
              rowSelection={rowSelection}
              dataSource={processedFeedback}
              columns={filteredColumns}
              rowKey="feedbackID"
              scroll={{ x: 2000, y: 1000 }}
              resizable={true}
              pagination={false}
            />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '1px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Pagination
                  total={feedbackCount}
                  showSizeChanger
                  pageSizeOpts={[10, 20, 50, 100, 500]}
                  pageSize={pageSize}
                  currentPage={currentPage}
                  onChange={handlePaginationChange}
                  showTotal
                />
                <Text type="tertiary" size="small" strong={true}>
                  共{feedbackCount}条
                </Text>
                <Text type="tertiary" size="small">
                  （实际每页条数可能小于{pageSize}条）
                </Text>
              </div>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                {/* 显示自动保存状态 */}
                {autoSaveStatus === 'saving' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Spin size="small" />
                    <Text style={{ marginLeft: '8px' }}>正在保存...</Text>
                  </div>
                )}
                {autoSaveStatus === 'success' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <IconTick style={{ color: 'green' }} />
                    <Text style={{ marginLeft: '8px', color: 'green' }}>已自动保存</Text>
                  </div>
                )}
                {autoSaveStatus === 'error' && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <IconAlertTriangle style={{ color: 'red' }} />
                    <Text style={{ marginLeft: '8px', color: 'red' }}>保存失败</Text>
                  </div>
                )}
                <Button
                  disabled={selectedRowKeys.length === 0}
                  onClick={() => {
                    copyShareLink(selectedRowKeys);
                  }}
                >
                  分享选中反馈 ({selectedRowKeys.length})
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const VoiceListCustomSlot = (info: CemOpenVoiceSearchFeedbackCallbackInfo) => (
    <div>
      <Typography.Text type="tertiary" size="normal">
        反馈ID: {info?.feedbackID || ''}
      </Typography.Text>
    </div>
  );

  const handleFeParamsChange = (params: any) => {
    console.log(params);
    setFeParams(params);
  };
  return (
    <div>
      <CemOpenVoiceSearch
        key={`voice-search-${refreshKey}`}
        defaultFeParams={generateFilterDefault(versionInfo)}
        onFeParamsChange={handleFeParamsChange}
        voiceSideSheetCustomSlot={VoiceListCustomSlot}
        metaKey="cem@cap_cut"
        queryVoiceFieldDbKeys={[
          'aid',
          'os_version',
          'app_version',
          'open_capcut_follow_status_list',
          'open_capcut_custome_labels',
          'open_capcut_priority',
          'feedback_id',
          'label_names',
          'label_ids',
          'image_list',
          'did',
          'update_version_code',
          'label_type',
        ]}
        extraVoiceFieldDbKeys={[
          { dbKey: 'open_capcut_follow_status_list', displayName: '跟进状态' },
          { dbKey: 'open_capcut_custome_labels', displayName: '自定义标签' },
          { dbKey: 'open_capcut_priority', displayName: '优先级' },
          { dbKey: 'label_type', displayName: '标签类型' },
        ]}
        voiceListCustomOrderFields={
          sortParams.length > 0 ? sortParams.map(param => ({ orderBy: param.orderBy, orderType: param.orderType })) : []
        }
        customRenderVoiceList={CustomRenderVoiceList}
        layout={'vertical'}
      />
      <br />

      <Modal
        title="修改负责人"
        visible={isResponsiblePeopleModalVisible}
        width={'60%'}
        mask={false}
        getPopupContainer={() => document.body}
        style={{ zIndex: 1050 }}
        maskStyle={{ zIndex: 1040 }}
        className="user-select-modal"
        onCancel={e => {
          if (e) {
            e.stopPropagation();
          }
          setCurrentFeedback(null);
          setResponsiblePeopleModalVisible(false);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={e => {
              if (e) {
                e.stopPropagation();
              }
              setCurrentFeedback(null);
              setResponsiblePeopleModalVisible(false);
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={e => {
              if (e) {
                e.stopPropagation();
              }
              responsiblePeopleform.submit();
              setResponsiblePeopleModalVisible(false);
            }}
          >
            提交
          </Button>,
        ]}
      >
        <div onClick={(e: React.MouseEvent) => e.stopPropagation()}>
          <ProForm
            form={responsiblePeopleform}
            // @ts-ignore
            onFinish={() => {
              if (currentFeedback) {
                handleResponsiblePeopleSubmit(currentFeedback.feedbackID, currentFeedback.index);
              }
            }}
            submitter={{
              render: () => null,
            }}
          >
            {buildUserSelector(responsiblePeopleform, 'name')}
          </ProForm>
        </div>
      </Modal>

      <Modal
        title="修改处理人"
        visible={isHandlerModalVisible}
        width={'60%'}
        mask={false}
        getPopupContainer={() => document.body}
        style={{ zIndex: 1050 }}
        maskStyle={{ zIndex: 1040 }}
        className="user-select-modal"
        onCancel={e => {
          if (e) {
            e.stopPropagation();
          }
          setCurrentFeedback(null);
          setHandlerPeopleModalVisible(false);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={e => {
              if (e) {
                e.stopPropagation();
              }
              setCurrentFeedback(null);
              setHandlerPeopleModalVisible(false);
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={e => {
              if (e) {
                e.stopPropagation();
              }
              handlerform.submit();
              setHandlerPeopleModalVisible(false);
            }}
          >
            提交
          </Button>,
        ]}
      >
        <div onClick={(e: React.MouseEvent) => e.stopPropagation()}>
          <ProForm
            form={handlerform}
            // @ts-ignore
            onFinish={() => {
              if (currentFeedback) {
                handleHandlerSubmit(currentFeedback.feedbackID, currentFeedback.index);
              }
            }}
            submitter={{
              render: () => null,
            }}
          >
            {buildUserSelector(handlerform, 'name')}
          </ProForm>
        </div>
      </Modal>
    </div>
  );
};

export const FeedbackCheckItemList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  stageName: string;
  stageInfo?: VersionStageInfo;
  updateCheckItem: (checkItem: VersionStageCheckItem) => void;
}> = ({ versionInfo, stageInfo, checkItems, stageName, updateCheckItem }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [searchParams] = useSearchParams();
  const [tableDataSource, setTableDataSource] = useState<VersionStageCheckItem[]>([]);
  const [appSettingState] = useModel(AppSettingModule);
  const [pass, setPass] = useState<boolean>(false);
  const [bugResolveInfo, setBugResolveInfo] = useState<any>({});
  const prefix = `${appSettingState.info.name}-${appSettingState.info.platform}-${versionInfo.version}`;
  const [feedbackProcessInfo, setFeedbackProcessInfo] = useState<any>(null);
  const [userStoryStageTime, setUserStoryStageTime] = useState<number>(0);
  const [risingInfo, setRisingInfo] = useState<{ areRising: boolean; allLabels: { level2: any[]; level3: any[] } }>({
    areRising: false,
    allLabels: {
      level2: [],
      level3: [],
    },
  });
  const currStageEndTime = getStageInfo(versionInfo.version_stages, stageName)?.real_end_time ?? 0;
  const endTimestamp = currStageEndTime > 0 ? currStageEndTime * 1000 : Date.now();
  // 剪映移动端和醒图是通用指标，pc端指标特殊。
  const [isPC, setIsPC] = useState<boolean>(false);
  const [isUniversal, setIsUniversal] = useState<boolean>(false);
  // 计算跟进率
  const calculateFollowUpRate = (statistics: any) => {
    if (!statistics || statistics.total === 0) {
      return 0;
    }
    const followUpRate = 1 - statistics.noLabel / statistics.total;
    return Number((followUpRate * 100).toFixed(2));
  };

  // 计算归因率
  const calculateAttributionRate = (statistics: any) => {
    if (!statistics || statistics.total === 0) {
      return 0;
    }
    const notFollowed = statistics.noLabel;
    const notReproduced = statistics.notHandled + statistics.localReproduction + statistics.contactErrorNoReproduction;
    const attributionRate = 1 - notReproduced / (statistics.total - notFollowed);
    return Number((attributionRate * 100).toFixed(2));
  };

  const fetchFeedbackData = async (app_id: number, version: string) => {
    const statics = await getFeedbackProcessInfo({
      data: {
        appId: app_id,
        version,
        endTimestamp: endTimestamp,
      },
    });
    if (statics) {
      setFeedbackProcessInfo(statics);
    }
    return statics;
  };

  const fetchRisingData = async (app_id: number, version: string) => {
    const info = await getRecentFeedbackRising({
      data: {
        appId: app_id,
        version,
        endTimestamp: endTimestamp,
      },
    });
    if (info) {
      setRisingInfo(info);
    }
  };

  const fetchBugResolveData = async (app_id: number, version: string) => {
    try {
      const info = await getVersionBugResolveInfo({
        data: {
          appId: app_id,
          version,
        },
      });
      if (info) {
        setBugResolveInfo(info);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (
      versionInfo.app_id === AppSettingId.LV_ANDROID ||
      versionInfo.app_id === AppSettingId.LV_IOS ||
      versionInfo.app_id === AppSettingId.RETOUCH_IOS ||
      versionInfo.app_id === AppSettingId.RETOUCH_ANDROID
    ) {
      fetchBugResolveData(versionInfo.app_id, versionInfo.version);
      setIsUniversal(true);
    } else if (versionInfo.app_id === AppSettingId.LV_WIN || versionInfo.app_id === AppSettingId.LV_MAC) {
      fetchRisingData(versionInfo.app_id, versionInfo.version);
      setIsPC(true);
    }

    const testSameInfo = fetchFeedbackData(versionInfo.app_id, versionInfo.version);

    const followupRate = calculateFollowUpRate(feedbackProcessInfo);
    const attributionRate = calculateAttributionRate(feedbackProcessInfo);

    console.log(`##### debug ${JSON.stringify(testSameInfo)} ${JSON.stringify(feedbackProcessInfo)}`);

    if (
      (versionInfo.app_id === AppSettingId.LV_ANDROID ||
        versionInfo.app_id === AppSettingId.LV_IOS ||
        versionInfo.app_id === AppSettingId.RETOUCH_IOS ||
        versionInfo.app_id === AppSettingId.RETOUCH_ANDROID) &&
      followupRate >= 95 &&
      attributionRate >= 50
    ) {
      updateSpecificFeedbackItemStatus({
        data: {
          appId: versionInfo.app_id,
          version: versionInfo.version,
          metric: '过程指标',
          status: CheckItemStatus.Exempt,
          stage: stageName,
        },
      });
    } else if (
      versionInfo.app_id === AppSettingId.LV_ANDROID ||
      versionInfo.app_id === AppSettingId.LV_IOS ||
      versionInfo.app_id === AppSettingId.RETOUCH_IOS ||
      versionInfo.app_id === AppSettingId.RETOUCH_ANDROID
    ) {
      updateSpecificFeedbackItemStatus({
        data: {
          appId: versionInfo.app_id,
          version: versionInfo.version,
          metric: '过程指标',
          status: CheckItemStatus.Blocked,
          stage: stageName,
        },
      });
    }

    if (
      (versionInfo.app_id === AppSettingId.LV_WIN || versionInfo.app_id === AppSettingId.LV_MAC) &&
      calculateFollowUpRate(feedbackProcessInfo) >= 98
    ) {
      updateSpecificFeedbackItemStatus({
        data: {
          appId: versionInfo.app_id,
          version: versionInfo.version,
          metric: '过程指标',
          status: CheckItemStatus.Exempt,
          stage: stageName,
        },
      });
    } else if (versionInfo.app_id === AppSettingId.LV_WIN || versionInfo.app_id === AppSettingId.LV_MAC) {
      updateSpecificFeedbackItemStatus({
        data: {
          appId: versionInfo.app_id,
          version: versionInfo.version,
          metric: '过程指标',
          status: CheckItemStatus.Blocked,
          stage: stageName,
        },
      });
    }

    if (
      (versionInfo.app_id === AppSettingId.LV_WIN || versionInfo.app_id === AppSettingId.LV_MAC) &&
      risingInfo.areRising
    ) {
      updateSpecificFeedbackItemStatus({
        data: {
          appId: versionInfo.app_id,
          version: versionInfo.version,
          metric: '激增指标',
          status: CheckItemStatus.Blocked,
          stage: stageName,
        },
      });
    } else if (
      (versionInfo.app_id === AppSettingId.LV_WIN || versionInfo.app_id === AppSettingId.LV_MAC) &&
      !risingInfo.areRising
    ) {
      updateSpecificFeedbackItemStatus({
        data: {
          appId: versionInfo.app_id,
          version: versionInfo.version,
          metric: '激增指标',
          status: CheckItemStatus.Exempt,
          stage: stageName,
        },
      });
    }

    const userStoryStage = versionInfo.version_stages.find(stage => stage.display_name === '用户故事');
    if (userStoryStage) {
      setUserStoryStageTime(userStoryStage.real_start_time);
    }

    const showParam = searchParams.get(ReleasePlatformUrlSearchParams.ShowNotHandledFeedback);
    if (showParam) {
      setVisible(true);
    }
    // }, [versionInfo, stageName]);

    // useEffect(() => {
    if (userStoryStageTime > 0 && bugResolveInfo) {
      const currentTime = new Date().getTime();
      // TODO: updateSpecificFeedbackItemStatus需要迁移逻辑到updatechecklist
      // 用户故事之前
      if (currentTime < userStoryStageTime) {
        if (bugResolveInfo.p1Stats?.total - bugResolveInfo.p1Stats?.completed <= 0) {
          updateSpecificFeedbackItemStatus({
            data: {
              appId: versionInfo.app_id,
              version: versionInfo.version,
              metric: '结果指标',
              status: CheckItemStatus.Exempt,
              stage: stageName,
            },
          });
        } else {
          updateSpecificFeedbackItemStatus({
            data: {
              appId: versionInfo.app_id,
              version: versionInfo.version,
              metric: '结果指标',
              status: CheckItemStatus.Blocked,
              stage: stageName,
            },
          });
        }
      } else {
        // 用户故事之后
        if (bugResolveInfo.p0Stats?.total - bugResolveInfo.p0Stats?.completed <= 0) {
          updateSpecificFeedbackItemStatus({
            data: {
              appId: versionInfo.app_id,
              version: versionInfo.version,
              metric: '结果指标',
              status: CheckItemStatus.Exempt,
              stage: stageName,
            },
          });
        } else {
          updateSpecificFeedbackItemStatus({
            data: {
              appId: versionInfo.app_id,
              version: versionInfo.version,
              metric: '结果指标',
              status: CheckItemStatus.Blocked,
              stage: stageName,
            },
          });
        }
      }
    }
    // }, [userStoryStageTime]);

    // useEffect(() => {
    checkItems.forEach(item => updateCheckItem(item));
    setTableDataSource(checkItems);
    // 这里进行初始化时的全部 exempt 检查
    setPass(checkItems.every(i => i.status === CheckItemStatus.Exempt));
  }, [stageName, versionInfo]);
  // }, [checkItems, risingInfo,versionInfo, stageName,userStoryStageTime]);

  const change = () => {
    setVisible(!visible);
  };

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: '指标名',
      width: '7%',
      dataIndex: 'name',
      render: (_, row) => (
        <div>
          <Text>{row.description}</Text>
          {(row.description === '结果指标' || row.description === '过程指标' || row.description === '激增指标') && (
            <Tooltip
              content={
                <div
                  style={{
                    overflowY: 'auto',
                    paddingRight: 8,
                    maxHeight: 400,
                    scrollbarColor: '#666',
                    scrollbarWidth: 'thin',
                  }}
                >
                  {isUniversal && row.description === '结果指标' && (
                    <>
                      <div>用户故事前：P1-bug解决率100%</div>
                      <div>用户故事后：P0-bug解决率100%</div>
                      <div>
                        P1-bug 总数：{bugResolveInfo.p1Stats?.total || 0} 已解决：
                        {bugResolveInfo.p1Stats?.completed || 0}
                      </div>
                      <div>
                        P0-bug 总数：{bugResolveInfo.p0Stats?.total || 0} 已解决：
                        {bugResolveInfo.p0Stats?.completed || 0}
                      </div>
                    </>
                  )}
                  {isUniversal && row.description === '过程指标' && (
                    <>
                      <div style={{ marginTop: '8px' }}>
                        跟进率：{calculateFollowUpRate(feedbackProcessInfo)}% (1-未跟进/总反馈数&gt;95%)
                      </div>
                      <div>
                        归因率：{calculateAttributionRate(feedbackProcessInfo)}%
                        (1-未复现反馈/（总反馈数-未跟进）&gt;=50%)
                      </div>
                      <div style={{ marginTop: '8px' }}>未跟进数：{feedbackProcessInfo?.noLabel || 0}</div>
                      <div>
                        未复现数：
                        {(feedbackProcessInfo?.notHandled || 0) +
                          (feedbackProcessInfo?.localReproduction || 0) +
                          (feedbackProcessInfo?.contactErrorNoReproduction || 0)}
                      </div>
                      <div>总反馈数：{feedbackProcessInfo?.total || 0}</div>
                    </>
                  )}
                  {isPC && row.description === '过程指标' && (
                    <>
                      <div style={{ marginTop: '8px' }}>
                        跟进率：{calculateFollowUpRate(feedbackProcessInfo)}% (1-未跟进/总反馈数&gt;98%)
                      </div>
                      <div style={{ marginTop: '8px' }}>未跟进数：{feedbackProcessInfo?.noLabel || 0}</div>
                      <div>
                        未复现数：
                        {(feedbackProcessInfo?.notHandled || 0) +
                          (feedbackProcessInfo?.localReproduction || 0) +
                          (feedbackProcessInfo?.contactErrorNoReproduction || 0)}
                      </div>
                      <div>总反馈数：{feedbackProcessInfo?.total || 0}</div>
                    </>
                  )}
                  {isPC && row.description === '激增指标' && (
                    <>
                      {risingInfo.allLabels.level2.length > 0 && (
                        <div style={{ marginTop: '8px' }}>
                          <div style={{ fontWeight: 'bold' }}>二级标签激增:</div>
                          {risingInfo.allLabels.level2.map((item: any, index: any) => (
                            <div key={index} style={{ marginLeft: '8px', color: 'red' }}>
                              {item.label}: {item.count}个
                            </div>
                          ))}
                        </div>
                      )}
                      {risingInfo.allLabels.level3.length > 0 && (
                        <div style={{ marginTop: '8px' }}>
                          <div style={{ fontWeight: 'bold' }}>三级标签激增:</div>
                          {risingInfo.allLabels.level3.map((item: any, index: any) => (
                            <div key={index} style={{ marginLeft: '8px', color: 'red' }}>
                              {item.label}: {item.count}个
                            </div>
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </div>
              }
            >
              <IconHelpCircle style={{ marginLeft: '8px', cursor: 'pointer', color: 'blue' }} />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      disable: true,
      title: '状态',
      width: '5%',
      dataIndex: 'status',
      ellipsis: true,
      valueType: 'select',
      render: (val, item, index) => {
        // TODO 要改成 复合2个指标才能准出，FeedbackMetricsItemStatus
        // const itemInfo = item.item_info as FeedbackMetricsItemInfo;
        // const internalStatus: CheckItemStatus = item.status;
        // if (itemInfo && itemInfo.status) {
        //   internalStatus = itemInfo.status;
        // // }
        // console.log(`#### ${internalStatus}`);
        // console.log(`#### ${JSON.stringify(item)}`);

        // const newItem = item;
        // newItem.status = risingInfo.areRising ? CheckItemStatus.Blocked : item.status;
        // updateCheckItem(newItem);
        console.log(`#### old item ${JSON.stringify(item)}`);
        // console.log(`#### new item ${JSON.stringify(newItem)}`);
        if (item.status === CheckItemStatus.TBD || item.status === CheckItemStatus.Blocked) {
          return (
            <Tag size="large" color="red">
              未通过
            </Tag>
          );
        }
        if (item.status === CheckItemStatus.Exempt) {
          return (
            <Tag size="large" color="light-green">
              通过
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: '准出值班人',
      width: '5%',
      dataIndex: 'operator',
      render: (val: any, item: VersionStageCheckItem) => {
        const info = item.item_info as StabilityMetricItemInfo;
        const duty = item.owner;
        if (duty === undefined) {
          return <></>;
        }
        // }
        return <UserAvatar checkUser={duty} />;
      },
    },
    {
      title: '指标Owner',
      width: '5%',
      render: (val: any, item: VersionStageCheckItem) => {
        let owners: User[] = [];
        if (item.owner !== undefined) {
          owners = [item.owner];
        }
        const info = item.item_info as StabilityMetricItemInfo;
        const users = info.owners;
        if (users && users.length > 0) {
          owners = users;
        }
        return (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <SemiReactUserGroup users={owners} triggerType={'hover'} />
          </div>
        );
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem | undefined) => {
        const loading = false;
        return (
          <Space spacing={'tight'}>
            {item ? (
              <>
                <Button theme={'solid'} type={'secondary'} onClick={change}>
                  {'跟进反馈'}
                </Button>
                <SideSheet
                  title={<div style={{ paddingLeft: '20px' }}>{`${prefix}   反馈跟进情况`}</div>}
                  visible={visible}
                  onCancel={change}
                  width={'80%'}
                  keepDOM={false}
                >
                  <Space align="center">
                    <div style={{ width: '100%', padding: '0 20px' }}>
                      <FeedBackFollowCheckList
                        versionInfo={versionInfo}
                        checkItems={checkItems}
                        title="反馈跟进情况"
                        stageName={stageName}
                      />
                    </div>
                  </Space>
                </SideSheet>
              </>
            ) : (
              <></>
            )}
          </Space>
        );
      },
    },
  ];
  const rowKey = (record: VersionStageCheckItem | undefined) => record!.check_item_id;

  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Space wrap={true}>
          <Title heading={6} style={{ margin: '8px 0' }}>
            {'跟进反馈指标'}
          </Title>
          <Divider layout={'vertical'} margin={'6px'} />
          <>
            <Tag
              color={pass ? 'light-green' : 'red'}
              size={'large'}
              shape={'square'}
              type={'ghost'}
              prefixIcon={pass ? <IconTick /> : <IconCrossStroked />}
            >
              {pass ? '通过' : '不通过'}
            </Tag>

            <Divider layout={'vertical'} margin={'6px'} />
          </>
        </Space>
      </div>
      <Divider margin={'6px'} />
      <Table
        columns={columns}
        rowKey={'check_item_id'}
        dataSource={tableDataSource}
        pagination={{
          pageSize: 5,
          total: tableDataSource.length,
        }}
        scroll={{
          x: '100%',
          scrollToFirstRowOnChange: true,
        }}
      />
    </Card>
  );
};

export const CircuitBreakerCheckList: React.FC<{
  versionInfo: VersionProcessInfo;
  checkItems: VersionStageCheckItem[];
  title: string;
  stageName: string;
}> = ({ versionInfo, checkItems, title, stageName }) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [tableDataSource, setTableDataSource] = useState<VersionStageCheckItem[]>([]);
  const [pass, setPass] = useState<boolean>(false);
  const [appSettingState] = useModel(AppSettingModule);

  const change = () => {
    setVisible(!visible);
  };

  useEffect(() => {
    setTableDataSource(checkItems);
    // 这里进行初始化时的全部 exempt 检查
    setPass(checkItems.every(i => i.status === CheckItemStatus.Exempt));
  }, [checkItems]);

  const columns: ColumnProps<VersionStageCheckItem>[] = [
    {
      title: '指标名',
      width: '7%',
      dataIndex: 'name',
      render: (_, row) => <Text>{row.description}</Text>,
    },
    {
      disable: true,
      title: '状态',
      width: '5%',
      dataIndex: 'status',
      ellipsis: true,
      valueType: 'select',
      render: (val, item, index) => {
        // CheckItemStatus
        const itemInfo = item.item_info as any;
        let internalStatus: any = item.status;
        if (itemInfo && itemInfo.status) {
          internalStatus = itemInfo.status;
        }
        if (internalStatus === CheckItemStatus.Blocked) {
          return (
            <Tag size="large" color="red">
              阻塞
            </Tag>
          );
        }
        if (internalStatus === CheckItemStatus.Exempt) {
          return (
            <Tag size="large" color="green">
              通过
            </Tag>
          );
        }
        if (internalStatus === CheckItemStatus.TBD) {
          return (
            <Tag size="large" color="yellow">
              无放量记录
            </Tag>
          );
        }
      },
    },
    {
      disable: true,
      title: '跟进人',
      width: '5%',
      dataIndex: 'operator',
      render: (val: any, item: VersionStageCheckItem) => {
        const info = item.item_info as StabilityMetricItemInfo;
        const duty = item.owner;
        if (duty === undefined) {
          return <></>;
        }
        // }
        return <UserAvatar checkUser={duty} />;
      },
    },
    {
      title: '操作',
      width: '10%',
      render: (_: any, item: VersionStageCheckItem | undefined) => (
        <Space spacing={'tight'}>
          {item ? (
            <>
              <Button theme={'solid'} type={'secondary'} onClick={change}>
                {'熔断问题详情'}
              </Button>
              <SideSheet
                title={
                  <div
                    style={{ paddingLeft: '20px' }}
                  >{`${appSettingState.info.name}-${appSettingState.info.platform}-${versionInfo.version}  灰度熔断跟进详情`}</div>
                }
                visible={visible}
                onCancel={change}
                width={'80%'}
                keepDOM={false}
              >
                <Space align="center" wrap>
                  <CircuitBreakerDetail checkItem={item} versionInfo={versionInfo} stageName={stageName} />
                </Space>
              </SideSheet>
            </>
          ) : (
            <></>
          )}
        </Space>
      ),
    },
  ];

  return (
    <CheckItemTableContainer
      checkItemTable={
        <Table
          columns={columns}
          rowKey={'check_item_id'}
          dataSource={tableDataSource}
          pagination={{
            pageSize: 5,
            total: tableDataSource.length,
          }}
          scroll={{
            x: '100%',
            scrollToFirstRowOnChange: true,
          }}
        />
      }
      itemTitle={'灰度熔断记录'}
      pass={pass}
      hideBatchNotify={true}
      versionInfo={versionInfo}
      customizeButton={
        <>
          <CircuitBreakerManualEntryModal
            versionInfo={versionInfo}
            stageInfo={getStageInfo(versionInfo.version_stages, stageName)}
          />
        </>
      }
      batchNotify={async () => {}}
      batchNotifyConfirmText={''}
      batchNotifyConfirmTitle={''}
    />
  );
};
