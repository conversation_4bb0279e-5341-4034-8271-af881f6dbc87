import { Inject, Injectable } from '@gulux/gulux';
import Mcp<PERSON><PERSON><PERSON>ibraHandler from './tool-handlers/McpToolLibraHandler';
import { McpToolBaseHandler, McpToolDefinition, McpToolRsp } from '@shared/mcp/mcp-info';
import Mcp<PERSON><PERSON>VersionHandler from './tool-handlers/McpToolVersionHandle';
import McpToolCodeFreezeHandler from './tool-handlers/McpToolCodeFreezeHandler';
import McpToolMrApprovalHandler from './tool-handlers/McpToolMrApprovalHandler';
import McpToolInitiateMRHandler from './tool-handlers/McpToolInitiateMRHandler';
import McpToolExperimentByVersionHandler from './tool-handlers/McpToolExperimentByVersionHandler';
import McpToolExperimentsByMeegoHandler from './tool-handlers/McpToolExperimentsByMeegoHandler';
import Mcp<PERSON><PERSON>ComponentInfoHandler from './tool-handlers/McpToolComponentInfoHandler';
import { teaCollect, teaCollectWithUser, TeaEvent } from 'api/tea';

@Injectable()
export default class McpToolHandlers {
  @Inject()
  private libraHandler: McpToolLibraHandler;
  @Inject()
  private versionHandler: McpToolVersionHandler;
  @Inject()
  private codeFreezeHandler: McpToolCodeFreezeHandler;
  @Inject()
  private mrApprovalHandler: McpToolMrApprovalHandler;
  @Inject()
  private experimentByVersionHandler: McpToolExperimentByVersionHandler;
  @Inject()
  private experimentsByMeegoHandler: McpToolExperimentsByMeegoHandler;
  @Inject()
  private componentInfoHandler: McpToolComponentInfoHandler;

  @Inject()
  private initiateMRHandler: McpToolInitiateMRHandler;

  // 所有 mcp tool handler
  allToolHandlers(): McpToolBaseHandler[] {
    return [
      this.libraHandler,
      this.versionHandler,
      // this.codeFreezeHandler,
      this.mrApprovalHandler,
      this.initiateMRHandler,
      this.experimentByVersionHandler,
      this.experimentsByMeegoHandler,
      this.componentInfoHandler,
    ];
  }

  // 所有 mcp tool definition
  allToolDefinitions(): McpToolDefinition[] {
    return this.allToolHandlers().flatMap(handler => handler.toolDefinition());
  }

  // handle 分发处理
  async didHandle(toolName: string, args: any) {
    const givenHandler = this.allToolHandlers().find(handler =>
      handler.toolDefinition().find((def: McpToolDefinition) => def.name === toolName),
    );
    if (givenHandler) {
      teaCollectWithUser(
        TeaEvent.MCP_USAGE,
        {
          tool_name: toolName,
        },
        args.email ?? '',
      );
      return await givenHandler.toolHandler(toolName, args);
    }

    return {
      content: [
        {
          type: 'text',
          text: `未找到对应的 tool handler: ${toolName}`,
        },
      ],
    } as McpToolRsp;
  }
}
