import { CodeProcessorParams, FinishType } from './codeProcessor';
import repos from '@shared/gitlab/repos';
import fetch from 'node-fetch';
import VersionProcessDao from '../dao/VersionProcessDao';
import { useInject } from '@edenx/runtime/bff';
import { PlatformType } from '@pa/shared/dist/src/core';
import { LVProductType } from '@shared/process/versionProcess';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import { BytedEnv } from '@gulux/gulux/byted-env';

function gitUrl(projectName: string): string {
  return `******************:${projectName}.git`;
}

// =========================== 自动回流解冲突任务 ============================

export function createLVIOSResolveConflicts(
  mrId: number,
  projectId: number,
  sourceBranch: string,
  targetBranch: string,
  hasRtSDK: boolean,
  hasRetouchMiddleware: boolean,
) {
  return {
    pipelineId: 50335,
    operator: 'tanhaiyang',
    finishType: FinishType.Nope,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'WORKSPACE',
            value: '/ws/50335',
          },
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'WORKFLOW_REPO_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'WORKFLOW_REPO_TARGET_BRANCH',
            value: targetBranch,
          },
        ],
      },
      {
        id: 76438,
        inputs: [
          {
            name: 'WORKFLOW_REPO_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'WORKFLOW_REPO_TARGET_BRANCH',
            value: targetBranch,
          },
        ],
      },
    ],
    env: {
      CUSTOM_CI_PROJECT_ID: projectId.toString(),
      CUSTOM_CI_MR_ID: mrId.toString(),
      hasRtSDK: hasRtSDK.toString(),
      hasRetouchMiddleware: hasRetouchMiddleware.toString(),
    },
  };
}

export function createLVPCResolveConflicts(
  mrId: number,
  projectId: number,
  sourceBranch: string,
  targetBranch: string,
): CodeProcessorParams {
  return {
    pipelineId: 54409,
    operator: 'tanhaiyang',
    finishType: FinishType.Nope,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'WORKSPACE',
            value: '/ws/54409',
          },
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'WORKFLOW_REPO_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'WORKFLOW_REPO_TARGET_BRANCH',
            value: targetBranch,
          },
        ],
      },
      {
        id: 80341,
        inputs: [
          {
            name: 'WORKFLOW_REPO_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'WORKFLOW_REPO_TARGET_BRANCH',
            value: targetBranch,
          },
        ],
      },
    ],
    env: {
      CUSTOM_CI_PROJECT_ID: projectId.toString(),
      CUSTOM_CI_MR_ID: mrId.toString(),
    },
  };
}

export function createLVAndroidResolveConflicts(sourceBranch: string, targetBranch: string): CodeProcessorParams {
  return {
    pipelineId: 50412,
    operator: 'tanhaiyang',
    finishType: FinishType.Nope,
    buildParams: [
      {
        id: 76484,
        inputs: [
          {
            name: 'gitlabSourceBranch',
            value: sourceBranch,
          },
          {
            name: 'gitlabTargetBranch',
            value: targetBranch,
          },
          {
            name: 'git_url',
            value: gitUrl(repos.androidMainRepo.projectName),
          },
        ],
      },
    ],
  };
}

export function createLVAndroidResolveConflictsV2(
  mrId: number,
  projectId: number,
  projectName: string,
  sourceBranch: string,
  targetBranch: string,
): CodeProcessorParams {
  return {
    pipelineId: 71232,
    operator: 'tanhaiyang',
    finishType: FinishType.Nope,
    buildParams: [
      {
        id: 84925,
        inputs: [
          {
            name: 'MAIN_GIT_URL',
            value: gitUrl(projectName),
          },
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
        ],
      },
      {
        id: 90653,
        inputs: [
          {
            name: 'BUILD_PARAMS',
            value: `{"RUNNING_TYPE":"auto_resolve_conflict","TARGET_BRANCH":"${targetBranch}","MERGE_TARGET":"true"}`,
          },
        ],
      },
    ],
    env: {
      CUSTOM_CI_PROJECT_ID: projectId.toString(),
      CUSTOM_CI_MR_ID: mrId.toString(),
    },
  };
}

export function createUpdateDraft(
  sourceBranch: string,
  targetBranch: string,
  updateDraft: string,
): CodeProcessorParams {
  return {
    pipelineId: 58762,
    operator: 'tanhaiyang',
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 84925,
        inputs: [
          {
            name: 'MAIN_GIT_URL',
            value: gitUrl(repos.androidMainRepo.projectName),
          },
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
        ],
      },
      {
        id: 87158,
        inputs: [
          {
            name: 'SOURCE_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'TARGET_BRANCH',
            value: targetBranch,
          },
          {
            name: 'UPDATE_DRAFT',
            value: updateDraft,
          },
        ],
      },
    ],
  };
}
export function createLVAndroidKtlint(sourceBranch: string, targetBranch: string): CodeProcessorParams {
  return {
    pipelineId: 57555,
    operator: 'tanhaiyang',
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 84925,
        inputs: [
          {
            name: 'MAIN_GIT_URL',
            value: gitUrl(repos.androidMainRepo.projectName),
          },
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
        ],
      },
      {
        id: 85363,
        inputs: [
          {
            name: 'SOURCE_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'TARGET_BRANCH',
            value: targetBranch,
          },
        ],
      },
    ],
  };
}

export function createLViOSUpdateLynxTemplate(
  sourceBranch: string,
  packageIds: string[],
  creator: string,
): CodeProcessorParams {
  return {
    pipelineId: 60189,
    operator: creator,
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
        ],
      },
      {
        id: 89483,
        inputs: [
          {
            name: 'PACKAGEIDS',
            value: packageIds.join(','),
          },
        ],
      },
    ],
  };
}

interface overseasToken {
  code: number;
  data: string;
}

export async function getOverseasJwtToken(ak: string) {
  const quality = useInject(RpcProxyManager).getQuality(true);
  const result = (await quality.queryJwtToken(ak)) as overseasToken;
  return result.data;
}

export async function createCCiOSUpdateLynxTemplate(
  sourceBranch: string,
  packageIds: string[],
  creator: string,
): Promise<CodeProcessorParams> {
  const jwtToken = await getOverseasJwtToken('f03b4510348e050da6f38fe8818be4da');
  return {
    pipelineId: 64276,
    operator: creator,
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
        ],
      },
      {
        id: 95632,
        inputs: [
          {
            name: 'PACKAGEIDS',
            value: packageIds.join(','),
          },
          {
            name: 'JWT_TOKEN',
            value: jwtToken,
          },
        ],
      },
    ],
  };
}

// 查询 dev 分支对应版本信息
async function getDevelopVersion(): Promise<string> {
  const logger = useInject(BytedLogger);
  const versionProcess = await useInject(VersionProcessDao).queryLastestDevelopingVersionInfo(
    PlatformType.iOS,
    LVProductType.lv,
  );
  logger.info(`[createiOSUpdateAllLynxTemplate] getDevelopVersionInfo: ${JSON.stringify(versionProcess)}`);
  if (!versionProcess) {
    logger.error(`[createiOSUpdateAllLynxTemplate] getDevelopVersionInfo is null`);
    return '';
  }
  return versionProcess.version;
}

export function generateiOSUpdateAllLynxTemplate(
  sourceBranch: string,
  isCapCut: boolean,
  isRelease: boolean,
  version: string,
  creator: string,
): CodeProcessorParams {
  const isCapCutValue = isCapCut ? 'true' : 'false';
  const isReleaseValue = isRelease ? 'true' : 'false';
  const scriptName = 'update_all_videocut_package_token.js';
  const pipelineID = isCapCut ? 64459 : 63789;
  return {
    pipelineId: pipelineID,
    operator: creator,
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
        ],
      },
      {
        id: 89483,
        inputs: [
          {
            name: 'APP_VERSION',
            value: version,
          },
          {
            name: 'IS_CAPCUT',
            value: isCapCutValue,
          },
          {
            name: 'IS_RELEASE',
            value: isReleaseValue,
          },
          {
            name: 'SCRIPT_NAME',
            value: scriptName,
          },
        ],
      },
    ],
  };
}

export async function generatePippitiOSUpdateAllLynxTemplate(
  branch: string,
  creator: string,
  blackList: string,
): Promise<CodeProcessorParams> {
  // const version = '0.0.1';
  return {
    pipelineId: 82004,
    operator: creator,
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'MAIN_GIT_BRANCH',
            value: branch,
          },
        ],
      },
      // {
      //   id: 112689,
      //   inputs: [
      //     {
      //       name: 'APP_VERSION',
      //       value: version,
      //     },
      //   ],
      // },
    ],
  };
}
export async function generatePippitAndroidUpdateAllLynxTemplate(
  branch: string,
  creator: string,
  blackList: string,
): Promise<CodeProcessorParams> {
  // const version = '0.0.1';
  return {
    pipelineId: 73682,
    operator: creator,
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'MAIN_GIT_BRANCH',
            value: branch,
          },
        ],
      },
      // {
      //   id: 112689,
      //   inputs: [
      //     {
      //       name: 'APP_VERSION',
      //       value: version,
      //     },
      //   ],
      // },
    ],
  };
}
export async function generateAndroidUpdateAllLynxTemplate(
  branch: string,
  creator: string,
  blackList: string,
): Promise<CodeProcessorParams> {
  let isRelease = false;
  let version;
  if (branch.includes('release')) {
    isRelease = true;
    version = (branch.split('/').pop() as string).split('-')[0] as string;
  } else {
    const versionProcess = await useInject(VersionProcessDao).queryLastestDevelopingVersionInfo(
      PlatformType.Android,
      'dreamina',
    );
    version = versionProcess?.version ?? '';
  }

  const isReleaseValue = isRelease ? 'true' : 'false';
  const scriptName = 'update_all_dreamina_package_token.js';
  const pipelineID = 65847;

  return {
    pipelineId: pipelineID,
    operator: creator,
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'MAIN_GIT_BRANCH',
            value: branch,
          },
        ],
      },
      {
        id: 99046,
        inputs: [
          {
            name: 'APP_VERSION',
            value: version,
          },
          {
            name: 'IS_RELEASE',
            value: isReleaseValue,
          },
          {
            name: 'SCRIPT_NAME',
            value: scriptName,
          },
          // {
          //   name: 'BLACKLIST',
          //   value: blackList,
          // },
          {
            name: 'IS_ANDROID',
            value: 'true',
          },
        ],
      },
    ],
  };
}

// 全应用通用更新
export function createiOSUpdateAllProductLynxTemplate(
  sourceBranch: string,
  product: string,
  creator: string,
  blackList: string,
  branch: string,
  packageID = '',
): CodeProcessorParams {
  let projectName = 'faceu-ios/iMovie';
  if (product === 'xt' || product === 'hp') {
    projectName = 'faceu-ios/rtsdk';
  } else if (product === 'cc' || product === 'cc_ttp' || product === 'cc_row') {
    projectName = 'faceu-ios/CapCut';
  }
  let pipelineID = 65211;
  const env = useInject(BytedEnv);
  if (env.isPPE()) {
    pipelineID = 65436;
  }

  return {
    pipelineId: pipelineID,
    operator: creator,
    finishType: FinishType.MR,
    buildParams: [
      {
        id: 2,
        inputs: [
          {
            name: 'MAIN_GIT_BRANCH',
            value: sourceBranch,
          },
          {
            name: 'MAIN_GIT_URL',
            value: gitUrl(projectName),
          },
        ],
      },
      {
        id: 97729,
        inputs: [
          {
            name: 'PRODUCT_NAME',
            value: product,
          },
          {
            name: 'BLACKLIST',
            value: blackList,
          },
          {
            name: 'MAIN_REPO_BRANCH',
            value: branch,
          },
          {
            name: 'PACKAGEID_LIST',
            value: packageID,
          },
        ],
      },
    ],
  };
}

export async function createiOSUpdateAllLynxTemplate(
  branch: string,
  sourceBranch: string,
  creator: string,
  isOversea: boolean,
): Promise<CodeProcessorParams> {
  let isRelease = false;
  const isCapCut = Boolean(isOversea);
  let version;
  if (branch.includes('release')) {
    isRelease = true;
    version = branch.split('/').pop() as string;
  } else {
    // develop
    version = await getDevelopVersion();
  }

  if (isCapCut && !branch.includes('overseas')) {
    // cc 更新且分支为国内分支或 dev 分支，需要进行版本号对齐
    const parts = version.split('.');
    parts[0] = (parseInt(parts[0], 10) - 2).toString();
    version = parts.join('.');
  }

  return generateiOSUpdateAllLynxTemplate(sourceBranch, isCapCut, isRelease, version, creator);
}
