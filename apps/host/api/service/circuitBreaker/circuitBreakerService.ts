import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import {
  CircuitBreakerCallbackActionType,
  CircuitBreakerIssueStatus,
  CircuitBreakerOSType,
  CircuitBreakerStatus,
  CircuitBreakerTicket,
  CircuitBreakerTicketStatus,
  CircuitBreakerTicketType,
  FeedbackCircuitBreakerTicket,
  SlardarCircuitBreakerAlarmLevel,
  SlardarCircuitBreakerCrashType,
  SlardarCircuitBreakerTicket,
  SlardarProjectAid,
} from '@shared/circuitBreaker/circuitBreakerTicket';
import { CardCallback } from '@pa/shared/dist/src/lark/larkCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from '../larkCard';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import ReleasePlatformMessageService, {
  ReleasePlatformMessageGroupType,
} from '../releasePlatform/releasePlatformMessageService';
import SlardarCircuitBreakerTicketModel from '../../model/SlardarCircuitBreakeTicketTable';
import FeedbackCircuitBreakerTicketModel from '../../model/FeedbackCircuitBreakeTicketTable';
import { dayjs } from '@pa/shared/dist/src/utils/dayjs';
import { v4 as uuidv4 } from 'uuid';
import { isOverseas, versionCodeToVersion } from '@shared/utils/VersionCommonUtils';
import MeegoService, { supportedAppsArray, MeegoBusinessType } from '../third/meego';
import { BmType } from '@shared/bits/bmInfo';
import { FieldValuePair } from '@shared/meego/MeegoCommon';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import versionUtils from '../../utils/versionUtils';
import { AppSettingId } from '@pa/shared/dist/src/appSettings/appSettings';

@Injectable()
export default class CircuitBreakerService {
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private larkCardService: LarkCardService;
  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;
  @Inject()
  private releasePlatformMessageService: ReleasePlatformMessageService;
  @Inject()
  private slardarCircuitBreakerTicketModel: SlardarCircuitBreakerTicketModel;
  @Inject()
  private feedbackCircuitBreakerTicketModel: FeedbackCircuitBreakerTicketModel;
  @Inject()
  private meegoService: MeegoService;
  @Inject()
  private rpcService: RpcProxyManager;

  getApplicationName = (appId: number) => {
    switch (appId) {
      case 177501:
      case 177502:
        return '剪映';
      case 300601:
      case 300602:
        return 'CapCut';
      case 251501:
      case 251502:
        return '醒图';
      case 2020093924:
      case 2020093988:
        return 'Hypic';
      case 2020092383:
      case 2020092892:
        return '剪映专业版';
      case 35928901:
      case 35928902:
        return 'CapCut专业版';
      default:
        return undefined;
    }
  };

  getAidOSByAppId(appId: number) {
    switch (appId) {
      case 177501:
        return { aid: 1775, os: CircuitBreakerOSType.iOS };
      case 177502:
        return { aid: 1775, os: CircuitBreakerOSType.Android };
      case 300601:
        return { aid: 3006, os: CircuitBreakerOSType.iOS };
      case 300602:
        return { aid: 3006, os: CircuitBreakerOSType.Android };
      case 251501:
        return { aid: 2515, os: CircuitBreakerOSType.iOS };
      case 251502:
        return { aid: 2515, os: CircuitBreakerOSType.Android };
      case 2020093988:
        return { aid: 7356, os: CircuitBreakerOSType.iOS };
      case 2020093924:
        return { aid: 7356, os: CircuitBreakerOSType.Android };
      case 2020092383:
        return { aid: SlardarProjectAid.LV_WIN, os: CircuitBreakerOSType.Windows };
      case 2020092892:
        return { aid: SlardarProjectAid.LV_MAC, os: CircuitBreakerOSType.Mac };
      case 35928901:
        return { aid: SlardarProjectAid.CC_WIN_MAC, os: CircuitBreakerOSType.Windows };
      case 35928902:
        return { aid: SlardarProjectAid.CC_WIN_MAC, os: CircuitBreakerOSType.Mac };
      default:
        return { aid: 1775, os: CircuitBreakerOSType.Android };
    }
  }

  getAppIdByAidOS(aid: number, os: CircuitBreakerOSType) {
    aid = Number(aid);
    if (aid === 1775 && os === CircuitBreakerOSType.iOS) {
      return 177501;
    }
    if (aid === 1775 && os === CircuitBreakerOSType.Android) {
      return 177502;
    }
    if (aid === 3006 && os === CircuitBreakerOSType.iOS) {
      return 300601;
    }
    if (aid === 3006 && os === CircuitBreakerOSType.Android) {
      return 300602;
    }
    if (aid === 2515 && os === CircuitBreakerOSType.iOS) {
      return 251501;
    }
    if (aid === 2515 && os === CircuitBreakerOSType.Android) {
      return 251502;
    }
    if (aid === 7356 && os === CircuitBreakerOSType.iOS) {
      return 2020093988;
    }
    if (aid === 7356 && os === CircuitBreakerOSType.Android) {
      return 2020093924;
    }
    if (aid === SlardarProjectAid.LV_WIN && os === CircuitBreakerOSType.Windows) {
      return 2020092383;
    }
    if (aid === SlardarProjectAid.LV_MAC && os === CircuitBreakerOSType.Mac) {
      return 2020092892;
    }
    if (aid === SlardarProjectAid.CC_WIN_MAC && os === CircuitBreakerOSType.Windows) {
      return 35928901;
    }
    if (aid === SlardarProjectAid.CC_WIN_MAC && os === CircuitBreakerOSType.Mac) {
      return 35928902;
    }
  }

  getSlardarAlarmUrl(aid: number, os: CircuitBreakerOSType, alarmRuleId: number) {
    const appId = this.getAppIdByAidOS(aid, os);
    return appId
      ? `https://${isOverseas(appId) ? 'slardar-us' : 'slardar'}.bytedance.net/node/app_detail/?region=${isOverseas(appId) ? 'maliva' : 'cn'}&aid=${aid}&os=${os.toString()}#/alarm/crash/rule/detail/${alarmRuleId}?`
      : undefined;
  }

  getCircuitRuleList(aid: number, os: string): number[] | undefined {
    if (aid === 1775 && os === CircuitBreakerOSType.Android.toLowerCase()) {
      return [120640830, 120640829, 120640824];
    }
    if (aid === 1775 && os === CircuitBreakerOSType.iOS.toLowerCase()) {
      return [];
    }
    if (aid === 2515 && os === CircuitBreakerOSType.iOS.toLowerCase()) {
      return [];
    }
    if (aid === 2515 && os === CircuitBreakerOSType.Android.toLowerCase()) {
      return [120628939, 120628940, 120628941, 120628942];
    }
    if (aid === 7356 && os === CircuitBreakerOSType.iOS.toLowerCase()) {
      return [];
    }
    if (aid === 7356 && os === CircuitBreakerOSType.Android.toLowerCase()) {
      return [99149, 99150, 99151, 99152];
    }
  }

  // 获取具体AppId的熔断工单
  async getCircuitBreakerRecords(
    appId: number,
    recordType: CircuitBreakerTicketType,
    filter?: Map<string, any>,
    pagination?: { pageNum: number; pageSize: number },
  ) {
    const { aid, os } = this.getAidOSByAppId(appId);
    const baseFilter = new Map<string, any>([
      ['appId', aid],
      ['os', os],
    ]);
    if (filter) {
      filter.set('appId', aid);
      filter.set('os', os);
    }
    if (recordType === CircuitBreakerTicketType.Feedback) {
      const records = await this.feedbackCircuitBreakerTicketModel.getTicketsByFilter(
        Object.fromEntries((filter ?? baseFilter).entries()),
        pagination ? { skip: (pagination.pageNum - 1) * pagination.pageSize, limit: pagination.pageSize } : undefined,
      );
      const totalLength = await this.feedbackCircuitBreakerTicketModel.getTicketsLenByFilter(
        Object.fromEntries((filter ?? baseFilter).entries()),
      );
      return {
        data: records,
        total: totalLength,
      };
    } else if (recordType === CircuitBreakerTicketType.Slardar) {
      const records = await this.slardarCircuitBreakerTicketModel.getTicketsByFilter(
        Object.fromEntries((filter ?? baseFilter).entries()),
        pagination ? { skip: (pagination.pageNum - 1) * pagination.pageSize, limit: pagination.pageSize } : undefined,
      );
      const totalLength = await this.slardarCircuitBreakerTicketModel.getTicketsLenByFilter(
        Object.fromEntries((filter ?? baseFilter).entries()),
      );
      return {
        data: records,
        total: totalLength,
      };
    }
    return {
      data: [],
      total: 0,
    };
  }

  isPCSlardar(aid: number) {
    return Object.values(SlardarProjectAid).includes(Number(aid));
  }

  isPCProject(appid: number) {
    switch (Number(appid)) {
      case AppSettingId.LV_WIN:
      case AppSettingId.CC_WIN:
        return 'Win';
      case AppSettingId.LV_MAC:
      case AppSettingId.CC_MAC:
        return 'Mac';
      default:
        return 'Win';
    }
  }

  async getUnHandleRecords(beforeTimestamp: number) {
    const slardarRecords = await this.slardarCircuitBreakerTicketModel.getTicketsByFilter({
      ticketStatus: { $ne: CircuitBreakerTicketStatus.Closed },
      ticketCreateTime: { $lte: beforeTimestamp },
    });
    const feedbackRecords = await this.feedbackCircuitBreakerTicketModel.getTicketsByFilter({
      ticketStatus: { $ne: CircuitBreakerTicketStatus.Closed },
      ticketCreateTime: { $lte: beforeTimestamp },
    });
    return {
      slardarRecords,
      feedbackRecords,
    };
  }

  toPCMeegoOs(os: CircuitBreakerOSType) {
    if (os === CircuitBreakerOSType.Windows) {
      return 'Windows';
    }
    if (os === CircuitBreakerOSType.Mac) {
      return 'MAC';
    }
    return 'Mac & Windows';
  }

  toPCMeegoVersionName(os: CircuitBreakerOSType) {
    if (os === CircuitBreakerOSType.Windows) {
      return 'Win';
    }
    if (os === CircuitBreakerOSType.Mac) {
      return 'Mac';
    }
    return 'Mac & Windows';
  }

  toPCMeegoVersionPrefix(busi: string) {
    switch (busi) {
      case MeegoBusinessType.LV_PC:
        return '剪映专业版';
      case MeegoBusinessType.CC_PC:
        return 'CC-PC';
      default:
        return busi;
    }
  }

  toPCVersion(versionCode: string) {
    // 2.8.0-beta4.13253 or 2.8.0.13253
    return versionCode.split('-').slice(0, 1).toString().split('.').slice(0, 3).join('.');
  }

  // 更新/创建 单个熔断工单数据
  async saveCircuitBreakerRecord(data: CircuitBreakerTicket, isNewRecord: boolean) {
    if (data.ticketType === CircuitBreakerTicketType.Slardar) {
      if (isNewRecord) {
        return await this.slardarCircuitBreakerTicketModel.createSlardarTicket(data as SlardarCircuitBreakerTicket);
      } else {
        return await this.slardarCircuitBreakerTicketModel.updateTicket(data as SlardarCircuitBreakerTicket);
      }
    } else if (data.ticketType === CircuitBreakerTicketType.Feedback) {
      if (isNewRecord) {
        return await this.feedbackCircuitBreakerTicketModel.createTicket(data as FeedbackCircuitBreakerTicket);
      } else {
        return await this.feedbackCircuitBreakerTicketModel.updateTicket(data as FeedbackCircuitBreakerTicket);
      }
    }
  }

  // 获取版本灰度熔断次数
  async getVersionCircuitBreakerCount(appId: number, version: string) {
    const { aid, os } = this.getAidOSByAppId(appId);

    const sc = await this.slardarCircuitBreakerTicketModel.getTicketsLenByFilter({
      appId: aid,
      version,
      circuitBreakerStatus: CircuitBreakerStatus.Confirmed,
    });
    const sca = await this.slardarCircuitBreakerTicketModel.getTicketsLenByFilter({
      appId: aid,
      version,
      circuitBreakerStatus: CircuitBreakerStatus.Confirmed,
      affectGray: true,
    });

    const fc = await this.feedbackCircuitBreakerTicketModel.getTicketsLenByFilter({
      appId: aid,
      version,
      circuitBreakerStatus: CircuitBreakerStatus.Confirmed,
    });
    const fca = await this.feedbackCircuitBreakerTicketModel.getTicketsLenByFilter({
      appId: aid,
      version,
      circuitBreakerStatus: CircuitBreakerStatus.Confirmed,
      affectGray: true,
    });
    return {
      grayCircuitBreakerCount: sc + fc,
      affectGrayCount: sca + fca,
    };
  }

  async handleSlardarTicket(messageId: string, ticketId: string, actionType: string, finishPayload: any) {
    switch (actionType) {
      case CircuitBreakerCallbackActionType.StartProcess.toString():
        // 「未跟进」-> 「处理中」
        return await this.slardarCircuitBreakerTicketModel.startProcessTicket(ticketId);
      case CircuitBreakerCallbackActionType.NotNeedProcess.toString():
        // 「未跟进」/「处理中」-> 「跟进完成」，标记 「无需处理」
        return await this.slardarCircuitBreakerTicketModel.stopProcessTicket(ticketId, false);
      case CircuitBreakerCallbackActionType.FinishProcess.toString():
        // 「处理中」/「跟进完成」-> 「跟进完成」，标记「跟进完成」
        return await this.slardarCircuitBreakerTicketModel.stopProcessTicket(ticketId, true);
      case CircuitBreakerCallbackActionType.FinishTicket.toString():
        return await this.slardarCircuitBreakerTicketModel.finishProcessTicket(ticketId, finishPayload);
      case CircuitBreakerCallbackActionType.CreateMeegoAndJoinChat.toString():
        // 提单不需要流转状态
        return await this.slardarCircuitBreakerTicketModel.getTicketByFilter({ ticketId });
      default:
        return;
    }
  }

  async handleFeedbackTicket(messageId: string, ticketId: string, actionType: string, finishPayload: any) {
    switch (actionType) {
      case CircuitBreakerCallbackActionType.StartProcess.toString():
        return await this.feedbackCircuitBreakerTicketModel.startProcessTicket(ticketId);
      case CircuitBreakerCallbackActionType.NotNeedProcess.toString():
        return await this.feedbackCircuitBreakerTicketModel.stopProcessTicket(ticketId, false);
      case CircuitBreakerCallbackActionType.FinishProcess.toString():
        return await this.feedbackCircuitBreakerTicketModel.stopProcessTicket(ticketId, true);
      case CircuitBreakerCallbackActionType.FinishTicket.toString():
        return await this.feedbackCircuitBreakerTicketModel.finishProcessTicket(ticketId, finishPayload);
      case CircuitBreakerCallbackActionType.CreateMeegoAndJoinChat.toString():
        // 提单不需要流转状态
        return await this.feedbackCircuitBreakerTicketModel.getTicketByFilter({ ticketId });
      default:
        return;
    }
  }

  async circuitBreakerCardStatusChange(data: CardCallback) {
    const { ticketId, ticketType, actionType, appId } = data.action.value;
    const messageId = data.open_message_id;
    let circuitBreakerInfo;
    switch (String(ticketType)) {
      case CircuitBreakerTicketType.Slardar.toString():
        circuitBreakerInfo = await this.handleSlardarTicket(
          messageId,
          ticketId,
          String(actionType),
          data.action.form_value,
        );
        break;
      case CircuitBreakerTicketType.Feedback.toString():
        circuitBreakerInfo = await this.handleFeedbackTicket(
          messageId,
          ticketId,
          String(actionType),
          data.action.form_value,
        );
        break;
      default:
        break;
    }
    if (circuitBreakerInfo) {
      if (String(actionType) === CircuitBreakerCallbackActionType.StartProcess.toString()) {
        // 卡片 从 开始跟进 变成 跟进中
        return await this.buildAndUpdateCircuitBreakerNotify(
          messageId,
          Number(appId),
          circuitBreakerInfo,
          CircuitBreakerCallbackActionType.StartProcess,
        );
      }
      if (String(actionType) === CircuitBreakerCallbackActionType.NotNeedProcess.toString()) {
        // 卡片 从 开始跟进 变成 无需跟进
        await this.buildAndUpdateCircuitBreakerNotify(
          messageId,
          Number(appId),
          circuitBreakerInfo,
          CircuitBreakerCallbackActionType.NotNeedProcess,
        );
        const newRecord = await this.saveCircuitBreakerRecord(circuitBreakerInfo, false);
        if (newRecord) {
          // 发送收集信息卡片
          circuitBreakerInfo.collectMessageId =
            (await this.buildAndSendCircuitBreakerNotify(
              Number(appId),
              newRecord,
              CircuitBreakerCallbackActionType.FinishTicket,
            )) ?? '';
        }
      }
      if (String(actionType) === CircuitBreakerCallbackActionType.FinishProcess.toString()) {
        // 卡片 从 开始跟进 变成 无需跟进
        await this.buildAndUpdateCircuitBreakerNotify(
          messageId,
          Number(appId),
          circuitBreakerInfo,
          CircuitBreakerCallbackActionType.FinishProcess,
        );
        const newRecord = await this.saveCircuitBreakerRecord(circuitBreakerInfo, false);
        if (newRecord) {
          // 发送收集信息卡片
          circuitBreakerInfo.collectMessageId =
            (await this.buildAndSendCircuitBreakerNotify(
              Number(appId),
              newRecord,
              CircuitBreakerCallbackActionType.FinishTicket,
            )) ?? '';
        }
      }
      if (String(actionType) === CircuitBreakerCallbackActionType.FinishTicket.toString()) {
        // 卡片 结束后禁止编辑
        return await this.buildAndUpdateCircuitBreakerNotify(
          messageId,
          Number(appId),
          circuitBreakerInfo,
          CircuitBreakerCallbackActionType.FinishTicket,
        );
      }

      if (String(actionType) === CircuitBreakerCallbackActionType.CreateMeegoAndJoinChat.toString()) {
        const meegoLinkReg = new RegExp(/.*\/faceu\/issue\/detail\/(\d+).?/);
        // 无单提单，有单进群
        if (circuitBreakerInfo.meegoLink && meegoLinkReg.test(circuitBreakerInfo.meegoLink)) {
          const tmp = meegoLinkReg.exec(circuitBreakerInfo.meegoLink);
          const existMeegoId = tmp ? tmp[1] : '';
          if (existMeegoId) {
            // 存在meegoId，尝试拉bot进群，并拉人
            const ret = await this.meegoService.addBotToMeegoChat(
              Number(existMeegoId),
              'cli_9c8628b7b1f1d102',
              'issue',
            );
            if (ret?.chatId) {
              await new Promise(resolve => setTimeout(resolve, 5000));
              await this.larkService.addUserToChatGroup(ret.chatId, UserIdType.openId, [data.open_id]);
            }
          }
        } else {
          // 无关联缺陷单，创建并拉群
          const ret = await this.createMeegoIssue(Number(appId), circuitBreakerInfo, data.open_id);
          if (ret?.code === 0) {
            const meegoId = ret.id;
            circuitBreakerInfo.meegoLink = `https://meego.larkoffice.com/faceu/issue/detail/${meegoId}`;
            await this.saveCircuitBreakerRecord(circuitBreakerInfo, false);
            await new Promise(resolve => setTimeout(resolve, 5000));
            await this.meegoService.addBotToMeegoChat(meegoId, 'cli_9c8628b7b1f1d102', 'issue');
          }
        }
      }
    }
  }

  async slardarAlarmClusterAndCreate(callbackPayload: any) {
    const alarmRuleName = callbackPayload?.rule.name;
    const alarmRuleId = callbackPayload?.rule.id;
    const alarmAid = callbackPayload?.rule.aid;
    const alarmOs = callbackPayload?.rule.os;
    const alarmCrashLink = callbackPayload?.alarm?.crash_link;
    const alarmContext = callbackPayload?.alarm?.alarm_context_v2?.alarm_context_v2;
    // 【熔断管理】122009+bits灰度熔断+单版本报警+ANR
    const alarmRuleTmp: string[] = alarmRuleName?.replaceAll('【熔断管理】', '').split('+');
    const alarmVersionCode: string = alarmRuleTmp[0];
    const alarmVersion = this.isPCSlardar(alarmAid)
      ? this.toPCVersion(alarmVersionCode)
      : versionCodeToVersion(alarmVersionCode, false);
    const alarmRuleType: string = alarmRuleTmp[alarmRuleTmp.length - 1];
    const alarmOSType = Object.values(CircuitBreakerOSType).includes(alarmOs) ? alarmOs : CircuitBreakerOSType.Default;

    let alarmCrashType: SlardarCircuitBreakerCrashType;
    switch (alarmRuleType) {
      case 'ANR':
        alarmCrashType = SlardarCircuitBreakerCrashType.ANR;
        break;
      case 'Java崩溃':
        alarmCrashType = SlardarCircuitBreakerCrashType.JavaCrash;
        break;
      case 'Java启动崩溃':
        alarmCrashType = SlardarCircuitBreakerCrashType.JavaStartCrash;
        break;
      case 'Native崩溃':
        alarmCrashType = SlardarCircuitBreakerCrashType.NativeCrash;
        break;
      case '崩溃':
        alarmCrashType = SlardarCircuitBreakerCrashType.Crash;
        break;
      case 'oom崩溃':
        alarmCrashType = SlardarCircuitBreakerCrashType.OOMCrash;
        break;
      case '卡死':
        alarmCrashType = SlardarCircuitBreakerCrashType.WatchDog;
        break;
      case 'Crash崩溃':
        alarmCrashType = SlardarCircuitBreakerCrashType.PCCrash;
        break;
      case '激增反馈':
        alarmCrashType = SlardarCircuitBreakerCrashType.PCFeedback;
        break;
      case '无响应':
        alarmCrashType = SlardarCircuitBreakerCrashType.PCANR;
        break;
      default:
        alarmCrashType = SlardarCircuitBreakerCrashType.Unknown;
        break;
    }

    // 查询是否有未关闭的工单
    const circuitBreakerTicket = await this.slardarCircuitBreakerTicketModel.getTicketNotClosed(
      alarmAid,
      alarmVersionCode,
      alarmOSType,
      alarmRuleId,
    );

    if (circuitBreakerTicket) {
      // 如果circuitBreakerTicket有记录，则更新记录，不创建新工单
      this.logger.info(
        `[slardarAlarmClusterAndCreate] circuitBreakerTicket has record ${circuitBreakerTicket.ticketId}, 
        increase alarm count ${circuitBreakerTicket.alarmCount} -> ${circuitBreakerTicket.alarmCount + 1}`,
      );
      await this.slardarCircuitBreakerTicketModel.updateTicketCount(circuitBreakerTicket);
      if (!circuitBreakerTicket.notifyMessageId) {
        const notifyMessageId = await this.buildAndSendCircuitBreakerNotify(
          this.getAppIdByAidOS(circuitBreakerTicket.appId, circuitBreakerTicket.os) ?? 0,
          circuitBreakerTicket,
          CircuitBreakerCallbackActionType.StartProcess,
        );
        if (notifyMessageId) {
          circuitBreakerTicket.notifyMessageId = notifyMessageId;
          await this.saveCircuitBreakerRecord(circuitBreakerTicket, false);
        }
      }
      return true;
    } else {
      // 如果circuitBreakerTicket没有记录，则创建新工单
      const newTicket = await this.slardarCircuitBreakerTicketModel.createSlardarTicket({
        ticketId: uuidv4(),
        ticketStatus: CircuitBreakerTicketStatus.NotStart,
        ticketType: CircuitBreakerTicketType.Slardar,
        affectGray: false,
        circuitBreakerStatus: CircuitBreakerStatus.UnConfirmed,
        circuitBreakerIssueStatus: CircuitBreakerIssueStatus.UnResolved,
        ticketCreateTime: dayjs(new Date()).tz('Asia/Shanghai').unix(),
        ticketUpdateTime: dayjs(new Date()).tz('Asia/Shanghai').unix(),
        ticketHandler: '', // 后面改成获取BM
        appId: alarmAid,
        version: alarmVersion,
        versionCode: alarmVersionCode,
        os: alarmOSType,
        alarmRuleId,
        alarmRuleName,
        alarmLevel: callbackPayload?.alarm.alarm_level as SlardarCircuitBreakerAlarmLevel,
        alarmType: alarmCrashType,
        alarmCount: 1,
        alarmStartTime: dayjs(new Date()).tz('Asia/Shanghai').unix(),
        alarmRuleLink: this.isPCSlardar(alarmAid)
          ? alarmCrashLink
          : (this.getSlardarAlarmUrl(alarmAid, alarmOSType, alarmRuleId) ?? ''),
      } as SlardarCircuitBreakerTicket);
      this.logger.info(`[slardarAlarmClusterAndCreate] create new circuitBreakerTicket ${JSON.stringify(newTicket)}`);
      const appId = this.getAppIdByAidOS(alarmAid, alarmOSType);
      this.logger.info(`[slardarAlarmClusterAndCreate] notify app: ${appId} ${alarmAid} ${alarmOSType}`);
      if (appId) {
        const notifyMessageId = await this.buildAndSendCircuitBreakerNotify(
          appId,
          newTicket,
          CircuitBreakerCallbackActionType.StartProcess,
        );
        if (notifyMessageId) {
          newTicket.notifyMessageId = notifyMessageId;
          await this.saveCircuitBreakerRecord(newTicket, false);
        }
      }
      return false;
    }
  }

  // 推送熔断卡片，两种类型，通知 & 数据回收
  async buildAndSendCircuitBreakerNotify(
    appId: number,
    circuitBreakerInfo: CircuitBreakerTicket,
    actionType: CircuitBreakerCallbackActionType,
  ): Promise<string | undefined> {
    const updateCard = false;
    const versionInfo = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: appId,
      version: circuitBreakerInfo.version,
    });
    this.logger.info(
      `[buildAndSendCircuitBreakerNotify] version info: ${appId} ${circuitBreakerInfo.version} ${versionInfo}`,
    );
    if (versionInfo) {
      const notifyCard = this.larkCardService.buildGrayCircuitBreakerInfoCard(
        this.getApplicationName(appId) ?? '',
        circuitBreakerInfo,
        versionInfo,
        actionType,
        updateCard,
      );
      this.logger.info(`[buildAndSendCircuitBreakerNotify] notifyCard: ${JSON.stringify(notifyCard)}`);
      if (notifyCard) {
        return await this.releasePlatformMessageService.sendVersionMessage(
          ReleasePlatformMessageGroupType.CircuitBreakerNotifyGroup,
          notifyCard,
          versionInfo,
          [],
        );
      }
    }
  }

  // 更新卡片，避免修改&重复提交
  async buildAndUpdateCircuitBreakerNotify(
    messageId: string,
    appId: number,
    circuitBreakerInfo: CircuitBreakerTicket,
    actionType: CircuitBreakerCallbackActionType,
  ) {
    const updateCard = true;
    const versionInfo = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: appId,
      version: circuitBreakerInfo.version,
    });
    if (versionInfo) {
      const newCard = this.larkCardService.buildGrayCircuitBreakerInfoCard(
        this.getApplicationName(appId) ?? '',
        circuitBreakerInfo,
        versionInfo,
        actionType,
        updateCard,
      );
      return await this.larkService.updateCard(JSON.stringify(newCard), messageId);
    }
  }

  async createMeegoIssue(appId: number, circuitBreakerInfo: CircuitBreakerTicket, initiatorOpenId?: string) {
    const projectKey = 'faceu';
    const business = this.getApplicationName(appId);
    const versionName = this.isPCProject(appId)
      ? `${this.toPCMeegoVersionPrefix(business ?? '')}-${this.toPCMeegoVersionName(circuitBreakerInfo.os)}-${circuitBreakerInfo.version}`
      : `${business}-${circuitBreakerInfo.os}-${circuitBreakerInfo.version}`;
    console.log(`${versionName}`);
    const versionKey = await this.meegoService.queryVersionId(projectKey, versionName);
    const versionInfo = await this.versionProcessInfoDao.findOneByCriteria({
      app_id: appId,
      version: circuitBreakerInfo.version,
    });
    if (!versionInfo) {
      return;
    }
    const assigner = versionInfo.bmInfo[BmType.crash].email ?? versionInfo.bmInfo[BmType.rd].email;
    const assignerUser = (
      await this.meegoService.requestMeegoUserInfos({
        emails: [assigner.endsWith('@bytedance.com') ? assigner : `${assigner}@bytedance.com`],
      })
    )[0];
    const issueAssignerUserKey = assignerUser?.user_key;

    const initiatorUser = initiatorOpenId
      ? (await this.meegoService.requestMeegoUserInfos({ out_ids: [initiatorOpenId] }))[0]
      : undefined;
    const reporter = initiatorUser?.email ?? versionInfo.bmInfo[BmType.qa].email;
    const reporterUser =
      initiatorUser ??
      (
        await this.meegoService.requestMeegoUserInfos({
          emails: [reporter.endsWith('@bytedance.com') ? reporter : `${reporter}@bytedance.com`],
        })
      )[0];
    const issueReportUserKey = reporterUser?.user_key;

    console.info(`meego user: ${assigner} ${initiatorUser} ${reporterUser}`);
    const getDescription = (info: CircuitBreakerTicket) => {
      if (info.ticketType === CircuitBreakerTicketType.Slardar) {
        const slardarInfo = info as SlardarCircuitBreakerTicket;
        return `【告警名称】: ${slardarInfo.alarmRuleName}\n【告警链接】: ${slardarInfo.alarmRuleLink}\n【首次告警时间】: ${dayjs(slardarInfo.alarmStartTime).format('YYYY-MM-DD HH:mm')}【累积告警次数】: ${slardarInfo.alarmCount}`;
      } else if (info.ticketType === CircuitBreakerTicketType.Feedback) {
        const feedbackInfo = info as FeedbackCircuitBreakerTicket;
        return `【反馈描述】: ${feedbackInfo.feedbackDescribe}\n【反馈链接】: ${feedbackInfo.feedbackLink}\n【首次反馈时间】: ${feedbackInfo.feedbackStartTime}\n【影响人数】: ${feedbackInfo.feedbackUserCount}`;
      }
      return '';
    };

    const fieldValuePairs = [
      {
        field_key: 'business',
        field_value: '65b78ac8eee88dcefd577896', // 基础技术
      },
      {
        field_key: 'field_dc0aa5', // 对应需求的supported_apps
        field_value: [
          {
            label: this.isPCSlardar(circuitBreakerInfo.appId) ? `PC-${business}` : `App-${business}`,
            value: supportedAppsArray[business ?? ''],
          },
        ],
      },
      {
        field_key: 'issue_stage',
        field_value: {
          label: '灰度阶段',
          value: 'option_12',
        },
      },
      // 5.Bug端分类
      {
        field_key: 'bug_classification',
        field_value: {
          label: this.isPCSlardar(circuitBreakerInfo.appId)
            ? this.toPCMeegoOs(circuitBreakerInfo.os)
            : circuitBreakerInfo.os,
          value: circuitBreakerInfo.os.toLowerCase(),
        },
      },
      // 6.bug报告人
      {
        field_key: 'issue_reporter',
        field_value: issueReportUserKey,
      },
      // 7.bug经办人
      {
        field_key: 'issue_operator',
        field_value: issueAssignerUserKey,
      }, // 8.issue 描述
      {
        field_key: 'description',
        field_value: `${getDescription(circuitBreakerInfo)}\n[来自纸飞机自动提单]${reporterUser?.email ? `${reporterUser.email}触发` : ''}`,
      },
      // 9.issue 发现版本
      {
        field_key: 'discovery_version',
        // "field_value": [8194975]
        field_value: [versionKey],
      },
      // # 10.issue 解决版本
      {
        field_key: 'resolve_version',
        // "field_value": [8194975]
        field_value: [versionKey],
      },
      // # 11.优先级
      {
        field_key: 'priority',
        field_value: {
          label: `P0`,
          value: '0',
        },
      },
      // # 12. issue发现方式
      {
        field_key: 'bug_channel',
        field_value: {
          label: '监控（端监控、Slardar监控、tea监控等）',
          value: '5',
        },
      },
      {
        field_key: 'owner',
        field_value: issueReportUserKey,
      },
      // bug标签(已废弃field_42ed2f) -> 标签
      // bug标签
      {
        field_key: 'field_42ed2f',
        field_value: JSON.stringify(['纸飞机自动建单']),
      },
      // 自动拉群
      {
        field_key: 'group_type',
        field_value: 'auto',
      },
    ] as FieldValuePair[];
    this.logger.info(`提单内容${JSON.stringify(fieldValuePairs)}`);
    const ret = await this.meegoService.workItemCreate(
      projectKey,
      'issue',
      `[${business}-${circuitBreakerInfo.os}-${circuitBreakerInfo.versionCode}]${CircuitBreakerTicketType[circuitBreakerInfo.ticketType]}灰度熔断提单`,
      fieldValuePairs,
      19617,
      issueReportUserKey ? issueReportUserKey : '<EMAIL>',
    );
    this.logger.info(`提单结果${JSON.stringify(ret)}`);
    return {
      code: ret.err_code,
      err_msg: JSON.stringify(ret.err),
      id: ret?.data,
      operator: assigner,
    };
  }

  async updateSlardarAlarm(appId: number, aid: number, os: string, versionCode: string) {
    const ruleList = this.getCircuitRuleList(aid, os.toLowerCase());
    if (appId && ruleList) {
      const updateResult = await this.rpcService
        .getQuality(versionUtils.isOverseas(Number(appId)))
        .updateCircuitAlarm(aid, os.toString(), ruleList, versionCode);
      this.logger.info(
        `[CircuitBreaker Service]updateSlardarAlarm ${appId} ${ruleList} ${versionCode}. updateResult: ${JSON.stringify(updateResult)}`,
      );
    } else {
      this.logger.error(
        `[CircuitBreaker Service]updateSlardarAlarm error ${aid} ${os} ${versionCode} cannot found appId ${appId} ruleList ${ruleList}`,
      );
    }
  }

  async getAllCircuitBreakerRecords(recordType: CircuitBreakerTicketType) {
    if (recordType === CircuitBreakerTicketType.Feedback) {
      const records = await this.feedbackCircuitBreakerTicketModel.getTicketsByFilter({});
      const totalLength = await this.feedbackCircuitBreakerTicketModel.getTicketsLenByFilter({});
      return {
        data: records,
        total: totalLength,
      };
    } else if (recordType === CircuitBreakerTicketType.Slardar) {
      const records = await this.slardarCircuitBreakerTicketModel.getTicketsByFilter({});
      const totalLength = await this.slardarCircuitBreakerTicketModel.getTicketsLenByFilter({});
      return {
        data: records,
        total: totalLength,
      };
    }
    return {
      data: [],
      total: 0,
    };
  }
}
