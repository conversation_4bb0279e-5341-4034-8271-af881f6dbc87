import { Inject, Injectable } from '@gulux/gulux';
import LarkCardService from './larkCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { useInject } from '@edenx/runtime/bff';
import {
  CardActionElement,
  CardActionValue,
  CardButtonAction,
  CardButtonType,
  CardCallbackType,
  CardColumnElement,
  CardColumnSetElement,
  CardContentElement,
  CardElement,
  CardElementTag,
  CardTemplate,
  CardTextTag,
} from '@pa/shared/dist/src/lark/larkCard';
import { CheckItemStatus } from '@shared/releasePlatform/versionStageInfoCheckList';
import { VersionAutoStage } from '@shared/releasePlatform/versionAutoStage';
import { BuildMasterInfo } from '@shared/process/versionProcess';
import { BmType } from '@shared/bits/bmInfo';
import { SlardarCrashType } from '@shared/experiment/experimentInfo';
import { getBitsAppId } from '../utils/appid';
import { MAIN_HOST_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';

@Injectable()
export default class AutoVersionStageLarkCardService {
  @Inject()
  private logger: BytedLogger;

  buildAutoVersionStageCard(versionAutoStage: VersionAutoStage, bmInfo: Record<number, BuildMasterInfo>) {
    this.logger.info(`AutoVersionStageLarkCardService.buildAutoVersionStageCard: ${JSON.stringify(versionAutoStage)}`);
    if (versionAutoStage.status === CheckItemStatus.TBD) {
      return this.buildAutoVersionStageWarningCard(versionAutoStage, bmInfo);
    } else {
      return this.buildAutoVersionStageResultCard(versionAutoStage, bmInfo);
    }
  }

  buildAutoVersionStageWarningCard(versionAutoStage: VersionAutoStage, bmInfo: Record<number, BuildMasterInfo>) {
    const cardService = useInject(LarkCardService);
    const baseCard = cardService.buildBaseCard({
      title: `${versionAutoStage.version_code} ${versionAutoStage.crash_type} 自动化准出详情`,
      template: CardTemplate.blue,
    });
    const rule = this.getRule(versionAutoStage);
    const elements: CardElement[] = [];
    elements.push(this.getAtElement(versionAutoStage, bmInfo));
    elements.push({
      tag: CardElementTag.hr,
    } as CardContentElement);
    elements.push(...this.getColumnElements(versionAutoStage, rule, null));
    elements.push({
      tag: CardElementTag.hr,
    } as CardContentElement);
    elements.push(...this.getDetailElements(versionAutoStage, false));
    baseCard.elements = elements;
    return baseCard;
  }

  buildAutoVersionStageResultCard(versionAutoStage: VersionAutoStage, bmInfo: Record<number, BuildMasterInfo>) {
    const cardService = useInject(LarkCardService);
    const baseCard = cardService.buildBaseCard({
      title: `${versionAutoStage.version_code} ${versionAutoStage.crash_type} 自动化准出结论：${this.getStatusString(versionAutoStage.status)}`,
      template: versionAutoStage.status === CheckItemStatus.Blocked ? CardTemplate.yellow : CardTemplate.blue,
    });
    const rule = this.getRule(versionAutoStage);
    const statility = this.getStability(versionAutoStage);
    const elements: CardElement[] = [];
    elements.push(this.getAtElement(versionAutoStage, bmInfo));
    elements.push({
      tag: CardElementTag.hr,
    } as CardContentElement);
    elements.push(...this.getColumnElements(versionAutoStage, rule, statility));
    elements.push({
      tag: CardElementTag.hr,
    } as CardContentElement);
    elements.push(...this.getDetailElements(versionAutoStage, true));
    baseCard.elements = elements;
    return baseCard;
  }

  getDetailElements(versionAutoStage: VersionAutoStage, addKuoMian: boolean): CardElement[] {
    const actions = [
      {
        tag: CardElementTag.button,
        text: {
          tag: CardTextTag.plain_text,
          content: '查看指标详情',
        },
        type: CardButtonType.primary,
        url: this.getDetailUrl(versionAutoStage),
      } as CardButtonAction,
      {
        tag: CardElementTag.button,
        text: {
          tag: CardTextTag.plain_text,
          content: '一键归因',
        },
        type: CardButtonType.primary,
        url: this.getReasonUrl(versionAutoStage),
      } as CardButtonAction,
    ];

    // if (addKuoMian) {
    //   actions.push({
    //     tag: CardElementTag.button,
    //     text: {
    //       tag: CardTextTag.plain_text,
    //       content: '一键豁免',
    //     },
    //     type: CardButtonType.primary,
    //     value: {
    //       cardCallbackType: CardCallbackType.VersionAutoStageChangeStatus,
    //       appId: versionAutoStage.bits_app_id.toString(),
    //       version: versionAutoStage.version,
    //       subStage: versionAutoStage.sub_stage,
    //       crashType: versionAutoStage.crash_type,
    //     } as CardActionValue,
    //   } as CardButtonAction);
    // }

    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.action,
      actions,
    } as CardActionElement);
    return elements;
  }

  getDetailUrl(versionAutoStage: VersionAutoStage) {
    return `${MAIN_HOST_HTTPS}/release/list?appid=${getBitsAppId(versionAutoStage.app_id, versionAutoStage.platform)}&version=${versionAutoStage.version}&show_detail=true&main_stage=smallFlow&sub_stage=smallFlow_after_check&stability_metrix_sheet=${versionAutoStage.crash_type}&focus_check_item=true`;
  }

  getReasonUrl(versionAutoStage: VersionAutoStage) {
    const baseStartTime = versionAutoStage.extra_data.per_real_start_hour;
    const targetStartTime = versionAutoStage.extra_data.real_start_hour;
    const maxHour = this.getMaxHour(versionAutoStage);
    const params = {
      appid: getBitsAppId(versionAutoStage.app_id, versionAutoStage.platform),
      version: versionAutoStage.version,
      tab: 'version',
      crash_type: versionAutoStage.crash_type,
      base_version_code: versionAutoStage.extra_data.per_version,
      target_version_code: versionAutoStage.version,
      base_update_version_code: versionAutoStage.extra_data.per_version_code,
      target_update_version_code: versionAutoStage.version_code,
      base_release_time: versionAutoStage.extra_data.per_real_start_time,
      target_release_time: versionAutoStage.extra_data.real_start_time,
      base_start_time: baseStartTime,
      target_start_time: targetStartTime,
      base_end_time: baseStartTime + maxHour * 3600,
      target_end_time: targetStartTime + maxHour * 3600,
    };

    const path = 'quality/diagnosis/issue-attribution/multi-attribution';
    const url = new URL(`${MAIN_HOST_HTTPS}/${path}`);
    for (const [key, value] of Object.entries(params)) {
      url.searchParams.append(key, `${value}`);
    }
    return url.toString();
  }

  getNextHourTimestamp(timestamp: number) {
    // 将时间戳转换为Date对象
    const date = new Date(timestamp * 1000); // 因为JavaScript的时间戳单位是毫秒
    if (date.getMinutes() === 0 && date.getSeconds() === 0 && date.getMilliseconds() === 0) {
      // 如果当前已经是整点，不需要加1
    } else {
      // 设置分钟和秒为0，毫秒为0
      date.setMinutes(0, 0, 0);
      date.setHours(date.getHours() + 1);
    }
    // 将Date对象转换回时间戳
    return Math.floor(date.getTime() / 1000); // 转换为秒
  }

  getMaxHour(versionAutoStage: VersionAutoStage) {
    const hours: string[] = [];
    const preValue = this.getPreValue(versionAutoStage);
    const curValue = this.getCurValue(versionAutoStage);
    const rule = this.getRule(versionAutoStage);
    for (const [key, value] of Object.entries(rule)) {
      let hour = '';
      let deviceLevel = 0;
      const deviceLevelHourKey = this.getDeviceLevelHourKey(key);
      const keys = deviceLevelHourKey.split('_');
      if (keys.length === 2) {
        deviceLevel = parseInt(keys[0], 10);
        hour = keys[1];
      }

      if (key.includes('stability_0')) {
        const cur = curValue[deviceLevelHourKey];
        const pre = preValue[deviceLevelHourKey];
        if (cur !== null && cur !== undefined && pre !== null && pre !== undefined) {
          hours.push(hour);
        }
      } else if (key.includes('stability_1')) {
        const cur = curValue[deviceLevelHourKey];
        if (cur !== null && cur !== undefined) {
          hours.push(hour);
        }
      }
    }

    if (hours.length > 0) {
      return parseInt(hours.sort((a, b) => parseInt(b, 10) - parseInt(a, 10))[0], 10);
    } else {
      return 24;
    }
  }

  getAtElement(versionAutoStage: VersionAutoStage, bmInfo: Record<number, BuildMasterInfo>) {
    const crashBmInfo = this.getCrashTypeBmInfo(versionAutoStage, bmInfo);
    return {
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: `请<at email=${crashBmInfo.email}></at>关注以下信息：`,
      },
    } as CardContentElement;
  }

  getCrashTypeBmInfo(versionAutoStage: VersionAutoStage, bmInfo: Record<number, BuildMasterInfo>) {
    if (bmInfo) {
      let bmType = BmType.rd;
      switch (versionAutoStage.crash_type) {
        case SlardarCrashType.Crash:
          bmType = BmType.crash;
          break;
        case SlardarCrashType.OOMCrash:
          bmType = BmType.iosoom;
          break;
        case SlardarCrashType.WatchDog:
          bmType = BmType.ioswatchdog;
          break;
        case SlardarCrashType.ANR:
          bmType = BmType.anr_crash;
          break;
        case SlardarCrashType.JavaCrash:
          bmType = BmType.java_crash;
          break;
        case SlardarCrashType.JavaOOM:
          bmType = BmType.oom_crash;
          break;
        case SlardarCrashType.NativeCrash:
          bmType = BmType.native_crash;
          break;
        case SlardarCrashType.NativeOOM:
          bmType = BmType.oom_crash;
          break;
        default:
          bmType = BmType.rd;
          break;
      }

      const crashBmInfo = bmInfo[bmType];
      if (crashBmInfo) {
        return crashBmInfo;
      }
      // 兜底给 rd BM
      const rdBm = bmInfo[BmType.rd];
      if (rdBm) {
        return rdBm;
      }
    }

    // 兜底给我
    return {
      type: BmType.rd,
      email: '<EMAIL>',
      nameCN: '许煜桐',
      avatarUrl: '',
      openId: '',
    };
  }

  getColumnElements(
    versionAutoStage: VersionAutoStage,
    rule: { [key: string]: number },
    statility: { [key: string]: number } | null,
  ): CardElement[] {
    const preValue = this.getPreValue(versionAutoStage);
    const curValue = this.getCurValue(versionAutoStage);
    const elements: CardElement[] = [];
    for (const [key, value] of Object.entries(rule)) {
      if (statility !== undefined && statility !== null) {
        if (!Object.prototype.hasOwnProperty.call(statility, key)) {
          continue;
        }
        // else {
        //   const sValue = statility[key];
        //   if (sValue === CheckItemStatus.TBD) {
        //     continue;
        //   }
        // }
      }
      let hour = '';
      let deviceLevel = 0;
      const deviceLevelHourKey = this.getDeviceLevelHourKey(key);
      const keys = deviceLevelHourKey.split('_');
      if (keys.length === 2) {
        deviceLevel = parseInt(keys[0], 10);
        hour = keys[1];
      }

      if (key.includes('stability_0')) {
        const cur = curValue[deviceLevelHourKey];
        const pre = preValue[deviceLevelHourKey];
        if (cur !== null && cur !== undefined && pre !== null && pre !== undefined) {
          let gain = 0;
          if (cur !== 0 || pre !== 0) {
            gain = (cur - pre) / pre;
          }
          let colorH = ``;
          let colorB = ``;
          if (gain >= value) {
            colorH = `**<font color='red'>`;
            colorB = `<font>**`;
          }
          elements.push({
            tag: CardElementTag.columnSet,
            // horizontal_spacing: '8px',
            columns: [
              {
                tag: CardElementTag.column,
                width: 'weighted',
                elements: [
                  {
                    tag: CardElementTag.div,
                    text: {
                      tag: CardTextTag.lark_md,
                      content: `${hour}H${this.deviceNumber2String(deviceLevel)}对比${versionAutoStage.extra_data.per_version_code}`,
                    },
                  } as CardElement,
                ],
                weight: 5,
              } as CardColumnElement,
              {
                tag: CardElementTag.column,
                width: 'weighted',
                elements: [
                  {
                    tag: CardElementTag.div,
                    text: {
                      tag: CardTextTag.lark_md,
                      content: `${colorH}${(pre * 1000).toFixed(2)}‰ -> ${(cur * 1000).toFixed(2)}‰(${(gain * 100).toFixed(2)}％)${colorB}`,
                    },
                  } as CardElement,
                ],
                weight: 5,
              } as CardColumnElement,
              {
                tag: CardElementTag.column,
                width: 'weighted',
                elements: [
                  {
                    tag: CardElementTag.div,
                    text: {
                      tag: CardTextTag.lark_md,
                      content: `阈值:${(value * 100).toFixed(2)}％`,
                    },
                  } as CardElement,
                ],
                weight: 5,
              } as CardColumnElement,
            ],
          } as CardColumnSetElement);
        }
      } else if (key.includes('stability_1')) {
        const cur = curValue[deviceLevelHourKey];
        if (cur !== null && cur !== undefined) {
          let colorH = ``;
          let colorB = ``;
          if (cur >= value) {
            colorH = `**<font color='red'>`;
            colorB = `<font>**`;
          }
          elements.push({
            tag: CardElementTag.columnSet,
            // horizontal_spacing: '8px',
            columns: [
              {
                tag: CardElementTag.column,
                width: 'weighted',
                elements: [
                  {
                    tag: CardElementTag.div,
                    text: {
                      tag: CardTextTag.lark_md,
                      content: `${hour}H${this.deviceNumber2String(deviceLevel)}`,
                    },
                  } as CardElement,
                ],
                weight: 5,
              } as CardColumnElement,
              {
                tag: CardElementTag.column,
                width: 'weighted',
                elements: [
                  {
                    tag: CardElementTag.div,
                    text: {
                      tag: CardTextTag.lark_md,
                      content: `${colorH}${(cur * 1000).toFixed(2)}‰${colorB}`,
                    },
                  } as CardElement,
                ],
                weight: 5,
              } as CardColumnElement,
              {
                tag: CardElementTag.column,
                width: 'weighted',
                elements: [
                  {
                    tag: CardElementTag.div,
                    text: {
                      tag: CardTextTag.lark_md,
                      content: `阈值:${(value * 1000).toFixed(2)}‰`,
                    },
                  } as CardElement,
                ],
                weight: 5,
              } as CardColumnElement,
            ],
          } as CardColumnSetElement);
        }
      }
    }
    return elements;
  }

  getStatusString(status: CheckItemStatus) {
    switch (status) {
      case CheckItemStatus.Blocked:
        return '阻塞';
      case CheckItemStatus.Exempt:
        return '通过';
      default:
        return '待定';
    }
  }

  getRule(versionAutoStage: VersionAutoStage): { [key: string]: number } {
    return versionAutoStage.auto_stage_info['rules_value'] as { [key: string]: number };
  }

  getCurValue(versionAutoStage: VersionAutoStage): { [key: string]: number } {
    return versionAutoStage.auto_stage_info['cur_value'] as { [key: string]: number };
  }

  getPreValue(versionAutoStage: VersionAutoStage): { [key: string]: number } {
    return versionAutoStage.auto_stage_info['pre_value'] as { [key: string]: number };
  }

  getStability(versionAutoStage: VersionAutoStage): { [key: string]: number } {
    return versionAutoStage.auto_stage_info['stability_value'] as { [key: string]: number };
  }

  deviceNumber2String(device: number): string {
    switch (device) {
      case 0:
        return '大盘';
      case 1:
        return '低端机';
      case 2:
        return '中高端机';
      default:
        return '未知机型';
    }
  }

  getDeviceLevelHourKey(ruleKey: string): string {
    const keys = ruleKey.split('_');
    if (keys.length === 4) {
      return keys.slice(2).join('_');
    }
    return '';
  }
}
