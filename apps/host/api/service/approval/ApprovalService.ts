import {
  ChatInfo,
  errorRsp,
  NetworkCode,
  NetworkResult,
  successRsp,
  User,
  PlatformType,
} from '@pa/shared/dist/src/core';

import { Inject, Injectable } from '@gulux/gulux';
import {
  ApprovalBusinessConfigs,
  ApprovalConfigDetail,
  ApprovalDetail,
  ApprovalFixVersionConfig,
  ApprovalHotfixConfig,
  ApprovalOrderStatus,
  ApprovalType,
  CreateTicketApprovalReq,
  FixVersionApprovalReq,
  HotfixTaskApprovalReq,
  LinkInfo,
  MeegoWorkFlowNodeStatus,
  PipelineStatus,
  PreApprovalDetail,
  RequirementApprovalReq,
  RequirementChangeActionType,
  RetouchApprovalBusinessConfigs,
} from '@shared/approval/ApprovalOrder';
import ApprovalModelService from '../model/approvalModel';
import BitsService from '../third/bits';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { StoryMeegoStatus } from '@shared/meego/WorkItemResult';
import MeegoService, { faceuProjectKey } from '../third/meego';
import dayjs from 'dayjs';
import { MrRelationInfo } from '@shared/bits/mrRelationInfo';
import { ApprovalInfoTable } from '../../model/approval/ApprovalInfoModel';
import CollectAbnormalMultiMrService from '../tasks/collectAbnormalMultiMr';
import { ApprovalDBService } from './ApprovalDBService';
import { isMultiMr, MrInfo } from '@shared/bits/mrInfo';
import { MrMeegoInfo } from '@shared/bits/mrMeegoInfo';
import { MrPipelineInfo } from '@shared/bits/mrPipelineInfo';
import LarkService from '@pa/backend/dist/src/third/lark';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { Card, LarkCard, MessageType } from '@pa/shared/dist/src/lark/larkCard';
import LarkCardService from '../larkCard';
import { batchGetUserInfo, batchGetUserInfoByOpenIds } from '@api/index';
import { WorkflowNode } from '@shared/meego/WorkflowResult';
import versionUtils from '../../utils/versionUtils';
import { useInject } from '@edenx/runtime/bff';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import { extractMeegoId } from '../utils/MeegoUtils';
import { compact, uniq } from 'lodash';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import AirplaneConfigService from '../AirplaneConfigService';
import { VersionProcessInfoEditorService } from '../releasePlatform/editor/VersionProcessInfoEditorService';
import { LarkClient } from '@gulux/gulux/lark';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import {
  DreaminaProductType,
  LVProductType,
  PippitProductType,
  RetouchProductType,
  TinycutProductType,
} from '@shared/process/versionProcess';
import { isRetouchApp } from '@shared/releasePlatform/versionStage';

@Injectable()
export default class ApprovalService {
  @Inject()
  private approvalModelService: ApprovalModelService;
  @Inject()
  private bitsService: BitsService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private meegoService: MeegoService;
  @Inject()
  private collectAbnormalMultiMr: CollectAbnormalMultiMrService;
  @Inject()
  private approvalDBService: ApprovalDBService;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private larkClient: LarkClient;
  @Inject()
  private larkCardService: LarkCardService;
  @Inject()
  private configService: AirplaneConfigService;
  @Inject()
  private versionProcessInfoEditorService: VersionProcessInfoEditorService;

  async requirementChangeApproved(approvalInfo?: ApprovalInfoTable) {
    this.logger.info(`[requirementChangeApproved] ${JSON.stringify(approvalInfo)}`);
    // 针对特殊的meegoId审批记录通过
    if (approvalInfo) {
      const appIds = approvalInfo?.requirementChangeInfo?.appIds ?? [];
      const meegoId = extractMeegoId(
        approvalInfo?.requirementChangeInfo?.meegoLink?.url ?? approvalInfo?.meegoLinks?.[0]?.link?.url,
      );
      const type = approvalInfo?.requirementChangeInfo?.type;
      this.logger.info(
        `[requirementChangeApproved] createMeegoInsertApproval appIds => ${JSON.stringify(appIds)} meegoId => ${meegoId} type => ${type}`,
      );
      if (meegoId && type) {
        await this.approvalDBService.createMeegoInsertApproval(
          appIds,
          meegoId,
          type,
          approvalInfo?.approvalCode,
          approvalInfo?.instanceCode,
        );
      }
    }

    // 发送通知
    const card = await this.larkCardService.buildRequirementChangeApprovalInfo(approvalInfo);
    this.logger.info(
      `[requirementChangeApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    // 相关知会人
    const userInfos = await batchGetUserInfo({
      data: {
        emails: approvalInfo?.requirementChangeInfo?.notifyUsersEmails,
      },
    });
    const userOpenIds = compact(userInfos?.map(userInfo => userInfo?.open_id)?.filter(it => it !== undefined));
    // 必须通知的群设置，如剪映核心群
    // const chatId = 'oc_042c6f4254e11f226437e683ffb5f4d7';// 测试群
    const requireChatGroups: string[] = [];
    // 核心群配置,仅封板后需要
    const includeCoreGroup = approvalInfo?.requirementChangeInfo?.type === RequirementChangeActionType.insert;
    const products = approvalInfo?.requirementChangeInfo?.appIds
      ?.map(appId => (appId ? versionUtils.appIdToAppInfo(appId.toString())?.product : undefined))
      ?.filter(it => it !== undefined);
    this.logger.info(
      `[requirementChangeApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, products: ${JSON.stringify(products)}`,
    );
    // TODO 多业务适配
    if (includeCoreGroup) {
      if (products?.some(it => it === LVProductType.lv || it === LVProductType.cc)) {
        const chatInfo = (await this.configService.queryConfigItem(
          '1775',
          VersionConfigKeys.coreGroupChatId,
        )) as ChatInfo;
        const coreChatId = chatInfo.chat_id;
        if (coreChatId) {
          requireChatGroups.push(coreChatId);
        }
      }

      if (products?.some(it => it === RetouchProductType.retouch)) {
        const chatInfo = (await this.configService.queryConfigItem(
          '2515',
          VersionConfigKeys.approvalGroupChatId,
        )) as ChatInfo;
        const coreChatId = chatInfo.chat_id;
        if (coreChatId) {
          requireChatGroups.push(coreChatId);
        }
      }

      if (products?.some(it => it === DreaminaProductType.dreamina)) {
        const chatInfo = (await this.configService.queryConfigItem(
          '581595',
          VersionConfigKeys.approvalGroupChatId,
        )) as ChatInfo;
        const coreChatId = chatInfo.chat_id;
        if (coreChatId) {
          requireChatGroups.push(coreChatId);
        }
      }

      // NOTE 多端接入配置-异常流程周知
      if (products?.some(it => it === PippitProductType.pippit)) {
        const chatInfo = (await this.configService.queryConfigItem(
          '8700',
          VersionConfigKeys.approvalGroupChatId,
        )) as ChatInfo;
        const coreChatId = chatInfo.chat_id;
        if (coreChatId) {
          requireChatGroups.push(coreChatId);
        }
      }

      if (products?.some(it => it === TinycutProductType.tinycut)) {
        const chatInfo = (await this.configService.queryConfigItem(
          '8702',
          VersionConfigKeys.approvalGroupChatId,
        )) as ChatInfo;
        const coreChatId = chatInfo.chat_id;
        if (coreChatId) {
          requireChatGroups.push(coreChatId);
        }
      }
    }
    const chatIds = approvalInfo?.requirementChangeInfo?.notifyChatIds;
    const newChatIds = Array.from(chatIds ?? [])?.filter(it => !requireChatGroups.includes(it));
    newChatIds?.push(...requireChatGroups);
    // 通知群和知会人
    for (const chatId of newChatIds) {
      if (!chatId) {
        continue;
      }
      if (userOpenIds) {
        const inviteResult = await this.larkService.inviteUsers2Group(userOpenIds, chatId);
        this.logger.info(
          `[requirementChangeApproved] approvalInfo: ${JSON.stringify(approvalInfo)} userOpenIds => ${JSON.stringify(userOpenIds)}, chatId => ${chatId}, inviteResult => ${JSON.stringify(inviteResult)}`,
        );
      }

      const result = await this.larkService.sendMessage(
        UserIdType.chatId,
        chatId,
        JSON.stringify(card),
        MessageType.interactive,
      );
      this.logger.info(`[requirementChangeApproved] result: ${JSON.stringify(result)}`);
    }
  }

  async getApprovalCount(appId: number, version: string, type: ApprovalType) {
    switch (type) {
      case ApprovalType.HotFix:
        return this.approvalDBService?.hotfixCount(appId, version);
      case ApprovalType.FixedVersionRequirements:
        return this.approvalDBService?.fixVersionCount(appId, version);
      default:
        return -1;
    }
  }

  async requirementChangeRejected(approvalInfo?: ApprovalInfoTable) {
    // 7369012758446260228
    this.logger.info(`[requirementChangeRejected] approvalInfo: ${JSON.stringify(approvalInfo)}`);
  }

  /** ************************************* 异常需求上车 **************************************************** **/
  getSupportAppId() {
    return [177502, 177501, 300602, 300601];
  }

  async getVersions(appId: number) {
    const versionProcessInfos = await useInject(VersionProcessInfoDao).findAllVersionInfos(appId);
    return versionProcessInfos
      ?.map(versionProcessInfo => versionProcessInfo.version)
      .sort((a, b) => a.localeCompare(b));
  }

  async getMeegoItemDetail(meegoInfo: MrMeegoInfo) {
    return await this.meegoService.requestTaskInfo(meegoInfo.platform_project_name, meegoInfo.task_type, {
      work_item_ids: [Number(meegoInfo.task_id)],
      fields: ['client', 'field_9a70e6', 'planning_version', 'field_edb46d', 'field_c28db5'], // 醒图需要校验「依赖配置上线情况」和「是否达到封板准入标准」
      expand: {
        need_workflow: true,
      },
    });
  }

  async checkRequirementInsertApproved(appIds: number[], meegoId: string) {
    const record = await this.approvalDBService.hasMeegoInsertApprovalRecord(meegoId);
    this.logger.info(
      `[requirementInsertApproved] appIds => ${JSON.stringify(appIds)}, meegoId => ${meegoId},record => ${JSON.stringify(record)}`,
    );
    if (!record) {
      return undefined;
    }
    return record.status === ApprovalOrderStatus.Completed;
  }

  async getStoryMeegoStatus(
    meegoItem: MrMeegoInfo,
    multiTag: boolean,
    bitsMrInfo?: MrInfo,
    platform?: string,
    appConfig?: any,
  ) {
    const meegoItemInfo = await this.getMeegoItemDetail(meegoItem);
    // if (!meegoItemInfo.data || !meegoItemInfo.data[0]) {
    //   return errorRsp('获取meego信息失败');
    // }
    const workItemInfo = meegoItemInfo.data[0];
    const meegoStatus = new StoryMeegoStatus();
    // 获取集成状态
    for (const nodes of workItemInfo.workflow_infos?.workflow_nodes ?? []) {
      // "status":1 //1:未开始 2:进行中 3:已完成
      // Android集成
      if (nodes.id === 'state_42' || nodes.id === 'state_50' || nodes.id === 'state_48') {
        meegoStatus.androidStatus = nodes.status;
      }
      // iOS集成
      if (nodes.id === 'state_41' || nodes.id === 'state_49') {
        meegoStatus.iOSStatus = nodes.status;
      }
      // PC集成
      if (nodes.id === 'ios_completed1') {
        meegoStatus.pcStatus = nodes.status;
      }
    }

    // 查询这个需求单下的解决率
    let fixPlatform = platform;
    if (!multiTag && meegoStatus.platforms.length > 1) {
      // 如果不是多端mr，是单端mr,但是story单是双端的需求单，查询解决率的时候要增加平台过滤参数
      if (bitsMrInfo?.platform) {
        fixPlatform = bitsMrInfo.platform;
      }
    } else if (platform === 'PC') {
      fixPlatform = 'Mac & Windows';
    }
    meegoStatus.fixRate = await this.getFixRate(meegoItem, fixPlatform);
    meegoStatus.standard =
      meegoStatus.fixRate.sP0P1Count === 0 &&
      ((Number(meegoStatus.fixRate.rate) >= appConfig.requireFixRate &&
        meegoStatus.fixRate.allIssue - meegoStatus.fixRate.fixIssue < 5) ||
        meegoStatus.fixRate.allIssue - meegoStatus.fixRate.fixIssue <= 2);
    // 是否已集成判断
    const curVersions: any[] = [];
    for (const field of workItemInfo.fields) {
      if (field.field_key === 'planning_version') {
        const fieldValue = JSON.stringify(field.field_value);
        const curPlanVersion = JSON.parse(fieldValue);
        if (curPlanVersion && curPlanVersion.length > 0) {
          const meegoVersion = await this.meegoService.queryVersionById('faceu', curPlanVersion);
          if (meegoVersion !== 0) {
            curVersions.push(...meegoVersion);
          }
        }
      }
    }
    const curPlatforms = new Set<PlatformType>();
    // 记录需求单对应的app&平台
    for (const version of curVersions) {
      // 剪映-Android-11.6.0
      const segs = version.versionName.split('-');
      if (['PC', 'Win', 'Mac'].includes(segs[1])) {
        curPlatforms.add(PlatformType.PC);
      } else if (PlatformType.Android === segs[1]) {
        curPlatforms.add(PlatformType.Android);
      } else if (PlatformType.iOS === segs[1]) {
        curPlatforms.add(PlatformType.iOS);
      }
    }
    meegoStatus.platforms = Array.from(curPlatforms);
    return meegoStatus;
  }

  isIntegration(platform: PlatformType, curStatus: StoryMeegoStatus) {
    let isIntegration = false;
    if (
      !(
        (platform === PlatformType.Android && curStatus.androidStatus === 1) ||
        (platform === PlatformType.iOS && curStatus.iOSStatus === 1) ||
        (platform === PlatformType.PC && curStatus.pcStatus === 1)
      )
    ) {
      isIntegration = true;
    }
    return isIntegration;
  }

  async isMultiTag(mrId: number) {
    const hostMrId = await this.bitsService.getMrHostInfo({
      mrId,
    });
    const hostBitsMrInfo = await this.bitsService.getMrInfo({
      mrId: hostMrId,
    });
    // 检查是否多仓MR
    const relation = await this.bitsService.getMrRelationList(mrId);
    // 检查是否多仓MR
    let multiTag = false;
    if (relation) {
      for (const mrInfo of relation) {
        if (
          (mrInfo.project_id === 40279 && hostBitsMrInfo?.project_id === 39995) ||
          (mrInfo.project_id === 39995 && hostBitsMrInfo?.project_id === 40279)
        ) {
          multiTag = true;
        }
      }
      const lvProject = relation.find(value => value.project_id === 40279);
      const ccProject = relation.find(value => value.project_id === 39995);
      if (!multiTag && lvProject && ccProject) {
        multiTag = true;
      }
    }
    return multiTag;
  }

  async getFixRate(meegoItem: MrMeegoInfo, platform?: string) {
    // 查询这个需求单下的解决率
    const queryStoryIssueFixRateResult = await this.meegoService.queryStoryIssueFixRate(
      meegoItem.task_id,
      platform === 'PC' ? 'Mac & Windows' : platform,
    );
    return queryStoryIssueFixRateResult;
  }

  async isQaReview(mrId: number) {
    // 检查QA reviewer的状态
    const mr_reviewer_status = await this.bitsService.getMrReviewersInfo(mrId);
    if (!mr_reviewer_status) {
      // return {
      //   code: ForceMergeErrorCode.GetMrReviewerInfoFailed,
      //   message: '获取MR Review信息失败',
      // };
      return;
    }
    let isQAReviewed = false;
    // 注意getMrReviewersInfo返回的数据中QA BM被视为RD
    for (const mr_reviewer of mr_reviewer_status) {
      if (mr_reviewer.review_role === 'QA' && mr_reviewer.approved !== 'approved') {
        isQAReviewed = false; // `QA reviewer未通过（${mr_reviewer.username}）`
      }
    }
    return isQAReviewed;
  }

  async getPipelineInfoList(mrId: number) {
    // 先获取所有关联的MR（当前MR可能是多主仓MR）
    const mrRelationInfos: MrRelationInfo[] | undefined = await this.bitsService.getMrRelationList(mrId);
    if (!mrRelationInfos) {
      // return {
      //   code: ForceMergeErrorCode.GetMrRelationInfoFailed,
      //   message: '获取关联MR信息失败',
      // };
      return;
    }
    const all_mr_ids = [...mrRelationInfos.map(info => info.id), mrId];
    console.log(all_mr_ids);
    for (const cur_mr_id of all_mr_ids) {
      // 获取MR的ppl列表
      const mrPipelines = await this.bitsService.getMrPipelineList(String(cur_mr_id));
      if (!mrPipelines) {
        // return {
        //   code: ForceMergeErrorCode.GetPipelineInfoFailed,
        //   message: '获取pipeline信息失败',
        // };
        return;
      }
      if (mrPipelines.length === 0) {
        continue;
      }
      for (const mrPipeline of mrPipelines) {
        if (mrPipeline.status === 'success') {
          // && mrPipeline.finish_time < timeThreshold
          // pplStatus = true;
          // return {
          //   code: ForceMergeErrorCode.Success,
          //   message: '获取pipeline信息成功',
          // };
          return [mrPipeline];
        }
        return [mrPipeline];
      }
    }
  }

  async getStoryStatusResult(storyMeegoStatus: StoryMeegoStatus[], appConfig: any, platform: string) {
    const storyStatusResult = {
      result: '',
      platform,
      storyMeegoStatus,
      appConfig,
    };
    for (const status of storyMeegoStatus) {
      if (status.skip) {
        continue;
      }
      if (
        !status.standard ||
        (platform === PlatformType.Android && status.androidStatus === 1) ||
        (platform === PlatformType.iOS && status.iOSStatus === 1) ||
        (platform === PlatformType.PC && status.pcStatus === 1) ||
        !status.ugCheckResult.pass
      ) {
        storyStatusResult.result += `${Array.from(status.platforms)}:storyId-${status.fixRate.storyId}`;
        if (platform === PlatformType.Android && status.androidStatus === 1) {
          storyStatusResult.result += `-Android集成节点未开始`;
        }
        if (platform === PlatformType.iOS && status.iOSStatus === 1) {
          storyStatusResult.result += `-iOS集成节点未开始`;
        }
        if (platform === PlatformType.PC && status.pcStatus === 1) {
          storyStatusResult.result += `-PC集成节点未开始`;
        }
        if (!status.standard) {
          if (status.fixRate.sP0P1Count > 0) {
            storyStatusResult.result += `-还剩${status.fixRate.sP0P1Count}个sP0P1未修复`;
          }
          if (Number(status.fixRate.rate) < appConfig.requireFixRate) {
            storyStatusResult.result += `-修复率${status.fixRate.rate}%不达${appConfig.requireFixRate}%`;
          }
          if (status.fixRate.allIssue - status.fixRate.fixIssue >= 5) {
            storyStatusResult.result += `-剩余未修复问题${status.fixRate.allIssue - status.fixRate.fixIssue}个,超过5个`;
          }
        }
        if (!status.ugCheckResult.pass) {
          storyStatusResult.result += `-${status.ugCheckResult.failReason}。`;
        }
      }
    }
    return storyStatusResult;
  }

  async isPipelinePass(mrPipelines?: MrPipelineInfo[]) {
    if (!mrPipelines) {
      return;
    }
    // 检查ppl状态
    // 找出关联的所有MR，对每个MR，找到最近一次的ppl id，确定其状态为success
    const timeThreshold = dayjs().startOf('d').add(18, 'h').unix();
    //  && mrPipeline.finish_time < timeThreshold
    const pplStatus = mrPipelines.some(
      mrPipeline => mrPipeline.status === 'success' && mrPipeline.finish_time <= dayjs().unix(),
    );
    return pplStatus;
  }

  async queryMeegoInfo(meegoUrl?: string) {
    if (!meegoUrl) {
      return errorRsp('meegoUrl is null!');
    }
    const meegoId = extractMeegoId(meegoUrl);
    if (!meegoId) {
      return errorRsp('解析meego Id错误, 请检查复制的meego链接!');
    }

    const workflowResult = await this.meegoService.requestWorkflow(faceuProjectKey, 'story', meegoId);
    const nodes = workflowResult.data.workflow_nodes;
    this.logger.info(`queryMeegoInfo url => ${meegoUrl}, meegoId => ${meegoId}, nodes => ${JSON.stringify(nodes)}`);
    const userKeys = nodes
      ?.filter(node => node !== undefined)
      ?.reduce((acc: string[], node: WorkflowNode) => {
        if (node.role_assignee) {
          // 过滤RD、FE、sever、QA、设计，其他人员暂不拉取，减少打扰
          const owners = node.role_assignee
            ?.filter(it => {
              const role = it?.role;
              return role
                ? ['PM', 'clientqa', 'clientrd', 'role_501834', 'Interaction', 'FE', 'Server'].includes(role)
                : false;
            })
            ?.flatMap(assignee => assignee.owners);
          acc.push(...owners);
        }
        return acc;
      }, []);
    const meegoUsers = await this.meegoService.requestMeegoUserInfos({
      user_keys: Array.from(new Set(userKeys ?? [])),
    });
    this.logger.info(
      `queryMeegoInfo url => ${meegoUrl}, meegoId => ${meegoId}, meegoUsers => ${JSON.stringify(meegoUsers)}`,
    );
    return successRsp('success', meegoUsers);
    // await this.meegoService.requestTaskInfo(faceuProjectKey, 'story', {
    //   work_item_ids: [Number(meegoId)],
    //   fields: ['name'],
    // });
  }

  async queryBitsInfo(bitsUrl?: string): Promise<NetworkResult<PreApprovalDetail | undefined>> {
    if (!bitsUrl) {
      return errorRsp('bitsUrl is null!');
    }
    // 获取MrId
    const mrId = this.getMrId(bitsUrl);
    // 获取bits信息
    const bitsMrInfo = await this.bitsService.getMrInfo({
      mrId,
    });
    // 主仓信息检查
    const isMulti = isMultiMr(bitsMrInfo);

    // 获取meego单链接
    const meegoResult = await this.bitsService.getBindMeegoTaskInfo({
      mr_id: mrId,
    });
    // app配置
    const appConfig = {
      apps: ['剪映', 'CC', '剪映专业版'], // meego版本中的app名
      requireFixRate: 85, // bug解决率
      checkPlaningVersion: true, // 检查计划上车版本
      checkMmrStory: true, // 检查多主仓MR必须绑定移动端需求
      extraMsg: '有疑问Android联系刘云娟,iOS联系刘彤彤,PC联系杨浩雁', // 额外需要提醒的文案
    };

    const platform = PlatformType.Android;
    if (!meegoResult || meegoResult.length <= 0) {
      return errorRsp('meegoResult is null or empty.', -1);
    }
    const storyMeegoStatus: StoryMeegoStatus[] = [];
    // 获取meego单的bug解决率
    const meegoItem = meegoResult[0];
    const multiTag = await this.isMultiTag(mrId);
    // 提取meego信息
    const curStatus = await this.getStoryMeegoStatus(meegoItem, multiTag, bitsMrInfo, platform, appConfig);
    // const curStatus = new StoryMeegoStatus();
    storyMeegoStatus.push(curStatus);

    // 获取ppl的状态
    // const isQAReviewed = await this.isQaReview(mrId);
    const pplInfos = await this.getPipelineInfoList(mrId);
    const pplStatus = await this.isPipelinePass(pplInfos);
    return successRsp<PreApprovalDetail>('success', {
      bitsInfo: {
        name: bitsMrInfo?.mr_detail_url,
        url: bitsMrInfo?.title,
        isMulti,
      },
      meegoInfo: [
        {
          link: new LinkInfo(meegoResult[0]?.task_url, meegoResult[0]?.task_title),
          issueInfo: curStatus.fixRate,
          meegoStatus:
            (curStatus.platforms?.length ?? 0) > 0 && curStatus.platforms.every(it => this.isIntegration(it, curStatus))
              ? MeegoWorkFlowNodeStatus.pass
              : MeegoWorkFlowNodeStatus.noPass,
        },
      ],
      pipelineInfo: {
        successTime: pplInfos?.at(pplInfos?.length - 1)?.finish_time ?? 0,
        pipelineStatus: pplStatus ? PipelineStatus.success : PipelineStatus.failed,
      },
    });
  }
  async giveTicketToBits(approvalInfo?: ApprovalInfoTable) {
    const mrUrl = approvalInfo?.bitsLink?.url;
    this.logger.info(`[ApprovalService] giveTicketToBits begin..., approvalInfo => ${JSON.stringify(approvalInfo)}`);
    if (mrUrl) {
      // const rsp = await this.collectAbnormalMultiMr.toggleMrTicket(mrUrl, true, true);
      // const mrId = parseInt(mrUrl.replace(/[\S]*\/([0-9]*)\?(.*)/, '$1'), 10);
      // const rsp = await this.collectAbnormalMultiMr.addVersionTicket(mrId, '14.9.0');
      const rsp = await this.collectAbnormalMultiMr.toggleMrTicket(mrUrl, true, true, 'abnormal_approval');
      if (rsp?.code === NetworkCode.Success) {
        this.logger.info(`[ApprovalService] giveTicketToBits success, approvalInfo => ${JSON.stringify(approvalInfo)}`);
      } else {
        this.logger.info(
          `[ApprovalService] giveTicketToBits failed,` +
            `approvalInfo => ${JSON.stringify(approvalInfo)}, reason => ${JSON.stringify(rsp?.message)} `,
        );
      }
      return rsp;
    }
    return errorRsp('请求bits链接为空');
  }

  async createApprovalTicketOrder(req: CreateTicketApprovalReq) {
    const approvalOrder =
      req.extraInfo?.appId && isRetouchApp(req.extraInfo?.appId)
        ? await this.approvalModelService.createRetouchApprovalTicketOrder(req)
        : await this.approvalModelService.createApprovalTicketOrder(req);
    if (!approvalOrder) {
      return errorRsp('error');
    }
    return successRsp('success', approvalOrder ?? {});
  }
  /**
   * ------------------------------- 热修任务相关 --------------------------------------------
   * */

  async buildRetouchCommonSummary(instanceCode: string, assessmentTasksInfo: Map<string, string[]>) {
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: instanceCode,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return undefined;
    }
    const instance = rsp?.data;

    // 发送之前阶段的信息汇总
    const commentSummary = new Map<string, any[]>();
    for (const node of instance?.timeline ?? []) {
      const nodeKeyName = [...assessmentTasksInfo.keys()].find(it =>
        assessmentTasksInfo.get(it)?.includes(node?.task_id ?? ''),
      );
      if (nodeKeyName) {
        const userOpenIds: string[] = uniq(
          instance?.timeline?.filter(it => it.type === 'PASS')?.map(it => it?.open_id),
        ).filter(it => it !== undefined);
        const notifyUsersEmail = await batchGetUserInfoByOpenIds({
          data: {
            openIds: userOpenIds,
          },
        });
        commentSummary.set(nodeKeyName, [
          ...(commentSummary.get(nodeKeyName) ?? []),
          {
            openId: node?.open_id,
            comment: node?.comment,
            userId: node?.user_id,
            userEmail: notifyUsersEmail.find(it => it?.open_id === node?.open_id)?.email,
          },
        ]);
      }
    }
    console.log('commentSummary', commentSummary);
    return commentSummary;
  }

  // 汇总审批信息，发送汇总报告
  async retouchHotfixSummary(approvalInfo: ApprovalDetail, summaryInfo: Map<string, any[]>) {
    // 发送通知
    const card = await this.larkCardService.buildRetouchHotfixSummary(approvalInfo, summaryInfo);
    this.logger.info(
      `[retouchHotfixSummary] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}, summaryInfo: ${JSON.stringify(summaryInfo)}`,
    );
    await this.sendHotfixTaskProcessCard(approvalInfo, card, []);
  }

  async createApprovalHotfixOrder(req: HotfixTaskApprovalReq) {
    const approvalOrder = isRetouchApp(req.appId)
      ? await this.approvalModelService.createRetouchApprovalHotfixOrder(req)
      : await this.approvalModelService.createApprovalHotfixOrder(req);
    if (!approvalOrder) {
      return errorRsp('error');
    }
    return successRsp('success', approvalOrder ?? {});
  }

  async sendProcessCard(approvalInfo?: ApprovalDetail, card?: LarkCard, notifyEmails?: string[], chatId?: string) {
    // 发送通知
    this.logger.info(`[sendProcessCard] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`);
    const userOpenIds: string[] = [];
    if (notifyEmails) {
      // 相关知会人
      const userInfos = await batchGetUserInfo({
        data: {
          emails: notifyEmails,
        },
      });
      const _userOpenIds: string[] | undefined = userInfos
        ?.map(userInfo => userInfo?.open_id)
        ?.filter(it => it !== undefined);
      userOpenIds.push(...(_userOpenIds ?? []));
    }
    const newChatIds: string[] = [];
    if (chatId) {
      newChatIds.push(chatId);
    }
    this.logger.info(`[sendProcessCard] chatId: ${chatId} , newChatIds: ${JSON.stringify(newChatIds)}`);
    // 通知群和知会人
    for (const groupChatChatId of newChatIds) {
      if (!groupChatChatId) {
        continue;
      }
      if (userOpenIds) {
        const inviteResult = await this.larkService.inviteUsers2Group(userOpenIds, groupChatChatId);
        this.logger.info(
          `[sendProcessCard] approvalInfo: ${JSON.stringify(approvalInfo)} userOpenIds => ${JSON.stringify(userOpenIds)}, chatId => ${chatId}, inviteResult => ${JSON.stringify(inviteResult)}`,
        );
      }

      const result = await this.larkService.sendMessage(
        UserIdType.chatId,
        groupChatChatId,
        JSON.stringify(card),
        MessageType.interactive,
      );
      this.logger.info(`[sendProcessCard] result: ${JSON.stringify(result)}`);
    }
  }

  async sendHotfixTaskProcessCard(approvalInfo?: ApprovalDetail, card?: Card, notifyEmails?: string[]) {
    return this.sendProcessCard(approvalInfo, card, notifyEmails, approvalInfo?.hotfixTask?.reviewChatGroup);
  }

  async hotfixTaskApproved(approvalInfo?: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildHotfixTaskApproved(approvalInfo);
    this.logger.info(
      `[hotfixTaskApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendHotfixTaskProcessCard(approvalInfo, card, approvalInfo?.hotfixTask?.businessQaEmails);
  }

  async hotfixTaskRejected(approvalInfo?: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildHotfixTaskRejected(approvalInfo);
    this.logger.info(
      `[hotfixTaskRejected] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendHotfixTaskProcessCard(approvalInfo, card, approvalInfo?.hotfixTask?.businessQaEmails ?? []);
  }

  // 可行评估通过
  async hotfixTaskAssessmentApproved(approvalInfo?: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildHotfixTaskAssessmentApproved(approvalInfo);
    this.logger.info(
      `[hotfixTaskAssessmentApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendHotfixTaskProcessCard(approvalInfo, card, approvalInfo?.hotfixTask?.businessQaEmails ?? []);
  }

  // 醒图热修审批节点
  async retouchHotfixTaskCommonApproved(approvalInfo: ApprovalDetail, nodeKeyName: string) {
    // 请求审批接口拉审批人
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: approvalInfo.instanceCode ?? '',
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }

    const instance = rsp?.data;
    const notifyUsersOpenId =
      (instance?.task_list?.filter(it => it?.node_id === nodeKeyName) ?? []).map(it => it?.open_id ?? '') ?? [];
    const notifyUsersEmail = (
      await batchGetUserInfoByOpenIds({
        data: {
          openIds: notifyUsersOpenId,
        },
      })
    )
      .filter(it => it !== undefined)
      .map(it => it?.email);

    // 拉人进群
    const inviteResult = await this.larkService.inviteUsers2Group(
      uniq(notifyUsersOpenId),
      approvalInfo.fixVersionTask?.reviewChatGroup ?? approvalInfo.chatId ?? '',
    );
    this.logger.info(
      `[retouchHotfixTaskCommonApproved] approvalInfo: ${JSON.stringify(approvalInfo)} nodeKeyName => ${nodeKeyName} userOpenIds => ${JSON.stringify(notifyUsersOpenId)}, chatId => ${approvalInfo.fixVersionTask?.reviewChatGroup ?? approvalInfo.chatId ?? ''}, inviteResult => ${JSON.stringify(inviteResult)}`,
    );
    // 发送通知
    const card = await this.larkCardService.buildRetouchHotfixTaskNodeCommonApproved(
      approvalInfo,
      notifyUsersEmail,
      nodeKeyName,
    );
    this.logger.info(
      `[retouchHotfixTaskCommonApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, nodeKeyName => ${nodeKeyName}, card: ${JSON.stringify(card)}`,
    );
    await this.sendHotfixTaskProcessCard(approvalInfo, card, notifyUsersEmail ?? []);
  }

  // 自测通过
  async hotfixTaskSelfTestingApproved(approvalInfo?: ApprovalDetail) {
    // 非前端问题且非全量版本热修无二级审批，跳过卡片推送
    const isFE = approvalInfo?.hotfixTask?.isFE ?? false;
    const isFullReleaseVersion = approvalInfo?.hotfixTask?.isFullReleaseVersion ?? true;
    if (!isFE && !isFullReleaseVersion) {
      return;
    }
    // 发送通知
    const card = await this.larkCardService.buildHotfixSelfTestingApproved(approvalInfo);
    this.logger.info(
      `[hotfixTaskAssessmentApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    const appId = approvalInfo?.hotfixTask?.appId;
    if (appId) {
      if (isFE) {
        await this.sendHotfixTaskProcessCard(
          approvalInfo,
          card,
          ['<EMAIL>']
            ?.concat(approvalInfo?.hotfixTask?.businessQaEmails ?? [])
            ?.concat(ApprovalHotfixConfig[appId]?.owners?.BD ?? []),
        );
      } else if (isFullReleaseVersion) {
        await this.sendHotfixTaskProcessCard(
          approvalInfo,
          card,
          (approvalInfo?.hotfixTask?.businessQaEmails ?? [])
            ?.concat(ApprovalHotfixConfig[appId].owners?.qaOwner ?? [])
            ?.concat(ApprovalHotfixConfig[appId].owners?.rdOwner ?? []),
        );
      } else {
        await this.sendHotfixTaskProcessCard(
          approvalInfo,
          card,
          (approvalInfo?.hotfixTask?.businessQaEmails ?? [])
            ?.concat(ApprovalHotfixConfig[appId].owners?.qaPoc ?? [])
            ?.concat(ApprovalHotfixConfig[appId].owners?.rdPoc ?? []),
        );
      }
    }
  }

  async hotfixTaskOnAssessment(approvalInfo: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildHotfixTaskAssessment(approvalInfo);
    this.logger.info(
      `[hotfixTaskOnAssessment] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendHotfixTaskProcessCard(approvalInfo, card, approvalInfo?.hotfixTask?.businessQaEmails ?? []);
  }

  async fixTaskRejected(approvalInfo?: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildHotfixTaskRejected(approvalInfo);
    this.logger.info(
      `[hotfixTaskRejected] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendHotfixTaskProcessCard(approvalInfo, card, approvalInfo?.hotfixTask?.businessQaEmails ?? []);
  }
  /**
   * ------------------------------------------------------------------------------
   * */

  /**
   * --------------------------------------小版本申请----------------------------------------
   * */
  async createFixVersionApprovalOrder(req: FixVersionApprovalReq) {
    const approvalOrder = isRetouchApp(req.appId)
      ? await this.approvalModelService.createRetouchFixVersionApprovalOrder(req)
      : await this.approvalModelService.createFixVersionApprovalOrder(req);
    if (!approvalOrder) {
      return errorRsp('error');
    }
    return successRsp('success', approvalOrder ?? {});
  }
  async sendFixVersionTaskProcessCard(approvalInfo?: ApprovalDetail, card?: Card, notifyEmails?: string[]) {
    return this.sendProcessCard(approvalInfo, card, notifyEmails, approvalInfo?.fixVersionTask?.reviewChatGroup);
  }
  async fixVersionTaskStartAssessment(approvalInfo: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildFixVersionAssessment(approvalInfo);
    this.logger.info(
      `[fixVersionTaskStartAssessment] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    const appId = approvalInfo?.fixVersionTask?.appId ?? -1;
    const userEmails: string[] = [];
    userEmails.push(...(ApprovalFixVersionConfig?.[appId]?.owners?.rdPoc ?? []));
    userEmails.push(...(ApprovalFixVersionConfig?.[appId]?.owners?.qaPoc ?? []));

    await this.sendFixVersionTaskProcessCard(approvalInfo, card, userEmails);
  }

  // 汇总审批信息，发送汇总报告
  async retouchFixVersionSummary(approvalInfo: ApprovalDetail, summaryInfo: Map<string, any[]>) {
    // 发送通知
    const card = await this.larkCardService.buildRetouchFixVersionSummary(approvalInfo, summaryInfo);
    this.logger.info(
      `[retouchFixVersionSummary] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}, summaryInfo: ${JSON.stringify(summaryInfo)}`,
    );
    await this.sendFixVersionTaskProcessCard(approvalInfo, card, []);
  }

  async retouchFixVersionTaskCommonApproved(approvalInfo: ApprovalDetail, nodeKeyName: string) {
    // 请求审批接口拉审批人
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: approvalInfo.instanceCode ?? '',
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }

    const instance = rsp?.data;
    const notifyUsersOpenId =
      (instance?.task_list?.filter(it => it?.node_id === nodeKeyName) ?? []).map(it => it?.open_id ?? '') ?? [];
    const notifyUsersEmail = (
      await batchGetUserInfoByOpenIds({
        data: {
          openIds: notifyUsersOpenId,
        },
      })
    )
      .filter(it => it !== undefined)
      .map(it => it?.email);

    // 拉人进群
    const inviteResult = await this.larkService.inviteUsers2Group(
      uniq(notifyUsersOpenId),
      approvalInfo.fixVersionTask?.reviewChatGroup ?? approvalInfo.chatId ?? '',
    );
    this.logger.info(
      `[retouchFixVersionTaskFirstCommonApproved] approvalInfo: ${JSON.stringify(approvalInfo)} nodeKeyName => ${nodeKeyName} userOpenIds => ${JSON.stringify(notifyUsersOpenId)}, chatId => ${approvalInfo.fixVersionTask?.reviewChatGroup ?? approvalInfo.chatId ?? ''}, inviteResult => ${JSON.stringify(inviteResult)}`,
    );
    // 发送通知
    const card = await this.larkCardService.buildRetouchFixVersionCommonNodeApproved(
      approvalInfo,
      notifyUsersEmail,
      nodeKeyName,
    );
    this.logger.info(
      `[retouchFixVersionTaskFirstCommonApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, nodeKeyName => ${nodeKeyName}, card: ${JSON.stringify(card)}`,
    );
    await this.sendFixVersionTaskProcessCard(approvalInfo, card, notifyUsersEmail ?? []);
  }

  async fixVersionTaskAssessmentApproved(approvalInfo?: ApprovalDetail) {
    const userEmails: string[] = [];
    const appId = approvalInfo?.fixVersionTask?.appId ?? -1;
    if (approvalInfo?.fixVersionTask?.isBugfixType === false) {
      userEmails.push(...(ApprovalFixVersionConfig?.[appId]?.owners?.rdManager ?? []));
      userEmails.push(...(ApprovalFixVersionConfig?.[appId]?.owners?.pmManager ?? []));
    } else {
      userEmails.push(...(ApprovalFixVersionConfig?.[appId]?.owners?.rdOwner ?? []));
      userEmails.push(...(ApprovalFixVersionConfig?.[appId]?.owners?.qaOwner ?? []));
    }
    // 发送通知
    const card = await this.larkCardService.buildFixVersionAssessmentApproved(approvalInfo, userEmails);
    this.logger.info(
      `[hotfixTaskAssessmentApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendFixVersionTaskProcessCard(approvalInfo, card, userEmails ?? []);
  }

  async fixVersionTaskApproved(approvalInfo?: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildFixVersionApproved(approvalInfo);
    this.logger.info(
      `[hotfixTaskApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendFixVersionTaskProcessCard(approvalInfo, card, []);
  }

  async fixVersionCreateSuccess(approvalInfo?: ApprovalDetail) {
    const card = await this.larkCardService.buildFixVersionCreateSuccess(approvalInfo);
    this.logger.info(
      `[fixVersionCreateSuccess] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    return this.sendFixVersionTaskProcessCard(approvalInfo, card, []);
  }

  // 插入小版本
  async onFixVersionTaskApproved(approvalInfo?: ApprovalDetail) {
    const instanceCode = approvalInfo?.instanceCode;
    if (!instanceCode) {
      return;
    }
    // 获取实例
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: instanceCode,
      },
    });
    if (!rsp || rsp.code !== 0) {
      return;
    }
    const instance = rsp?.data;
    this.logger.info(`[onFixVersionTaskApproved] instance: ${JSON.stringify(instance)}`);
    if (!instance?.form) {
      return;
    }
    const approvalForm: { id: string; value: string }[] = JSON.parse(instance?.form);
    this.logger.info(`[onFixVersionTaskApproved] approvalForm: ${JSON.stringify(approvalForm)}`);
    let version: string | undefined, timeStr: string | undefined;
    if (isRetouchApp(approvalInfo.app_id)) {
      version = approvalForm?.find(it => it?.id === 'widget17520690004520001')?.value;
      timeStr = approvalForm?.find(it => it?.id === 'widget17520691507270001')?.value;
    } else {
      version = approvalForm?.find(it => it?.id === 'widget17311507808880001')?.value;
      timeStr = approvalForm?.find(it => it?.id === 'widget17311509584420001')?.value;
    }
    this.logger.info(
      `[onFixVersionTaskApproved] approvalForm: ${JSON.stringify(approvalForm)}, version: ${version}, timeStr: ${timeStr}`,
    );
    dayjs.extend(utc);
    dayjs.extend(timezone);
    const time = timeStr ? dayjs(timeStr).unix() : dayjs().unix();
    const fixVersionTask = approvalInfo?.fixVersionTask;
    const appId = fixVersionTask?.appId;
    // const version = fixVersionTask?.desireVersion;
    // const integrationTime = fixVersionTask?.expectedIntegrationTime;
    // const larkService = useInject(LarkService);
    // const chatGroupId = fixVersionTask?.reviewChatGroup;
    // if (chatGroupId) {
    // await larkService.sendTextMessage(
    //   UserIdType.chatId,
    //   chatGroupId,
    //   `fixVersion: ${fixVersion}, version:${version} ,time: ${convertToCustomFormat(time)} integrationTime:  ${convertToCustomFormat(integrationTime)}`,
    // );
    // }
    const fixVersion = versionUtils.plusVersion(version);
    this.logger.info(
      `[onFixVersionTaskApproved] approvalForm: ${JSON.stringify(approvalForm)}, appId: ${appId}, version: ${version}, fixVersion: ${fixVersion}, time: ${time}`,
    );
    if (!appId || !version || !time || !fixVersion) {
      return;
    }
    if (fixVersionTask?.isBugfixType) {
      await this.versionProcessInfoEditorService.insertBugfixVersion(appId, version, fixVersion, time);
    } else {
      await this.versionProcessInfoEditorService.createFixVersion(appId, version, fixVersion, time);
    }
    await this.fixVersionCreateSuccess(approvalInfo);

    // 核心群配置,仅封板后需要
    await this.sendFixVersionCreateSuccessToCoreGroup(approvalInfo);
  }
  // 发送消息到核心群中
  async sendFixVersionCreateSuccessToCoreGroup(approvalInfo?: ApprovalDetail) {
    const appId = approvalInfo?.fixVersionTask?.appId;
    const product = appId ? versionUtils.appIdToAppInfo(appId.toString())?.product : undefined;
    const requireChatGroups: string[] = [];
    if (product) {
      // 通知核心群
      if (product === LVProductType.lv || product === LVProductType.cc) {
        const chatInfo = (await this.configService.queryConfigItem(
          '1775',
          VersionConfigKeys.coreGroupChatId,
        )) as ChatInfo;
        const coreChatId = chatInfo.chat_id;
        if (coreChatId) {
          requireChatGroups.push(coreChatId);
        }
      }

      if (product === RetouchProductType.retouch || product === RetouchProductType.hypic) {
        const chatInfo = (await this.configService.queryConfigItem(
          '2515',
          VersionConfigKeys.approvalGroupChatId,
        )) as ChatInfo;
        const coreChatId = chatInfo.chat_id;
        if (coreChatId) {
          requireChatGroups.push(coreChatId);
        }
      }
    }
    for (const chatId of requireChatGroups) {
      if (!chatId) {
        continue;
      }
      const coreCard = await this.larkCardService.buildFixVersionInsertSuccess(approvalInfo);
      await this.sendProcessCard(approvalInfo, coreCard, [], chatId);
    }
  }

  async fixVersionTaskRejected(approvalInfo?: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildFixVersionTaskRejected(approvalInfo);
    this.logger.info(
      `[hotfixTaskApproved] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendFixVersionTaskProcessCard(approvalInfo, card, []);
  }
  /**
   * ------------------------------------需求插入------------------------------------------
   * */
  async sendRequirementInsertProcessCard(approvalInfo?: ApprovalDetail, card?: LarkCard, notifyEmails?: string[]) {
    return this.sendProcessCard(approvalInfo, card, notifyEmails, approvalInfo?.chatId);
  }

  async createApprovalRequirementOrder(req: RequirementApprovalReq) {
    const containRetouchApp = (appIds: number[]) => {
      for (const appId of appIds) {
        if (isRetouchApp(appId)) {
          return true;
        }
      }
      return false;
    };
    const approvalOrder =
      containRetouchApp(req.appIds) && req.type === RequirementChangeActionType.insert
        ? await this.approvalModelService.createRetouchApprovalInsertRequirementAfterCodeFreeze(req)
        : await this.approvalModelService.createApprovalRequirementOrder(req);
    if (!approvalOrder) {
      return errorRsp('error');
    }
    return successRsp('success', approvalOrder ?? {});
  }

  async queryApprovalConfig(approvalCode?: string) {
    if (!approvalCode) {
      return errorRsp('approvalCode is null!');
    }
    const config = await this.approvalModelService.queryApprovalConfig(approvalCode);
    if (!config) {
      return errorRsp(`approval config is not found, please check your approvalCode ${approvalCode}`);
    }
    return successRsp('success', config);
  }

  async queryApprovalTemplates(appId: number): Promise<NetworkResult<ApprovalConfigDetail[]>> {
    const configs = await this.approvalModelService.queryApprovalTemplates(appId);
    return successRsp('success', configs);
  }

  async createApprovalInstance(appId: number, approvalCode: string, formInfoJson: string, formInstanceJson: string) {
    return await this.approvalModelService.createDefineApprovalOrder(
      appId,
      approvalCode,
      formInfoJson,
      formInstanceJson,
    );
  }

  async createApprovalConfig(appId: number, configJsonStr?: string) {
    if (!configJsonStr) {
      return errorRsp('configJsonStr is null!');
    }
    try {
      const rsp = await this.approvalModelService.createApprovalConfig(appId, JSON.parse(configJsonStr));
      return successRsp('createApprovalConfig success', rsp?.data);
    } catch (e) {
      if (e instanceof Error) {
        this.logger.error(e);
        return errorRsp(e.message);
      } else {
        console.error(e);
      }
    }
  }

  async queryApprovalList(appId: number) {
    return this.approvalDBService.allApprovals(appId);
  }

  async retouchApprovalInvolveBusinessLineAssessment(approvalInfo: ApprovalDetail) {
    // 发送通知
    const card = await this.larkCardService.buildRetouchApprovalBusinessInvolveCard(approvalInfo);
    this.logger.info(
      `[retouchApprovalInvolveBusinessLineAssessment] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendRequirementInsertProcessCard(approvalInfo, card, []);
  }

  async retouchApprovalInvolveBusinessLineAction(approvalInfo: ApprovalDetail, taskId: string) {
    let businessLines: string[] = [];
    switch (approvalInfo.approvalType) {
      case ApprovalType.InsertProductRequirements:
        businessLines = uniq(
          Object.values(approvalInfo?.requirementChangeInfo?.affectBusinessLine ?? {}).reduce(
            (pre, cur) => [...pre, ...cur],
            [],
          ),
        );
        break;
      case ApprovalType.HotFix:
        businessLines = uniq(
          Object.values(approvalInfo?.hotfixTask?.affectBusinessLine ?? {}).reduce((pre, cur) => [...pre, ...cur], []),
        );
        break;
      case ApprovalType.FixedVersionRequirements:
        businessLines = uniq(
          Object.values(approvalInfo?.fixVersionTask?.affectBusinessLine ?? {}).reduce(
            (pre, cur) => [...pre, ...cur],
            [],
          ),
        );
        break;
      default:
        businessLines = uniq(
          Object.values(approvalInfo?.affectBusinessLine ?? {}).reduce((pre, cur) => [...pre, ...cur], []),
        );
        break;
    }
    const businessEmails = uniq(
      businessLines
        .map(key =>
          RetouchApprovalBusinessConfigs[key].owners
            ?.map(i => [...(i?.FTO ?? []), ...(i?.pmPOC ?? []), ...(i?.qaPoc ?? [])])
            .flat(),
        )
        .flat(),
    );

    this.logger.info(`[retouchApprovalInvolveBusinessLineAction] businessEmails: ${businessEmails.join(', ')}`);
    const addUserId = await this.larkService.batchGetUserId(UserIdType.userId, {
      emails: businessEmails,
    });
    const operator = await this.larkService.batchGetUserId(UserIdType.userId, {
      emails: ['<EMAIL>'],
    });
    const result = await this.larkClient.approval.instance.addSign({
      data: {
        approval_code: approvalInfo?.approvalCode ?? '',
        instance_code: approvalInfo?.instanceCode ?? '',
        user_id: operator[0]?.user_id ?? '',
        add_sign_user_ids: addUserId.map(it => it.user_id),
        task_id: taskId,
        add_sign_type: 3, // 共同审批
        approval_method: 2, // 会签
      },
    });
    this.logger.info(`[retouchApprovalInvolveBusinessLineAction] result: ${JSON.stringify(result)}`);
  }

  async retouchRequirementInsertAlert(approvalInfo: ApprovalDetail, notifyUserEmails: string[]) {
    const card = await this.larkCardService.buildRetouchRequirementInsertAlert();
    this.logger.info(
      `[retouchRequirementInsertAlert] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}`,
    );
    await this.sendRequirementInsertProcessCard(approvalInfo, card, notifyUserEmails);
  }
  async retouchRequirementInsertCommonAssessment(approvalInfo: ApprovalDetail, nodeKeyName: string) {
    // 请求审批接口拉审批人
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: approvalInfo.instanceCode ?? '',
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }

    const instance = rsp?.data;
    const notifyUsersOpenId =
      (instance?.task_list?.filter(it => it?.node_id === nodeKeyName) ?? []).map(it => it?.open_id ?? '') ?? [];
    const notifyUsersEmail = (
      await batchGetUserInfoByOpenIds({
        data: {
          openIds: notifyUsersOpenId,
        },
      })
    )
      .filter(it => it !== undefined)
      .map(it => it?.email);

    // 拉人进群
    const inviteResult = await this.larkService.inviteUsers2Group(uniq(notifyUsersOpenId), approvalInfo.chatId ?? '');
    this.logger.info(
      `[retouchRequirementInsertCommonAssessment] approvalInfo: ${JSON.stringify(approvalInfo)} nodeKeyName => ${nodeKeyName} userOpenIds => ${JSON.stringify(notifyUsersOpenId)}, chatId => ${approvalInfo.fixVersionTask?.reviewChatGroup ?? approvalInfo.chatId ?? ''}, inviteResult => ${JSON.stringify(inviteResult)}`,
    );

    // 发送通知
    const card = await this.larkCardService.buildRetouchRequirementInsertCommonNodeAssessment(
      approvalInfo,
      notifyUsersEmail,
      nodeKeyName,
    );
    this.logger.info(
      `[retouchRequirementInsertCommonAssessment] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}, notifyUsersEmail: ${notifyUsersEmail.join(', ')}`,
    );
    await this.sendRequirementInsertProcessCard(approvalInfo, card, notifyUsersEmail);
  }

  // 汇总审批信息，发送汇总报告
  async retouchRequirementInsertSummary(approvalInfo: ApprovalDetail, summaryInfo: Map<string, any[]>) {
    // 发送通知
    const card = await this.larkCardService.buildRetouchRequirementInsertSummary(approvalInfo, summaryInfo);
    this.logger.info(
      `[retouchRequirementInsertSummary] approvalInfo: ${JSON.stringify(approvalInfo)}, card: ${JSON.stringify(card)}, summaryInfo: ${JSON.stringify(summaryInfo)}`,
    );
    await this.sendRequirementInsertProcessCard(approvalInfo, card, []);
  }

  /**
   * ------------------------------------异常上车------------------------------------------
   * */

  async sendTicketApprovalProcessCard(approvalInfo?: ApprovalDetail, card?: Card, notifyEmails?: string[]) {
    return this.sendProcessCard(approvalInfo, card, notifyEmails, approvalInfo?.chatId);
  }

  async retouchTicketApprovalCommonAssessment(approvalInfo: ApprovalDetail, nodeKeyName: string) {
    // 请求审批接口拉审批人
    const larkClient = useInject(LarkClient);
    const rsp = await larkClient.approval.instance.get({
      path: {
        instance_id: approvalInfo.instanceCode ?? '',
      },
    });
    if (!rsp || rsp.code !== 0) {
      return false;
    }

    const instance = rsp?.data;
    const notifyUsersOpenId =
      (instance?.task_list?.filter(it => it?.node_id === nodeKeyName) ?? []).map(it => it?.open_id ?? '') ?? [];
    const notifyUsersEmail = (
      await batchGetUserInfoByOpenIds({
        data: {
          openIds: notifyUsersOpenId,
        },
      })
    )
      .filter(it => it !== undefined)
      .map(it => it?.email);

    // 拉人进群
    const inviteResult = await this.larkService.inviteUsers2Group(uniq(notifyUsersOpenId), approvalInfo.chatId ?? '');
    this.logger.info(
      `[retouchTicketApprovalCommonAssessment] approvalInfo: ${JSON.stringify(approvalInfo)} nodeKeyName => ${nodeKeyName} userOpenIds => ${JSON.stringify(notifyUsersOpenId)}, chatId => ${approvalInfo.fixVersionTask?.reviewChatGroup ?? approvalInfo.chatId ?? ''}, inviteResult => ${JSON.stringify(inviteResult)}`,
    );

    // 发送通知
    const card = await this.larkCardService.buildRetouchTicketApprovalCommonNodeAssessment(
      approvalInfo,
      notifyUsersEmail,
      nodeKeyName,
    );
    this.logger.info(
      `[retouchTicketApprovalCommonAssessment] approvalInfo: ${JSON.stringify(approvalInfo)}, nodeKeyName => ${nodeKeyName}, card: ${JSON.stringify(card)}`,
    );
    await this.sendTicketApprovalProcessCard(approvalInfo, card, notifyUsersEmail ?? []);
  }

  /**
   * ------------------------------------ 历史工单相关接口 ------------------------------------------
   */

  // 加入接口群聊
  async inviteApprovalGroup(email: string, groupChatId: string) {
    const usrInfo = await this.larkService.getUserInfoByEmail(email);
    const openId = usrInfo?.open_id;
    this.logger.info(
      `[inviteApprovalGroup] email: ${email}, openId: ${openId}, groupChatId => ${groupChatId},userInfo => ${JSON.stringify(usrInfo)}`,
    );
    if (openId) {
      return await this.larkService.inviteUsers2Group([openId], groupChatId);
    }
  }

  private getMrId(bitsUrl: string) {
    // TODO 检查是bits发起的链接
    const mrIdStr = bitsUrl.replace(/.*\/(\d+)/, '$1');
    const mrId = parseInt(mrIdStr, 10);
    this.logger.info(`[getMrId] mrUrl => ${bitsUrl} mrIdStr => ${mrIdStr} to mrId => ${mrId}`);
    return mrId;
  }
}
