import {
  CloudEvent,
  CloudEventTriggerType,
  TimerCloudEvent,
  TimerCloudEventTrigger,
} from '@gulux/gulux/byted-cloudevent';
import DataRequestEngine from '../service/DataRequestEngine';
import SlardarRequestEngine from '../service/SlardarRequestEngine';
import SlardarService from '../service/slardar/slardar';
import OwnerSyncEngine from '../service/OwnerSyncEngine';
import ExperimentService from '../service/experiment/experiment';
import TeaRequestEngine from '../service/tea/TeaRequestEngine';
import iOSVersionQualityService from '../service/slardar/iOSVersionQuality';
import VersionSyncService from '../service/slardar/VersionSync';
import AutoVersionStageService from '../service/stage/autoVersionStageService';
import AlarmMetricRiskDetectService from '../service/alarm/AlarmMetricRiskDetectService';
import MRProfilerService from 'api/service/mrProfiler/MRProfilerService';
import IssueAutoPush from '../service/slardar/IssueAutoPush';
import { CronJob, handleTimerTrigger } from '@pa/backend/dist/src/utils/cronJob';
import { useInject } from '@edenx/runtime/bff';
import MemoryGraphService from '../service/memoryGraph/MemoryGraph';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { isSG } from '@pa/backend/dist/src/utils/region';
import { Region } from '../utils/region';
import { useBusinessInject } from '@pa/backend/dist/src/utils/business';
import { LibraNotifyService } from '../service/libra/LibraNotifyService';
import { ProcessorCrash } from '../service/experiment/ProcessorCrash';
import { ProcessorAd } from '../service/experiment/ProcessorAd';
import { ProcessorFeedback } from '../service/experiment/ProcessorFeedback';
import { ProcessorLT } from '../service/experiment/ProcessorLT';
import { ProcessorSearch } from '../service/experiment/ProcessorSearch';
import { ProcessorGroup } from '../service/experiment/ProcessorGroup';
import { ProcessorCommercial } from 'api/service/experiment/ProcessorCommercial';

@TimerCloudEventTrigger()
export default class QualityCronJobTrigger implements CloudEventTriggerType {
  async handle(@CloudEvent() event?: TimerCloudEvent): Promise<void> {
    await handleTimerTrigger(this, event);
  }

  @CronJob('* * * * *', 'heartBeats', ['zhanglinwei.yimu'], false, [Region.CN])
  async heartBeats() {
    useInject(BytedLogger).info('[CronJobQuality] -> 心跳ing');
  }

  @CronJob('10 * * * *', 'TEA Old Data Collect', ['tanhaiyang'], isSG())
  async triggerDataCollect() {
    await useInject(DataRequestEngine).triggerDataCollect();
  }

  @CronJob('7 * * * *', 'Bits Version Collect', ['weijingdong'])
  async saveAllVersions() {
    await useInject(VersionSyncService).saveAllVersions();
  }

  @CronJob('10 * * * *', 'Slardar Data Collect', ['zhoubaoding.1'], true)
  async slardarDataCollect() {
    await useInject(SlardarRequestEngine).slardarDataCollect();
  }

  @CronJob('0 0 * * 5', 'Slardar Data Collect Top Version History', ['zhoubaoding.1'])
  async slardarVersionDataWeekly() {
    await useInject(SlardarRequestEngine).slardarVersionDataWeekly();
  }

  @CronJob('50 0 * * *', 'Slardar Data Collect Version New', ['zhoubaoding.1'])
  async slardarNewCrashIssueListCollect() {
    await useInject(SlardarRequestEngine).slardarNewCrashIssueListCollect();
  }

  @CronJob('50 0 * * *', 'Slardar Version Quality Data Collect', ['zhoubaoding.1'])
  async slardarVersionQualityDataCollect() {
    await useInject(SlardarRequestEngine).versionQualityDataCollect();
  }

  @CronJob('0 10,14,18 * * 1-5', 'Slardar Auto Notice', ['zhoubaoding.1'])
  async slardarAutoNotice() {
    // await useInject(SlardarRequestEngine).slardarAutoNotice();
  }

  @CronJob('30 18 * * 1-5', 'Alarm Risk Detect', ['tanhaiyang'])
  async alarmDetect() {
    await useInject(AlarmMetricRiskDetectService).detectRisk();
  }

  // @CronJob('30 10,11,14,16,17 * * 1-5', 'Libra Info Engine', ['zhanglinwei.yimu'])
  // async libraInfoEngineTrigger() {
  //   await useInject(LibraInfoEngine).trigger();
  // }

  @CronJob('0 21 * * *', 'Slardar Quota Cache Cleanup', ['zhanglinwei.yimu'])
  async slardarQuotaUsageExpire() {
    await useInject(SlardarService).quotaUsageExpire();
  }

  @CronJob('10 * * * *', 'TEA Data Collect', ['tanhaiyang'], !isSG())
  async teaDataCollect() {
    await useInject(TeaRequestEngine).dataCollector();
  }

  // @CronJob('0 11 * * *', 'Component Owner Sync', ['zhanglinwei.yimu'])
  // async ownerSync() {
  //   await useInject(OwnerSyncEngine).sync();
  // }

  @CronJob('0 11,16 * * 1-5', 'Flight Auto Notice', ['zhanglinwei.yimu'])
  async experimentAutoNotice() {
    await useBusinessInject(ExperimentService)?.AutoWarn();
  }

  @CronJob('30 10 * * 1-5', 'Flight Change Notify Of Yesterday', ['longguoyang'], true, [Region.SG])
  async experimentChangeNotifyOfYesterday() {
    await useBusinessInject(ExperimentService)?.changeNotifyOfYesterday();
  }

  @CronJob('40 10 * * 1-5', 'Flight Close Attribution Not Finish Notify', ['longguoyang'], true, [Region.SG])
  async experimentCloseAttributionNotFinishNotify() {
    await useBusinessInject(ExperimentService)?.closeAttributionNotFinishNotifyByMeegoTeam();
  }

  @CronJob('10 9 * * 1', 'iOS Quality Data Auto Fetch', ['ouyangquan'])
  async iosVersionQualityDataFetch() {
    await useInject(iOSVersionQualityService).entry({});
  }

  @CronJob('30 * * * *', 'check auto stage enable', ['ouyangquan'])
  async versionStageCheck() {
    await useInject(AutoVersionStageService).autoCheck();
  }

  @CronJob('15 */12 * * * ', 'check auto memorygraph', ['ouyangquan'])
  async memoryGraphCheck() {
    await useInject(MemoryGraphService).autoCheck();
  }

  @CronJob('*/10 * * * *', 'batch run mr profiler task', ['huangsimeng.simen'])
  async mrProfilerTask() {
    await useInject(MRProfilerService).batchStartMRProfilerTask();
  }

  @CronJob('*/30 10-21 * * 1-5', 'IssueAutoPush', ['weijingdong'])
  async issueAutoPush() {
    await useInject(IssueAutoPush).autoPushTimer();
  }

  @CronJob('0 11 * * 1-5', 'Libra Remind Review Data Notify', ['longguoyang'])
  async libraRemindReviewDataNotify() {
    await useInject(LibraNotifyService).remindDataReviewNotify();
  }

  // @CronJob('2 * * * *', 'Libra Crash Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  // async libraPatrolCrash() {
  //   await useInject(ProcessorCrash).getExperimentPatrolData([147, 305, 255]);
  // }
  //
  // @CronJob('8 * * * *', 'Libra Ad Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  // async libraPatrolAd() {
  //   await useInject(ProcessorAd).getExperimentPatrolData([147, 305, 255]);
  // }
  //
  // @CronJob('4 * * * *', 'Libra Feedback Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  // async libraPatrolFeedback() {
  //   await useInject(ProcessorFeedback).getExperimentPatrolData([147, 305, 255]);
  // }

  @CronJob('0 4 * * *', 'Libra LT Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  async libraPatrolLT() {
    const res = useInject(ProcessorLT).getExperimentPatrolData([147, 255, 399]);
    return 0;
  }

  @CronJob('0 1 * * *', 'Libra LT Patrol Oversea', ['zhengbolun.patlon'], false, [Region.SG])
  async libraPatrolLTOversea() {
    const res = useInject(ProcessorLT).getExperimentPatrolData([305, 360]);
    return 0;
  }

  @CronJob('0 6 * * *', 'Libra Commerce Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  async libraPatrolCommerce() {
    const res = useInject(ProcessorCommercial).getExperimentPatrolData([147, 255]);
    return 0;
  }

  @CronJob('0 3 * * *', 'Libra Commerce Patrol Oversea', ['zhengbolun.patlon'], false, [Region.SG])
  async libraPatrolCommerceOversea() {
    const res = useInject(ProcessorCommercial).getExperimentPatrolData([305]);
    return 0;
  }

  @CronJob('0 2 * * *', 'Libra AD Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  async libraPatrolAd() {
    const res = useInject(ProcessorAd).getExperimentPatrolData([147, 255]);
    return 0;
  }

  @CronJob('0 5 * * *', 'Libra Ad Patrol Oversea', ['zhengbolun.patlon'], false, [Region.SG])
  async libraPatrolAdOversea() {
    const res = useInject(ProcessorAd).getExperimentPatrolData([305]);
    return 0;
  }

  // @CronJob('7 * * * *', 'Libra LT Ecology Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  // async libraPatrolLTEcology() {
  //   await useInject(ProcessorLT).getExperimentPatrolData([147, 305, 255], true);
  // }
  //
  // @CronJob('5 * * * *', 'Libra Search Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  // async libraPatrolSearch() {
  //   await useInject(ProcessorSearch).getExperimentPatrolData([147, 305, 255]);
  // }
  //
  // @CronJob('1 * * * *', 'Libra User Patrol', ['zhengbolun.patlon'], false, [Region.CN])
  // async libraPatrolUser() {
  //   await useInject(ProcessorGroup).getExperimentPatrolData([147, 305, 255]);
  // }
}
