import BaseCheckItemAgent, { TCCStageCheckItem } from './baseCheckItemAgent';
import {
  CheckItemStatus,
  ItemType,
  VersionStageCheckItem,
  VersionStageCheckList,
} from '@shared/releasePlatform/versionStageInfoCheckList';
import { Inject, Injectable } from '@gulux/gulux';
import { ReleasePlatformUtilService } from '../releasePlatformUtil';
import VersionStageCheckListDao from '../../dao/releasePlatform/VersionStageCheckListDao';
import { VersionProcessInfo, VersionStageInfo } from '@shared/releasePlatform/versionStage';
import {
  BaseItemInfo,
  ManuallyCheckItemInfo,
  ManuallyCheckItemStatus,
} from '@shared/releasePlatform/versionStageCheckItemInfo';
import { BmType } from '@shared/bits/bmInfo';
import { User } from '@pa/shared/dist/src/core';

@Injectable()
export class FeedbackFollowCheckItemAgent extends BaseCheckItemAgent {
  itemType(): ItemType {
    return ItemType.FeedBackFollow;
  }

  itemNamePrefix(appId: number, version: string, stage: string): string {
    return `${appId}_${version.split('.').join('_')}_${stage}`;
  }

  async produceCheckItems(
    version: string,
    stage: string,
    appId: number,
    item: TCCStageCheckItem,
    versionInfo: VersionProcessInfo,
    stageInfo: VersionStageInfo,
    existedChecklist: VersionStageCheckList | null,
  ): Promise<VersionStageCheckItem[]> {
    const tccItemInfo = item.item_info as ManuallyCheckItemInfo;
    const itemId = this.itemNamePrefix(appId, version, stage) + `_${item.name}`;
    const existedCheckItem = existedChecklist?.check_items?.find(it => it.check_item_id === itemId);
    this.logger.info(`[feedbackCheckItem] checkitem ${JSON.stringify(existedCheckItem)}`);
    const ownerEmail = item.owner_email ?? versionInfo.bmInfo[BmType.qa].email;
    const owner = await this.lark.getUserInfoByEmail(ownerEmail);
    const department = await this.lark.getUserDepartment(owner?.open_id ?? '');
    const newCheckItem = {
      check_item_id: itemId,
      status: existedCheckItem ? existedCheckItem.status : CheckItemStatus.TBD,
      description: item.description,
      owner: owner ?? ({} as User),
      item_type: this.itemType(),
      item_info: existedCheckItem
        ? ({ ...existedCheckItem.item_info, type: tccItemInfo?.type ?? 0 } as ManuallyCheckItemInfo)
        : ({ status: ManuallyCheckItemStatus.TBD, type: tccItemInfo?.type ?? 0 } as ManuallyCheckItemInfo),
      result_detail: existedCheckItem ? existedCheckItem.result_detail : '',
      department,
      extraData: {},
    } as VersionStageCheckItem;
    const itemInfo = newCheckItem?.item_info as ManuallyCheckItemInfo;
    if (itemInfo?.status === ManuallyCheckItemStatus.TBD) {
      newCheckItem.status = CheckItemStatus.TBD;
    }
    return [newCheckItem];
  }

  async updateCheckItemStatus(
    version: string,
    stage: string,
    appId: number,
    checkItem: VersionStageCheckItem,
    newItemInfo: BaseItemInfo,
  ): Promise<VersionStageCheckItem> {
    const manuallyCheckItemInfo = newItemInfo as ManuallyCheckItemInfo;
    if (manuallyCheckItemInfo.status === undefined) {
      return checkItem;
    }
    const query = { app_id: appId, version, stage };
    const newCheckItem = checkItem;
    newCheckItem.item_info = manuallyCheckItemInfo;
    newCheckItem.status =
      manuallyCheckItemInfo.status === ManuallyCheckItemStatus.Exempted
        ? CheckItemStatus.Exempt
        : CheckItemStatus.Blocked;
    await this.updateCheckItem(query, newCheckItem.check_item_id, newCheckItem);
    this.logger.info(
      `[feedbackCheckItem] old item ${JSON.stringify(checkItem)} #### new item ${JSON.stringify(newItemInfo)}`,
    );
    return newCheckItem;
  }
}
