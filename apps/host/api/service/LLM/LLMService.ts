import { Inject, Injectable } from '@gulux/gulux';
import CozeService from '../third/coze';
import { CozeWorkFlowContextObject, CozeWorkFlowDebugInfo, CozeWorkFlowResponse } from '@shared/coze/WorkFlow';
import VersionBotService from '../releasePlatform/versionBotService';
import { CozeWorkflowActionType } from '@shared/releasePlatform/cozeWorkflowContent';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from '../larkCard';
import { LarkBotMessageHookReceiveData, MessageMentionInfo } from '@pa/shared/dist/src/lark/chat';
import { MessageType } from '@pa/shared/dist/src/lark/larkCard';
import AlogService from '../releasePlatform/alogService';
import LibraAttributionService from '../LibraAttributionService';
import { teaCollect, TeaEvent } from '../../tea';
import ComponentMgrService from '../componentMgrService';
import StackTraceService from '../StackTraceService';

@Injectable()
export default class LLMService {
  @Inject()
  private coze: CozeService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private larkCard: LarkCardService;
  @Inject()
  private versionBotService: VersionBotService;
  @Inject()
  private alogService: AlogService;
  @Inject()
  private libraAttributeService: LibraAttributionService;
  @Inject()
  private componentMrgService: ComponentMgrService;
  @Inject()
  private stackTraceService: StackTraceService;

  async processUserQuery(actions: string[], receiveData: LarkBotMessageHookReceiveData) {
    const { mentions } = receiveData.message;
    if (
      receiveData.message.chat_type === 'group' &&
      (!mentions || !mentions.find((it: MessageMentionInfo) => it.name === '纸飞机') || mentions.length > 1)
    ) {
      return;
    }
    const query = actions.join(' ');
    const result: [CozeWorkFlowContextObject | undefined, CozeWorkFlowDebugInfo] =
      await this.coze.executCozeWorkFLow<CozeWorkFlowContextObject>(this.workflowId(), {
        BOT_USER_INPUT: query,
      });
    if (result[0] === undefined) {
      await this.sendExecutCozeFailureCard(receiveData, result[1]?.debug_url ?? '_blank');
    } else {
      teaCollect(TeaEvent.BOT_INTERACT_LLM, {
        action_type: result[0]?.classification?.id,
        user_id: receiveData?.sender?.sender_id?.user_id ?? '',
      });
      const event = result[0].classification.id;
      if (
        [
          CozeWorkflowActionType.VersionStatus,
          CozeWorkflowActionType.VersionMrStantard,
          CozeWorkflowActionType.VersionReleasePlan,
          CozeWorkflowActionType.VersionBuildMaster,
          CozeWorkflowActionType.VersionUpdateCode,
        ].includes(event)
      ) {
        await this.versionBotService.processCozeResult(result[0], receiveData);
      } else if (event === CozeWorkflowActionType.UserAction) {
        await this.alogService.sendGetActionInfosAndSendLarkCard(result[0], result[1], receiveData);
      } else if (event === CozeWorkflowActionType.LibraAttribution) {
        await this.libraAttributeService.sendLibraAttributionLarkCard(result[0], result[1], receiveData);
      } else if (event === CozeWorkflowActionType.ComponentInfo) {
        await this.componentMrgService.processCozeResult(result[0], result[1], receiveData);
      } else if (event === CozeWorkflowActionType.StackTrace) {
        await this.stackTraceService.sendStackTraceMessage(query, result[0], result[1], receiveData);
      } else {
        // 没有对此意图进行处理，返回兜底结果
        await this.sendUnResolvedCard(event, receiveData);
      }
    }
  }

  async sendExecutCozeFailureCard(receiveData: LarkBotMessageHookReceiveData, debugUrl: string) {
    const card = this.larkCard.buildExecutCozeFailureCard(debugUrl);
    if (receiveData.message.message_id) {
      await this.lark.replyMessage(JSON.stringify(card), receiveData.message.message_id, MessageType.interactive);
    }
  }

  async sendUnResolvedCard(actionType: CozeWorkflowActionType, receiveData: LarkBotMessageHookReceiveData) {
    const card = this.larkCard.buildLLMUnResolvedCard(actionType);
    if (receiveData.message.message_id) {
      await this.lark.replyMessage(JSON.stringify(card), receiveData.message.message_id, MessageType.interactive);
    }
  }

  workflowId() {
    return '7439981149865312306';
  }
}
