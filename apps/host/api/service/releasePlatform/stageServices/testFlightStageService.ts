import { Inject, Injectable } from '@gulux/gulux';
import VersionProcessInfoDao from '../../dao/releasePlatform/VersionProcessInfoDao';
import {
  currentStage,
  VersionMrStandard,
  VersionPhase,
  VersionProcessInfo,
  VersionStageInfo,
  VersionStageStatus,
} from '@shared/releasePlatform/versionStage';
import { VersionStageCheckListService } from '../versionStageCheckListService';
import BaseStageService from './baseStageService';
import { LVProductType, ProductType } from '@shared/process/versionProcess';
import { PlatformType } from '@pa/shared/dist/src/core';
import { SubTestFlightExtraData } from '@shared/releasePlatform/versionStageInfoCheckList';
import LarkService from '@pa/backend/dist/src/third/lark';
import VersionReleaseCardService from '../VersionReleaseCard';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { ReleasePlatformUtilService } from '../releasePlatformUtil';
import timeUtils from '../../../utils/timeUtil';
import { Temporal } from '@js-temporal/polyfill';
import commonUtils from '../../../utils/commonUtils';
import { SegmentsItem } from '@shared/bits/calendar';
import { JobStepInfo } from '@shared/bits/jobInfo';

@Injectable()
export default class TestFlightStageService extends BaseStageService {
  @Inject()
  private versionProcessDao: VersionProcessInfoDao;
  @Inject()
  private stageChecklistService: VersionStageCheckListService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private versionReleaseCard: VersionReleaseCardService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private releasePlatformUtil: ReleasePlatformUtilService;

  stageName() {
    return 'testFlight';
  }

  async createStageInfo(
    calendars: SegmentsItem[],
    timelineConfig: VersionPhase,
  ): Promise<VersionStageInfo | undefined> {
    const info = await super.createStageInfo(calendars, timelineConfig);
    info?.sub_stages?.forEach(it => {
      if (it.extra_data) {
        it.extra_data.packageTime = it.start_time;
      }
    });
    return info;
  }

  async testFlightInfoUpdate(versionInfo: VersionProcessInfo, versionCode: string, time: number, subStageName: string) {
    const tfStage = versionInfo.version_stages.find(it => it.stage_name === this.stageName());
    if (!tfStage) {
      return;
    }
    const subStage = tfStage?.sub_stages.find(it => it.stage_name === subStageName);
    if (subStage) {
      (subStage.extra_data as SubTestFlightExtraData)
        ? ((subStage.extra_data as SubTestFlightExtraData).releaseTime = time)
        : {};
      (subStage.extra_data as SubTestFlightExtraData)
        ? ((subStage.extra_data as SubTestFlightExtraData).versionCode = versionCode)
        : {};
    } else {
      const currentTime = new Date();
      const subGrayStageInfo: VersionStageInfo = {
        stage_name: `final_testflight@${tfStage.sub_stages.length}`,
        display_name: `加灰-第${tfStage.sub_stages.length}轮灰度`,
        start_time: 0,
        real_start_time: 0,
        end_time: 0,
        real_end_time: 0,
        status: time > 0 ? VersionStageStatus.OnProgress : VersionStageStatus.NotStart,
        sub_stages: [],
        status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
        message_count_map: {},
        extra_data: {
          releaseTime: time > 0 ? time : -1,
          versionCode,
          packageTime: this.getDesiredTimestamp(currentTime.getTime()) / 1000,
        } as SubTestFlightExtraData,
        is_delay: false,
        mr_standard: VersionMrStandard.SP0P1,
        parent_stage_name: this.stageName(),
      };
      tfStage.sub_stages.push(subGrayStageInfo);
    }
    await this.versionProcessDao.updateByCriteria(
      { version: versionInfo.version, app_id: versionInfo.app_id },
      versionInfo,
    );
    await this.releasePlatformUtil.createReleaseTag(versionInfo, versionCode);
  }

  async getGrayCount(version: string, platform: string, product: ProductType) {
    if (platform !== PlatformType.iOS) {
      return;
    }
    const appID = product === LVProductType.lv ? 177501 : product === LVProductType.cc ? 300601 : undefined;
    if (appID === undefined) {
      return;
    }
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appID, version });
    if (!versionInfo) {
      return;
    }
    const grayStage = versionInfo.version_stages.find(it => it.stage_name === this.stageName());
    if (!grayStage) {
      return;
    }
    const count = grayStage.sub_stages.filter(
      it => it.extra_data !== undefined && (it.extra_data as SubTestFlightExtraData).versionCode !== '',
    ).length;
    return count;
  }

  async testFlightReleased(
    version: string,
    platform: string,
    product: ProductType,
    versionCode: string,
    jobInfo?: JobStepInfo,
  ) {
    if (platform !== PlatformType.iOS) {
      return;
    }
    const appID = product === LVProductType.lv ? 177501 : product === LVProductType.cc ? 300601 : undefined;
    if (appID === undefined) {
      return;
    }
    const versionInfo = await this.versionProcessDao.findOneByCriteria({ app_id: appID, version });
    if (!versionInfo) {
      return;
    }
    const grayStage = versionInfo.version_stages.find(it => it.stage_name === this.stageName());
    if (!grayStage) {
      return;
    }
    const currentTime = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    for (let i = 0; i < grayStage.sub_stages.length; i++) {
      if (
        grayStage.sub_stages[i].status === VersionStageStatus.OnProgress ||
        grayStage.sub_stages[i].status === VersionStageStatus.NotStart
      ) {
        if (grayStage.sub_stages[i].stage_name === 'testflight_pre_check') {
          grayStage.sub_stages[i].status = VersionStageStatus.Complete;
          grayStage.sub_stages[i + 1].status = VersionStageStatus.OnProgress;
          grayStage.sub_stages[i + 1].extra_data = {
            versionCode,
            releaseTime: currentTime,
          } as SubTestFlightExtraData;
          await this.versionProcessDao.updateVersionProcessInfo(versionInfo);
          break;
        } else {
          const extraData = grayStage.sub_stages[i].extra_data
            ? (grayStage.sub_stages[i].extra_data as SubTestFlightExtraData)
            : undefined;
          if ((extraData?.releaseTime ?? 0) > 0) {
            grayStage.sub_stages[i].status = VersionStageStatus.Complete;
            const nextNotStartStage = grayStage.sub_stages.find(it => it.status === VersionStageStatus.NotStart);
            if (nextNotStartStage) {
              nextNotStartStage.status = VersionStageStatus.OnProgress;
              nextNotStartStage.extra_data = {
                versionCode,
                releaseTime: currentTime,
                packageTime: jobInfo?.started ?? currentTime,
              } as SubTestFlightExtraData;
            } else {
              const subGrayStageInfo: VersionStageInfo = {
                stage_name: `final_testflight@${i + 1}`,
                display_name: `加灰-第${i + 1}轮灰度`,
                start_time: 0,
                real_start_time: 0,
                end_time: 0,
                real_end_time: 0,
                status: VersionStageStatus.OnProgress,
                sub_stages: [],
                status_route: ['stability_overview', 'gray_bugs', 'approval_report'],
                message_count_map: {},
                extra_data: {
                  releaseTime: currentTime,
                  versionCode,
                  // packageTime: this.getDesiredTimestamp(currentTime) / 1000,
                  packageTime: jobInfo?.started ?? currentTime,
                } as SubTestFlightExtraData,
                is_delay: false,
                mr_standard: VersionMrStandard.SP0P1,
                parent_stage_name: this.stageName(),
              };
              grayStage.sub_stages.push(subGrayStageInfo);
            }
          } else {
            grayStage.sub_stages[i].extra_data = {
              versionCode,
              releaseTime: currentTime,
              packageTime: jobInfo?.started ?? currentTime,
            } as SubTestFlightExtraData;
          }
          // await this.versionProcessDao.updateByCriteria({ version, app_id: appID }, versionInfo);
          await this.versionProcessDao.updateVersionProcessInfo(versionInfo);
          break;
        }
      }
    }
    await this.releasePlatformUtil.createReleaseTag(versionInfo, versionCode);
  }

  getDesiredTimestamp(timestamp: number) {
    const now = new Date();
    const today19PM = new Date(now);
    today19PM.setHours(11, 0, 0, 0);

    if (timestamp > today19PM.getTime()) {
      return today19PM.getTime();
    } else {
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterday19PM = new Date(yesterday);
      yesterday19PM.setHours(11, 0, 0, 0);
      return yesterday19PM.getTime();
    }
  }

  async stageSummary(
    versionInfo: VersionProcessInfo,
    stageInfo: VersionStageInfo,
    subStageInfo?: VersionStageInfo,
  ): Promise<string> {
    const extraData = subStageInfo?.extra_data as SubTestFlightExtraData;
    if (!subStageInfo || subStageInfo.stage_name === 'testflight_pre_check') {
      if (subStageInfo?.status === VersionStageStatus.Complete) {
        return '准入检查已完成';
      } else {
        return '准入检查中，如有严重问题需合入请联系BM';
      }
    }
    if ((extraData?.releaseTime ?? -1) < 0) {
      if (subStageInfo?.status === VersionStageStatus.Complete) {
        return '本轮灰度已跳过';
      } else {
        return '灰度打包中，如有严重问题需合入请联系BM';
      }
    }
    return `小版本号：${extraData.versionCode}, 放量时间：${timeUtils.now2Timeformat(extraData.releaseTime * 1000)}`;
  }
}
