import { ConditionBundleItem, FilterRule, FilterVersionInfo, Flight } from '@shared/libra/flight';
import { LibraAppIds, LibraPlatform, LibraRegion, LibraStatus } from '@shared/libra/commonLibra';
import { LibraEvent, LibraEventType, LibraVersionPlatform } from '@shared/libra/libraInfo';
import { LVProductType, ProductType } from '@pa/shared/dist/src/appSettings/appSettings';
import { DBLibraInfo, LibraInfoTable } from '../model/LibraInfoTable';
import { current_region, Region } from './region';
import { versionCodeToMeegoVersion, versionName2Code } from '@shared/utils/version_utils';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { FlightEventType, LibraControlRecord } from '@shared/libra/libraControl';

const isHitFilterVersionRule = (
  version: string,
  platform: LibraPlatform,
  versionConfigList: FilterVersionInfo[],
): boolean => {
  const minVersionValue =
    platform === LibraPlatform.android
      ? versionName2Code(version, current_region() !== Region.SG)
      : version
          .split('.')
          .slice(0, 2)
          .map((it, idx) => Number(it) * Math.pow(10, 3 * (2 - idx)))
          .reduce((a, b) => a + b);
  const maxVersionValue = minVersionValue + (LibraPlatform.android ? 9999 : 999);
  const min_value = versionConfigList.reduce((cur, it) => (['==', '<=', '<'].includes(it.op) ? it.value : cur), -1);
  // const max_value = versionConfigList.reduce((cur, it) => (["==", ">=", ">"].includes(it.op) ? it.value : cur), -1);
  // # 获取实验配置的最大值 max_value 和最小值 min_value
  // # 获取当前版本的最大值 maxVersionValue 和最小值 minVersionValue
  // # minVersionValue <= min_value <= maxVersionValue 则属于当前版本的实验
  return minVersionValue <= min_value && min_value <= maxVersionValue;
};

export function filterLibraByVersion(
  product: ProductType,
  platform: LibraPlatform,
  version: string,
  flights: Flight[],
): Flight[] {
  return flights.filter(({ filter_rule }) =>
    filter_rule.find(({ conditions }) => {
      const conditionItems = conditions.map(it => it.condition);
      const isAndroidHit = conditionItems.find(
        ({ key, value }) => key === 'device_platform' && value.toString().includes(LibraPlatform.android),
      );
      const isIphoneHit = conditionItems.find(
        ({ key, value }) => key === 'device_platform' && value.toString().includes(LibraPlatform.iPhone),
      );
      const versionConfigList: FilterVersionInfo[] = conditionItems
        .filter(({ key }) => key === '_version_code')
        .map(({ op, value }) => ({ op, value }) as FilterVersionInfo);
      return (
        ((isAndroidHit && platform === LibraPlatform.android) || (isIphoneHit && platform === LibraPlatform.iPhone)) &&
        isHitFilterVersionRule(version, platform, versionConfigList)
      );
    }),
  );
}

/**
 * libra openAPI 平台数据转换成纸飞机存储的libraInfo数据
 * @param product
 * @param version
 * @param platform
 * @param flight
 */
export function formatFlight2Libra(
  product: ProductType,
  platform: LibraPlatform,
  version: string,
  flight: Flight,
): LibraInfoTable {
  return {
    id: flight.id.toString(),
    product,
    version,
    platform,
    name: flight.name,
    app_id: flight.app_id.toString(),
    create_time: flight.create_time,
    start_time: flight.start_time,
    end_time: flight.end_time,
    product_id: flight.product_id.toString(),
    owner: flight.owner.toString(),
    numerical_traffic: flight.version_resource,
    meego_array: flight.meego_info.meego_array,
    description: flight.description,
    status: flight.status.toString(),
    flight_url: '',
    filter_rule: flight.filter_rule,
    stageTraffic: {},
    pushTimesByDay: {},
    pushTimesByStage: {},
    readTimesByDay: {},
  } as LibraInfoTable;
}

export function getNewUserTime(filterRule: FilterRule[]) {
  const newUserTime = '';
  for (const rule of filterRule) {
    for (const condition of rule.conditions) {
      if (condition.condition.key === 'first_install_time') {
        return condition.condition.value as unknown as string;
      }
    }
  }
  return newUserTime;
}

// 【重要】这个判断目前仅适用于剪映 App + CapCut App，对其他 Libra 空间的应用不适用
// 【重要】这个判断目前仅适用于剪映 App + CapCut App，对其他 Libra 空间的应用不适用
export function getLibraVersionPlatformFromFilterRule(
  filterRule: FilterRule[],
  libraRegion: LibraRegion,
  libraName: string,
  libraDescription: string,
) {
  const libraVersionPlatform: LibraVersionPlatform = {
    android: {
      isHit: false,
      minVersionCode: 0,
      maxVersionCode: 0,
      version: '',
      isBeta: false,
    },
    iphone: {
      isHit: false,
      minVersionCode: 0,
      maxVersionCode: 0,
      version: '',
      isBeta: false,
    },
  };
  for (const { conditions } of filterRule) {
    const conditionItems = conditions.map(it => it.condition);

    // ios_vc、android_vc
    // 商业化/广告（服务端过滤条件）：https://bytedance.larkoffice.com/sheets/MMiisEaEUhBCipt0CzRcS7fGnud?sheet=4daf13
    // 参考实验：https://data.bytedance.net/libra/flight/3531629/edit
    // 包括剪映、醒图、CapCut，版本号里面有：ios_vc、android_vc 关键字

    // 查看实验中的流量过滤规则，获取平台信息
    const min_version = conditionItems
      .filter(({ key }) =>
        [
          '_version_code',
          '_version_code_normal',
          '_update_version_code',
          'ios_vc',
          'android_vc',
          'version_code', // version_code，比如 "15.9.0"、"13009000" 等，参考实验：https://libra-sg.tiktok-row.net/libra/flight/71148218/edit
        ].includes(key),
      )
      .reduce((cur, { value, op }) => {
        let res = Number.MAX_SAFE_INTEGER;
        let innerValue = value;
        if (typeof value === 'string') {
          if (value.split('.').length === 4 || value.split('.').length === 3) {
            // 如果是 ios_vc，它是"********"格式，需要转换成数字
            // 如果是 version_code，它是"15.9.0"格式，需要转换成数字
            innerValue = parseInt(
              value
                .split('.')
                .map(it => it.padStart(3, '0'))
                .join(''),
              10,
            );
          } else {
            // version_code，比如 "13009000" 等，转成整型
            innerValue = Number(value);
          }
        }
        if (['==', '>='].includes(op)) {
          res = parseInt(innerValue.toString());
        } else if (['>'].includes(op)) {
          res = parseInt(innerValue.toString()) + 1;
        }
        return Math.min(cur, res);
      }, Number.MAX_SAFE_INTEGER);
    const max_version = conditionItems
      .filter(({ key }) =>
        [
          '_version_code',
          '_version_code_normal',
          '_update_version_code',
          'ios_vc',
          'android_vc',
          'version_code', // version_code，比如 "15.9.0"、"13009000" 等，参考实验：https://libra-sg.tiktok-row.net/libra/flight/71148218/edit
        ].includes(key),
      )
      .reduce((cur, { value, op }) => {
        let res = -1;
        let innerValue = value;
        if (typeof value === 'string') {
          if (value.split('.').length === 4 || value.split('.').length === 3) {
            // 如果是 ios_vc，它是"********"格式，需要转换成数字
            // 如果是 version_code，它是"15.9.0"格式，需要转换成数字
            innerValue = parseInt(
              value
                .split('.')
                .map(it => it.padStart(3, '0'))
                .join(''),
              10,
            );
          } else {
            // version_code，比如 "13009000" 等，转成整型
            innerValue = Number(value);
          }
        }
        if (['==', '<='].includes(op)) {
          res = parseInt(innerValue.toString());
        } else if (['<'].includes(op)) {
          res = parseInt(innerValue.toString()) - 1;
        }
        return Math.max(cur, res);
      }, -1);
    let currentPlatform: '' | LibraPlatform = conditionItems
      .filter(({ key }) => key === 'device_platform')
      .reduce(
        (cur: '' | LibraPlatform, { value }) =>
          [LibraPlatform.iPhone, LibraPlatform.android, LibraPlatform.iOS, LibraPlatform.iPad].find(it =>
            value.toString().includes(it),
          ) ?? cur,
        '',
      );
    if (currentPlatform === '') {
      const items = conditionItems.filter(({ key }) => key === 'device_platform');
      for (const item of items) {
        if (Array.isArray(item.value) && item.op === 'in_bundle') {
          if ((item.value[0] as ConditionBundleItem).name === 'ios_device_platform') {
            currentPlatform = LibraPlatform.iOS;
          } else if (
            (item.value[0] as ConditionBundleItem).id === 102400 ||
            (item.value[0] as ConditionBundleItem).id === 100358 ||
            (item.value[0] as ConditionBundleItem).id === 149
          ) {
            currentPlatform = LibraPlatform.iOS;
          }
        }
      }
    }

    // 获取 channel 参数
    const eqChannels: string[] = [];
    const neqChannels: string[] = [];
    conditionItems
      .filter(({ key }) => key === 'channel')
      .forEach(item => {
        if (item.op === '==') {
          eqChannels.push(...(item.value as string[]));
        } else if (item.op === '!=') {
          neqChannels.push(...(item.value as string[]));
        } else if (item.op === 'in_bundle') {
          eqChannels.push(...(item.value as ConditionBundleItem[]).map(it => it.name));
        }
      });

    // 获取 app_id 参数
    const appIds: number[] = [];
    conditionItems
      .filter(({ key }) => key === 'app_id')
      .forEach(item => {
        if (item.op === '==') {
          appIds.push(...(item.value as number[]));
        }
      });

    if (currentPlatform !== '' && min_version !== Number.MAX_SAFE_INTEGER) {
      if (currentPlatform === LibraPlatform.android) {
        libraVersionPlatform.android.isHit = true;
        // "value":11600000,
        libraVersionPlatform.android.minVersionCode = min_version;
        libraVersionPlatform.android.maxVersionCode = max_version;
        libraVersionPlatform.android.version = versionCodeToMeegoVersion(min_version.toString());
        // 131001600
        const releaseVersionCode =
          versionName2Code(libraVersionPlatform.android.version, libraRegion === LibraRegion.CN) + 1600;
        // 如果max_version和releaseVersionCode相差过大，例如999999和131001600，则放弃判断灰度实验，服务端的code会比较小
        if (max_version !== -1 && max_version > 10000000 && max_version < releaseVersionCode) {
          libraVersionPlatform.android.isBeta = true;
        }
        // 除了版本号判断，有部分 Android 实验也是携带 channel 的，比如：https://data.bytedance.net/libra/flight/3491908/edit
        else if (eqChannels.length > 0 && eqChannels.some(channel => channel.toLowerCase() === 'grey_channel')) {
          libraVersionPlatform.android.isBeta = true;
        }
        // 除了 channel 判断，Android 的独立灰度也需要判断下（最后两位在 10~19 之间），比如：
        else if (
          min_version !== Number.MAX_SAFE_INTEGER &&
          max_version !== -1 &&
          min_version % 100 >= 10 &&
          min_version % 100 <= 19
        ) {
          libraVersionPlatform.android.isBeta = true;
        }
        // 最后，根据实验标题、实验描述判断是否是灰度实验
        else if (libraName.includes('灰度') || libraDescription.includes('灰度')) {
          libraVersionPlatform.android.isBeta = true;
        }
      } else if (
        currentPlatform === LibraPlatform.iPhone ||
        currentPlatform === LibraPlatform.iOS ||
        currentPlatform === LibraPlatform.iPad
      ) {
        libraVersionPlatform.iphone.isHit = true;
        // "value":11006000,
        libraVersionPlatform.iphone.minVersionCode = min_version;
        libraVersionPlatform.iphone.maxVersionCode = max_version;
        libraVersionPlatform.iphone.version = `${Math.floor(min_version / 1000000)}.${Math.floor(
          (min_version / 1000) % 10,
        )}.0`;
        // iOS的灰度版本是小于95的版本，例如11.1.30换算后是11001003
        if (max_version !== -1 && max_version % 100 < 95) {
          libraVersionPlatform.iphone.isBeta = true;
        }

        // 如果渠道不包含appstore说明是灰度实验
        const APPSTORE = 'App Store';
        // 将 APPSTORE 转换为小写进行比较
        const normalizedAppStore = APPSTORE.toLowerCase();

        if (eqChannels.length > 0 && !eqChannels.some(channel => channel.toLowerCase() === normalizedAppStore)) {
          libraVersionPlatform.iphone.isBeta = true;
        }

        if (neqChannels.length > 0 && neqChannels.some(channel => channel.toLowerCase() === normalizedAppStore)) {
          libraVersionPlatform.iphone.isBeta = true;
        }

        // 最后，根据实验标题、实验描述判断是否是灰度实验
        if (libraName.includes('灰度') || libraDescription.includes('灰度')) {
          libraVersionPlatform.iphone.isBeta = true;
        }
      }
    }

    if (currentPlatform === '') {
      // 如果还是没有命中平台，大概率是服务端实验，例如：https://libra-sg.tiktok-row.net/libra/flight/71074728/edit
      // 如果标题或者描述有“灰度”关键字，判定其为灰度实验
      if (
        (appIds.includes(1775) || appIds.includes(3006)) &&
        (libraName.includes('灰度') || libraDescription.includes('灰度'))
      ) {
        libraVersionPlatform.android.isHit = true;
        libraVersionPlatform.iphone.isHit = true;
        libraVersionPlatform.android.isBeta = true;
        libraVersionPlatform.iphone.isBeta = true;
      }
    }
  }
  return libraVersionPlatform;
}

export function isBetaByFilterRule(
  filterRule: FilterRule[],
  libraRegion: LibraRegion,
  libraName: string,
  libraDescription: string,
) {
  const hitVersionPlatform = getLibraVersionPlatformFromFilterRule(
    filterRule,
    libraRegion,
    libraName,
    libraDescription,
  );
  return hitVersionPlatform.android.isBeta || hitVersionPlatform.iphone.isBeta;
}

/**
 * 将EventBus平台的libra_event数据转换成LibraInfo
 */
export function formatLibraEvent2LibraInfo(
  platform: LibraPlatform,
  version: string,
  libraEvent: LibraEvent,
): DBLibraInfo {
  const product = libraEvent.app_info.app_ids === LibraAppIds.lv ? LVProductType.lv : LVProductType.cc;
  return {
    id: libraEvent.flight_info.flight_id,
    product,
    version,
    platform,
    name: libraEvent.flight_info.flight_display_name,
    app_id: libraEvent.app_info.app_ids,
    create_time: libraEvent.flight_info.create_time,
    start_time: libraEvent.flight_info.start_time,
    end_time: libraEvent.flight_info.end_time,
    product_id: libraEvent.product_info.product_id,
    owner: libraEvent.flight_info.owner,
    numerical_traffic: libraEvent.flight_info.numerical_traffic,
    meego_array: libraEvent.flight_info.meego_info.meego_array,
    description: libraEvent.flight_info.description,
    status: libraEvent.event.event_type.includes('stop') ? LibraStatus.stopped : libraEvent.flight_info.status,
    flight_url: libraEvent.flight_info.flight_url,
    filter_rule: libraEvent.flight_info.filter_rule,
    stageTraffic: {},
    pushTimesByDay: {},
    pushTimesByStage: {},
    readTimesByDay: {},
  } as DBLibraInfo;
}

/**
 * 获取当前时间戳，单位毫秒
 */
export function nowMilliseconds(): number {
  return dayjs().valueOf();
}
/**
 * 获取当前时间:年-月-日
 */
export function todayFormat(): string {
  dayjs.extend(utc);
  return dayjs().format('YYYY-MM-DD');
}

export function getAppIdAndName(
  platform: LibraPlatform,
  product: ProductType,
): { bits_app_id: number; app_name: string } {
  let bits_app_id = 0;
  let app_name = '';
  if (platform === LibraPlatform.android && product === LVProductType.lv) {
    bits_app_id = 177502;
    app_name = 'LV-Android';
  } else if (platform === LibraPlatform.android && product === LVProductType.cc) {
    bits_app_id = 2000001157;
    app_name = 'CapCut-Android';
  } else if (platform === LibraPlatform.iPhone && product === LVProductType.lv) {
    bits_app_id = 177501;
    app_name = 'LV-iOS';
  } else if (platform === LibraPlatform.iPhone && product === LVProductType.cc) {
    bits_app_id = 2020092636;
    app_name = 'CapCut-iOS';
  }
  return { bits_app_id, app_name };
}

export function getAppId(platform: LibraPlatform, product: ProductType): { app_id: number } {
  let app_id = 0;
  if (platform === LibraPlatform.android && product === LVProductType.lv) {
    app_id = 177502;
  } else if (platform === LibraPlatform.android && product === LVProductType.cc) {
    app_id = 300602;
  } else if (platform === LibraPlatform.iPhone && product === LVProductType.lv) {
    app_id = 177501;
  } else if (platform === LibraPlatform.iPhone && product === LVProductType.cc) {
    app_id = 300601;
  }
  return { app_id };
}

export function inPeakTime(ts: number) {
  dayjs.extend(utc);
  const date = dayjs.unix(ts).utcOffset(8);
  const day = date.day();
  const hours = date.hour();
  return (
    [0, 6].includes(day) || // 周六和周日
    hours >= 18 || // 18:00-24:00
    hours < 10 || // 0:00-10:00
    (hours >= 12 && hours < 14) // 12:00-14:00
  );
}

export function isSmallFlow(row: LibraControlRecord) {
  return !(
    row.event_type === FlightEventType.StartFlight &&
    row.version_resource &&
    Math.abs(1 - row.version_resource) < 0.01
  );
}

export function isBigFlow(item: LibraEvent) {
  return (
    item.event.event_type === LibraEventType.FlightStart && item.traffic_change_strategy?.[0]?.end_traffic === '100.0%'
  );
}
