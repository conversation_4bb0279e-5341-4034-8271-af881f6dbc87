import {
  isGrayStage,
  LVProductType,
  ProductType,
  ProgressState,
  RetouchProductType,
  VersionProcess,
  VersionStage,
} from '@shared/process/versionProcess';
import { PlatformType } from '@pa/shared/dist/src/core';
import { Inject, Injectable } from '@gulux/gulux';
import { ModelType } from '@gulux/gulux/typegoose';
import { VersionProcessTable } from 'api/model/VersionProcessTable';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';

/**
 * 版本流程数据处理服务
 */
@Injectable()
export default class VersionProcessDao {
  @Inject(VersionProcessTable)
  private versionProcessModel: ModelType<VersionProcessTable>;

  @Inject()
  private rpcProxyManager: RpcProxyManager;

  async queryLvVersionList() {
    return this.versionProcessModel
      .find(
        {
          platform: PlatformType.iOS,
          product: LVProductType.lv,
        },
        { version: 1, versionNum: 1, pcInfo: 1 },
      )
      .limit(20)
      .sort({ versionNum: -1 });
  }

  async queryVersionList(product: string, platform: PlatformType, size = 20) {
    return this.versionProcessModel
      .find(
        {
          platform,
          product,
        },
        { version: 1, versionNum: 1, platform: 1, _id: 0 },
      )
      .limit(size)
      .sort({ versionNum: -1 });
  }

  aidConvertToAppId(aid: number, platform: SlardarPlatformType) {
    let appId = 177502;
    if (aid === 1775 && platform === SlardarPlatformType.iOS) {
      appId = 177501;
    } else if (aid === 1775 && platform === SlardarPlatformType.Android) {
      appId = 177502;
    } else if (aid === 3006 && platform === SlardarPlatformType.Android) {
      appId = 300602;
    } else if (aid === 3006 && platform === SlardarPlatformType.iOS) {
      appId = 300601;
    }
    return appId;
  }

  async queryVersionIntegrationTime(aid: number, version: string, platform: SlardarPlatformType) {
    const appId = this.aidConvertToAppId(aid, platform);
    return await this.rpcProxyManager.getHost().getVersionIntegrationTime(version, appId);
  }

  async queryVersionIntegrationEndTime(aid: number, version: string, platform: SlardarPlatformType) {
    const appId = this.aidConvertToAppId(aid, platform);
    return await this.rpcProxyManager.getHost().getVersionIntegrationEndTime(version, appId);
  }

  async getPullOfflineVersion(aid: number, platform: SlardarPlatformType) {
    const appId = this.aidConvertToAppId(aid, platform);
    return await this.rpcProxyManager.getHost().getPullOfflineVersion(appId);
  }

  async getPullOfflineVersionsByIntegrationTime(aid: number, platform: SlardarPlatformType) {
    const appId = this.aidConvertToAppId(aid, platform);
    return await this.rpcProxyManager.getHost().getPullVersionsByIntegrationTime(appId);
  }
}
