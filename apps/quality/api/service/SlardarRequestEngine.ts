import { Inject, Injectable } from '@gulux/gulux';
import SlardarInfoModel, { DBSlardarInfoTable } from '../model/SlardarInfoTable';
import AlarmVersionModel, { DBAlarmVersion } from '../model/AlarmVersionInfoTable';
import SlardarService from './slardar/slardar';
import SlardarValueModel, { DBSlardarValue } from '../model/SlardarValueTable';
import dayjs from 'dayjs';
import SlardarCrashIssueModel, { DBSlardarCrashIssue } from '../model/SlardarCrashIssueTable';
import { extractor } from '@shared/utils/tools';
import {
  CrashType2Url,
  getSlardarIssueListParams,
  getSlardarUrl,
  GranularityType,
  SlardarCrashCommon,
} from '@shared/typings/slardar/common';
import { CrashListItem, CrashListResult, CrashType } from '@shared/typings/slardar/crash/issueListSearch';
import SlardarCrashIssueListModel, { DBSlardarCrashIssueList } from '../model/SlardarCrashIssueListTable';
import { crashListItemToIssue, crashListItemToIssueList } from '../utils/crashConverter';
import { DeviceLevel, highAndroidFilter, highIosFilter, lowAndroidFilter, lowIosFilter } from '@shared/common';
import SlardarVersionQualityValueModel from '../model/SlardarVersionQualityValueTable';
import SlardarVersionQualityIssueModel from '../model/SlardarVersionQualityIssueTable';
import { compact, groupBy, mergeWith, range, sumBy } from 'lodash';
import SlardarUserCountModel, { DBSlardarUserCount } from '../model/SlardarUserCountTable';
import SlardarVersionMetricsModel, { MetricType } from '../model/SlardarVersionMetricsTable';
import { FlexFilter, FlexGroupBy, FlexSeriesResponseData } from '@shared/typings/slardar/flex/querySeries';
import utc from 'dayjs/plugin/utc';
import { VersionType } from '@shared/utils/version_utils';
import SlardarAutoWarnService from './slardar/autoWarn';
import { useInject } from '@edenx/runtime/bff';
import { current_region, Region } from '../utils/region';
import SlardarVersionQualityRateModel from '../model/SlardarVersionQualityRateTable';
import { GetCrashTrendResponseData } from '@shared/typings/slardar/crash/trend';
import SlardarDAU3IssueListModel, { SlardarDAU3IssueListInfo } from '../model/SlardarDAU3IssueListTable';
import SlardarVersionNewIssueListModel, {
  SlardarVersionNewIssueListInfo,
} from '../model/SlardarVersionNewIssueListTable';
import SlardarVersionNewIssueRateModel from '../model/SlardarVersionNewIssueRateTable';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { Dreamina_SLARDAR_APP_ID, HYPIC_SLARDAR_APP_ID, SLARDAR_APP_ID } from './slardar/shared';
import SlardarConfig from './slardar/SlardarConfig';
import LarkService from '@pa/backend/dist/src/third/lark';
import LarkCardService from './larkCard';
import {
  CardContentElement,
  CardElement,
  CardElementTag,
  CardTemplate,
  CardTextTag,
} from '@pa/shared/dist/src/lark/larkCard';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import { isSG } from '@pa/backend/dist/src/utils/region';
import { GrayScaleCreateBug } from './slardar/GrayscaleCreateBug';
import { getPullOfflineVersions, getVersionIntegrationTime } from '@api/version';
import { SelectedName } from '@shared/utils/offlineslardar';

const iPhoneLowEndList: Readonly<string[]> = ['7plus', '7', '6', '6plus', '6s', '6splus', '5c', '5s', '5'];
export const CrashId2ValueType: Readonly<Record<string, MetricType>> = {
  JavaStartCrash: MetricType.JAVASTART_USER_COUNT,
  JavaCrash: MetricType.JAVA_USER_COUNT,
  NativeCrash: MetricType.NATIVE_USER_COUNT,
  ANR: MetricType.ANR_USER_COUNT,
};

interface CardColumnElement extends CardElement {
  elements: CardElement[];
}

interface CardColumnSetElement extends CardElement {
  columns: CardColumnElement[];
}

@Injectable()
export default class SlardarRequestEngine {
  @Inject()
  private slardarInfoModel: SlardarInfoModel;

  @Inject()
  private alarmVersionModel: AlarmVersionModel;

  @Inject()
  private slardarService: SlardarService;

  @Inject()
  private slardarValueModel: SlardarValueModel;

  @Inject()
  private slardarCrashIssueModel: SlardarCrashIssueModel;

  @Inject()
  private slardarCrashIssueListModel: SlardarCrashIssueListModel;

  @Inject()
  private slardarVersionQualityValueModel: SlardarVersionQualityValueModel;

  @Inject()
  private slardarVersionQualityIssueModel: SlardarVersionQualityIssueModel;

  @Inject()
  private slardarUserCountModel: SlardarUserCountModel;

  @Inject()
  private slardarVersionMetricsModel: SlardarVersionMetricsModel;

  @Inject()
  private logger: BytedLogger;

  @Inject()
  private config: SlardarConfig;

  async fetchRawCrashList(
    aid: number,
    crash_name: string,
    version_code: string,
    platform: SlardarPlatformType,
    granularity: GranularityType,
    start_time: number,
    end_time: number,
    additional_params: Partial<SlardarCrashCommon> = {},
  ) {
    const ret: CrashListItem[] = [];
    let pageNum = 0;
    while (pageNum < 4) {
      pageNum++;
      const page = await this.slardarService.crashIssueList(
        aid,
        crash_name,
        platform,
        500,
        start_time,
        end_time,
        granularity,
        version_code,
        undefined,
        pageNum,
        additional_params,
        false,
      );
      const total = page.total ?? 0;
      if (page.result) {
        ret.push(...page.result);
      }
      if (!page.result || ret.length === total) {
        break;
      }
    }
    return ret;
  }

  /**
   * 获取小版本号新增问题中影响用户数大于等于N的issue
   * @param crash_name
   * @param version_code
   * @param platform
   * @param granularity
   * @param start_time
   * @param end_time
   * @param additional_params
   * @param N 过滤用户数大于等于N
   */
  async fetchNewCrashListGteN(
    aid: number,
    crash_name: string,
    version_code: string,
    platform: SlardarPlatformType,
    granularity: GranularityType,
    start_time: number,
    end_time: number,
    additional_params: Partial<SlardarCrashCommon> = {},
    N: number,
  ): Promise<CrashListItem[]> {
    const ret: CrashListItem[] = [];
    let pageNum = 0;
    while (pageNum < 10) {
      pageNum++;
      const page = await this.slardarService.crashIssueList(
        aid,
        crash_name,
        platform,
        500,
        start_time,
        end_time,
        granularity,
        version_code,
        undefined,
        pageNum,
        additional_params,
        true,
      );
      const total = page.total ?? 0;
      const gteN = page.result?.filter(v => v.user && v.user >= N);
      ret.push(...(gteN ? gteN : []));
      if (!page.result || !gteN || gteN.length < page.result?.length || ret.length === total) {
        break;
      }
    }
    return ret;
  }

  /**
   * 获取某大版本对应时间段TopN的issue信息
   * @param crash_name
   * @param platform
   * @param granularity
   * @param release_ts
   * @param is_official
   * @param additional_params
   * @param N 小于500
   * @param version
   */
  async fetchTopNVersionCrashList(
    aid: number,
    crash_name: string,
    platform: SlardarPlatformType,
    granularity: GranularityType,
    end_time: number,
    additional_params: Partial<SlardarCrashCommon> = {},
    N: number,
  ): Promise<CrashListItem[]> {
    const page_1 = await this.slardarService.crashIssueList(
      aid,
      crash_name,
      platform,
      N,
      end_time - 7 * 86400,
      end_time,
      granularity,
      'DAU TOP3 version',
      undefined,
      1,
      additional_params,
    );
    return page_1.result ? page_1.result : [];
  }

  async fetchCrashListFor(
    aid: number,
    start_time: number,
    end_time: number,
    crash_name: string,
    platform: SlardarPlatformType,
    deviceLevel: DeviceLevel,
    granularity: GranularityType,
    additional_params: Partial<SlardarCrashCommon> = {},
    version_code?: string,
    version?: string,
    isOffline = false,
  ): Promise<CrashListItem[]> {
    const totalSize = 500;
    const pageSize = isSG() ? 100 : 500;
    // 加参数去掉线上版本
    let not_version_code: string[] | undefined;
    if (version && !isOffline) {
      const ONLINEversion = await this.alarmVersionModel.findONLINEVersionCodes(aid, version, platform);
      not_version_code = ONLINEversion.map(v => v.version_code);
    }
    const page_1 = await this.slardarService.crashIssueList(
      aid,
      crash_name,
      platform,
      pageSize,
      start_time,
      end_time,
      granularity,
      version_code,
      version,
      1,
      additional_params,
      false,
      deviceLevel,
      undefined,
      not_version_code,
      isOffline,
    );
    const total = page_1.total ?? 0;
    const ret: CrashListItem[] = page_1.result ?? [];

    const pull_promises: Promise<CrashListResult>[] = [];
    for (let pageNum = 2; (pageNum - 1) * pageSize < total && (pageNum - 1) * pageSize < totalSize; ++pageNum) {
      pull_promises.push(
        this.slardarService.crashIssueList(
          aid,
          crash_name,
          platform,
          pageSize,
          start_time,
          end_time,
          granularity,
          version_code,
          version,
          pageNum,
          additional_params,
          false,
          deviceLevel,
          undefined,
          not_version_code,
          isOffline,
        ),
      );
    }
    this.logger.info(
      `Pulled crash issues for deviceLevel=${deviceLevel} [${version ? version : version_code}/${crash_name}]}`,
    );
    return Promise.all(pull_promises)
      .then(result => result.flatMap(it => it.result ?? []))
      .then(all => ret.concat(all));
  }

  async slardarAutoNotice() {
    this.logger.info('触发自动播报');
    const autoWarnAid = await this.config.autoWarnAid();
    for (const aid of autoWarnAid) {
      const versions = await this.alarmVersionModel.findVersionsIn(3 * 24 * 60, [aid]);

      if (aid === HYPIC_SLARDAR_APP_ID()) {
        // 醒图最新一轮灰度需要持续播报7天
        // 如果醒图最新灰度版本不在3天查询列表内，且灰度时间未超过7天，则加入列表
        const lastGrayVersion = [
          await this.alarmVersionModel.findLastestVersion(aid, SlardarPlatformType.Android),
          await this.alarmVersionModel.findLastestVersion(aid, SlardarPlatformType.iOS),
        ];
        for (const v of lastGrayVersion) {
          if (!v) {
            continue;
          }
          if (!versions.find(a => a.version_code === v.version_code)) {
            if (Math.floor(Date.now() / 1000) - v.timestamp < 7 * 24 * 60 * 60) {
              versions.push(v);
            }
          }
        }
      }

      if (aid === Dreamina_SLARDAR_APP_ID) {
        // TODO 补充定时播报逻辑
        continue;
      }
      const AndroidVersions = versions.filter(v => v.platform === SlardarPlatformType.Android);
      const iOSVersions = versions.filter(
        v => v.platform === SlardarPlatformType.iOS && v.aid === HYPIC_SLARDAR_APP_ID(),
      );
      // 按版本分类
      const lastVersions: DBAlarmVersion[] = [];

      let groupedList = groupBy(AndroidVersions, v => v.version);
      for (const version in groupedList) {
        const lastVersion = groupedList[version].reduce((a, b) => (a.version_step > b.version_step ? a : b));
        lastVersions.push(lastVersion);
      }
      groupedList = groupBy(iOSVersions, v => v.version);
      for (const version in groupedList) {
        const lastVersion = groupedList[version].reduce((a, b) => (a.version_step > b.version_step ? a : b));
        lastVersions.push(lastVersion);
      }

      this.logger.info('播报版本', lastVersions);
      const to_warn: Promise<void>[] = [];
      const slardarAutoWarnService = useInject(SlardarAutoWarnService);
      for (const version of lastVersions) {
        to_warn.push(
          slardarAutoWarnService.autoWarnSlardarIssue(aid, version.version_code, version.timestamp, version.platform),
        );
      }
      await Promise.all(to_warn);
    }
  }

  async slardarValueCollect(
    versions: DBAlarmVersion[],
    metrics: DBSlardarInfoTable[],
    deviceLevel: DeviceLevel,
    from: string,
  ): Promise<DBSlardarValue[]> {
    const value_promises: Promise<DBSlardarValue[]>[] = [];
    const now = dayjs();
    const start = now.subtract(1, 'h').startOf('h').unix();
    for (const version of versions) {
      if (start < version.timestamp) {
        continue;
      }
      const { aid } = version;
      const version_str = version.version_code;
      const is_ios = version_str.includes('.');
      for (const metric of metrics) {
        if (
          (is_ios && metric.Platform === SlardarPlatformType.Android) ||
          (!is_ios && metric.Platform === SlardarPlatformType.iOS)
        ) {
          continue;
        }

        // 整个版本开始的时间
        const version_start_ts = dayjs.unix(version.timestamp).add(1, 'h').startOf('h').unix();
        // 到上一个整点或 168 小时中的先者
        const end_ts = Math.min(
          now.startOf('h').unix(),
          dayjs.unix(version.timestamp).add(1, 'h').add(7, 'd').startOf('h').unix(),
        );

        // 筛选出缺少的时间点
        const existing_ts = (
          await this.slardarValueModel.query({
            aid,
            VersionCode: version.version_code,
            device_level: DeviceLevel.ALL,
          })
        ).map(it => it.Timestamp);
        const lacking_ts = range(version_start_ts, end_ts, 3600).filter(it => !existing_ts.includes(it));

        this.logger.info(`[${metric.Id}-${version_str}] Lacking timestamps: [${lacking_ts.join(',')}]`);

        // 保险
        if (lacking_ts.length <= 0) {
          continue;
        }

        // 一起存，减少 IO
        value_promises.push(
          this.pullOneCrashRate(
            aid,
            metric,
            lacking_ts,
            end_ts,
            version_str,
            version.timestamp,
            version_start_ts,
            deviceLevel,
            from,
          ),
        );
      }
    }
    return Promise.all(value_promises).then(r => r.flatMap(it => it));
  }

  /**
   *
   * @param aid
   * @param metric
   * @param version_ts
   * @param is_official
   * @param deviceLevel
   * @param version_code 按小版本拉取
   * @param version 按大版本拉取
   * @param isOffline 线下数据拉取
   */
  async slardarCrashIssueCollect(
    aid: number,
    metric: DBSlardarInfoTable,
    version_ts: number,
    is_official: boolean,
    deviceLevel: DeviceLevel,
    version_code?: string,
    version?: string,
    isOffline = false,
    stage = SelectedName.OtherStage,
  ) {
    let start_time = dayjs.unix(version_ts).add(1, 'h').startOf('h').unix();
    // 灰度最长查三天，正式最长查 7 天
    let end_time = version
      ? Math.floor(Date.now() / 1000)
      : Math.min(Math.floor(Date.now() / 1000), start_time + (is_official ? 7 : 3) * 86400);
    if (version && isOffline) {
      if (stage === SelectedName.BeforeIntegration) {
        //TODO 过渡完改回来 end_time 就是  dayjs.unix(version_ts).unix()
        start_time = Math.min(Math.floor(Date.now() / 1000), dayjs.unix(version_ts).unix() - 14 * 86400);
        end_time = Math.min(Math.floor(Date.now() / 1000), start_time + 28 * 86400);
      }
      if (stage === SelectedName.AfterIntegration) {
        start_time = dayjs.unix(version_ts).unix();
        end_time = Math.min(Math.floor(Date.now() / 1000), start_time + 7 * 86400);
      }
    }
    this.logger.info(`${version},${start_time}:${end_time}`);
    const this_crash = await this.fetchCrashListFor(
      aid,
      start_time,
      end_time,
      metric.MeasureName,
      metric.Platform,
      deviceLevel,
      '3600', // 1 小时。写死了，反正不写死后面也没需求改
      metric.AdditionalParams,
      version_code,
      version,
      isOffline,
    );
    let crashRate: GetCrashTrendResponseData | undefined;
    if (version && !isOffline) {
      crashRate = await this.slardarService.getCrashTrendRate(
        aid,
        metric.Platform,
        start_time,
        end_time,
        version,
        metric.MeasureName,
        60,
      );
    }
    const mem_graph_params: Partial<SlardarCrashCommon> = {
      filters_conditions: {
        sub_conditions: [
          {
            dimension: 'is_memory_graph',
            op: 'eq',
            value: 'true',
            type: 'expression',
          },
        ],
      },
    };
    mergeWith(mem_graph_params, metric.AdditionalParams, (dst, src) => {
      if (Array.isArray(dst)) {
        return dst.concat(src);
      }
    });
    const mem_graph_crash =
      metric.Id === 'OOMCrash'
        ? (
            await this.fetchCrashListFor(
              aid,
              start_time,
              end_time,
              metric.MeasureName,
              metric.Platform,
              deviceLevel,
              '3600', // 1 小时。写死了，反正不写死后面也没需求改
              mem_graph_params,
              version_code,
              version,
              isOffline,
            )
          )
            .map(it => it.issue_id)
            .filter(it => it)
            .map(it => it!)
        : [];

    this.logger.info(`oyq ppe with ${JSON.stringify(this_crash)}`);

    const cur_issue = this_crash.map(it => crashListItemToIssue(aid, it, metric.Platform));
    const cur_list = this_crash
      .map(it =>
        crashListItemToIssueList(
          aid,
          it,
          metric.Platform,
          version_code ? version_code : '',
          CrashType[metric.Id as keyof typeof CrashType],
          deviceLevel,
          version,
          crashRate,
        ),
      )
      .sort((a, b) => {
        if (metric.Platform === SlardarPlatformType.Android && metric.Id === CrashType.JavaSmallInstance) {
          // Android小对象以平均占用大小降序排序
          return (b.memory_object_size ?? 0) / b.users - (a.memory_object_size ?? 0) / a.users;
        } else {
          // 其它以影响用户数降序排序
          return b.users - a.users;
        }
      })
      .map((cur, idx) => {
        cur.ranking = 1 + idx;
        if (mem_graph_crash.includes(cur.issue_id)) {
          cur.has_memory_graph = true;
        }
        return cur;
      });
    this.logger.info(
      `fetchCrashListFor ${aid} ${metric.MeasureName} ${metric.Platform} ${version_code} ${version} size:${cur_list.length}`,
    );
    return {
      cur_issue,
      cur_list,
    };
  }

  // 每小时一次
  async slardarDataCollect() {
    // 灰度稳定性拉取
    for (const aid of await this.config.syncGrayAid()) {
      const metrics = await this.slardarInfoModel.findEnabled(aid, [
        SlardarPlatformType.Android,
        SlardarPlatformType.iOS,
      ]);
      // Get All versions to pull data
      // default: 7 d
      const versions = await this.alarmVersionModel.findVersionsIn(7 * 24 * 60, [aid]);
      this.logger.info(`Got ${versions.length} versions to pull`);
      // Get All metrics to pull
      // await initSlardar();
      this.logger.info(`Got ${metrics.length} metrics to pull`);
      await this.pullUserCount(versions, DeviceLevel.LOW, 'slardarDataCollect').catch(e => {
        this.logger.info(`pullUserCount  low error ${e.message} ${e.stack}`);
      });
      await this.pullUserCount(versions, DeviceLevel.HIGH, 'slardarDataCollect').catch(e => {
        this.logger.info(`pullUserCount high error ${e.message} ${e.stack}`);
      });
      await this.pullUserCount(versions, DeviceLevel.ALL, 'slardarDataCollect').catch(e => {
        this.logger.info(`pullUserCount all error ${e.message} ${e.stack}`);
      });

      // iOS 线上阶段发版后12小时、24小时 进行自动定级
      const iOSOnlineAutoLevelVersions = [
        ...(await this.alarmVersionModel.findVersionBy(12 * 60, [aid])),
        ...(await this.alarmVersionModel.findVersionBy(24 * 60, [aid])),
        ...(HYPIC_SLARDAR_APP_ID() === aid ? await this.alarmVersionModel.findVersionBy(48 * 60, [aid]) : []),
        ...(HYPIC_SLARDAR_APP_ID() === aid ? await this.alarmVersionModel.findVersionBy(72 * 60, [aid]) : []),
        // TODO Dreamina iOS定级检查规则
        // ...(Dreamina_SLARDAR_APP_ID === aid ? await this.alarmVersionModel.findVersionBy(48 * 60, [aid]) : []),
        // ...(Dreamina_SLARDAR_APP_ID === aid ? await this.alarmVersionModel.findVersionBy(72 * 60, [aid]) : []),
      ]
        .filter(value => value.platform === SlardarPlatformType.iOS)
        .filter(value => [VersionType.SMALL, VersionType.FULL].includes(value.version_type));
      const iOSGreyAutoLevelVersions = versions
        .filter(value => value.platform === SlardarPlatformType.iOS)
        .filter(value => [VersionType.TF_GRAY, VersionType.SMALL_TF, VersionType.FULL_TF].includes(value.version_type));
      const iOSAutoLevelVersions = [...iOSOnlineAutoLevelVersions, ...iOSGreyAutoLevelVersions];
      const iOSVersions = versions.filter(value => value.platform === SlardarPlatformType.iOS);
      // 仅进行Android 自动定级的版本
      const androidOnlyAutoLevel = versions.filter(value => value.platform === SlardarPlatformType.Android);
      this.logger.info(`android: ${JSON.stringify(androidOnlyAutoLevel)} ios:${JSON.stringify(iOSAutoLevelVersions)}`);
      /* await this.slardarDataCollectForVersionMetrics(
				aid,
				androidOnlyAutoLevel,
				metrics,
				DeviceLevel.LOW,
				SlardarPlatformType.Android,
				androidOnlyAutoLevel
			);
			await this.slardarDataCollectForVersionMetrics(
				aid,
				androidOnlyAutoLevel,
				metrics,
				DeviceLevel.HIGH,
				SlardarPlatformType.Android,
				androidOnlyAutoLevel
			); */
      await this.slardarDataCollectForVersionMetrics(
        aid,
        androidOnlyAutoLevel,
        metrics,
        DeviceLevel.ALL,
        '3',
        SlardarPlatformType.Android,
        androidOnlyAutoLevel,
      );
      // 进行 iOS 自动定级的版本
      await this.slardarDataCollectForVersionMetrics(
        aid,
        iOSVersions,
        metrics,
        DeviceLevel.LOW,
        '4',
        SlardarPlatformType.iOS,
        iOSAutoLevelVersions,
      );
      await this.slardarDataCollectForVersionMetrics(
        aid,
        iOSVersions,
        metrics,
        DeviceLevel.HIGH,
        '5',
        SlardarPlatformType.iOS,
        iOSAutoLevelVersions,
      );
      await this.slardarDataCollectForVersionMetrics(
        aid,
        iOSVersions,
        metrics,
        DeviceLevel.ALL,
        '6',
        SlardarPlatformType.iOS,
        iOSAutoLevelVersions,
      );

      let offlineAndroidVersions: string[] = [];
      let offlineiOSVersions: string[] = [];

      // 对于特定aid，直接从接口拉取线下版本列表
      if (aid === 1775 || aid === 3006) {
        const offlineAndroidVersionsResponse = await getPullOfflineVersions({
          data: { aid, platform: SlardarPlatformType.Android },
        });
        if (offlineAndroidVersionsResponse.code === 200 && offlineAndroidVersionsResponse.data?.length > 0) {
          // 修正：将返回的对象数组映射为字符串数组
          offlineAndroidVersions = offlineAndroidVersionsResponse.data;
        }
        const offlineiOSVersionsResponse = await getPullOfflineVersions({
          data: { aid, platform: SlardarPlatformType.iOS },
        });
        if (offlineiOSVersionsResponse.code === 200 && offlineiOSVersionsResponse.data?.length > 0) {
          // 修正：将返回的对象数组映射为字符串数组
          offlineiOSVersions = offlineiOSVersionsResponse.data;
        }

        // aid为1775 或者 3006
        if (offlineAndroidVersions.length > 0) {
          await this.slardarOfflineDataCollectForVersionMetrics(
            aid,
            offlineAndroidVersions,
            metrics,
            DeviceLevel.ALL,
            SlardarPlatformType.Android,
            SelectedName.BeforeIntegration,
          );
          await this.slardarOfflineDataCollectForVersionMetrics(
            aid,
            offlineAndroidVersions,
            metrics,
            DeviceLevel.ALL,
            SlardarPlatformType.Android,
            SelectedName.AfterIntegration,
          );
        }
        if (offlineiOSVersions.length > 0) {
          await this.slardarOfflineDataCollectForVersionMetrics(
            aid,
            offlineiOSVersions,
            metrics,
            DeviceLevel.ALL,
            SlardarPlatformType.iOS,
            SelectedName.BeforeIntegration,
          );
          await this.slardarOfflineDataCollectForVersionMetrics(
            aid,
            offlineiOSVersions,
            metrics,
            DeviceLevel.ALL,
            SlardarPlatformType.iOS,
            SelectedName.AfterIntegration,
          );
        }
      }
      // 进行 iOS 自动定级的版本
      // await this.slardarOfflineDataCollectForVersionMetrics(
      //   aid,
      //   iOSVersions,
      //   metrics,
      //   DeviceLevel.LOW,
      //   '4',
      //   SlardarPlatformType.iOS,
      //   iOSAutoLevelVersions,
      // );
      // await this.slardarOfflineDataCollectForVersionMetrics(
      //   aid,
      //   iOSVersions,
      //   metrics,
      //   DeviceLevel.HIGH,
      //   '5',
      //   SlardarPlatformType.iOS,
      //   iOSAutoLevelVersions,
      // );
      // await this.slardarOfflineDataCollectForVersionMetrics(
      //   aid,
      //   iOSVersions,
      //   metrics,
      //   DeviceLevel.ALL,
      //   '6',
      //   SlardarPlatformType.iOS,
      //   iOSAutoLevelVersions,
      // );
    }
    // 聚合稳定性拉取
    for (const aid of await this.config.syncGrayFullAid()) {
      const metrics = await this.slardarInfoModel.findEnabled(aid, [SlardarPlatformType.Android]);
      const GrayingVersion = await this.alarmVersionModel.findGrayingVersions(aid);
      // 暂时屏蔽掉定时任务提单
      await this.slardarDataCollectForAllVersionMetrics(aid, GrayingVersion, metrics, DeviceLevel.ALL);
      // await this.slardarDataCollectForAllVersionMetrics(aid, GrayingVersion, metrics, DeviceLevel.LOW);
      // await this.slardarDataCollectForAllVersionMetrics(aid, GrayingVersion, metrics, DeviceLevel.HIGH);
    }
  }

  async slardarNewCrashIssueListCollect(version_code?: string[]) {
    const metrics = await this.slardarInfoModel.find({
      Enabled: true,
      Platform: SlardarPlatformType.Android,
      Id: { $in: ['NativeCrash', 'JavaCrash', 'JavaStartCrash', 'ANR'] },
    });
    return await this.slardarNewIssueCollectGteNWeekly(metrics, version_code);
  }

  calculateRateForList(
    items: CrashListItem[],
    crashRate: GetCrashTrendResponseData,
    crash_type: CrashType,
    platform: SlardarPlatformType,
    version_code: string,
    rawList: CrashListItem[],
  ) {
    // list的总用户数
    const list_user_sum = sumBy(items, 'user');
    // 异常用户总数
    const user_sum = crashRate.active_total_;
    // 平均影响用户比例
    const rate_total = crashRate.user_active_total_;
    const list: SlardarVersionNewIssueListInfo[] = items.map((v, index) => {
      let ranking = rawList.findIndex(i => v.issue_id! === i.issue_id!);
      ranking = ranking === -1 ? 2001 + index : ranking + 1;
      return {
        issue_id: v.issue_id!,
        ranking,
        crash_type,
        platform,
        rate: rate_total * (v.user! / user_sum),
        count: v.count!,
        user: v.user!,
        version_code,
      };
    });
    return { rate: rate_total * (list_user_sum / user_sum), list };
  }

  async slardarNewIssueCollectGteNWeekly(metrics: DBSlardarInfoTable[], version_codes?: string[]) {
    const listModel = useInject(SlardarVersionNewIssueListModel);
    const rateModel = useInject(SlardarVersionNewIssueRateModel);
    const now = Math.floor(dayjs().valueOf() / 1000);
    const syncNewAids = await this.config.syncVersionNewAid();
    const version = version_codes
      ? await this.alarmVersionModel.queryVersionCodes(version_codes, syncNewAids)
      : await this.alarmVersionModel.queryONLINEDay2To8VersionCode(syncNewAids);
    const ret: any[] = [];
    for (const ver of version) {
      const { aid } = ver;
      for (const metric of metrics) {
        if (metric.Platform === ver.platform) {
          const gteList = await this.fetchNewCrashListGteN(
            aid,
            metric.MeasureName,
            ver.version_code,
            metric.Platform,
            '86400', // 1 天。写死了，反正不写死后面也没需求改
            ver.timestamp + 2 * 24 * 3600,
            Math.min(now, ver.timestamp + 8 * 24 * 3600),
            metric.AdditionalParams,
            2,
          );
          const rawList = await this.fetchRawCrashList(
            aid,
            metric.MeasureName,
            ver.version_code,
            metric.Platform,
            '86400', // 1 天。写死了，反正不写死后面也没需求改
            ver.timestamp + 2 * 24 * 3600,
            Math.min(now, ver.timestamp + 8 * 24 * 3600),
            metric.AdditionalParams,
          );
          const crashRate = await this.slardarService.getCrashRate(
            aid,
            metric.Platform,
            ver.timestamp + 2 * 24 * 3600,
            Math.min(now, ver.timestamp + 8 * 24 * 3600),
            ver.version_code,
            metric.MeasureName,
            1440,
            metric.AdditionalParams,
            DeviceLevel.ALL,
            true,
          );
          const res = this.calculateRateForList(
            gteList,
            crashRate,
            metric.Id as CrashType,
            ver.platform,
            ver.version_code,
            rawList,
          );
          await listModel.update(...res.list);
          await rateModel.update({
            Value: res.rate,
            crash_type: metric.Id as CrashType,
            end_time: Math.min(now, ver.timestamp + 8 * 24 * 3600),
            platform: ver.platform,
            start_time: ver.timestamp + 2 * 24 * 3600,
            version_code: ver.version_code,
          });
          ret.push({
            version_code: ver.version_code,
            rate: res.rate,
            active_total: crashRate.active_total_,
            rate_total: crashRate.user_active_total_,
            list_length: gteList.length,
            crash_type: metric.Id,
            start_time: ver.timestamp,
            end_time: Math.min(now, ver.timestamp + 8 * 24 * 3600),
          });
        }
      }
    }
    return ret;
  }

  async slardarVersionDataWeekly(time?: number) {
    const metrics = await this.slardarInfoModel.find({
      Enabled: true,
      Platform: SlardarPlatformType.Android,
      Id: { $in: ['NativeCrash', 'JavaCrash', 'JavaStartCrash', 'ANR'] },
    });
    return await this.slardarDataCollectTopNWeekly(metrics, time);
  }

  calculateIssueCrashRate(
    aid: number,
    items: CrashListItem[],
    crashRate: GetCrashTrendResponseData,
    Timestamp: number,
    date: string,
    crashType: CrashType,
    platform: SlardarPlatformType,
  ) {
    // 影响用户数
    const user_sum = crashRate.active_total_;
    // 平均影响用户比例
    const rate_total = crashRate.user_active_total_;
    const res: SlardarDAU3IssueListInfo[] = [];
    let index = 1;
    for (const d of items) {
      res.push({
        aid,
        Timestamp,
        count: d.count!,
        crash_type: crashType,
        date,
        issue_id: d.issue_id!,
        platform,
        ranking: index,
        rate: rate_total * (d.user! / user_sum),
        user: d.user!,
      });
      index++;
    }
    return {
      data: res,
      versions: crashRate.user_active_total_series,
    };
  }

  async slardarDataCollectTopNWeekly(metrics: DBSlardarInfoTable[], time?: number) {
    const model = useInject(SlardarVersionQualityRateModel);
    const listModel = useInject(SlardarDAU3IssueListModel);
    // await model.delete_all();
    // await listModel.delete_all();
    const end_time = time ? time : Math.floor(dayjs().valueOf() / 1000);
    const res: any[] = [];
    // TODO 仅剪映开启
    const aid = SLARDAR_APP_ID();
    for (const metric of metrics) {
      // 获取Top200
      const this_crash = await this.fetchTopNVersionCrashList(
        aid,
        metric.MeasureName,
        metric.Platform,
        '86400', // 1 天。写死了，反正不写死后面也没需求改
        end_time,
        metric.AdditionalParams,
        200,
      );
      // 获取当前总劣化率
      const crashRate = await this.slardarService.getTopVersionCrashRate(
        aid,
        metric.Platform,
        end_time - 7 * 86400,
        end_time,
        metric.MeasureName,
        1440,
        metric.AdditionalParams,
      );
      const date = new Date(end_time * 1000);
      const formattedDate = `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date
        .getDate()
        .toString()
        .padStart(2, '0')}`;
      // 计算当前每个issue的劣化率, 并查找上周
      const bindInfo = this.calculateIssueCrashRate(
        aid,
        this_crash,
        crashRate,
        end_time,
        formattedDate,
        metric.Id as CrashType,
        metric.Platform,
      );
      // 保存本周Top200数据
      await listModel.update(...bindInfo.data);
      // 获取上周Top200
      const lastInfo = await listModel.findLastWeekInfo(metric.Id as CrashType, end_time);
      const issue_ids: string[] = [];
      let sum = 0;
      let index = 0;
      for (const d of bindInfo.data) {
        // Top50
        if (index >= 50) {
          break;
        }
        const compare = lastInfo.find(v => v.issue_id === d.issue_id);
        if (compare) {
          if (d.rate > compare.rate) {
            issue_ids.push(d.issue_id);
            sum += d.rate - compare.rate;
          }
        }
        index++;
      }
      await model.update({
        Timestamp: end_time,
        Value: sum,
        crash_type: metric.Id as CrashType,
        date: formattedDate,
        issue_ids,
        platform: metric.Platform,
        top_versions: bindInfo.versions,
      });
    }
    return res;
  }

  /**
   * 按大版本号拉取线下数据
   * @param aid
   * @param versions
   * @param metrics
   * @param deviceLevel
   * @param platformType
   * @param autoLevelVersions
   * @param stage 封板前后阶段
   * @param checkGray 灰度检查提单
   */
  async slardarOfflineDataCollectForVersionMetrics(
    aid: number,
    versions: string[],
    metrics: DBSlardarInfoTable[],
    deviceLevel: DeviceLevel,
    platformType: SlardarPlatformType,
    stage: SelectedName,
  ) {
    const date = new Date();
    const timeStamp = date.getTime();
    const currentTimestampInSeconds = Math.floor(Date.now() / 1000);
    this.logger.info(
      `oyq ppe start with ${timeStamp} ${aid} ${JSON.stringify(versions)} ${JSON.stringify(metrics)} ${deviceLevel} ${platformType} }`,
    );

    this.logger.info(
      `verisons ${versions} ${aid} ${JSON.stringify(versions)} ${JSON.stringify(metrics)} ${deviceLevel} ${platformType}}`,
    );

    const issue_and_list_promise: Promise<[DBSlardarCrashIssue[], DBSlardarCrashIssueList[]]>[] = [];
    for (const version of versions) {
      let startTime: number | undefined; // 先写死，为今天 就是默认时间都能拿到 在VersionProcessInfoTable里
      try {
        const res: any = await getVersionIntegrationTime({
          data: {
            aid,
            version,
            platform: platformType,
            stage,
          },
        });
        if (res.code === 200 && res.data !== null) {
          startTime = res.data;
        }
      } catch (error) {
        this.logger.error('获取开始时间失败:', error);
      }

      if (!startTime) {
        this.logger.warn(`无法获取版本 ${version} 的起始时间，将跳过此版本的处理。`);
        continue;
      }

      const now = Math.floor(Date.now() / 1000);
      if (stage === SelectedName.AfterIntegration && startTime > now) {
        this.logger.warn(`版本 ${version} (封板后) 的起始时间晚于当前，跳过处理。`);
        continue;
      }

      if (stage === SelectedName.BeforeIntegration) {
        // 封板前的数据从封板时间点往前推14天开始拉取
        const effectiveStartTime = startTime - 14 * 86400;
        if (effectiveStartTime > now) {
          this.logger.warn(`版本 ${version} (封板前) 的数据拉取起始时间晚于当前，跳过处理。`);
          continue;
        }
      }

      this.logger.info(
        `startTime ${startTime} ${aid} ${JSON.stringify(versions)} ${JSON.stringify(metrics)} ${deviceLevel} ${platformType}}`,
      );
      for (const metric of metrics) {
        // 内存泄漏按大版本号拉取
        if (metric.Platform !== platformType || ['NativeMemLeak'].includes(metric.Id)) {
          continue;
        }

        if (platformType === SlardarPlatformType.Android && platformType === metric.Platform) {
          if (!['JavaCrash', 'NativeCrash', 'ANR', 'JavaMemLeak'].includes(metric.Id)) {
            continue;
          }
        }

        // 线下拉取的时候 java页面泄漏的过滤条件得变化
        if (
          platformType === SlardarPlatformType.Android &&
          platformType === metric.Platform &&
          metric.Id === 'JavaMemLeak'
        ) {
          if (metric.AdditionalParams?.filters_conditions?.sub_conditions?.length) {
            metric.AdditionalParams.filters_conditions.sub_conditions =
              metric.AdditionalParams.filters_conditions.sub_conditions.filter(
                cond => cond.dimension === 'memory_object_node_type',
              );
          }
        }

        const zeros = aid === 1775 ? '00000' : aid === 3006 ? '0000' : '';
        // 根据 stage 决定后缀
        const suffix = stage === SelectedName.BeforeIntegration ? '-offline' : '-later';
        issue_and_list_promise.push(
          this.slardarCrashIssueCollect(
            aid,
            metric,
            startTime,
            false,
            deviceLevel,
            platformType === SlardarPlatformType.Android && version
              ? `${version.replaceAll('.', '')}${zeros}`
              : `${version}.1`,
            version,
            true,
            stage,
          ).then(({ cur_issue, cur_list }) => {
            cur_list.forEach(item => {
              if (item.version) {
                item.version += suffix;
              }
            });
            return [cur_issue, cur_list];
          }),
        );
      }
    }

    const issue_result = await Promise.all(issue_and_list_promise);
    this.logger.info(
      `issue_result ${issue_result.length} ${aid} ${JSON.stringify(versions)} ${JSON.stringify(metrics)} ${deviceLevel} ${platformType} }`,
    );
    const crash_issues = new Map<string, DBSlardarCrashIssue>();
    const crash_issue_list = issue_result.flatMap(([, it]) => it);
    for (const issue of issue_result.flatMap(([it]) => it)) {
      crash_issues.set(issue.issue_id, issue);
    }
    const issues = extractor(...crash_issues.values());
    this.logger.info(
      `issues ${issues.length} ${aid} ${JSON.stringify(versions)} ${JSON.stringify(metrics)} ${deviceLevel} ${platformType} }`,
    );
    this.logger.info(`oyq ppe start with ${timeStamp} Saving ${issues.length} issues`);

    if (deviceLevel === DeviceLevel.ALL) {
      await this.slardarCrashIssueModel.save(...issues);
      await this.slardarService.autoOfflineLevelProcessor(aid, crash_issue_list, issues, platformType);
    }
  }

  /**
   * 按大版本号拉取
   * @param aid
   * @param versions
   * @param metrics
   * @param deviceLevel
   * @param checkGray 检查提单
   */
  async slardarDataCollectForAllVersionMetrics(
    aid: number,
    versions: DBAlarmVersion[],
    metrics: DBSlardarInfoTable[],
    deviceLevel: DeviceLevel,
    checkGray = true,
  ) {
    const currentTimestampInSeconds = Math.floor(Date.now() / 1000);
    const issue_and_list_promise: Promise<[DBSlardarCrashIssue[], DBSlardarCrashIssueList[]]>[] = [];
    for (const version of versions) {
      for (const metric of metrics) {
        if (metric.Platform !== version.platform) {
          continue;
        }
        issue_and_list_promise.push(
          this.slardarCrashIssueCollect(
            aid,
            metric,
            version.timestamp,
            [VersionType.ONLINE, VersionType.FULL, VersionType.SMALL].includes(version.version_type),
            deviceLevel,
            undefined,
            version.version,
          ).then(({ cur_issue, cur_list }) => {
            this.logger.info(`[${version.version}] Fetched crash issue list for ${metric.DisplayName}.`);
            return [cur_issue, cur_list];
          }),
        );
      }
    }
    const issue_result = await Promise.all(issue_and_list_promise);
    const crash_issues = new Map<string, DBSlardarCrashIssue>();
    const crash_issue_list = issue_result.flatMap(([, it]) => it);
    for (const issue of issue_result.flatMap(([it]) => it)) {
      crash_issues.set(issue.issue_id, issue);
    }
    const issues = extractor(...crash_issues.values());
    this.logger.info(`Saving ${issues.length} issues`);
    if (deviceLevel === DeviceLevel.ALL) {
      await this.slardarCrashIssueModel.save(...issues);
      await this.slardarService.autoLevelProcessor(aid, crash_issue_list, issues, SlardarPlatformType.Android, true);
      if (checkGray) {
        // 控制是否灰度提单
        for (const v of versions) {
          if (v.platform === SlardarPlatformType.Android) {
            await new GrayScaleCreateBug(v.platform, currentTimestampInSeconds, true).filterCreateBug(v);
          }
        }
      }
    } else {
      await this.slardarCrashIssueListModel.save(crash_issue_list, false);
    }
  }

  /**
   * 按小版本号拉取
   * @param aid
   * @param versions
   * @param metrics
   * @param deviceLevel
   * @param from
   * @param platformType
   * @param autoLevelVersions
   * @param checkGray 灰度检查提单
   */
  async slardarDataCollectForVersionMetrics(
    aid: number,
    versions: DBAlarmVersion[],
    metrics: DBSlardarInfoTable[],
    deviceLevel: DeviceLevel,
    from: string,
    platformType?: SlardarPlatformType,
    autoLevelVersions?: DBAlarmVersion[],
    checkGray = true,
  ) {
    const date = new Date();
    const timeStamp = date.getTime();
    const currentTimestampInSeconds = Math.floor(Date.now() / 1000);
    this.logger.info(
      `oyq ppe start with ${timeStamp} ${aid} ${JSON.stringify(versions)} ${JSON.stringify(metrics)} ${deviceLevel} ${platformType} ${JSON.stringify(autoLevelVersions)}`,
    );

    const value_promise = this.slardarValueCollect(versions, metrics, deviceLevel, from).then(val => {
      this.logger.info(`Saving ${val.length} values`);
      this.slardarValueModel.save_many(val);
    });

    const issue_and_list_promise: Promise<[DBSlardarCrashIssue[], DBSlardarCrashIssueList[]]>[] = [];
    for (const version of versions) {
      for (const metric of metrics) {
        // 内存泄漏按大版本号拉取
        if (metric.Platform !== version.platform || ['NativeMemLeak'].includes(metric.Id)) {
          continue;
        }
        issue_and_list_promise.push(
          this.slardarCrashIssueCollect(
            aid,
            metric,
            version.timestamp,
            [VersionType.ONLINE, VersionType.FULL, VersionType.SMALL].includes(version.version_type),
            deviceLevel,
            version.version_code,
            undefined,
          ).then(({ cur_issue, cur_list }) => [cur_issue, cur_list]),
        );
      }
    }

    const issue_result = await Promise.all(issue_and_list_promise);
    const crash_issues = new Map<string, DBSlardarCrashIssue>();
    const crash_issue_list = issue_result.flatMap(([, it]) => it);
    for (const issue of issue_result.flatMap(([it]) => it)) {
      crash_issues.set(issue.issue_id, issue);
    }
    const issues = extractor(...crash_issues.values());
    this.logger.info(`oyq ppe start with ${timeStamp} Saving ${issues.length} issues`);
    if (DeviceLevel.ALL === deviceLevel) {
      await this.slardarCrashIssueModel.save(...issues);
    }

    const version_codes = versions.map(it => it.version_code);
    const alVersions = autoLevelVersions?.filter(it => version_codes.includes(it.version_code)) ?? [];
    if (alVersions.length > 0 && DeviceLevel.ALL === deviceLevel) {
      this.logger.info(`oyq ppe 111 ${timeStamp} alVersions ${JSON.stringify(alVersions)}`);
      const autoLevelVersionCodes = alVersions.map(value => value.version_code);
      this.logger.info(`oyq ppe 222 ${timeStamp} autoLevelVersionCodes ${JSON.stringify(autoLevelVersionCodes)}`);
      const autoLevelIssueResult = issue_result.filter(
        ([, it]) => it.length > 0 && autoLevelVersionCodes.includes(it[0].version_code),
      );
      // 有一部分被抛弃了
      const excludeIssueResult = issue_result.filter(
        ([, it]) => it.length > 0 && !autoLevelVersionCodes.includes(it[0].version_code),
      );
      const excludeCrashIssueList = excludeIssueResult.flatMap(([, it]) => it);

      const autoLevelCrashIssues = new Map<string, DBSlardarCrashIssue>();
      const autoLevelCrashIssueList = autoLevelIssueResult.flatMap(([, it]) => it);
      for (const issue of autoLevelIssueResult.flatMap(([it]) => it)) {
        autoLevelCrashIssues.set(issue.issue_id, issue);
      }
      const autoLevelIssues = extractor(...autoLevelCrashIssues.values());

      const toWaitPromise: Promise<any>[] = [
        value_promise,
        this.slardarService.autoLevelProcessor(aid, autoLevelCrashIssueList, autoLevelIssues, platformType),
      ];
      if (excludeCrashIssueList && excludeCrashIssueList.length > 0) {
        toWaitPromise.push(this.slardarCrashIssueListModel.save(excludeCrashIssueList, false));
      }
      await Promise.all(toWaitPromise);
      if (checkGray && platformType === SlardarPlatformType.Android) {
        for (const v of versions) {
          await new GrayScaleCreateBug(v.platform, currentTimestampInSeconds, false).filterCreateBug(v);
        }
      }
    } else {
      // 直接保存，不定级，不覆写定级结果
      await Promise.all([value_promise, this.slardarCrashIssueListModel.save(crash_issue_list, false)]);
    }
  }

  async iOSSingleQualityCollectXiaoShi(start: number, end: number, version?: string, other_filters?: FlexFilter[]) {
    const ret = await Promise.all(
      ['ios_crash.user_ratio', 'ios_oom_crash.user_ratio', 'ios_watch_dog.user_ratio'].map(metric =>
        this.pullOneMetric(
          SLARDAR_APP_ID(),
          start,
          end,
          metric,
          SlardarPlatformType.iOS,
          compact([version]),
          '1',
          DeviceLevel.ALL,
          d => d.series?.shift()?.time_rollup_data,
          v => v,
          [],
          '3600',
          other_filters,
        ).then(data => ({
          metricId: metric,
          value: data ?? -1,
        })),
      ),
    );
    return {
      start,
      end,
      data: ret,
    };
  }

  async iOSSingleQualityCollect(start: number, end: number, version?: string, other_filters?: FlexFilter[]) {
    const ret = await Promise.all(
      ['ios_crash.user_ratio', 'ios_oom_crash.user_ratio', 'ios_watch_dog.user_ratio'].map(metric =>
        this.pullOneMetric(
          SLARDAR_APP_ID(),
          start,
          end,
          metric,
          SlardarPlatformType.iOS,
          compact([version]),
          '2',
          DeviceLevel.ALL,
          d => d.series?.shift()?.avg,
          v => v,
          [],
          '86400',
          other_filters,
        ).then(data => ({
          metricId: metric,
          value: data ?? -1,
        })),
      ),
    );
    return {
      start,
      end,
      data: ret,
    };
  }

  async iOSOverallQualityCollect(start: number, end: number) {
    const data = await this.iOSSingleQualityCollect(start, end, undefined);
    return {
      ...data,
      url: getSlardarUrl(
        SLARDAR_APP_ID(),
        CrashType2Url.Crash,
        SlardarPlatformType.iOS,
        'abnormal_list',
        getSlardarIssueListParams(SlardarPlatformType.iOS, undefined, start, end),
      ),
    };
  }

  async iOSDauTop3QualityCollect(start: number, end: number) {
    const data = await this.iOSSingleQualityCollect(start, end, 'top3');
    return {
      ...data,
      url: getSlardarUrl(
        SLARDAR_APP_ID(),
        CrashType2Url.Crash,
        SlardarPlatformType.iOS,
        'abnormal_list',
        getSlardarIssueListParams(SlardarPlatformType.iOS, 'DAU TOP3 version', start, end),
      ),
    };
  }

  async checkCrashUserValid(user: string | undefined) {
    if (user === undefined) {
      return false;
    }
    return await useInject(LarkService).checkUserValid(user);
  }

  async consumeIssue(
    aid: number,
    issue_id: string,
    platform: SlardarPlatformType,
    crash_type: CrashType,
    extraArgs: any = null,
  ) {
    this.logger.info(
      '[consumeIssue] consumeIssue aid:',
      aid,
      ',  issue_id:',
      issue_id,
      ',  crash_type:',
      crash_type,
      ', extra args',
      extraArgs,
      '.',
    );
    this.slardarService.IssueConsume(aid, issue_id, platform, crash_type, extraArgs, false, true, true, true);
  }

  async getLastSceneInfo(
    aid: number,
    issue_id: string,
    platform: SlardarPlatformType,
    crash_type: string,
    version: string,
  ) {
    const resp = await this.slardarService.getMostLastScene(aid, issue_id, platform, crash_type, version);

    for (let i = 0; i < resp.data.detail.length; i++) {
      if (resp.data.detail[i].field === 'unknown') {
        continue;
      }

      if (resp.data.detail[i].percent > 0.5) {
        return resp.data.detail[i].field;
      }

      return undefined;
    }

    return undefined;
  }

  async getSceneList(scene?: string, owner?: string) {
    let resp = await this.slardarService.getSceneList();
    if (scene !== undefined && scene.length > 0) {
      resp = resp.filter(item => item.scene !== undefined && item.scene.includes(scene));
    }
    if (owner !== undefined && owner.length > 0) {
      resp = resp.filter(item => item.owner !== undefined && item.owner.includes(owner));
    }
    return {
      data: resp,
      success: true,
      total: resp.length,
    };
  }

  async updateSceneOwner(appid: number, platform: string, scene: string, owner: string) {
    const larkService = useInject(LarkService);
    const isValid = await larkService.checkUserValid(owner);
    if (!isValid) {
      return {
        success: false,
        errormsg: '更新失败，用户名无效',
      };
    }

    await this.slardarService.updateSceneOwner(appid, platform, scene, owner);
    return {
      success: true,
    };
  }

  async chatId2OpenChatId(chatId: string): Promise<string> {
    const url = `https://oncall-backend.bytedance.net/api/inf/v1/platform/chat_id_to_open_chat_id?chat_id=${chatId}`;
    const resp = await fetch(url).then(it => it.json());
    return resp.data ?? '';
  }

  getbuildElement(title: string, content: string, originalval: string, curval: string) {
    return {
      tag: CardElementTag.columnSet,
      columns: [
        {
          tag: CardElementTag.column,
          width: 'weighted',
          elements: [
            {
              tag: CardElementTag.div,
              text: {
                tag: CardTextTag.lark_md,
                content: title,
              },
            } as CardElement,
          ],
          weight: 5,
        } as CardColumnElement,
        {
          tag: CardElementTag.column,
          width: 'weighted',
          elements: [
            {
              tag: CardElementTag.div,
              text: {
                tag: CardTextTag.lark_md,
                content: originalval,
              },
            } as CardElement,
          ],
          weight: 2,
        } as CardColumnElement,
        {
          tag: CardElementTag.column,
          width: 'weighted',
          elements: [
            {
              tag: CardElementTag.div,
              text: {
                tag: CardTextTag.lark_md,
                content: '-->',
              },
            } as CardElement,
          ],
          weight: 1,
        } as CardColumnElement,
        {
          tag: CardElementTag.column,
          width: 'weighted',
          elements: [
            {
              tag: CardElementTag.div,
              text: {
                tag: CardTextTag.lark_md,
                content: curval,
              },
            } as CardElement,
          ],
          weight: 5,
        } as CardColumnElement,
        {
          tag: CardElementTag.column,
          width: 'weighted',
          elements: [
            {
              tag: CardElementTag.div,
              text: {
                tag: CardTextTag.lark_md,
                content,
              },
            } as CardElement,
          ],
          weight: 5,
        } as CardColumnElement,
      ],
    } as CardColumnSetElement;
  }

  async sendVersionGetWorseCard(
    data: {
      crash: string;
      crash_org: string;
      crash_cur: string;
      oom: string;
      oom_org: string;
      oom_cur: string;
      watchdog: string;
      watchdog_org: string;
      watchdog_cur: string;
    },
    title: string,
    cardtitle: string,
  ) {
    const cardService = useInject(LarkCardService);
    const baseCard = cardService.buildBaseCard({
      title: (SLARDAR_APP_ID() === 1775 ? '剪映' : 'Capcut') + cardtitle,
      template: CardTemplate.blue,
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        tag: CardTextTag.lark_md,
        content: (SLARDAR_APP_ID() === 1775 ? '剪映' : 'Capcut') + title,
      },
    } as CardContentElement);

    elements.push(this.getbuildElement('crash:', data.crash, data['crash_org'], data['crash_cur']));
    elements.push(this.getbuildElement('oom:', data.oom, data['oom_org'], data['oom_cur']));
    elements.push(this.getbuildElement('watchdog:', data.watchdog, data['watchdog_org'], data['watchdog_cur']));
    baseCard.elements = elements;

    const lark = useInject(LarkService);
    const oid = await this.chatId2OpenChatId('7245563531993579522'); // 正式群
    // var oid = await this.chatId2OpenChatId('7423947774418239490');
    await lark.sendCardMessage(UserIdType.chatId, oid, baseCard);
  }

  async insertSceneList(sceneList: any[]) {
    return this.slardarService.insertSceneList(sceneList);
  }

  async querySceneOwner(appid?: number, platform?: string, scene?: string) {
    if (!appid || !platform || !scene) {
      return undefined;
    }
    return this.slardarService.querySceneOwner(appid, platform, scene);
  }

  async querySceneExist(scene?: string) {
    if (!scene) {
      return undefined;
    }
    return this.slardarService.querySceneExist(scene);
  }

  async iOSVersionQualityCollect(version: DBAlarmVersion, full_time: number, other_filters?: FlexFilter[]) {
    dayjs.extend(utc);
    const version_ts = dayjs.unix(full_time).utcOffset(current_region() === Region.SG ? 0 : 8);
    const start_ts = version_ts.add(1, 'd').startOf('d').unix();
    const end_ts = start_ts + 6 * 24 * 60 * 60;
    const ret = await this.iOSSingleQualityCollect(start_ts, end_ts, version.version_code, other_filters);
    return {
      ...ret,
      version,
      url: getSlardarUrl(
        SLARDAR_APP_ID(),
        CrashType2Url.Crash,
        SlardarPlatformType.iOS,
        'abnormal_list',
        getSlardarIssueListParams(SlardarPlatformType.iOS, version.version_code, start_ts, end_ts),
      ),
    };
  }

  async iOSXiaoLiuLiangQualityCollect(version: string, start_ts: number, end_ts: number, other_filters?: FlexFilter[]) {
    dayjs.extend(utc);
    const ret = await this.iOSSingleQualityCollectXiaoShi(
      dayjs
        .unix(start_ts)
        .utcOffset(current_region() === Region.SG ? 0 : 8)
        .unix(),
      dayjs
        .unix(end_ts)
        .utcOffset(current_region() === Region.SG ? 0 : 8)
        .unix(),
      version,
      other_filters,
    );
    return {
      ...ret,
      version,
      url: getSlardarUrl(
        SLARDAR_APP_ID(),
        CrashType2Url.Crash,
        SlardarPlatformType.iOS,
        'abnormal_list',
        getSlardarIssueListParams(SlardarPlatformType.iOS, version, start_ts, end_ts),
      ),
    };
  }

  // 若数据不完全，就再推迟一点点。总之每天拉一次。
  async versionQualityDataCollect() {
    // 得到 10d 以内所有正式版本
    const versions = (
      await this.alarmVersionModel.findVersionsIn(30 * 24 * 60, await this.config.syncVersionEscapeAid())
    ).filter(it => [VersionType.ONLINE, VersionType.FULL, VersionType.SMALL].includes(it.version_type));
    this.logger.info(`Got ${versions.length} versions to pull`);
    // Get All metrics to pull
    const metrics = await this.slardarInfoModel.find({
      Enabled: true,
    });
    this.logger.info(`Got ${metrics.length} metrics to pull`);

    const promise_list: Promise<any>[] = [];
    for (const ver of versions) {
      const start_ts = dayjs.unix(ver.timestamp).add(2, 'd').startOf('d').unix();
      const end_ts = Math.min(dayjs.unix(ver.timestamp).add(8, 'd').startOf('d').unix(), dayjs().startOf('d').unix());
      const { aid } = ver;
      if (start_ts >= end_ts) {
        continue;
      }

      const cur_promises = metrics
        .filter(it => it.Platform === ver.platform)
        .map(metric =>
          Promise.all([
            this.slardarService.getCrashRate(
              aid,
              metric.Platform,
              start_ts,
              end_ts,
              ver.version_code,
              metric.MeasureName,
              1440,
              metric.AdditionalParams,
              DeviceLevel.ALL,
            ),
            this.slardarService.getCrashRate(
              aid,
              metric.Platform,
              start_ts,
              end_ts,
              ver.version_code,
              metric.MeasureName,
              1440,
              metric.AdditionalParams,
              DeviceLevel.LOW,
            ),
            this.slardarService.getCrashRate(
              aid,
              metric.Platform,
              start_ts,
              end_ts,
              ver.version_code,
              metric.MeasureName,
              1440,
              metric.AdditionalParams,
              DeviceLevel.HIGH,
            ),
            this.slardarService.crashIssueList(
              aid,
              metric.MeasureName,
              metric.Platform,
              100,
              start_ts,
              end_ts,
              '86400',
              ver.version_code,
              undefined,
              1,
              metric.AdditionalParams,
              false,
              DeviceLevel.ALL,
            ),
            this.slardarService.crashIssueList(
              aid,
              metric.MeasureName,
              metric.Platform,
              100,
              start_ts,
              end_ts,
              '86400',
              ver.version_code,
              undefined,
              1,
              metric.AdditionalParams,
              false,
              DeviceLevel.LOW,
            ),
            this.slardarService.crashIssueList(
              aid,
              metric.MeasureName,
              metric.Platform,
              100,
              start_ts,
              end_ts,
              '86400',
              ver.version_code,
              undefined,
              1,
              metric.AdditionalParams,
              false,
              DeviceLevel.HIGH,
            ),
          ]).then(([dataAll, dataLow, dataHigh, issuesAll, issuesLow, issuesHigh]) =>
            Promise.all([
              this.slardarVersionQualityValueModel.save({
                metric_name: metric.Id,
                timestamp: end_ts,
                value: dataAll.user_active_total_,
                version_code: ver.version_code,
                device_level: DeviceLevel.ALL,
                aid,
              }),
              this.slardarVersionQualityValueModel.save({
                metric_name: metric.Id,
                timestamp: end_ts,
                value: dataLow.user_active_total_,
                version_code: ver.version_code,
                device_level: DeviceLevel.LOW,
                aid,
              }),
              this.slardarVersionQualityValueModel.save({
                metric_name: metric.Id,
                timestamp: end_ts,
                value: dataHigh.user_active_total_,
                version_code: ver.version_code,
                device_level: DeviceLevel.HIGH,
                aid,
              }),
              this.slardarVersionQualityIssueModel.save(
                ...issuesAll
                  .result!.map(it => ({
                    issue_id: it.issue_id!,
                    crash_type: CrashType[metric.Id as keyof typeof CrashType],
                    version_code: ver.version_code,
                    users: it.user!,
                    user_rate: it.user! / (dataAll.active_total_ / dataAll.user_active_total_),
                    platform: metric.Platform,
                    issue_level: it.issue_level!,
                    ranking: -1,
                    device_level: DeviceLevel.ALL,
                    aid,
                  }))
                  .sort((a, b) => b.users - a.users)
                  .map((it, idx) => {
                    it.ranking = 1 + idx;
                    return it;
                  }),
              ),
              this.slardarVersionQualityIssueModel.save(
                ...issuesLow
                  .result!.map(it => ({
                    issue_id: it.issue_id!,
                    crash_type: CrashType[metric.Id as keyof typeof CrashType],
                    version_code: ver.version_code,
                    users: it.user!,
                    user_rate: it.user! / (dataLow.active_total_ / dataLow.user_active_total_),
                    platform: metric.Platform,
                    issue_level: it.issue_level!,
                    ranking: -1,
                    device_level: DeviceLevel.LOW,
                    aid,
                  }))
                  .sort((a, b) => b.users - a.users)
                  .map((it, idx) => {
                    it.ranking = 1 + idx;
                    return it;
                  }),
              ),
              this.slardarVersionQualityIssueModel.save(
                ...issuesHigh
                  .result!.map(it => ({
                    issue_id: it.issue_id!,
                    crash_type: CrashType[metric.Id as keyof typeof CrashType],
                    version_code: ver.version_code,
                    users: it.user!,
                    user_rate: it.user! / (dataHigh.active_total_ / dataHigh.user_active_total_),
                    platform: metric.Platform,
                    issue_level: it.issue_level!,
                    ranking: -1,
                    device_level: DeviceLevel.HIGH,
                    aid,
                  }))
                  .sort((a, b) => b.users - a.users)
                  .map((it, idx) => {
                    it.ranking = 1 + idx;
                    return it;
                  }),
              ),
            ]),
          ),
        );
      promise_list.push(...cur_promises);
    }
    await Promise.all(promise_list);
  }

  // // 每天收集一次信息，形成 Android 问题趋势信息
  // async versionQualityTrendDataCollect() {
  // 	// 得到 10d 以内所有正式版本
  // 	const versions = (await this.alarmVersionModel.findVersionsIn(10 * 24 * 60)).filter((it) =>
  // 			it.version_type === VersionType.ONLINE ||
  // 			it.version_type === VersionType.FULL ||
  // 			it.version_type === VersionType.SMALL
  // 	);
  // 	this.logger.info(`Got ${versions.length} versions to pull`);
  // 	// Get All metrics to pull
  // 	const metrics = await this.slardarInfoModel.find({
  // 		Enabled: true,
  // 	});
  // 	this.logger.info(`Got ${metrics.length} metrics to pull`);
  //
  // 	const promise_list: Promise<any>[] = [];
  // 	for (const ver of versions) {
  // 		const start_ts = dayjs.unix(ver.timestamp).add(2, "d").startOf("d").unix();
  // 		const end_ts = Math.min(
  // 			dayjs.unix(ver.timestamp).add(8, "d").startOf("d").unix(),
  // 			dayjs().startOf("d").unix()
  // 		);
  // 		if (start_ts >= end_ts) {
  // 			continue;
  // 		}
  //
  // 		const cur_promises = metrics
  // 			.filter((it) => it.Platform === ver.platform)
  // 			.map((metric) =>
  // 				Promise.all([
  // 					this.slardarService.getCrashRate(
  // 						metric.Platform,
  // 						start_ts,
  // 						end_ts,
  // 						ver.version_code,
  // 						metric.MeasureName,
  // 						1440,
  // 						metric.AdditionalParams
  // 					),
  // 					this.slardarService.crashIssueList(
  // 						metric.MeasureName,
  // 						metric.Platform,
  // 						500,
  // 						start_ts,
  // 						end_ts,
  // 						"86400",
  // 						ver.version_code,
  // 						undefined,
  // 						1,
  // 						metric.AdditionalParams
  // 					),
  // 				]).then(([data, issues]) =>
  // 					Promise.all([
  // 						this.slardarVersionQualityValueModel.save({
  // 							metric_name: metric.Id,
  // 							timestamp: end_ts,
  // 							value: data.user_active_total_,
  // 							version_code: ver.version_code,
  // 						}),
  // 						this.slardarVersionQualityIssueModel.save(
  // 							...issues
  // 								.result!.map((it) => ({
  // 								issue_id: it.issue_id!,
  // 								crash_type: CrashType[metric.Id as keyof typeof CrashType],
  // 								version_code: ver.version_code,
  // 								users: it.user!,
  // 								user_rate: it.user! / (data.active_total_ / data.user_active_total_),
  // 								platform: metric.Platform,
  // 								issue_level: it.issue_level!,
  // 								ranking: -1,
  // 							}))
  // 								.sort((a, b) => b.users - a.users)
  // 								.map((it, idx) => {
  // 									it.ranking = 1 + idx;
  // 									return it;
  // 								})
  // 						),
  // 					])
  // 				)
  // 			);
  // 		promise_list.push(...cur_promises);
  // 	}
  // 	await Promise.all(promise_list);
  // }

  // 预期，每天 00:10 拉一次
  async rePullValue(aid: number, version_codes: string[], from: string) {
    const versions = await this.alarmVersionModel.findVersions(aid, version_codes);
    const metrics = await this.slardarInfoModel.findEnabled(aid, [SlardarPlatformType.Android]);

    await this.slardarValueModel.cleanVersion(aid, version_codes); // 这里为什么要做一次删除
    await this.slardarUserCountModel.cleanVersion(aid, version_codes); // 这里为什么要做一次删除

    await this.pullUserCount(versions, DeviceLevel.ALL, `${from}-rePullValue`);
    await this.pullUserCount(versions, DeviceLevel.LOW, `${from}-rePullValue`);
    await this.pullUserCount(versions, DeviceLevel.HIGH, `${from}-rePullValue`);
    await this.slardarDataCollectForVersionMetrics(
      aid,
      versions,
      metrics,
      DeviceLevel.HIGH,
      '7',
      SlardarPlatformType.Android,
      versions,
    );
    await this.slardarDataCollectForVersionMetrics(
      aid,
      versions,
      metrics,
      DeviceLevel.LOW,
      '8',
      SlardarPlatformType.Android,
      versions,
    );
    await this.slardarDataCollectForVersionMetrics(
      aid,
      versions,
      metrics,
      DeviceLevel.ALL,
      '9',
      SlardarPlatformType.Android,
      versions,
    );
  }

  /**
   * 从slardar拉取一段时间内的总用户数
   * @param versions
   * @param deviceLevel
   * @param from
   */
  async pullUserCount(versions: DBAlarmVersion[], deviceLevel: DeviceLevel, from: string) {
    const now = dayjs();
    const iPhoneLowEndSorted = iPhoneLowEndList.map(it => it.split('').sort().join(''));

    const to_save_promise: Promise<DBSlardarUserCount | undefined>[] = [];
    const to_wait_promise: Promise<any>[] = [];

    for (const version of versions) {
      const version_start_ts = dayjs.unix(version.timestamp).startOf('h').add(1, 'h').unix();
      const last_ts =
        (await this.slardarUserCountModel.lastTimestamp(version.aid, version.version_code, DeviceLevel.ALL))
          ?.timestamp || 0;
      const end_ts = Math.min(
        now.startOf('h').unix(),
        dayjs.unix(version.timestamp).add(1, 'h').add(8, 'd').startOf('h').unix(),
      );
      const { aid } = version;
      for (
        let cur_end_ts = Math.max(version_start_ts, last_ts + 3600) + 3600;
        cur_end_ts <= end_ts;
        cur_end_ts += 3600
      ) {
        to_save_promise.push(
          this.pullOneMetric(
            aid,
            version_start_ts,
            cur_end_ts,
            version.platform === SlardarPlatformType.iOS ? 'ios_real_session.user' : 'android_real_session.user',
            version.platform,
            [version.version_code],
            `${from}-3`,
            deviceLevel,
            undefined,
            val =>
              val !== undefined
                ? {
                    aid,
                    timestamp: cur_end_ts - 3600,
                    version_code: version.version_code,
                    count: val,
                    device_level: deviceLevel,
                  }
                : undefined,
          ),
        );
      }

      if (version.platform !== SlardarPlatformType.iOS) {
        continue;
      }

      to_wait_promise.push(
        // iOS 低端机
        this.pullOneMetric(
          aid,
          version_start_ts,
          end_ts,
          'ios_real_session.user',
          version.platform,
          [version.version_code],
          `${from}-4`,
          deviceLevel,
          d =>
            d.series
              ?.filter(it =>
                iPhoneLowEndSorted.find(
                  comp =>
                    comp.localeCompare(
                      (it.group_by_values![0]?.value ?? '')
                        .replace('iphone', '')
                        .replace(' ', '')
                        .split('')
                        .sort()
                        .join(''),
                      undefined,
                      {
                        sensitivity: 'accent',
                      },
                    ) === 0,
                ),
              )
              ?.reduce((sum, cur) => sum + (cur.time_rollup_data ?? 0), 0) ?? 0,
          it => it,
          [
            {
              group_by_name: 'device_model',
              label: '机型',
            },
          ],
        ).then(low_end_count => {
          this.logger.info(`[${version.version_code}] Low End Device -> ${low_end_count}`);
          return low_end_count !== undefined
            ? this.slardarVersionMetricsModel.setValue(
                version.aid,
                version.version_code,
                MetricType.LOW_END_USER,
                low_end_count,
                deviceLevel,
              )
            : undefined;
        }),
      );

      const generic_pull = async (measure: string, type: MetricType) => {
        const x = await this.pullOneMetric(
          aid,
          version_start_ts,
          end_ts,
          measure,
          version.platform,
          [version.version_code],
          `${from}-5`,
          deviceLevel,
        );
        if (x !== undefined) {
          await this.slardarVersionMetricsModel.setValue(version.aid, version.version_code, type, x, deviceLevel);
        }
      };
      const metrics_list: Readonly<Readonly<[string, MetricType]>[]> = [
        ['ios_real_session.count', MetricType.START_COUNT],
        ['ios_real_session.oom_count', MetricType.OOM_START_COUNT],
        ['ios_watch_dog.user', MetricType.WATCHDOG_USER_COUNT],
      ];
      // fixme: 这里可以改为只用一个请求
      to_wait_promise.push(...metrics_list.map(([m, t]) => generic_pull(m, t)));
    }

    await Promise.all([
      ...to_wait_promise,
      Promise.all(to_save_promise)
        .then(it => it.filter(c => c) as DBSlardarUserCount[])
        .then(it => this.slardarUserCountModel.save(...it)),
    ]);
  }

  /**
   * 启动数据不支持拆分机型评分
   */
  async pullOneMetric<T = number>(
    aid: number,
    start: number,
    end: number,
    measure_name: string,
    platform: SlardarPlatformType,
    versions: string[],
    from: string,
    deviceLevel: DeviceLevel = DeviceLevel.ALL,
    val_extractor?: (data: FlexSeriesResponseData) => number | undefined,
    postprocessor: (val?: number) => T | undefined = it => it as T,
    groupByList: FlexGroupBy[] = [],
    granularity: '3600' | '86400' = '3600',
    other_filters?: FlexFilter[],
  ): Promise<T | undefined> {
    dayjs.extend(utc);
    let deviceFilter: FlexFilter[] = [];
    if (deviceLevel === DeviceLevel.LOW && measure_name !== 'android_real_session.user') {
      deviceFilter = platform === SlardarPlatformType.Android ? lowAndroidFilter : lowIosFilter;
    }
    if (deviceLevel === DeviceLevel.HIGH && measure_name !== 'android_real_session.user') {
      deviceFilter = platform === SlardarPlatformType.Android ? highAndroidFilter : highIosFilter;
    }
    if (other_filters && other_filters.length > 0) {
      other_filters = other_filters.concat(deviceFilter);
    } else {
      other_filters = deviceFilter;
    }
    const resp = await this.slardarService.querySeries(
      aid,
      start,
      end,
      granularity,
      measure_name,
      platform,
      versions,
      groupByList,
      other_filters,
      `pullOneMetric-${from}`,
    );
    const val = val_extractor ? val_extractor(resp) : resp.series?.shift()?.time_rollup_data;
    const ret = postprocessor(val);
    this.logger.info(
      `Pulled ${measure_name} deviceLevel=${deviceLevel} for [${versions.join(', ')}] at [${dayjs
        .unix(start)
        .utcOffset(8)
        .format('YYYY-MM-DD HH:mm')} -> ${dayjs.unix(end).utcOffset(8).format('YYYY-MM-DD HH:mm')}]`,
    );
    return ret;
  }

  private async pullOneCrashRate(
    aid: number,
    metric: DBSlardarInfoTable,
    lacking_ts: number[],
    end_ts: number,
    version_str: string,
    version_ts: number,
    version_start_ts: number,
    deviceLevel: DeviceLevel,
    from: string,
  ): Promise<DBSlardarValue[]> {
    dayjs.extend(utc);

    if (metric.Platform === SlardarPlatformType.Android) {
      const resp = await this.slardarService.getCrashRate(
        aid,
        metric.Platform,
        version_start_ts,
        end_ts,
        version_str,
        metric.MeasureName,
        60,
        metric.AdditionalParams,
        deviceLevel,
      );
      const ret = resp.user_active
        .filter(it => lacking_ts.includes(it.start_time))
        .map(it => {
          const ts: number = it.start_time;
          const val: number = it[version_str] || it?.total || 0;
          return {
            aid,
            SlardarInfoId: metric.Id,
            Value: val,
            Timestamp: ts,
            HourFromVersion: Math.ceil((ts - version_ts) / 3600),
            VersionCode: version_str,
            device_level: deviceLevel,
          };
        });
      if (resp.active_total_ && ['JavaStartCrash', 'JavaCrash', 'NativeCrash', 'ANR'].includes(metric.Id)) {
        // 保存
        await this.slardarVersionMetricsModel.setValue(
          aid,
          version_str,
          CrashId2ValueType[metric.Id],
          resp.active_total_,
          deviceLevel,
        );
      }
      this.logger.info(
        `[${metric.Id}-${version_str}] deviceLevel=${deviceLevel} Pulled ${ret.length} values, ${ret
          .map(it => `[${it.HourFromVersion} -> ${it.Value}]`)
          .join(',')}`,
      );
      return ret;
    }

    // iOS
    const ret_promise: Promise<DBSlardarValue | undefined>[] = [];
    for (const current_start of lacking_ts) {
      const current_end = current_start + 3600;
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      let otherFilters: FlexFilter[] = [];
      if (deviceLevel === DeviceLevel.LOW) {
        otherFilters = lowIosFilter;
      }
      if (deviceLevel === DeviceLevel.HIGH) {
        otherFilters = highIosFilter;
      }
      ret_promise.push(
        this.slardarService
          .querySeries(
            aid,
            version_start_ts,
            current_end,
            '3600',
            `ios_${metric.MeasureName}.user_ratio`,
            SlardarPlatformType.iOS,
            [version_str],
            [],
            otherFilters,
            `pullOneCrashRate-${from}`,
          )
          .then(resp => {
            const val = resp.series?.shift()?.time_rollup_data;
            this.logger.info(
              `Pulled iOS value [${version_str}-${metric.MeasureName}] at [${dayjs
                .unix(version_start_ts)
                .utcOffset(8)
                .format('YYYY-MM-DD HH:mm')} -> ${dayjs.unix(current_end).utcOffset(8).format('YYYY-MM-DD HH:mm')}]`,
            );
            return val !== undefined
              ? {
                  aid,
                  SlardarInfoId: metric.Id,
                  Value: val,
                  Timestamp: current_start,
                  HourFromVersion: Math.ceil((current_start - version_ts) / 3600),
                  VersionCode: version_str,
                  device_level: deviceLevel,
                }
              : undefined;
          }),
      );
    }
    return Promise.all(ret_promise)
      .then(it => it.filter(c => c) as DBSlardarValue[])
      .then(ret => {
        this.logger.info(
          `[${metric.Id}-${version_str}] Pulled ${ret_promise.length} values, ${ret
            .map(it => `[${it.HourFromVersion} -> ${it.Value}]`)
            .join(',')}`,
        );
        return ret;
      });
  }
}
