// https://modelcontextprotocol.io/docs/concepts/tools#tool-definition-structure
export interface McpToolDefinition {
  name: string; // Unique identifier for the tool
  description?: string; // Human-readable description
  inputSchema: {
    // JSON Schema for the tool's parameters
    type: 'object';
    properties: {
      [key: string]: any; // 保留原有的任意属性
      _mcpToolsCallId?: string; // 预定义字段 _mcpToolsCallId
    }; // Tool-specific parameters
    required?: string[]; // Required parameters
  };
  annotations?: {
    // Optional hints about tool behavior
    title?: string; // Human-readable title for the tool
    readOnlyHint?: boolean; // If true, the tool does not modify its environment
    destructiveHint?: boolean; // If true, the tool may perform destructive updates
    idempotentHint?: boolean; // If true, repeated calls with same args have no additional effect
    openWorldHint?: boolean; // If true, tool interacts with external entities
  };
}

export interface McpToolRspItem {
  type: 'text';
  text: string;
}

export interface McpToolRsp {
  content: McpToolRspItem[];
}

export interface McpToolBaseHandler {
  toolDefinition: () => McpToolDefinition[];
  toolHandler: (toolName: string, args: any) => Promise<McpToolRsp>;
}

export const PaMcpCallId = 'pa-mcp-call-id';
/**
 * MCP Token 接口定义
 * 用于前后端共享的 MCP Token 数据结构
 */

/**
 * MCP Token 数据结构
 */
export interface McpToken {
  /**
   * 用户邮箱，作为唯一标识
   */
  email: string;

  /**
   * 加密后的 token
   */
  token: string;

  /**
   * 创建时间
   */
  createdAt?: Date;

  /**
   * 更新时间
   */
  updatedAt?: Date;
}

/**
 * MCP Token 请求响应
 */
export interface McpTokenResponse {
  /**
   * 加密后的 token
   */
  token: string;
}

/**
 * MCP Token 操作结果
 */
export enum McpTokenOperationResult {
  /**
   * 操作成功
   */
  Success = 'success',

  /**
   * 操作失败
   */
  Failed = 'failed',

  /**
   * Token 不存在
   */
  NotFound = 'not_found',

  /**
   * Token 无效
   */
  Invalid = 'invalid',
}
