import { Msg<PERSON><PERSON><PERSON><PERSON>, MsgStrategy, MsgTemplate, MsgType } from '@pa/shared/dist/src/message';
import {
  CardActionValue,
  CardButtonAction,
  CardButtonType,
  CardCallbackType,
  CardColumnElement,
  CardColumnSetElement,
  CardContentElement,
  CardElement,
  CardElementTag,
  CardElementTagV2,
  CardMarkdownElement,
  CardTemplate,
  CardTextTag,
  LarkCard,
} from '@pa/shared/dist/src/lark/larkCard';
import BaseCardUtils from '@pa/backend/dist/src/utils/baseCard';
import { LibraBaseInfo, LibraEvent, LibraEventType } from '@shared/libra/libraInfo';
import { add_suffix_ne } from '@shared/utils/tools';
import { User } from '@pa/shared/dist/src/core';
import {
  LibraFlightCloseMainType,
  LibraFlightCloseReopenSubTypeCanPreInterceptType,
  LibraMeegoTeamInfo,
  LibraNewInfo,
} from '@shared/libra/LibraNewInfo';
import { SettingsDetail } from '@shared/settings/SettingsDetail';
import {
  isP00Story,
  LibraChangeNotifyCardFixedObservers,
  libraDetailUrl,
  libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap,
  libraFlightCloseAttributionMainType2DisplayNameMap,
  libraFlightCloseAttributionOfflineSubType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubTypeCanPreInterceptType2DisplayNameMap,
  libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap,
  MeegoTeamFixedPOCs,
  MeegoTeamType,
} from '@shared/libra/libraManageUtils';
import { MAIN_HOST_HTTPS } from '@pa/shared/dist/src/appSettings/appSettings';
import { LibraChangeEvent } from '@shared/libra/LibraChangeEvent';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { LibraAppIds, LibraRegion } from '@shared/libra/commonLibra';

export enum LibraMsgCategory {
  Control = '实验管控通知',
  Patrol = '实验巡检通知',
  Operation = '实验操作通知',
}

// 话题群消息卡片类型
export enum LibraTopicGroupsCardType {
  ChangeNotifyOfYesterday = 1, // 昨日实验变更列表
  CloseAttributionDidFinishNotifyOfYesterday = 2, // 昨日已完成关闭归因的实验列表
  CloseAttributionDidFinishNotifyOfThisWeek = 3, // 本周已完成关闭归因的实验列表
  CloseAttributionDidFinishNotifyOfLastWeek = 4, // 上周已完成关闭归因的实验列表
  CloseAttributionNotFinishNotifyOfYesterday = 5, // 昨日尚未填写关闭归因的实验列表
  CloseAttributionNotFinishNotifyOfThisWeek = 6, // 本周尚未填写关闭归因的实验列表
  CloseAttributionNotFinishNotifyOfLastWeek = 7, // 上周尚未填写关闭归因的实验列表
  CloseAttributionNotFinishNotifyOfCustomDaysDuration = 8, // 自定义时长内尚未填写关闭归因的实验列表
}

const LibraTopicGroupsCardTitleMap: Record<LibraTopicGroupsCardType, string> = {
  [LibraTopicGroupsCardType.ChangeNotifyOfYesterday]: '昨日实验变更列表',
  [LibraTopicGroupsCardType.CloseAttributionDidFinishNotifyOfYesterday]: '昨日已完成关闭归因的实验列表',
  [LibraTopicGroupsCardType.CloseAttributionDidFinishNotifyOfThisWeek]: '本周已完成关闭归因的实验列表',
  [LibraTopicGroupsCardType.CloseAttributionDidFinishNotifyOfLastWeek]: '上周已完成关闭归因的实验列表',
  [LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfYesterday]: '昨日尚未填写关闭归因的实验列表',
  [LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfThisWeek]: '本周尚未填写关闭归因的实验列表',
  [LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfLastWeek]: '上周尚未填写关闭归因的实验列表',
  [LibraTopicGroupsCardType.CloseAttributionNotFinishNotifyOfCustomDaysDuration]: '尚未填写关闭归因的实验列表',
};

export interface LibraTopicGroupsSendCardProps {
  // 卡片类型
  cardType: LibraTopicGroupsCardType;
  // 查询语句（用于筛选哪些实验需要被卡片推送）
  query: any;
  // 日期范围（用于在卡片标题结尾处显示）
  dateRange: string;
  // 指定 TeamId
  fixedTeamIds?: number[];
  // 当数据为空时，仍发送卡片通知
  sendWhenDataEmpty?: boolean;
  // 卡片私发给个人（若开启此选项，则优先级最高，会优先发送给个人）
  sendPrivate?: boolean;
  // 自定义卡片标题
  customTitle?: string;
  // 自定义 chat_id，用于发送给特定群
  customChatId?: string;
  // 是否将 libra 操作人拉进群聊
  inviteLibraOperatorsJoinGroup?: boolean;
  // 是否尝试再次查找主责团队(默认：当找不到主责团队时，会尝试查找主责团队)
  dontTryToFindMainTeamId?: boolean;
}

const firstLibraOwnerAtEmailMarkdownStrByBaseInfo = (info: LibraBaseInfo) => {
  // 当操作人是 Libra 时，尝试读取第一个实验 Owner 的 email
  let firstOwnerEmail = '';
  if (info.flightOwners && info.flightOwners.length > 0) {
    firstOwnerEmail = info.flightOwners[0].email ?? '';
  }
  if (firstOwnerEmail.length > 0) {
    firstOwnerEmail = `<at email=${firstOwnerEmail}></at>`;
  } else {
    firstOwnerEmail = 'not found';
  }
  return firstOwnerEmail;
};

const firstLibraOwnerAtEmailMarkdownStrByLibraInfo = (info: LibraNewInfo) => {
  // 当操作人是 Libra 时，尝试读取第一个实验 Owner 的 email
  let firstOwnerEmail = '';
  if (info.flightInfo.owners && info.flightInfo.owners.length > 0) {
    firstOwnerEmail = info.flightInfo.owners[0].email ?? '';
  }
  if (firstOwnerEmail.length > 0) {
    firstOwnerEmail = `<at email=${firstOwnerEmail}></at>`;
  } else {
    firstOwnerEmail = 'not found';
  }
  return firstOwnerEmail;
};

const closeAttributionInfoDisplayStr = (libraInfo: LibraNewInfo | null) => {
  if (!libraInfo) {
    return '';
  }
  const { closeAttributionInfo } = libraInfo.flightInfo;
  if (closeAttributionInfo) {
    // 主要分类
    const mainTypeName = libraFlightCloseAttributionMainType2DisplayNameMap[closeAttributionInfo.mainType];
    // 细分分类
    let subTypeName = '';
    // 详细原因
    let subTypeDetailName = '';
    // 自定义原因填写补充
    let customReason = '';
    if (closeAttributionInfo.reopenSubType !== undefined) {
      // 细分分类(实验重开)
      subTypeName = libraFlightCloseAttributionReopenSubType2DisplayNameMap[closeAttributionInfo.reopenSubType];
      if (closeAttributionInfo.reopenSubTypeDetailType !== undefined) {
        // 当 LibraFlightCloseReopenType 时，对应的子分类(详细原因)
        subTypeDetailName =
          libraFlightCloseAttributionReopenSubTypeDetailType2DisplayNameMap[
            closeAttributionInfo.reopenSubTypeDetailType
          ];
      }
    } else if (closeAttributionInfo.fullReleaseSubType !== undefined) {
      // 细分分类(实验全量)
      subTypeName =
        libraFlightCloseAttributionFullReleaseSubType2DisplayNameMap[closeAttributionInfo.fullReleaseSubType];
    } else if (closeAttributionInfo.offlineSubType !== undefined) {
      // 细分分类(实验下线)
      subTypeName = libraFlightCloseAttributionOfflineSubType2DisplayNameMap[closeAttributionInfo.offlineSubType];
    }

    if (closeAttributionInfo.customReason !== undefined && closeAttributionInfo.customReason.length > 0) {
      // 自定义原因填写
      customReason = `${closeAttributionInfo.customReason}(补充原因)`;
    }

    const attributionArray: string[] = [];
    if (mainTypeName.length > 0) {
      attributionArray.push(mainTypeName);
    }
    if (subTypeName.length > 0) {
      attributionArray.push(subTypeName);
    }
    if (subTypeDetailName.length > 0) {
      attributionArray.push(subTypeDetailName);
    }
    if (customReason.length > 0) {
      attributionArray.push(customReason);
    }
    return attributionArray.join('/');
  }

  return '';
};

const closeAttributionReopenCanPreInterceptDisplayStr = (type: LibraFlightCloseReopenSubTypeCanPreInterceptType) => {
  const displayStr = libraFlightCloseAttributionReopenSubTypeCanPreInterceptType2DisplayNameMap[type];
  if (displayStr.length > 0) {
    // 截取掉“是，”和“否，”
    if (displayStr.startsWith('是，') || displayStr.startsWith('否，')) {
      return `${displayStr.slice(2)}`;
    }
    return displayStr;
  }
  return '';
};

// 实验变更时间(YYYY-MM-DD HH:mm:ss 格式)
const libraChangeTimeStr = (timestamp: number) => {
  // 使用插件
  dayjs.extend(utc);
  dayjs.extend(timezone);
  return dayjs(timestamp)
    .utc() // 将时间标记为 UTC
    .tz('Asia/Shanghai') // 转换为东八区
    .format('YYYY-MM-DD HH:mm:ss');
};

// 是否是 Web 的实验（Web 实验一般需要隐藏实验 Owner）
const isWebLibra = (info: LibraBaseInfo) =>
  info.appIds.includes(LibraAppIds.cc_web) || info.appIds.includes(LibraAppIds.lv_web);

const isWebLibraByLibraInfo = (info: LibraNewInfo) => info.libraAppId === 381 || info.libraAppId === 1071;

// 添加 Meego 相关信息（需求名称、PM、QA 等）
const meegoRelatedInfoDisplayStrMap = (libraInfo: LibraNewInfo | null) => {
  if (!libraInfo || !libraInfo.meegoInfo) {
    return {
      meegoNames: '',
      meegoQAs: '',
      meegoPMs: '',
    };
  }

  // 补充需求相关信息
  let meegoNames = '';
  let meegoPMs = '';
  let meegoQAs = '';
  const meegoRelatedRoleStr = '';
  const meegoListCount = libraInfo.meegoInfo.length;
  for (let i = 0; i < meegoListCount; i++) {
    const singleMeegoInfo = libraInfo.meegoInfo[i];
    // 需求名称
    if (i === meegoListCount - 1) {
      meegoNames = `${meegoNames}[${singleMeegoInfo.name}](${singleMeegoInfo.url})`;
    } else {
      meegoNames = `${meegoNames}[${singleMeegoInfo.name}](${singleMeegoInfo.url}),`;
    }
    if (singleMeegoInfo.pmOwners) {
      for (const pm of singleMeegoInfo.pmOwners) {
        // 需求 PM
        meegoPMs = `${meegoPMs}<at email=${pm.email}></at>`;
      }
    }
    if (singleMeegoInfo.clientQAs) {
      for (const qa of singleMeegoInfo.clientQAs) {
        // 需求业务 QA
        meegoQAs = `${meegoQAs}<at email=${qa.email}></at>`;
      }
    }
  }

  return {
    meegoNames,
    meegoQAs,
    meegoPMs,
  };
};

const appendMeegoRelatedInfoForElements = (elements: CardElement[], libraInfo: LibraNewInfo | null) => {
  const { meegoNames, meegoQAs, meegoPMs } = meegoRelatedInfoDisplayStrMap(libraInfo);
  if (meegoNames.length > 0) {
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `**需求名称**：${meegoNames}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
  }

  let meegoRelatedRoles = '';
  if (meegoPMs.length > 0 || meegoQAs.length > 0) {
    if (meegoPMs.length > 0) {
      meegoRelatedRoles = `${meegoRelatedRoles}PM ${meegoPMs}`;
    }
    if (meegoQAs.length > 0) {
      const qaLabel = meegoPMs.length > 0 ? ' QA' : 'QA';
      meegoRelatedRoles = `${meegoRelatedRoles}${qaLabel} ${meegoQAs}`;
    }
  }
  if (meegoRelatedRoles.length > 0) {
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `**需求相关人员**：${meegoRelatedRoles}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
  }
};

// 添加 Libra 变更关注人
const libraChangeObserverDisplayStr = (libraInfo: LibraNewInfo | null) => {
  if (!libraInfo || !libraInfo.meegoTeamInfo) {
    return '';
  }

  // 根据虚拟业务团队添加固定关注人
  const observersList: string[] = [];
  for (const teamInfo of libraInfo.meegoTeamInfo) {
    const observers = LibraChangeNotifyCardFixedObservers(teamInfo.teamId);
    for (const singleObserver of observers) {
      if (!observersList.includes(singleObserver)) {
        observersList.push(singleObserver);
      }
    }
  }

  if (observersList.length > 0) {
    // 添加实验变更关注人
    let observers = '';
    for (const observer of observersList) {
      observers = `${observers}<at email=${observer}></at>`;
    }
    return observers;
  }

  return '';
};

const allRelatedUsersDisplayStr = (
  elements: CardElement[],
  info: LibraBaseInfo,
  libraInfo: LibraNewInfo | null,
  meegoQAs: string,
  meegoPMs: string,
) => {
  // 添加实验关注人
  const observers = libraChangeObserverDisplayStr(libraInfo);
  // 其他周知人（汇总实验 Owner、PM、QA等）
  const allRelatedUsers: string[] = [];
  // 实验 Owner
  let owners = '';
  for (const owner of info.flightOwners) {
    owners = `${owners}<at email=${owner.email}></at>`;
  }
  if (owners.length > 0 && !isWebLibra(info)) {
    allRelatedUsers.push(`实验Owner: ${owners}`);
  }
  if (meegoQAs.length > 0) {
    allRelatedUsers.push(`QA: ${meegoQAs}`);
  }
  if (meegoPMs.length > 0) {
    allRelatedUsers.push(`PM: ${meegoPMs}`);
  }
  if (observers.length > 0) {
    allRelatedUsers.push(`固定关注人: ${observers}`);
  }
  if (allRelatedUsers.length > 0) {
    return allRelatedUsers.join(' ');
  }

  return '';
};

const appendAllRelatedUsersForElements = (
  elements: CardElement[],
  info: LibraBaseInfo,
  libraInfo: LibraNewInfo | null,
  meegoQAs: string,
  meegoPMs: string,
) => {
  const users = allRelatedUsersDisplayStr(elements, info, libraInfo, meegoQAs, meegoPMs);
  if (users.length > 0) {
    elements.push({
      tag: CardElementTag.markdown,
      content: `> *其他关注人周知*\n*${users}*`,
      text_size: 'notation',
    } as CardMarkdownElement);
  }
};

export class MsgLibraStartV2 implements MsgTemplate {
  name = '实验开启通知';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Control;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(info: LibraBaseInfo, traffic: string, libraInfo: LibraNewInfo | null, libraEvent: LibraEvent) {
    this.name = process.env.NODE_ENV === 'development' ? '实验开启通知(测试环境)' : '实验开启通知';
    if (isP00Story(libraInfo)) {
      // 确定是否为 P00 需求
      this.name = `P00需求-${this.name}`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `【${info.appNames.join(',')}】[${info.flightName}](${info.flightUrl}) 实验**已开启**`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 补充需求相关信息-需求名称
    const { meegoNames, meegoQAs, meegoPMs } = meegoRelatedInfoDisplayStrMap(libraInfo);
    const operatorStr =
      info.operator === 'Libra' || info.operator === 'OpenAPI'
        ? '操作人：Libra 平台'
        : `操作人：<at email=${add_suffix_ne('@bytedance.com')(info.operator)}></at>`;
    let detailInfos = `- ${operatorStr}\n- 实验类型：${info.flightType}\n- 起始流量：${traffic}`;
    if (info.appIds.includes(LibraAppIds.cc_web) || info.appIds.includes(LibraAppIds.lv_web)) {
      // 如果是 Web 实验，则需要隐藏实验 Owner
      detailInfos = `- 实验类型：${info.flightType}\n- 起始流量：${traffic}`;
    }
    if (meegoNames.length > 0) {
      detailInfos = `${detailInfos}\n- 需求名称：${meegoNames}`;
    }
    detailInfos = `${detailInfos}\n- 开启时间：${libraChangeTimeStr(Number(libraEvent.ts) * 1000)}`;
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: detailInfos,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    // 其他周知人（汇总实验 Owner、PM、QA等）
    appendAllRelatedUsersForElements(elements, info, libraInfo, meegoQAs, meegoPMs);
    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

export class MsgLibraTrafficChangeV2 implements MsgTemplate {
  name = '实验流量变更通知';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Control;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(
    info: LibraBaseInfo,
    traffic: string,
    trafficChangeType: string,
    startTraffic = '',
    endTraffic = '',
    libraInfo: LibraNewInfo | null,
    libraEvent: LibraEvent,
  ) {
    this.name = process.env.NODE_ENV === 'development' ? '实验流量变更通知(测试环境)' : '实验流量变更通知';
    if (isP00Story(libraInfo)) {
      // 确定是否为 P00 需求
      this.name = `P00需求-${this.name}`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.yellow,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `【${info.appNames.join(',')}】[${info.flightName}](${info.flightUrl}) 流量变更为：**${traffic}**`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 补充需求相关信息-需求名称
    const { meegoNames, meegoQAs, meegoPMs } = meegoRelatedInfoDisplayStrMap(libraInfo);
    let trafficChangeDetail = '';
    if (startTraffic.length > 0 && endTraffic.length > 0) {
      trafficChangeDetail = `(${startTraffic} -> ${endTraffic})`;
    }
    const operatorStr =
      info.operator === 'Libra' || info.operator === 'OpenAPI'
        ? '操作人：Libra 平台'
        : `操作人：<at email=${add_suffix_ne('@bytedance.com')(info.operator)}></at>`;
    let detailInfos = `- ${operatorStr}\n- 实验类型：${info.flightType}\n- 变更类型：${trafficChangeType === 'increase' ? `增加流量${trafficChangeDetail}` : `减少流量${trafficChangeDetail}`}`;
    if (isWebLibra(info)) {
      // 如果是 Web 实验，则需要隐藏实验 Owner
      detailInfos = `- 实验类型：${info.flightType}\n- 变更类型：${trafficChangeType === 'increase' ? `增加流量${trafficChangeDetail}` : `减少流量${trafficChangeDetail}`}`;
    }
    if (meegoNames.length > 0) {
      detailInfos = `${detailInfos}\n- 需求名称：${meegoNames}`;
    }
    detailInfos = `${detailInfos}\n- 变更时间：${libraChangeTimeStr(Number(libraEvent.ts) * 1000)}`;
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: detailInfos,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    // 其他周知人（汇总实验 Owner、PM、QA等）
    appendAllRelatedUsersForElements(elements, info, libraInfo, meegoQAs, meegoPMs);
    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

export class MsgLibraFullV2 implements MsgTemplate {
  name = '实验全量通知';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Control;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(
    info: LibraBaseInfo,
    reportUrl: string,
    daOwners: User[],
    libraInfo: LibraNewInfo | null,
    libraEvent: LibraEvent,
  ) {
    this.name = process.env.NODE_ENV === 'development' ? '实验全量通知(测试环境)' : '实验全量通知';
    if (isP00Story(libraInfo)) {
      // 确定是否为 P00 需求
      this.name = `P00需求-${this.name}`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.green,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    const das = daOwners.map(v => `<at email=${v.email}></at>`).join('');
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `【${info.appNames.join(',')}】[${info.flightName}](${info.flightUrl}) 实验**已全量**`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 突出下一步重点操作
    let operateTips = '';
    let firstOwnerEmail = '';
    if (info.operator === 'Libra' || info.operator === 'OpenAPI') {
      // 当操作人是 Libra 时，尝试读取第一个实验 Owner 的 email
      if (info.flightOwners && info.flightOwners.length > 0) {
        firstOwnerEmail = info.flightOwners[0].email ?? '';
      }
      if (firstOwnerEmail.length > 0) {
        firstOwnerEmail = `<at email=${firstOwnerEmail}></at>`;
      } else {
        firstOwnerEmail = '实验 Owner';
      }
    }
    let operatorStr =
      info.operator === 'Libra' || info.operator === 'OpenAPI'
        ? `${firstOwnerEmail}`
        : `<at email=${add_suffix_ne('@bytedance.com')(info.operator)}></at>`;
    if (isWebLibra(info)) {
      // 如果是 Web 实验，则需要隐藏实验 Owner
      operatorStr = '实验 Owner';
    }

    if (operatorStr.includes('@bytedance.com')) {
      // 有邮箱
      operatorStr = `请 ${operatorStr}`;
    } else {
      // 无邮箱
      operatorStr = `请${operatorStr}`;
    }

    if (reportUrl) {
      if (das.length > 0) {
        operateTips = `**<font color='red'>下一步操作：${operatorStr} 邀请 DA 同学 ${das} 及时 review 实验报告</font>**`;
      } else {
        operateTips = `**<font color='red'>下一步操作：${operatorStr} 邀请 DA 同学及时 review 实验报告</font>**`;
      }
    } else {
      operateTips = `**<font color='red'>下一步操作：${operatorStr} 邀请 DA 同学及时 review 实验收益</font>**`;
    }

    elements.push({
      tag: CardElementTagV2.markdown,
      content: operateTips,
    } as CardMarkdownElement);
    // 补充需求相关信息-需求名称
    const { meegoNames, meegoQAs, meegoPMs } = meegoRelatedInfoDisplayStrMap(libraInfo);
    let detailInfos = `- 实验类型：${info.flightType}`;
    if (meegoNames.length > 0) {
      detailInfos = `${detailInfos}\n- 需求名称：${meegoNames}`;
    }
    detailInfos = `${detailInfos}\n- 全量时间：${libraChangeTimeStr(Number(libraEvent.ts) * 1000)}`;
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: detailInfos,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 其他周知人（汇总实验 Owner、PM、QA等）
    appendAllRelatedUsersForElements(elements, info, libraInfo, meegoQAs, meegoPMs);
    const cardButtonActions: CardButtonAction[] = [];
    if (reportUrl.length > 0) {
      // 实验报告按钮
      cardButtonActions.push({
        tag: CardElementTagV2.button,
        url: reportUrl,
        text: {
          tag: CardTextTag.plain_text,
          content: `查看实验报告`,
        },
        type: CardButtonType.primary,
      } as CardButtonAction);
    }
    // 按钮排布 column
    if (cardButtonActions.length > 0) {
      // 按钮排布 column
      const buttonColumns: CardColumnElement[] = [];
      for (const buttonAction of cardButtonActions) {
        buttonColumns.push({
          tag: CardElementTagV2.column,
          width: 'auto',
          weight: 1,
          vertical_align: 'top',
          elements: [buttonAction],
        } as CardColumnElement);
      }
      const buttonColumnSet = {
        tag: CardElementTagV2.columnSet,
        flex_mode: 'flow',
        background_style: 'default',
        columns: buttonColumns,
      } as CardColumnSetElement;
      elements.push(buttonColumnSet);
    }
    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

// 实验关闭通知新样式
export class MsgLibraOfflineV2 implements MsgTemplate {
  name = '实验关闭通知';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Control;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(
    info: LibraBaseInfo,
    stopReason: string,
    libraInfo: LibraNewInfo | null,
    libraEvent: LibraEvent,
    reportUrl?: string,
  ) {
    this.name = process.env.NODE_ENV === 'development' ? '实验关闭通知(测试环境)' : '实验关闭通知';
    if (isP00Story(libraInfo)) {
      // 确定是否为 P00 需求
      this.name = `P00需求-${this.name}`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.orange,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `【${info.appNames.join(',')}】[${info.flightName}](${info.flightUrl}) 实验**已关闭**`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 判断是否已经填写了关闭归因（排除重开的 case）
    let hasFinishedCloseAttribution = false;
    if (libraInfo && libraInfo.flightInfo.closeAttributionInfo) {
      // 有了归因 info，则标记为已填写
      hasFinishedCloseAttribution = true;
    }
    // 突出下一步重点操作
    let operateTips = '';
    let firstOwnerEmail = '';
    if (info.operator === 'Libra' || info.operator === 'OpenAPI') {
      // 当操作人是 Libra 时，尝试读取第一个实验 Owner 的 email
      if (info.flightOwners && info.flightOwners.length > 0) {
        firstOwnerEmail = info.flightOwners[0].email ?? '';
      }
      if (firstOwnerEmail.length > 0) {
        firstOwnerEmail = `<at email=${firstOwnerEmail}></at>`;
      } else {
        firstOwnerEmail = '实验 Owner';
      }
    }
    let operatorStr =
      info.operator === 'Libra' || info.operator === 'OpenAPI'
        ? `${firstOwnerEmail}`
        : `<at email=${add_suffix_ne('@bytedance.com')(info.operator)}></at>`;
    if (isWebLibra(info)) {
      // 如果是 Web 实验，则需要隐藏实验 Owner
      operatorStr = '实验 Owner';
    }

    if (operatorStr.includes('@bytedance.com')) {
      // 有邮箱
      operatorStr = `请 ${operatorStr}`;
    } else {
      // 无邮箱
      operatorStr = `请${operatorStr}`;
    }
    if (reportUrl) {
      operateTips = `**<font color='red'>下一步操作：${operatorStr} 填写实验关闭归因，并查看实验报告及时 review 数据</font>**`;
    } else {
      operateTips = `**<font color='red'>下一步操作：${operatorStr} 填写实验关闭归因</font>**`;
    }
    if (!hasFinishedCloseAttribution) {
      // 没有填写，就告知下一步应该怎么操作；否则，就不需要
      elements.push({
        tag: CardElementTagV2.markdown,
        content: operateTips,
      } as CardMarkdownElement);
    }
    // 补充需求相关信息-需求名称
    const { meegoNames, meegoQAs, meegoPMs } = meegoRelatedInfoDisplayStrMap(libraInfo);
    let detailInfos = `- 实验类型：${info.flightType}\n`;
    if (hasFinishedCloseAttribution) {
      // 如果不是重开，且有关闭归因，则自动填充并展示实验归因
      detailInfos = `- 实验类型：${info.flightType}\n- 关闭归因：**${closeAttributionInfoDisplayStr(libraInfo)}**`;
    }
    if (meegoNames.length > 0) {
      detailInfos = `${detailInfos}\n- 需求名称：${meegoNames}`;
    }
    detailInfos = `${detailInfos}\n- 关闭时间：${libraChangeTimeStr(Number(libraEvent.ts) * 1000)}`;
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: detailInfos,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 实验 Owner
    let owners = '';
    for (const owner of info.flightOwners) {
      owners = `${owners}<at email=${owner.email}></at>`;
    }
    // 其他周知人（汇总实验 Owner、PM、QA等）
    const otherNotifyUsers = allRelatedUsersDisplayStr(elements, info, libraInfo, meegoQAs, meegoPMs);
    let notionStr = `> *其他关注人周知*\n*${otherNotifyUsers}*`;
    if (!hasFinishedCloseAttribution) {
      // 如果没有填写归因（一般是重开类型实验），追加一下 Libra 实验关闭归因
      notionStr = `> *其他关注人周知*\n*${otherNotifyUsers}*\n*Libra关闭原因：${stopReason}（Libra 现有归因无法满足诉求，请通过纸飞机补充填写）*`;
    }
    elements.push({
      tag: CardElementTag.markdown,
      content: notionStr,
      text_size: 'notation',
    } as CardMarkdownElement);

    let domainHost = MAIN_HOST_HTTPS;
    if (process.env.NODE_ENV === 'development') {
      domainHost = 'http://localhost:8080';
    }

    const cardButtonActions: CardButtonAction[] = [];
    const sheetUrl = `${domainHost}/libra/lark-sidebar-submit?libra_flight_id=${info.flightId}&sidebar_type=0`;
    const editLibraCloseAttributionUrl = encodeURIComponent(sheetUrl);
    const closeAttributionFinalUrl = `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&width=480&reload=false&url=${editLibraCloseAttributionUrl}`;
    if (!hasFinishedCloseAttribution) {
      // 没有填写，就展示填写按钮
      cardButtonActions.push({
        // 填写实验关闭归因
        tag: CardElementTagV2.button,
        url: closeAttributionFinalUrl,
        text: {
          tag: CardTextTag.plain_text,
          content: `填写实验关闭归因`,
        },
        type: CardButtonType.primary,
      } as CardButtonAction);
    }
    if (reportUrl) {
      cardButtonActions.push({
        tag: CardElementTagV2.button,
        url: reportUrl,
        text: {
          tag: CardTextTag.plain_text,
          content: `查看实验报告`,
        },
        type: CardButtonType.primary,
      } as CardButtonAction);
    }
    // 按钮排布 column
    const buttonColumns: CardColumnElement[] = [];
    for (const buttonAction of cardButtonActions) {
      buttonColumns.push({
        tag: CardElementTagV2.column,
        width: 'auto',
        weight: 1,
        vertical_align: 'top',
        elements: [buttonAction],
      } as CardColumnElement);
    }
    const buttonColumnSet = {
      tag: CardElementTagV2.columnSet,
      flex_mode: 'flow',
      background_style: 'default',
      columns: buttonColumns,
    } as CardColumnSetElement;
    elements.push(buttonColumnSet);
    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

export class MsgLibraStartReview implements MsgTemplate {
  name = '实验关闭通知';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = '实验管控通知';
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;
}

export class MsgLibraOverdue implements MsgTemplate {
  name = '实验超期巡检提醒';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Patrol;
  strategy = MsgStrategy.Timer;
  msgContent: LarkCard | string;

  constructor(info: LibraNewInfo, version: string, gap: number) {
    const baseCard = BaseCardUtils.buildBaseCard({
      title: this.name,
      template: CardTemplate.orange,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `检测到以下实验所关联的版本 **${version}** 已全量发布 **<font color="red">${gap}</font>** 天，请注意及时Review实验报告并操作全量实验！`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `**实验名称：**[${info.flightInfo.name}](${libraDetailUrl(info.flightInfo.region, info.flightInfo.id)})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    if (!isWebLibraByLibraInfo(info)) {
      let owners = '';
      for (const owner of info.flightInfo.owners) {
        owners = `${owners}<at email=${owner.email}></at>`;
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `**实验Owner：**${owners}`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    }
    baseCard.elements = elements;
    this.msgContent = baseCard;
  }
}

export class MsgSettingSolidify implements MsgTemplate {
  name = 'Settings实验固化提醒';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Control;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(info: LibraNewInfo, realReason: string, items: SettingsDetail[]) {
    const baseCard = BaseCardUtils.buildBaseCard({
      title: this.name,
      template: CardTemplate.orange,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTag.div,
      text: {
        // eslint-disable-next-line max-len
        content: `检测到实验关闭原因为【${realReason}】，以下关联的 settings 配置尚未固化，请及时到 settings 平台完成固化（说明：固化状态仅通过 settings 的“实验固化”操作改变，若已使用其他方式完成固化，请忽略该提醒）`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `**实验名称：**[${info.flightInfo.name}](${libraDetailUrl(info.flightInfo.region, info.flightInfo.id)})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    elements.push({
      tag: CardElementTag.div,
      text: {
        content: `**未固化Setting Key：**\n${items
          .map(
            item =>
              `- [${item.name}](https://cloud.bytedance.net/appSettings-v2/detail/config/${item.item_id}/detail/ab-test)`,
          )
          .join('\n')}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    if (!isWebLibraByLibraInfo(info)) {
      let owners = '';
      for (const owner of info.flightInfo.owners) {
        owners = `${owners}<at email=${owner.email}></at>`;
      }
      elements.push({
        tag: CardElementTag.div,
        text: {
          content: `**实验Owner：**${owners}`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    }
    baseCard.elements = elements;
    this.msgContent = baseCard;
  }
}

export class MsgLibraCreateSuccess implements MsgTemplate {
  name = '纸飞机实验创建成功';
  type = MsgType.DirectChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Operation;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(info: LibraNewInfo) {
    const baseCard = BaseCardUtils.buildBaseCard({
      title: this.name,
      template: CardTemplate.green,
      config: { enable_forward: true, update_multi: true },
    });
    baseCard.elements = [
      {
        tag: CardElementTag.div,
        text: {
          content: `纸飞机已成功创建实验 [${info.flightInfo.name}](${libraDetailUrl(info.flightInfo.region, info.flightInfo.id)}) , 请及时关注实验状态`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement,
    ];
    this.msgContent = baseCard;
  }
}

// 昨日实验变更通知卡片
export class LibraChangeListSummaryOfYesterday implements MsgTemplate {
  name = '昨日实验变更列表';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Patrol;
  strategy = MsgStrategy.Timer;
  msgContent: LarkCard | string;

  constructor(
    cardType: LibraTopicGroupsCardType,
    libraMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }>,
    dateRange: string,
    meegoTeamType: MeegoTeamType,
    meegoTeamInfos: LibraMeegoTeamInfo[],
  ) {
    this.name = process.env.NODE_ENV === 'development' ? '昨日实验变更列表(测试环境)' : '昨日实验变更列表';
    if (dateRange.length > 0) {
      this.name = `${this.name}-${dateRange}`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.orange,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    if (libraMap.size === 0) {
      elements.push({
        tag: CardElementTagV2.markdown,
        content: `✅ 无任何实验变更`,
      } as CardMarkdownElement);

      // 直接返回
      baseCard.body = {
        elements,
      };
      this.msgContent = baseCard;
      return;
    }

    const meegoTeamAndLibraInfosMap = new Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }[]>();

    // 使用插件
    dayjs.extend(utc);
    dayjs.extend(timezone);

    // 需要按照 Meego 团队拆分统计（遍历 libraMap）
    for (const [key, value] of libraMap) {
      const { event } = value;
      const { libraInfo } = value;
      if (libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0) {
        // 获取 teamInfo 里面的所有 teamId
        const teamIds = libraInfo.meegoTeamInfo.map(team => team.teamId);
        // 遍历 teamIds，放到 meegoTeamAndLibraInfosMap 里面
        for (const teamId of teamIds) {
          if (meegoTeamAndLibraInfosMap.has(teamId)) {
            // 如果已经存在，直接 push
            meegoTeamAndLibraInfosMap.get(teamId)?.push({ event, libraInfo });
          } else {
            // 如果不存在，先创建
            meegoTeamAndLibraInfosMap.set(teamId, [{ event, libraInfo }]);
          }
        }
      }
    }

    // 遍历 meegoTeamAndLibraInfosMap，统计各个团队的实验变更情况
    for (const [key, value] of meegoTeamAndLibraInfosMap) {
      const teamId = Number(key);
      const teamName = meegoTeamInfos.find(team => team.teamId === teamId)?.teamName ?? '';
      if (teamName.length === 0) {
        // 如果找不到 teamName，一般是这个 libra 对应了多个 Meego 团队（并且是跨端，比如 移动端 + web），此时跳过一下，不处理
        continue;
      }
      let count = 0; // 统计每个团队下的变更实验数量
      elements.push({
        tag: CardElementTagV2.markdown,
        content: `**${teamName}** <number_tag>${value.length}</number_tag>`,
        text_size: 'heading',
      } as CardMarkdownElement);
      for (const libra of value) {
        count = count + 1;
        const { event } = libra;
        const { libraInfo } = libra;
        let libraInfoStr = `${count}. [${event.flightName}](${event.flightUrl})`;
        if (event.isBigFlow) {
          libraInfoStr = `${libraInfoStr} <text_tag color='violet'>大流量</text_tag>`;
        }
        if (event.inPeakTime) {
          libraInfoStr = `${libraInfoStr} <text_tag color='orange'>高峰期</text_tag>`;
        }
        const operatorStr =
          event.eventOperator === 'Libra' || event.eventOperator === 'OpenAPI'
            ? '操作人：Libra 平台'
            : `操作人：<at email=${add_suffix_ne('@bytedance.com')(event.eventOperator)}></at>`;
        let libraChangeInfoStr = `变更描述：`;
        if (event.eventType === LibraEventType.FlightStart) {
          libraChangeInfoStr = `${libraChangeInfoStr}**实验开启**`;
        } else if (event.eventType === LibraEventType.FlightStop) {
          libraChangeInfoStr = `${libraChangeInfoStr}**实验关闭**`;
        } else if (event.eventType === LibraEventType.FlightChangeTraffic) {
          const { eventInfluence } = event;
          // "eventInfluence" : "实验流量10.0% -> 100.0%，实验结束时间2025-04-05 17:56:58 -> 2025-04-05 17:56:58",
          const split = eventInfluence.split('，');
          if (split.length === 2) {
            libraChangeInfoStr = `${libraChangeInfoStr}**流量变更（${split[0].replace('实验流量', '')}）**`;
          } else {
            libraChangeInfoStr = `${libraChangeInfoStr}**流量变更**`;
          }
        } else if (event.eventType === LibraEventType.FlightFull) {
          libraChangeInfoStr = `${libraChangeInfoStr}**实验全量**`;
        }
        // 转换时间到东八区
        const changeTimeStr = `变更时间：${libraChangeTimeStr(event.ts * 1000)}`;
        const libraTrafficStr = `实验流量: ${event.traffic ?? ''}`;
        let libraMeegoInfoStr = '关联 Meego：';
        if (libraInfo.meegoInfo && libraInfo.meegoInfo.length > 0) {
          const meegoCount = libraInfo.meegoInfo.length;
          for (let i = 0; i < meegoCount; i++) {
            if (meegoCount === 1) {
              // 只有一个需求
              libraMeegoInfoStr = `${libraMeegoInfoStr}[链接](${libraInfo.meegoInfo[i].url})`;
            } else {
              if (i === 0) {
                libraMeegoInfoStr = `${libraMeegoInfoStr}[链接${i + 1}](${libraInfo.meegoInfo[i].url})`;
              } else {
                libraMeegoInfoStr = `${libraMeegoInfoStr}，[链接${i + 1}](${libraInfo.meegoInfo[i].url})`;
              }
            }
          }
        } else {
          libraMeegoInfoStr = `${libraMeegoInfoStr}无`;
        }

        // 第一行
        // elements.push({
        //   tag: CardElementTagV2.markdown,
        //   content: `${libraInfoStr}`,
        // } as CardMarkdownElement);
        // 其余行
        let notifyContent = `${libraInfoStr}\n> ${operatorStr}\n> ${libraChangeInfoStr}\n> ${changeTimeStr}\n> ${libraTrafficStr}\n> ${libraMeegoInfoStr}`;
        if (isWebLibraByLibraInfo(libraInfo)) {
          // 如果是 Web 实验，则不展示实验 Owner
          notifyContent = `${libraInfoStr}\n> ${libraChangeInfoStr}\n> ${changeTimeStr}\n> ${libraTrafficStr}\n> ${libraMeegoInfoStr}`;
        }
        elements.push({
          tag: CardElementTagV2.markdown,
          // eslint-disable-next-line max-len
          content: notifyContent,
          text_size: 'notation',
        } as CardMarkdownElement);
      }
      // 添加一下分割线
      elements.push({
        tag: CardElementTagV2.markdown,
        content: '<hr>',
      } as CardMarkdownElement);
    }

    // 添加一下备注（数据解释说明）
    elements.push({
      tag: CardElementTagV2.markdown,
      content:
        "*<font color='grey'>[说明]大流量：开启实验时流量即为100%；高峰期：周六/周日，以及工作日18:00-次日10:00、12:00-14:00</font>*",
      text_size: 'x-small',
    } as CardMarkdownElement);

    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

// 填写了实验关闭归因的实验列表（通知卡片）
export class LibraFinishCloseAttributionSummary implements MsgTemplate {
  name = '已完成关闭归因的实验列表';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Patrol;
  strategy = MsgStrategy.Timer;
  msgContent: LarkCard | string;

  constructor(
    cardType: LibraTopicGroupsCardType,
    libraMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }>,
    dateRange: string,
    meegoTeamType: MeegoTeamType,
    meegoTeamInfos: LibraMeegoTeamInfo[],
  ) {
    this.name =
      process.env.NODE_ENV === 'development'
        ? `${LibraTopicGroupsCardTitleMap[cardType]}(测试环境)`
        : `${LibraTopicGroupsCardTitleMap[cardType]}`;
    if (dateRange.length > 0) {
      this.name = `${this.name}(${dateRange})`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.green,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];

    const meegoTeamAndLibraInfosMap = new Map<number, LibraNewInfo[]>();

    // 使用插件
    dayjs.extend(utc);
    dayjs.extend(timezone);

    // 需要按照 Meego 团队拆分统计（遍历 libraMap）
    for (const [key, value] of libraMap) {
      const { event } = value;
      const { libraInfo } = value;
      if (libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0) {
        // 获取 teamInfo 里面的所有 teamId
        const teamIds = libraInfo.meegoTeamInfo.map(team => team.teamId);
        // 遍历 teamIds，放到 meegoTeamAndLibraInfosMap 里面
        for (const teamId of teamIds) {
          if (meegoTeamAndLibraInfosMap.has(teamId)) {
            // 如果已经存在，直接 push
            meegoTeamAndLibraInfosMap.get(teamId)?.push(libraInfo);
          } else {
            // 如果不存在，先创建
            meegoTeamAndLibraInfosMap.set(teamId, [libraInfo]);
          }
        }
      }
    }

    // 遍历 meegoTeamAndLibraInfosMap，统计各个团队的实验变更情况
    const mapSize = meegoTeamAndLibraInfosMap.size;
    let index = -1;
    for (const [key, value] of meegoTeamAndLibraInfosMap) {
      index = index + 1; // 从 0 开始计索引
      const teamId = Number(key);
      const teamName = meegoTeamInfos.find(team => team.teamId === teamId)?.teamName ?? '';
      if (teamName.length === 0) {
        // 如果找不到 teamName，一般是这个 libra 对应了多个 Meego 团队（并且是跨端，比如 移动端 + web），此时跳过一下，不处理
        continue;
      }
      let count = 0; // 统计每个团队下的变更实验数量
      elements.push({
        tag: CardElementTagV2.markdown,
        content: `**${teamName}** <number_tag>${value.length}</number_tag>`,
        text_size: 'heading',
      } as CardMarkdownElement);
      for (const libraInfo of value) {
        count = count + 1;
        const { flightInfo } = libraInfo;
        const libraInfoStr = `${count}. [${flightInfo.name}](${libraDetailUrl(flightInfo.region, flightInfo.id)})`;
        const { closeAttributionInfo } = flightInfo;
        let operatorStr = '操作人：未知';
        if (closeAttributionInfo && closeAttributionInfo.updateUser) {
          if (
            closeAttributionInfo.updateUser.includes('@bytedance.com') &&
            !closeAttributionInfo.updateUser.includes('Libra') &&
            !closeAttributionInfo.updateUser.includes('OpenAPI')
          ) {
            operatorStr = `操作人：<at email=${closeAttributionInfo.updateUser}></at>`;
          } else {
            operatorStr = `操作人：${closeAttributionInfo.updateUser.replace('@bytedance.com', '')}`;
          }
        }
        let libraChangeInfoStr = `关闭归因：`;
        let reopenCanPreIntercept = ''; // 实验重开-代码相关问题-是否可前置拦截
        if (closeAttributionInfo) {
          libraChangeInfoStr = `${libraChangeInfoStr}**${closeAttributionInfoDisplayStr(libraInfo)}**`;
          if (closeAttributionInfo.reopenSubTypeCanPreInterceptType !== undefined) {
            reopenCanPreIntercept = `可否前置拦截：${closeAttributionReopenCanPreInterceptDisplayStr(closeAttributionInfo.reopenSubTypeCanPreInterceptType)}`;
          }
        } else {
          libraChangeInfoStr = `${libraChangeInfoStr}**未知**`;
        }
        // 转换时间到东八区
        let changeTimeStr = '填写时间：未知';
        if (closeAttributionInfo?.updateTime) {
          changeTimeStr = `填写时间：${libraChangeTimeStr(closeAttributionInfo?.updateTime * 1000)}`;
        }

        const libraTrafficStr = `实验流量: ${flightInfo.trafficInfo.currentTrafficValue * 100}%`;
        let libraMeegoInfoStr = '关联 Meego：';
        if (libraInfo.meegoInfo && libraInfo.meegoInfo.length > 0) {
          const meegoCount = libraInfo.meegoInfo.length;
          for (let i = 0; i < meegoCount; i++) {
            if (meegoCount === 1) {
              // 只有一个需求
              libraMeegoInfoStr = `${libraMeegoInfoStr}[链接](${libraInfo.meegoInfo[i].url})`;
            } else {
              if (i === 0) {
                libraMeegoInfoStr = `${libraMeegoInfoStr}[链接${i + 1}](${libraInfo.meegoInfo[i].url})`;
              } else {
                libraMeegoInfoStr = `${libraMeegoInfoStr}，[链接${i + 1}](${libraInfo.meegoInfo[i].url})`;
              }
            }
          }
        } else {
          libraMeegoInfoStr = `${libraMeegoInfoStr}无`;
        }
        let notifyContent = `${libraInfoStr}\n> ${operatorStr}\n> ${libraChangeInfoStr}\n> ${changeTimeStr}\n> ${libraTrafficStr}\n> ${libraMeegoInfoStr}`;
        if (isWebLibraByLibraInfo(libraInfo)) {
          // 如果是 Web 实验，则不展示实验 Owner
          notifyContent = `${libraInfoStr}\n> ${libraChangeInfoStr}\n> ${changeTimeStr}\n> ${libraTrafficStr}\n> ${libraMeegoInfoStr}`;
        }
        if (reopenCanPreIntercept.length > 0) {
          notifyContent = `${notifyContent}\n> ${reopenCanPreIntercept}`;
        }
        elements.push({
          tag: CardElementTagV2.markdown,
          // eslint-disable-next-line max-len
          content: notifyContent,
          text_size: 'notation',
        } as CardMarkdownElement);
      }
      if (index < mapSize - 1) {
        // 添加一下分割线
        elements.push({
          tag: CardElementTagV2.markdown,
          content: '<hr>',
        } as CardMarkdownElement);
      }
    }

    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

// 未填写实验关闭归因的实验列表（通知卡片）
export class LibraNotFinishCloseAttributionSummary implements MsgTemplate {
  name = '尚未填写关闭归因的实验列表';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Patrol;
  strategy = MsgStrategy.Timer;
  msgContent: LarkCard | string;

  constructor(
    cardType: LibraTopicGroupsCardType,
    libraMap: Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }>,
    dateRange: string,
    meegoTeamType: MeegoTeamType,
    meegoTeamInfos: LibraMeegoTeamInfo[],
    customTitle?: string,
    fixedTeamIds?: number[],
  ) {
    this.name =
      process.env.NODE_ENV === 'development'
        ? `${customTitle ?? LibraTopicGroupsCardTitleMap[cardType]}(测试环境)`
        : `${customTitle ?? LibraTopicGroupsCardTitleMap[cardType]}`;
    if (dateRange.length > 0) {
      this.name = `${this.name}(${dateRange})`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.red,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];

    const meegoTeamAndLibraInfosMap = new Map<number, { event: LibraChangeEvent; libraInfo: LibraNewInfo }[]>();

    // 使用插件
    dayjs.extend(utc);
    dayjs.extend(timezone);

    // 需要按照 Meego 团队拆分统计（遍历 libraMap）
    for (const [key, value] of libraMap) {
      const { event } = value;
      const { libraInfo } = value;
      if (libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0) {
        // 获取 teamInfo 里面的所有 teamId
        let teamIds = libraInfo.meegoTeamInfo.map(team => team.teamId);
        if (fixedTeamIds && fixedTeamIds.every(item => teamIds.includes(item))) {
          // 如果指定了固定的 teamId，那么就只处理这个 teamId
          teamIds = [...fixedTeamIds];
        }
        // 遍历 teamIds，放到 meegoTeamAndLibraInfosMap 里面
        for (const teamId of teamIds) {
          if (meegoTeamAndLibraInfosMap.has(teamId)) {
            // 如果已经存在，直接 push
            meegoTeamAndLibraInfosMap.get(teamId)?.push({ event, libraInfo });
          } else {
            // 如果不存在，先创建
            meegoTeamAndLibraInfosMap.set(teamId, [{ event, libraInfo }]);
          }
        }
      }
    }

    // 遍历 meegoTeamAndLibraInfosMap，统计各个团队的实验变更情况
    const mapSize = meegoTeamAndLibraInfosMap.size;
    let index = -1;
    for (const [key, value] of meegoTeamAndLibraInfosMap) {
      index = index + 1; // 从 0 开始计索引
      const teamId = Number(key);
      const teamName = meegoTeamInfos.find(team => team.teamId === teamId)?.teamName ?? '';
      if (teamName.length === 0) {
        // 如果找不到 teamName，一般是这个 libra 对应了多个 Meego 团队（并且是跨端，比如 移动端 + web），此时跳过一下，不处理
        continue;
      }
      let count = 0; // 统计每个团队下的变更实验数量
      elements.push({
        tag: CardElementTagV2.markdown,
        content: `**${teamName}** <number_tag>${value.length}</number_tag>`,
        text_size: 'heading',
      } as CardMarkdownElement);
      for (const libra of value) {
        count = count + 1;
        const { event } = libra;
        const { libraInfo } = libra;
        // 填写关闭归因链接
        let domainHost = MAIN_HOST_HTTPS;
        if (process.env.NODE_ENV === 'development') {
          domainHost = 'http://localhost:8080';
        }
        const sheetUrl = `${domainHost}/libra/lark-sidebar-submit?libra_flight_id=${libraInfo.flightInfo.id}&sidebar_type=0`;
        const editLibraCloseAttributionUrl = encodeURIComponent(sheetUrl);
        const closeAttributionFinalUrl = `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&width=480&reload=false&url=${editLibraCloseAttributionUrl}`;
        const editLink = `**填写归因：[链接](${closeAttributionFinalUrl})**`;
        // 实验链接 + 填写关闭归因链接
        const libraInfoStr = `${count}. [${event.flightName}](${event.flightUrl})`;
        const operatorStr =
          event.eventOperator === 'Libra' || event.eventOperator === 'OpenAPI'
            ? `操作人：Libra 平台，默认 Owner：${firstLibraOwnerAtEmailMarkdownStrByLibraInfo(libraInfo)}`
            : `操作人：<at email=${add_suffix_ne('@bytedance.com')(event.eventOperator)}></at>`;
        // 关注 POC
        const pocArray = MeegoTeamFixedPOCs[teamId];
        const pocStr = `关注 POC：${pocArray.map(poc => `<at email=${poc}></at>`).join(',')}`;
        // 关闭时间（转换时间到东八区）
        const changeTimeStr = `关闭时间：${libraChangeTimeStr(event.ts * 1000)}`;
        let notifyContent = `${libraInfoStr}\n> ${operatorStr}\n> ${pocStr}\n> ${changeTimeStr}\n> ${editLink}`;
        if (isWebLibraByLibraInfo(libraInfo)) {
          notifyContent = `${libraInfoStr}\n> ${pocStr}\n> ${changeTimeStr}\n> ${editLink}`;
        }
        elements.push({
          tag: CardElementTagV2.markdown,
          // eslint-disable-next-line max-len
          content: notifyContent,
          text_size: 'notation',
        } as CardMarkdownElement);
      }
      if (index < mapSize - 1) {
        // 添加一下分割线
        elements.push({
          tag: CardElementTagV2.markdown,
          content: '<hr>',
        } as CardMarkdownElement);
      }
    }

    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

// 单个实验完成关闭归因填写（通知卡片）
export class LibraFinishCloseAttribution implements MsgTemplate {
  name = '实验关闭归因填写完成';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Operation;
  strategy = MsgStrategy.Auto;
  msgContent: LarkCard | string;

  constructor(info: LibraBaseInfo, libraInfo: LibraNewInfo) {
    this.name = process.env.NODE_ENV === 'development' ? '实验关闭归因填写完成(测试环境)' : '实验关闭归因填写完成';
    if (isP00Story(libraInfo)) {
      // 确定是否为 P00 需求
      this.name = `P00需求-${this.name}`;
    }
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.green,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `【${info.appNames.join(',')}】[${info.flightName}](${info.flightUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    const { flightInfo } = libraInfo;
    const { closeAttributionInfo } = flightInfo;
    let operatorStr = '操作人：未知';
    if (closeAttributionInfo && closeAttributionInfo.updateUser) {
      if (
        closeAttributionInfo.updateUser.includes('@bytedance.com') &&
        !closeAttributionInfo.updateUser.includes('Libra') &&
        !closeAttributionInfo.updateUser.includes('OpenAPI')
      ) {
        operatorStr = `操作人：<at email=${closeAttributionInfo.updateUser}></at>`;
      } else {
        operatorStr = `操作人：${closeAttributionInfo.updateUser.replace('@bytedance.com', '')}`;
      }
    }
    const { meegoNames, meegoQAs, meegoPMs } = meegoRelatedInfoDisplayStrMap(libraInfo);
    let libraChangeInfoStr = `关闭归因：`;
    let reopenCanPreIntercept = ''; // 实验重开-代码相关问题-是否可前置拦截
    if (closeAttributionInfo) {
      libraChangeInfoStr = `${libraChangeInfoStr}**${closeAttributionInfoDisplayStr(libraInfo)}**`;
      if (closeAttributionInfo.reopenSubTypeCanPreInterceptType !== undefined) {
        reopenCanPreIntercept = `可否前置拦截：${closeAttributionReopenCanPreInterceptDisplayStr(closeAttributionInfo.reopenSubTypeCanPreInterceptType)}`;
      }
    } else {
      libraChangeInfoStr = `${libraChangeInfoStr}**未知**`;
    }
    // 转换时间到东八区
    let changeTimeStr = '填写时间：未知';
    if (closeAttributionInfo?.updateTime) {
      changeTimeStr = `填写时间：${libraChangeTimeStr(closeAttributionInfo?.updateTime * 1000)}`;
    }

    const libraTrafficStr = `实验流量: ${flightInfo.trafficInfo.currentTrafficValue * 100}%`;
    let libraMeegoInfoStr = '关联 Meego：';
    if (libraInfo.meegoInfo && libraInfo.meegoInfo.length > 0) {
      const meegoCount = libraInfo.meegoInfo.length;
      for (let i = 0; i < meegoCount; i++) {
        if (meegoCount === 1) {
          // 只有一个需求
          libraMeegoInfoStr = `${libraMeegoInfoStr}[链接](${libraInfo.meegoInfo[i].url})`;
        } else {
          if (i === 0) {
            libraMeegoInfoStr = `${libraMeegoInfoStr}[链接${i + 1}](${libraInfo.meegoInfo[i].url})`;
          } else {
            libraMeegoInfoStr = `${libraMeegoInfoStr}，[链接${i + 1}](${libraInfo.meegoInfo[i].url})`;
          }
        }
      }
    } else {
      libraMeegoInfoStr = `${libraMeegoInfoStr}无`;
    }
    let notifyContent = `- ${operatorStr}\n- ${libraChangeInfoStr}\n- ${changeTimeStr}\n- ${libraTrafficStr}\n- ${libraMeegoInfoStr}`;
    if (isWebLibraByLibraInfo(libraInfo)) {
      // 如果是 Web 实验，则不展示实验 Owner
      notifyContent = `- ${libraChangeInfoStr}\n- ${changeTimeStr}\n- ${libraTrafficStr}\n- ${libraMeegoInfoStr}`;
    }
    if (reopenCanPreIntercept.length > 0) {
      notifyContent = `${notifyContent}\n- ${reopenCanPreIntercept}`;
    }
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: notifyContent,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    // 其他周知人（汇总实验 Owner、PM、QA等）
    appendAllRelatedUsersForElements(elements, info, libraInfo, meegoQAs, meegoPMs);
    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}

// 灰度版本结束（通知卡片）
export class MsgGrayVersionEnd implements MsgTemplate {
  name = '灰度版本结束通知';
  type = MsgType.GroupChat;
  category = MsgCategory.Experiment;
  subCategory = LibraMsgCategory.Control;
  strategy = MsgStrategy.Event;
  msgContent: LarkCard | string;

  constructor(info: LibraBaseInfo, region: LibraRegion, isClose: boolean, isSuccess: boolean) {
    const baseCard = BaseCardUtils.buildBaseCardV2({
      title: this.name,
      template: CardTemplate.orange,
      config: { enable_forward: true, update_multi: true },
    });
    const elements: CardElement[] = [];

    // 实验基本信息
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**灰度实验ID**: ${info.flightId}`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**实验名称**: [${info.flightName}](${info.flightUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**实验状态**: 灰度结束`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);

    // 实验链接
    elements.push({
      tag: CardElementTagV2.div,
      text: {
        content: `**实验链接**: [${info.flightUrl}](${info.flightUrl})`,
        tag: CardTextTag.lark_md,
      },
    } as CardContentElement);
    // 实验Owner信息
    let owners = '';
    if (!isWebLibra(info)) {
      for (const owner of info.flightOwners) {
        owners = `${owners}<at email=${owner.email}></at>`;
      }
      elements.push({
        tag: CardElementTagV2.div,
        text: {
          content: `**实验Owner**: ${owners}`,
          tag: CardTextTag.lark_md,
        },
      } as CardContentElement);
    }

    // 操作按钮
    // 操作按钮 - 根据状态显示不同按钮
    const cardButtonActions: (CardButtonAction | CardMarkdownElement)[] = [];

    if (!isClose) {
      // 未关闭状态显示关闭按钮
      cardButtonActions.push({
        tag: CardElementTagV2.button,
        text: {
          tag: CardTextTag.plain_text,
          content: '关闭灰度实验',
        },
        type: CardButtonType.danger,
        value: {
          cardCallbackType: CardCallbackType.LibraCloseGray,
          libraInfo: info,
          operator: 'nieweishan',
          region,
        } as unknown as CardActionValue,
        confirm: {
          title: {
            tag: CardTextTag.plain_text,
            content: '确认关闭灰度实验',
          },
          text: {
            tag: CardTextTag.plain_text,
            content: '确定要关闭这个灰度实验吗？此操作不可撤销。',
          },
        },
      } as CardButtonAction);
    } else {
      // 已关闭状态显示结果按钮

      content: cardButtonActions.push({
        tag: CardElementTagV2.markdown,
        content: isSuccess
          ? `<font color="green">✅ 实验关闭成功</font>` // size="4" 增大字体
          : `<font color="red">❌ 实验关闭失败</font>`, // 统一设置字体大小
        text_size: 'heading',
      } as CardMarkdownElement);
    }

    // 创建正式实验按钮
    cardButtonActions.push({
      tag: CardElementTagV2.button,
      url: `https://applink.feishu.cn/client/web_url/open?mode=sidebar-semi&width=1000&url=${encodeURIComponent(`https://pa.bytedance.net/libra/create?appid=177501&version=15.3.0&flightId=${info.flightId}`)}`,
      text: {
        tag: CardTextTag.plain_text,
        content: '创建正式实验',
      },
      type: CardButtonType.primary,
    } as CardButtonAction);

    // 按钮布局
    const buttonColumns: CardColumnElement[] = cardButtonActions.map(
      buttonAction =>
        ({
          tag: CardElementTagV2.column,
          width: 'auto',
          weight: 1,
          vertical_align: 'top',
          elements: [buttonAction],
        }) as CardColumnElement,
    );

    elements.push({
      tag: CardElementTagV2.columnSet,
      flex_mode: 'flow',
      background_style: 'default',
      columns: buttonColumns,
    } as CardColumnSetElement);

    baseCard.body = {
      elements,
    };
    this.msgContent = baseCard;
  }
}
