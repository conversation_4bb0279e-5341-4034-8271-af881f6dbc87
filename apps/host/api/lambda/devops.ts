import { Api, Data, Post, useInject } from '@edenx/runtime/bff';
import { z } from 'zod';
import DevOpsSendMessage from '../service/devops/devopsSendMsg';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import DevOpsCheckPodsTTPStatus from 'api/service/devops/devopsCheckPodsTTPStatus';

export const openDevOpsSendMessage = Api(
  Post('/open/devops/send_msg'),
  Data(
    z.object({
      category_name: z.string(),
      chat_id: z.string(),
      card_title: z.string(),
      card_template: z.string(),
      card_content: z.string(),
    }),
  ),
  async ({ data }) => {
      useInject(BytedLogger).info(`openDevOpsSendMessage data:${JSON.stringify(data)}`);
    const devOpsSendMessage = useInject(DevOpsSendMessage);
    return await devOpsSendMessage.sendMessage(data.category_name, data.chat_id, data.card_title, data.card_template, data.card_content);
  },
);

export const openDevOpsAddUser = Api(
  Post('/open/devops/add_user'),
  Data(
    z.object({
      user_name: z.string(),
      chat_id: z.string(),
      context: z.string(),
    }),
  ),
  async ({ data }) => {
    useInject(BytedLogger).info(`openDevOpsAddUser data:${JSON.stringify(data)}`);
    const devOpsSendMessage = useInject(DevOpsSendMessage);
    return await devOpsSendMessage.addUserToChatGroup(data.user_name, data.chat_id);
  },
);

export const openDevOpsCurrentBusStatus = Api(
  Post('/open/devops/current_bus_status'),
  Data(
    z.object({
      app_id: z.number(),
    }),
  ),
  async ({ data }) => {
    useInject(BytedLogger).info(`openDevOpsCurrentBusStatus data:${JSON.stringify(data)}`);
    const devOpsSendMessage = useInject(DevOpsSendMessage);
    return await devOpsSendMessage.getBusStatus(data.app_id);
  },
);

export const openDevOpsCheckPodsTTPStatus = Api(
  Post('/open/devops/pods_ttp_status'),
  Data(
    z.object({
      project_id: z.string(),
      branch: z.string(),
    }),
  ),
  async ({ data }) => {
    useInject(BytedLogger).info(`openDevOpsCheckPodsTTPStatus data:${JSON.stringify(data)}`);
    return await useInject(DevOpsCheckPodsTTPStatus).checkPodsTTPStatus(data.project_id, data.branch);
  },
);
