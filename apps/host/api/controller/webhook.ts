import { Body, Controller, Post } from '@gulux/application-http';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { Header, Inject } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LarkService from '@pa/backend/dist/src/third/lark';
import { RedisClient } from '@gulux/gulux/redis';
import QaBuildServer from '../service/handler/meegoCallback/QaBuild';
import VersionReleaseServer from '../service/handler/meegoCallback/VersionRelease';
import * as lark from '@larksuiteoapi/node-sdk';
import {
  BitsHookData,
  BitsStageHookData,
  BusMrConflictCheckData,
  BusMrConflictCheckParams,
  BytestMRCrashTestData,
  ConflictCheckEvent,
  MrMergeCheckData,
  PublishTaskInfo,
} from '@shared/bits/webHook';
import versionUtils from '../utils/versionUtils';
import { LVProductType } from '@shared/process/versionProcess';
import { PlatformType } from '@pa/shared/dist/src/core';
import BitsService from '../service/third/bits';
import VersionProcessModelService from '../service/model/versionProcessModel';
import VersionSubscriptionService from '../service/dao/VersionSubscription';
import VersionSubscriptionMeegoService from '../service/dao/VersionSubscriptionMeego';
import BitsHandlers from '../service/handler/bits/BitsHandlers';
import ConsulHandlers from '../service/handler/consul/ConsulHandlers';
import GitlabHandlerDispatch from '../service/handler/gitlab/gitlabHandlerDispatch';
import minimist from 'minimist';
import { ApprovalInstanceEvent, ApprovalTaskEvent } from '@pa/shared/dist/src/lark/approval';
import BusAssistService from '../service/bus/busAssist';
import SyncTranslation from '../service/SyncTranslation';
import GrayMRCollect from '../service/grayMRCollect';
import BranchOpHandler from '../service/branchOpHandler';
import { cloneDeep, toNumber } from 'lodash';
import { ModelType } from '@gulux/gulux/typegoose';
import { PackageApprovalTable } from '../model/PackageApprovalModel';
import PackageQuotaManageDao from '../service/dao/PackageQuotaManage';
import CardCallbackHandlerService from '../service/CardCallbackHandler';
import { LarkCardTemplate } from '@pa/shared/dist/src/lark/larkCard';
import { useInject } from '@edenx/runtime/bff';
import BusMrConflictCheckService from '../service/bus/BusMrConflictCheck';
import AdrGrayStageService from '../service/releasePlatform/stageServices/adrGrayStageService';
import ZhongkuiService from '../service/third/zhongkui';
import { ApprovalDispatchService } from '../service/approval/ApprovalDispatchService';
import MeegoIssueChangeStatusHandler from '../service/handler/meegoCallback/MeegoIssueChangeStatus';
import MeegoStoryChangeFieldsHandler from '../service/handler/meegoCallback/MeegoStoryChangeFields';
import MeegoStorySubmitTestDoneHandler from '../service/handler/meegoCallback/MeegoStorySubmitTestDone';
import MeegoTechStorySubmitTestDoneHandler from '../service/handler/meegoCallback/MeegoTechStorySubmitTestDone';
import MRProfilerHostService from '../service/mrProfiler/MRProfilerHostService';
import { AcceptanceFeatureBitableService } from '../service/acceptanceCenter/acceptanceFeatureBitableService';
import { BitableFieldChangedEvent, BitableRecordChangedEvent } from '@pa/shared/dist/src/lark/doc';
import AdrCCFullReleaseStageService from '../service/releasePlatform/stageServices/adrCCFullReleaseStageService';
import AdrLvFullReleaseService from '../service/releasePlatform/stageServices/adrLvFullReleaseService';
import MeegoStoryCreateAcceptanceFeatureHandler from '../service/handler/meegoCallback/MeegoStoryCreateAcceptanceFeature';
import LLMService from '../service/LLM/LLMService';
import MeegoStoryCreateOneAcceptanceFeatureHandler from '../service/handler/meegoCallback/MeegoStoryCreateOneAcceptanceFeature';
import { PaAlarmService } from '@pa/backend/dist/src/utils/alarm';
import { MsgRecordModel } from '@pa/backend/dist/src/model/msgRecordModel';
import CustomBuildDao from '../service/dao/releasePlatform/CustomBuildDao';
import { teaCollect, TeaEvent } from '../tea';
import GitlabMergeRequestWebHookService from '../service/gitlab/gitlabMergeRequestWebHookService';
import { LanguageApp } from '@shared/multiLanguage/MulitiLanguageConfig';
import IssuePriorityChangeNotify from '../service/handler/meegoCallback/IssuePriorityChangeNotify';
import TestFlightStageService from '../service/releasePlatform/stageServices/testFlightStageService';
import { IndependentGreyApprovalTable } from '../model/approval/IndependentGreyApproval';
import CircuitBreakerService from '../service/circuitBreaker/circuitBreakerService';
import { IndependentGreyApprovalService } from '../service/approval/IndependentGreyApprovalService';
import appRepos from '@shared/gitlab/appRepos';
import GitLabService from '../service/third/gitlab';
import { IndependentGreyStageInfoService } from '../service/approval/IndependentGreyStageInfoService';
import { VersionStageStatus } from '@shared/releasePlatform/versionStage';
import { IndependentGreyStageInfo } from '@shared/approval/IndependentGreyStageInfo';

@Controller('')
export default class WebhookController {
  @Inject()
  private logger: BytedLogger;

  @Inject()
  private lark: LarkService;

  @Inject()
  private redis: RedisClient;

  @Inject()
  private qaBuild: QaBuildServer;

  @Inject()
  private versionRelease: VersionReleaseServer;

  @Inject()
  private bitsHandlers: BitsHandlers;

  @Inject()
  private consulHandlers: ConsulHandlers;

  @Inject()
  private bits: BitsService;

  @Inject()
  private versionProcessModelService: VersionProcessModelService;

  @Inject()
  private VersionSubscriptionMeegoService: VersionSubscriptionMeegoService;

  @Inject()
  private versionSubscriptionDao: VersionSubscriptionService;

  @Inject()
  private cardCallbackHandlerService: CardCallbackHandlerService;

  @Inject()
  private gitlabHandler: GitlabHandlerDispatch;

  @Inject()
  private busAssist: BusAssistService;

  @Inject()
  private syncTranslation: SyncTranslation;

  @Inject()
  private grayMrCollect: GrayMRCollect;

  @Inject()
  private branchOpHandler: BranchOpHandler;

  @Inject(PackageApprovalTable)
  private packageApprovalModel: ModelType<PackageApprovalTable>;

  @Inject(IndependentGreyApprovalTable)
  private independentGreyApprovalModel: ModelType<IndependentGreyApprovalTable>;

  @Inject()
  private packageQuotaManageDao: PackageQuotaManageDao;

  @Inject()
  private conflictCheckService: BusMrConflictCheckService;

  @Inject()
  private adrGrayStageService: AdrGrayStageService;

  @Inject()
  private iosGrayStageService: TestFlightStageService;

  @Inject()
  private adrLvFullReleaseStageService: AdrLvFullReleaseService;

  @Inject()
  private adrCCFullReleaseStageService: AdrCCFullReleaseStageService;

  @Inject()
  private approvalDispatchService: ApprovalDispatchService;

  @Inject()
  private meegoStorySubmitTestDoneHandler: MeegoStorySubmitTestDoneHandler;

  @Inject()
  private meegoIssueChangeStatusHandler: MeegoIssueChangeStatusHandler;

  @Inject()
  private meegoStoryChangeFieldsHandler: MeegoStoryChangeFieldsHandler;

  @Inject()
  private meegoStoryCreateAcceptanceFeatureHandler: MeegoStoryCreateAcceptanceFeatureHandler;

  @Inject()
  private meegoStoryCreateOneAcceptanceFeatureHandler: MeegoStoryCreateOneAcceptanceFeatureHandler;

  @Inject()
  private meegoTechStorySubmitTestDoneHandler: MeegoTechStorySubmitTestDoneHandler;

  @Inject()
  private mrProfilerHostService: MRProfilerHostService;

  @Inject()
  private acceptanceFeatureBitableService: AcceptanceFeatureBitableService;

  @Inject()
  private llmService: LLMService;

  @Inject()
  private alarmService: PaAlarmService;

  @Inject()
  private customBuildDao: CustomBuildDao;

  @Inject()
  private gitlabMergeRequestWebHookService: GitlabMergeRequestWebHookService;

  @Inject()
  private issuePriorityChangeNotify: IssuePriorityChangeNotify;

  @Inject()
  private circuitBreakerService: CircuitBreakerService;

  @Post('/webhook/meegoRequest')
  async meegoRequest(@Body() b: any) {
    this.logger.info(`[meegoRequest]:${JSON.stringify(b)}`);

    const handlerMap = [
      this.qaBuild,
      this.versionRelease,
      this.meegoStorySubmitTestDoneHandler,
      this.meegoTechStorySubmitTestDoneHandler,
      this.meegoIssueChangeStatusHandler,
      this.meegoStoryChangeFieldsHandler,
      this.meegoStoryCreateAcceptanceFeatureHandler,
      this.meegoStoryCreateOneAcceptanceFeatureHandler,
      this.issuePriorityChangeNotify,
    ];
    const { header, payload } = b;
    const oldUuid = await this.redis.get(header.uuid);
    if (oldUuid && oldUuid === header.uuid) {
      return { status: 0 };
    }
    await this.redis.set(header.uuid, header.uuid);
    await this.redis.expire(header.uuid, 300);
    if (header && payload && header.token) {
      for (const handler of handlerMap) {
        if (handler.canHandle(header.token)) {
          handler.handler(payload).then();
        }
      }
    }

    return { status: 0 };
  }

  @Post('/webhook/mrMergeCheck')
  async mrMergeCheck(@Body() mrData: MrMergeCheckData) {
    this.logger.info(`[MR Merge Check] ${JSON.stringify(mrData)}`);
    const checkResult = { status: true, message: '', conflict: {} };
    try {
      const conflictCheck = await this.conflictCheckService.checkMrCanMerged(
        mrData.host.mr_iid,
        mrData.host.project_id,
        mrData.host.mr_id,
      );
      checkResult.status = conflictCheck.status;
      checkResult.message = conflictCheck.message;
      checkResult.conflict = conflictCheck.conflict;
    } catch (e) {
      this.logger.info(`[MR Merge Check] [Conflict Check Exception] ${e}`);
    }
    this.logger.info(`[MR Merge Check] result ${checkResult}`);
    if (!checkResult.status) {
      return checkResult;
    }
    const zhongkuiRes = await useInject(ZhongkuiService).accessLaneSync(mrData);
    this.logger.info(`[MR Merge Check] zhongkui:${JSON.stringify(zhongkuiRes)}`);
    return zhongkuiRes;
  }

  @Post('/webhook/checkBusMrsConflict')
  async checkBusMrsConflict(@Body() checkData: BusMrConflictCheckData) {
    this.logger.info(`[Bus Conflict Check] ${JSON.stringify(checkData)}`);
    const params = checkData.content as BusMrConflictCheckParams;
    switch (checkData.event) {
      case ConflictCheckEvent.CheckBusMrConflict:
        await this.conflictCheckService.checkUnmergedMrsConflicts();
        break;
      case ConflictCheckEvent.SetMMRProtected:
        await this.conflictCheckService.setMMRProtected(params.mrId);
        break;
      case ConflictCheckEvent.CancelMMRProtected:
        await this.conflictCheckService.cancelProtected(params.cancelReason, params.mrId, undefined);
        break;
      case ConflictCheckEvent.FindSingleMrConflict:
        return await this.conflictCheckService.scanMrConflict(params.mrId);
      default:
        await this.conflictCheckService.checkUnmergedMrsConflicts();
    }
  }

  @Post('/conflict_check/compare_mrs_conflict')
  async compareMrsConflict(@Body() mrs: { base_mr_id: number; compare_mr_id: number }) {
    this.logger.info(`[Bus Conflict Check] [compareMrsConflict] ${mrs}`);
    const res = await this.conflictCheckService.compareMrs(mrs.base_mr_id, mrs.compare_mr_id);
    return res;
  }

  @Post('/open/webhook/tf_auto_build_fail')
  async tfAutoBuildFail(@Body() data: PublishTaskInfo) {
    this.logger.info(`[tf_auto_build_fail]:${JSON.stringify(data)}`);
  }

  @Post('/open/webhook/tf_auto_build_success')
  async tfAutoBuildSuccess(@Body() data: PublishTaskInfo) {
    this.logger.info(`[tf_auto_build_success]:${JSON.stringify(data)}`);
    // const buildRecord = await this.customBuildDao.findByCriteria({ tfTaskFlowId: data.TaskFlowId });
    // if (buildRecord) {
    //   buildRecord.buildResult = CustomBuildResultType.success;
    // }
  }

  @Post('/webhook/larkCardCallback')
  async larkCardCallback(@Body() b: any) {
    const { isChallenge, challenge } = lark.generateChallenge(b, {
      encryptKey: 'SJmJPPnhYq4WTl2Z76V25bzpVwm3YPfb',
    });
    if (isChallenge) {
      return challenge;
    }

    const timeout = setTimeout(() => {
      // 测试用消息发送
      this.alarmService.reportError(`lark回调执行超时 ${JSON.stringify(b)}`);
    }, 2700);
    this.logger.info(`[larkCardCallback]:${JSON.stringify(b)}`);
    const result = await this.cardCallbackHandlerService.handle(b.action.value.cardCallbackType, b);
    clearTimeout(timeout);
    return result ?? {};
  }

  @Post('/webhook/bitsCallBack')
  async bitsCallBack(@Body() hookData: BitsHookData) {
    this.logger.info(`[MRINFO] ${JSON.stringify(hookData)}`);

    const handlers = this.bitsHandlers.handlers().filter(it => it.canHandle(hookData.action));

    for (const h of handlers) {
      try {
        await h.handler(hookData.content);
      } catch (e) {
        await this.lark.sendTextMessage(
          UserIdType.chatId,
          'oc_421725f66bd9b296fff97e71a9e50466',
          `[${h.handlerName}触发失败]:${JSON.stringify(hookData)}，${e}`,
        );
      }
      this.logger.info(`[bitsCallBack]:当前handler[${h.handlerName}]处理`);
    }

    return {};
  }

  @Post('/webhook/bytestMRCrash')
  async bytestMRCrashTest(@Body() data: BytestMRCrashTestData) {
    return {};
  }

  @Post('/open/webhook/bitsReleaseCallBack')
  async bitsReleaseCallBack(@Body() hookData: BitsStageHookData) {
    this.logger.info(`[bitsReleaseCallBack]:${JSON.stringify(hookData)}`);
    const jobInfo = hookData?.build_job_id ? await this.bits.fetchJobInfo(Number(hookData.build_job_id)) : undefined;
    if (hookData.bits_app_id && hookData.stage_name && hookData.version_code && hookData.version_name) {
      if (hookData.bits_app_id === '2020095701') {
        // ios
        if (hookData.stage_name.includes('灰')) {
          // 灰度通知
          const grayCount = await this.iosGrayStageService.getGrayCount(
            hookData.version_name,
            PlatformType.iOS,
            LVProductType.cc,
          );
          this.logger.info(`bits原子任务接口触发 ios cc:  ${hookData.version_name}, grayCount: ${grayCount}`);
          await this.versionSubscriptionDao.send_gray_messages(
            LVProductType.cc,
            PlatformType.iOS,
            hookData.version_name,
            hookData.version_code,
            grayCount ?? 0,
            true,
          );
          await this.iosGrayStageService.testFlightReleased(
            hookData.version_name,
            PlatformType.iOS,
            LVProductType.cc,
            hookData.version_code,
            jobInfo,
          );
        }
      } else if (hookData.bits_app_id === '2020095699') {
        // android cc
        if (hookData.stage_name.includes('正式阶段')) {
          this.logger.info('自定义产物接口触发安卓lv正式版本同步');
          await this.adrCCFullReleaseStageService.adrCCSubmitNotification(300602, hookData.version_name);
          await this.VersionSubscriptionMeegoService.send_android_cc_submit_messages(
            hookData.version_name,
            '10%',
            hookData.version_code,
          );
          this.logger.info(
            `[版本大盘]海马平台提审信息 appId: ${hookData.bits_app_id}, version: ${hookData.version_name}`,
          );
          // Android lv 众测信息同步
        } else if (hookData.stage_name === '众测阶段') {
          this.logger.info('自定义产物接口触发安卓cc众测阶段同步');
          await this.versionSubscriptionDao.send_gray_messages(
            LVProductType.cc,
            PlatformType.Android,
            hookData.version_name,
            hookData.version_code,
            0,
            true,
          );

          // Android lv 灰度信息同步
        } else {
          const grayCount = await this.adrGrayStageService.getGrayCount(
            hookData.version_name,
            PlatformType.Android,
            LVProductType.cc,
          );

          this.logger.info(`bits原子任务接口触发 安卓cc第${grayCount}轮灰度同步, version: ${hookData.version_name}`);

          if (grayCount === 0 || grayCount === undefined) {
            this.logger.error('自定义产物接口触发安卓cc灰度同步，但没有灰度轮次信息');
          }
          await this.versionSubscriptionDao.send_gray_messages(
            LVProductType.cc,
            PlatformType.Android,
            hookData.version_name,
            hookData.version_code,
            grayCount ?? 0,
            true,
          );
          try {
            this.logger.info('android灰度扭转版本大盘状态');
            // 强制扭转状态
            await this.adrGrayStageService.adrGrayReleased(
              hookData.version_name,
              PlatformType.Android,
              LVProductType.cc,
              hookData.version_code,
              jobInfo,
            );
          } catch (e) {
            if (e instanceof Error) {
              this.logger.error(e);
            } else {
              console.error(e);
            }
          }
        }
      }
    }
  }

  @Post('/webhook/bitsAtomCallBack')
  async bitsAtomCallBack(@Body() hookData: BitsStageHookData) {
    this.logger.info(`[自定义检测任务接口到达]：${JSON.stringify(hookData)}`);
    if (hookData.bits_app_id && hookData.stage_name && hookData.version_code && hookData.version_name) {
      this.logger.info(`[自定义检测任务合法]`);

      const version = versionUtils.reConstructVersion(hookData.version_name);
      const jobInfo = await this.bits.fetchJobInfo(Number(hookData?.build_job_id ?? '0'));

      // 更新灰度包、小流量包和正式包的小版本号
      this.versionProcessModelService
        .updateVersionCodeFromBits(hookData.bits_app_id, hookData.stage_name, hookData.version_code, version)
        .then(async () => {
          // Android lv 的提审信息同步
          if (hookData.bits_app_id === '177502') {
            if (hookData.stage_name.includes('正式阶段')) {
              this.logger.info('自定义产物接口触发安卓lv正式版本同步');

              await this.VersionSubscriptionMeegoService.send_android_lv_submit_messages(
                version,
                hookData.version_code,
              );
              // 版本大盘国内剪映提审
              await this.adrLvFullReleaseStageService.adrLvSubmitNotification(toNumber(hookData.bits_app_id), version);
              this.logger.info(`[版本大盘]海马平台提审信息 appId: ${hookData.bits_app_id}, version: ${version}`);
              // Android lv 众测信息同步
            } else if (hookData.stage_name === '众测阶段') {
              this.logger.info('自定义产物接口触发安卓lv众测阶段同步');
              await this.versionSubscriptionDao.send_gray_messages(
                LVProductType.lv,
                PlatformType.Android,
                version,
                hookData.version_code,
                0,
                true,
              );

              // Android lv 灰度信息同步
            } else {
              const latestGray = await this.versionProcessModelService.getLatestGray(
                LVProductType.lv,
                PlatformType.Android,
                version,
              );

              this.logger.info(`自定义产物接口触发 安卓lv第${latestGray.gray_count}轮灰度同步`);

              if (latestGray.gray_count === -1) {
                this.logger.error('自定义产物接口触发安卓lv灰度同步，但没有灰度轮次信息');
              }
              const grayCount = await this.adrGrayStageService.getGrayCount(
                hookData.version_name,
                PlatformType.Android,
                LVProductType.lv,
              );
              await this.versionSubscriptionDao.send_gray_messages(
                LVProductType.lv,
                PlatformType.Android,
                version,
                // FIXME 偶现bits返回stage_status=4发布失败状态
                hookData.version_code,
                grayCount ?? 0,
                true,
              );
              try {
                this.logger.info('android灰度扭转版本大盘状态');
                // 强制扭转状态
                await this.adrGrayStageService.adrGrayReleased(
                  version,
                  PlatformType.Android,
                  LVProductType.lv,
                  latestGray.gray_count === -1 ? hookData.version_code : latestGray.versionCode,
                  jobInfo,
                );
              } catch (e) {
                if (e instanceof Error) {
                  this.logger.error(e);
                } else {
                  console.error(e);
                }
              }
            }
          } else if (hookData.bits_app_id === '2000001157') {
            // CapCut Android
            if (hookData.stage_name.includes('正式阶段')) {
              this.logger.info('自定义产物接口触发CapCut Android正式版本同步');
              await this.adrCCFullReleaseStageService.adrCCSubmitNotification(300602, version);
            }
          }
        });
    }

    // 回调bits接口
    await this.bits.bitsCallBack(hookData.job_id);

    // 更新熔断管理配置
    if (hookData.aid && hookData.platform) {
      try {
        await this.circuitBreakerService.updateSlardarAlarm(
          Number(hookData.bits_app_id),
          Number(hookData.aid),
          hookData.platform,
          hookData.version_code,
        );
      } catch (err) {
        this.logger.info(`[sendLVAdrGrayReleaseMessages] [更新告警配置异常]: ${JSON.stringify(err)}`);
        this.lark.sendTextMessage(
          UserIdType.chatId,
          'oc_9780a78309d4d61260d6d436359744bb',
          `${hookData.aid} ${hookData.platform} 更新熔断规则异常`,
        );
      }
    }

    return {
      code: 0,
      message: 'success',
    };
  }

  @Post('/webhook/consulEvent')
  async consulEvent(@Body() { event }: any) {
    const handlers = this.consulHandlers.handlers().filter(it => it.canHandle(event));
    for (const h of handlers) {
      h.handler(event);
      this.logger.info(`[consulEvent]:当前handler[${h.handlerName}]处理`);
    }
    return {};
  }

  @Post('/webhook/gitlab')
  async gitlabHandlerController(@Header('x-gitlab-token') event: string, @Body() body: any) {
    await this.gitlabHandler.dispatch(event, body);
  }

  @Post('/webhook/gitlab_merge_request')
  async gitlabMergeRequestHandlerController(@Body() body: any) {
    // 异步处理即可，不需要 await
    this.gitlabMergeRequestWebHookService.handlerMergeRequestWebhook(body);
  }

  @Post('/lark/message')
  async larkMessageHandler(@Header() headers: any, @Body() body: any) {
    const larkData = Object.assign(
      Object.create({
        headers,
      }),
      body,
    );
    const { isChallenge, challenge } = lark.generateChallenge(larkData, {
      encryptKey: 'SJmJPPnhYq4WTl2Z76V25bzpVwm3YPfb',
    });
    if (isChallenge) {
      return challenge;
    }
    const dispatch = new lark.EventDispatcher({
      verificationToken: 'JEh4gWA51ThOI9IWkME7re6u6pcq73Kz',
      encryptKey: 'SJmJPPnhYq4WTl2Z76V25bzpVwm3YPfb',
      logger: this.logger,
    }).register({
      'im.message.receive_v1': async data => {
        this.logger.info(`[larkMessageHandler]:${JSON.stringify(data)}`);
        let context: { text: string };
        try {
          context = JSON.parse(data.message.content);
        } catch (e) {
          return;
        }
        let rawData = context?.text;
        if (!rawData) {
          return;
        }
        if (rawData.startsWith('/')) {
          await this.busAssist.handleBusLarkMessage(data.message.chat_id, rawData);
          return;
        }
        if (data.message.mentions) {
          for (const mention of data.message.mentions) {
            rawData = rawData.replace(mention.key, '');
          }
        }
        if (context.text.includes('ttest')) {
          await useInject(LarkService).sendCardMessage(
            UserIdType.chatId,
            'oc_3334135fbac77d2944d6bbef4158365f',
            new LarkCardTemplate('ctp_AAVCtuV2PjZD', {
              id: '纸飞机私发消息测试',
              content: `${JSON.stringify(data.message)}`,
            }),
          );
        }
        // 旧的机器人指令
        // language --lv feature/edit_page_scroll_horizontally
        // language --mrId 12345
        // 新的机器人指令
        // language --all --branch feature/edit_page_scroll_horizontally
        // language --all --branch feature/edit_page_scroll_horizontally
        // language --jy --branch feature/edit_page_scroll_horizontally
        // language --cc --branch feature/edit_page_scroll_horizontally
        // language --jy --mrId 12345
        // language --cc --mrId 12345
        const args = minimist(rawData.trim().replace(/\s+/g, ' ').split(' '));
        // await ctx.service.third.larkV2.sendTextMessage(UserIdType.chatId, 'oc_421725f66bd9b296fff97e71a9e50466', JSON.stringify(args))
        const actions = args._.filter(value => value && value !== '');
        const senderId = data.sender.sender_id?.open_id;
        const chatId = data.message.chat_id;
        if (actions.length > 0) {
          teaCollect(TeaEvent.BOT_INTERACT, {
            query: actions.join(','),
            user_id: data.sender.sender_id?.user_id ?? '',
          });
          const action = actions[0];
          if (action === 'language') {
            await this.syncTranslation.dispatch(args, chatId, senderId);
          } else if (action === 'mr') {
            if (args.version) {
              await this.grayMrCollect.collectAndInsert(args.version, data.message.chat_id);
            }
          } else if (typeof action === 'string' && action.startsWith('/')) {
            await this.busAssist.handleBusLarkMessage(data.message.chat_id, rawData);
          } else if (action === 'branch') {
            await this.branchOpHandler.handle(data.sender.sender_id?.open_id, rawData);
          } else if (action === 'mr_attr') {
            await this.mrProfilerHostService.handleMrAttrMessage(data.message.chat_id, rawData);
          } else {
            await this.llmService.processUserQuery(actions, data);
          }
        }
      },
    });
    // noinspection TypeScriptValidateTypes
    // @ts-ignore
    dispatch.register({
      // @ts-ignore
      approval_instance: async data => {
        this.logger.info(`[PackageApproval] approval_instance:${JSON.stringify(data)}`);
        const instance: ApprovalInstanceEvent = cloneDeep(data);
        this.logger.info(`[PackageApproval] cloneDeep:${JSON.stringify(instance)}`);
        // 审批流拦截
        if (instance && (await this.approvalDispatchService.dispatch(instance))) {
          this.logger.info(`[approvalDispatchService] intercept approval_instance:${JSON.stringify(data)}`);
          return;
        }

        // 独立灰度审批
        const independentApproval = await this.independentGreyApprovalModel
          .findOne({
            instance_code: instance.instance_code,
          })
          .exec();
        if (independentApproval) {
          independentApproval.status = instance.status;
          try {
            const approvalService = new IndependentGreyApprovalService();
            independentApproval.update_version_code =
              await approvalService.generateAndAssignVersionCode(independentApproval);
          } catch (error) {
            this.logger.error(
              `[IndependentGreyApprovalService] Failed to generate version code instance_code: ${instance.instance_code} error: ${error}`,
            );
            independentApproval.update_version_code = '-';
          }

          // 拉灰度分支
          const gitlabSrevice = useInject(GitLabService);
          const user = independentApproval.responsible_person;
          const emailPrefix = user.email.split('@')[0];
          const info = appRepos.find(it => it.appId === independentApproval.app_id);
          if (info) {
            try {
              const result = await gitlabSrevice.createBranch(
                info.projectId,
                `${info.defaultBranch}beta/${independentApproval}/gray_${emailPrefix}`,
                `${info.defaultBranch}${independentApproval}`,
              );
              if (result && result.data) {
                independentApproval.branch = result.data.targetBranch;
              }
            } catch (error) {
              this.logger.error(
                `[GitlabService] Failed to create branch instance_code: ${instance.instance_code} error: ${error}`,
              );
            }
          }
          await this.independentGreyApprovalModel
            .updateOne({ instance_code: instance.instance_code }, independentApproval)
            .exec();
          this.logger.info(`[IndependentGreyApproval]:approval_update:${JSON.stringify(independentApproval)}`);

          try {
            const independentGreyStageInfoService = useInject(IndependentGreyStageInfoService);
            await independentGreyStageInfoService.createIndependentGreyStageWithChecklist({
              stageName: 'single_independent_grey_stage',
              displayName: '第1轮灰度',
              startTime: independentApproval.expect_start_time,
              endTime: independentApproval.expect_end_time,
              approvalCode: independentApproval.approval_code,
              instanceCode: independentApproval.instance_code,
              appId: independentApproval.app_id,
              version: independentApproval.based_version,
              updateVersionCode: independentApproval.update_version_code,
              meego_link: independentApproval.meego_link,
            });
          } catch (error) {
            this.logger.error(
              `[IndependentGreyStageInfoService] Failed to create stage info instance_code: ${instance.instance_code} error: ${error}`,
            );
          }
        }

        const packageApproval = await this.packageApprovalModel
          .findOne({
            instance_code: instance.instance_code,
          })
          .exec();
        if (!packageApproval) {
          return;
        }
        packageApproval.status = instance.status;
        await this.packageApprovalModel.updateOne({ instance_code: instance.instance_code }, packageApproval).exec();
        this.logger.info(`[PackageApproval]:approval_update:${JSON.stringify(packageApproval)}`);
        // 审批通过就要更新包大小额度的配额
        if (packageApproval.status === 'APPROVED') {
          await this.packageQuotaManageDao.updatePackageQuotaFromApproval(packageApproval);
        }
      },
    });
    dispatch.register({
      // @ts-ignore
      approval_task: async data => {
        this.logger.info(`[Approval] approval_task:${JSON.stringify(data)}`);
        const task: ApprovalTaskEvent = cloneDeep(data);
        this.logger.info(`[Approval] cloneDeep:${JSON.stringify(task)}`);
        // 审批流拦截
        if (task && (await this.approvalDispatchService.dispatchTask(task))) {
          this.logger.info(`[approvalDispatchService] intercept approval_task:${JSON.stringify(data)}`);
          return;
        }
      },
    });
    // noinspection TypeScriptValidateTypes
    // @ts-ignore
    dispatch.register({
      // @ts-ignore
      approval: async data => {
        this.logger.info(`[larkMessageHandler]approval:${JSON.stringify(data)}`);
      },
    });
    dispatch.register({
      'drive.file.bitable_record_changed_v1': async data => {
        this.logger.info(`[larkMessageHandler]drive.file.bitable_record_changed_v1:${JSON.stringify(data)}`);
        this.acceptanceFeatureBitableService.handleBitableRecordChanged(data as BitableRecordChangedEvent);
      },
    });
    dispatch.register({
      'drive.file.bitable_field_changed_v1': async data => {
        this.logger.info(`[larkMessageHandler]drive.file.bitable_field_changed_v1:${JSON.stringify(data)}`);
        this.acceptanceFeatureBitableService.handleBitableFieldChanged(data as BitableFieldChangedEvent);
      },
    });
    dispatch.register({
      // 单聊消息已读
      'im.message.message_read_v1': async data => {
        this.logger.info(`[larkMessageHandler]im.message.message_read_v1:${JSON.stringify(data)}`);
        data.message_id_list?.forEach(async (messageId: string) => {
          useInject(MsgRecordModel).markRead(messageId);
        });
      },
    });

    dispatch.invoke(larkData).catch(e => this.alarmService.reportError(`[larkMessageHandler]error:${e} ${e.stack}`));
    return {};
  }
}
