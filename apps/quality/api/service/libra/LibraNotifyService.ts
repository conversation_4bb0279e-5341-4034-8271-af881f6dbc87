import { Inject, Injectable } from '@gulux/gulux';
import { LibraNewInfoListService } from './LibraNewInfoListService';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import {
  LibraFlightStatus,
  LibraMeegoInfo,
  LibraNewInfo,
  LibraNotifyReceiverType,
  LibraNotifyRecordInfo,
  LibraNotifyType,
} from '@shared/libra/LibraNewInfo';
import MeegoRawService from '../third/meego';
import { NetworkCode } from '@pa/shared/dist/src/core';
import {
  LibraCreateState,
  LibraLaunchNotifyAllInfo,
  LibraLaunchNotifyCardInfo,
  LibraLaunchNotifyCardType,
  LibraLaunchNotifyCreateCardInfo,
  LibraRemindDataReviewNotifyCardInfo,
} from '@shared/libra/LibraNotifyInfo';
import LarkCardService from '../larkCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { MsgTemplate } from '@pa/shared/dist/src/message';
import { TEST_CHAT_ID } from '@pa/backend/dist/src/utils/common';
import dayjs from 'dayjs';
import MessageService from '@pa/backend/dist/src/service/message';
import { LibraFinishCloseAttribution, MsgLibraCreateSuccess, MsgLibraOverdue } from './LibraCard';
import RpcProxyManager from '@pa/backend/dist/src/rpc/proxy';
import LibraOverdueNotifySettingDao from '../../dao/libra/LibraOverdueNotifySettingDao';
import { LibraControlService } from './LibraControlService';
import { AbnormalFlightReportInfoService } from './AbnormalFlightReportInfoService';
import {
  convertLibraNewInfoToBaseInfo,
  isP00Story,
  LarkChatIdsByMeegoTeamInfo,
  MeegoProjectKeyFaceU,
} from '@shared/libra/libraManageUtils';
import LibraNewInfoListDao from '../../dao/libra/LibraNewInfoListDao';
import {
  generateFilterRule,
  getCNTokenAsync,
  getSGTokenAsync,
  GrayOrRelease,
  processFilterRules,
} from '@shared/libra/LibraCreate';
import { batch_create_experiment, experiment, flight_tag_app, param_filters_op } from '@api/libraNonOpen';
import { Experiment, libraRegionFromAppId, ParamFilters } from '@shared/libra/NonOpenCommon';
import { libraCreateParamsSchema } from '@shared/libra/NonOpenSchema';
import z from 'zod';
import { createLibraSuccess } from '@api/libra';
import { Token } from '@shared/libra/NonOpen';
import { useInject } from '@edenx/runtime/bff';

@Injectable()
export class LibraNotifyService {
  @Inject()
  private libraNewInfoListService: LibraNewInfoListService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private meegoService: MeegoRawService;
  @Inject()
  private larkCard: LarkCardService;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private messageService: MessageService;
  @Inject()
  private rpcProxy: RpcProxyManager;
  @Inject()
  private libraOverdueNotifySettingDao: LibraOverdueNotifySettingDao;
  @Inject()
  private libraControlService: LibraControlService;
  @Inject()
  private abnormalFlightReportService: AbnormalFlightReportInfoService;
  @Inject()
  private libraNewInfoListDao: LibraNewInfoListDao;

  // 获取要发送消息的 chat_id
  async getNotifyChatId(libraInfo: LibraNewInfo | null, meegoInfo?: LibraMeegoInfo): Promise<string> {
    if (meegoInfo === undefined || meegoInfo === null) {
      return TEST_CHAT_ID;
    }
    if (meegoInfo.businessLine.some(item => item.includes('商业化')) || (libraInfo && libraInfo.meegoTeamInfo)) {
      // 先在商业化团队试点 或者 包含 Meego 团队信息
      const result = await this.rpcProxy
        .getHost()
        .getMeegoChatId(
          MeegoProjectKeyFaceU,
          meegoInfo.id,
          meegoInfo.pmOwners && meegoInfo.pmOwners.length > 0 ? meegoInfo.pmOwners[0].email : undefined,
        );
      return result.chatId;
    }
    return TEST_CHAT_ID;
  }

  // 获取当前跟版的 Meego 需求以及对应的实验列表
  async getMeegoListWithLibraMapInfoByVersion(
    queryArgs: any,
    isFilterGray = false,
  ): Promise<LibraLaunchNotifyAllInfo | undefined> {
    // 筛选条件：{meegoInfo: { $ne: [], $type: "array", }, "meegoInfo.releaseVersion": { $in: ["剪映-iOS-15.1.0", "剪映-Android-15.1.0"]}}
    let pageNum = 1;
    const pageSize = 100;
    // 查询当前版本实验列表
    let libraList: LibraNewInfo[] = [];
    const pageList = await this.libraNewInfoListService.list(queryArgs, pageNum, pageSize);

    const total = await this.libraNewInfoListService.count(queryArgs);
    if (pageList !== null && pageList !== undefined && pageList.length > 0) {
      // 当分页结果添加到 libraList 中，直到 libraList 中的元素数量等于 total
      libraList.push(...pageList);
      while (libraList.length < total) {
        pageNum++;
        const nextPageList = await this.libraNewInfoListService.list(queryArgs, pageNum, pageSize);
        if (nextPageList !== null && nextPageList !== undefined && nextPageList.length > 0) {
          libraList.push(...nextPageList);
        } else {
          break;
        }
      }
    }
    if (isFilterGray) {
      libraList = libraList?.filter(item => this.libraNewInfoListService.isGrayFlightInner(item));
    }
    // 获取 Meego 和 实验列表的映射关系
    const meegoAndLibraMap = new Map<string, LibraNewInfo[]>();
    for (const libraInfo of libraList) {
      // 遍历实验列表，获取 Meego 信息
      const { meegoInfo } = libraInfo;
      if (meegoInfo === null || meegoInfo === undefined || meegoInfo.length === 0) {
        continue;
      }
      for (const meego of meegoInfo) {
        // 提取出 Meego ID
        let meegoId = meego.id;
        if (meegoId === undefined) {
          const match = meego.url.match(/\/(\d+)(\/|$)/);
          const result = match ? match[1] : undefined;
          if (result) {
            meegoId = parseInt(result, 10);
          }
        }
        if (meegoId === undefined) {
          // 如果还是找不到 meegoId 则跳过
          continue;
        }
        const meegoIdStr = meegoId.toString();
        const existingLibraInfos = meegoAndLibraMap.get(meegoIdStr) ?? [];
        const isLibraInfoExists = existingLibraInfos.some(info => info.libraAppId === libraInfo.libraAppId);
        if (!isLibraInfoExists) {
          // 存储 Meego 和实验信息的映射关系
          meegoAndLibraMap.set(meegoIdStr, [...existingLibraInfos, libraInfo]);
        }
      }
    }
    // 获取 Meego 需求信息
    const meegoIdList = Array.from(meegoAndLibraMap.keys());
    const meegoInfoList: LibraMeegoInfo[] = [];
    for (const meegoId of meegoIdList) {
      const libraInfoList = meegoAndLibraMap.get(meegoId) ?? [];
      if (libraInfoList.length > 0) {
        const libraBindMeegos = libraInfoList.map(info => info.meegoInfo ?? []).flat();
        for (const libraBindMeego of libraBindMeegos) {
          if (libraBindMeego.id && libraBindMeego.id.toString() === meegoId) {
            meegoInfoList.push(libraBindMeego);
            break;
          }
        }
      }
    }

    return {
      meegoInfoList,
      meegoAndLibraMap,
    } as LibraLaunchNotifyAllInfo;
  }

  // 发送实验未创建通知
  async sendNoLibraInfoNotify(
    appId: number,
    version: string,
    fullVersionDesc: string,
    versionStageName: string,
    meegoId: number,
  ) {
    const meegoInfo = await this.meegoService.requestWorkItem('faceu', 'story', [meegoId]);
    if (meegoInfo && meegoInfo.data && meegoInfo.data.length > 0) {
      const card = this.larkCard.createNoLibraNotifyCard(
        appId,
        version,
        fullVersionDesc,
        versionStageName,
        meegoId,
        meegoInfo.data[0].name,
      );
      const libraNotifyType = LibraNotifyType.Gray100Percent;
      if (card) {
        // const meegoChatId = await this.rpcProxy.getHost().getMeegoChatId(meegoInfo.data[0].name, meegoId, undefined);
        const meegoChatId = TEST_CHAT_ID;
        if (meegoChatId !== TEST_CHAT_ID) {
          // 说明是推送给了需求群，用日志记录一下
          this.logger.info(
            // eslint-disable-next-line max-len
            `[LibraNotifyService] sendLaunchNotifyCard, meegoChatId: ${meegoChatId}, libraNotifyType: ${libraNotifyType}`,
          );
        }
        await this.larkService.sendCardMessage(UserIdType.chatId, meegoChatId, card);
      }
    }
  }

  // 发送开启100%流量灰度实验通知
  async send100PercentGrayExperimentNotifyCard(createCardInfo: LibraLaunchNotifyCreateCardInfo) {
    const { appId, version, fullVersionDesc, versionStageName, startTime, meegoInfo, libraInfos, cardType } =
      createCardInfo;
    // 遍历实验列表，发送通知
    for (const libraInfo of libraInfos) {
      const notifyCardInfo = {
        appId,
        version,
        fullVersionDesc,
        versionStageName,
        startTime,
        meegoId: meegoInfo.id,
        meegoName: meegoInfo.name,
        meegoUrl: meegoInfo.url,
        pmOwners: meegoInfo.pmOwners ?? [],
        techOwners: meegoInfo.techOwners ?? [],
        libraInfo,
      } as LibraLaunchNotifyCardInfo;
      const card = this.larkCard.create100PercentGrayNotifyCard(notifyCardInfo);
      const libraNotifyType = LibraNotifyType.Gray100Percent;
      if (card) {
        const meegoChatId = await this.getNotifyChatId(libraInfo, meegoInfo);
        // const meegoChatId = TEST_CHAT_ID;
        if (meegoChatId !== TEST_CHAT_ID) {
          // 说明是推送给了需求群，用日志记录一下
          this.logger.info(
            // eslint-disable-next-line max-len
            `[LibraNotifyService] sendLaunchNotifyCard, meegoChatId: ${meegoChatId}, libraNotifyType: ${libraNotifyType}`,
          );
        }
        await this.larkService.sendCardMessage(UserIdType.chatId, meegoChatId, card);
        // await this.larkService.sendCardMessage(UserIdType.openId, 'ou_4595be854888d986de5e9c8914183828', card);
        // 更新消息提醒记录
        const record = {
          type: libraNotifyType,
          receiverType: LibraNotifyReceiverType.Group,
          receiverId: meegoChatId,
          time: Math.floor(Date.now() / 1000),
          title: card.header?.title.content ?? '',
        };
        if (libraInfo.notifyRecordInfo === undefined) {
          // 事先没有消息提醒记录，创建 1 个
          libraInfo.notifyRecordInfo = [record];
        } else {
          // 更新已有的
          libraInfo.notifyRecordInfo.push(record);
        }
        await this.libraNewInfoListService.update(libraInfo);
      }
    }
  }

  // 发送开启实验通知卡片
  async sendLaunchNotifyCard(createCardInfo: LibraLaunchNotifyCreateCardInfo) {
    const { appId, version, fullVersionDesc, versionStageName, startTime, meegoInfo, libraInfos, cardType } =
      createCardInfo;
    // 遍历实验列表，发送通知
    for (const libraInfo of libraInfos) {
      const notifyCardInfo = {
        appId,
        version,
        fullVersionDesc,
        versionStageName,
        startTime,
        meegoId: meegoInfo.id,
        meegoName: meegoInfo.name,
        meegoUrl: meegoInfo.url,
        pmOwners: meegoInfo.pmOwners ?? [],
        techOwners: meegoInfo.techOwners ?? [],
        libraInfo,
      } as LibraLaunchNotifyCardInfo;
      let card;
      let libraNotifyType = LibraNotifyType.Unknown;
      if (cardType === LibraLaunchNotifyCardType.Gray) {
        // 开启灰度实验卡片通知
        card = this.larkCard.createLibraLaunchGrayNotifyCard(notifyCardInfo);
        libraNotifyType = LibraNotifyType.LaunchGray;
      } else if (cardType === LibraLaunchNotifyCardType.Release) {
        // 开启正式实验卡片通知
        card = this.larkCard.createLibraLaunchReleaseNotifyCard(notifyCardInfo);
        libraNotifyType = LibraNotifyType.LaunchRelease;
        // 版本全量两天后还未开启正式实验，记录异常
        // const currentTime = Math.floor(Date.now() / 1000);
        // if (currentTime - startTime > 3600 * 48) {
        //   await this.abnormalFlightReportService.generalSaveAbnormalInfo(
        //     meegoInfo.id,
        //     libraInfo,
        //     AbnormalFlightReportInfoType.StoryNotStartReleaseFlightAfterFullRelease,
        //   );
        // }
      } else if (cardType === LibraLaunchNotifyCardType.CloseGray) {
        // 关闭灰度实验卡片通知
        card = this.larkCard.createLibraCloseGrayNotifyCard(notifyCardInfo, LibraCreateState.Uncreated);
        libraNotifyType = LibraNotifyType.CloseGray;
      }
      if (card) {
        const meegoChatId = await this.getNotifyChatId(libraInfo, meegoInfo);
        if (meegoChatId !== TEST_CHAT_ID) {
          // 说明是推送给了需求群，用日志记录一下
          this.logger.info(
            // eslint-disable-next-line max-len
            `[LibraNotifyService] sendLaunchNotifyCard, meegoChatId: ${meegoChatId}, libraNotifyType: ${libraNotifyType}`,
          );
        }
        await this.larkService.sendCardMessage(UserIdType.chatId, meegoChatId, card);
        //await this.larkService.sendCardMessage(UserIdType.openId, 'ou_288568c940e05d5a96e07227d7103b6a', card);
        if (libraNotifyType !== LibraNotifyType.Unknown) {
          // 更新消息提醒记录
          const record = {
            type: libraNotifyType,
            receiverType: LibraNotifyReceiverType.Group,
            receiverId: meegoChatId,
            time: Math.floor(Date.now() / 1000),
            title: card.header?.title.content ?? '',
          };
          if (libraInfo.notifyRecordInfo === undefined) {
            // 事先没有消息提醒记录，创建 1 个
            libraInfo.notifyRecordInfo = [record];
          } else {
            // 更新已有的
            libraInfo.notifyRecordInfo.push(record);
          }
          await this.libraNewInfoListService.update(libraInfo);
        }
      }
    }
  }

  // 发送 Review 实验数据卡片
  async sendRemindReviewDataNotifyCard(meegoInfo: LibraMeegoInfo, libraInfos: LibraNewInfo[]) {
    // 获取今天的日期对象
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 忽略时间部分，仅比较日期
    // 遍历实验列表，发送通知
    for (const libraInfo of libraInfos) {
      let shouldNotify = false; // 是否需要发送通知
      const { startTime } = libraInfo.flightInfo;
      const startDate = new Date(startTime * 1000);
      // 如果是纸飞机创建的实验，则会有数据回收提醒频率(单位:天)
      if (libraInfo.extraInfo && libraInfo.extraInfo.dataRecoveryRemind.length > 0) {
        for (const frequency of libraInfo.extraInfo.dataRecoveryRemind) {
          // 间隔第 frequency 天
          const day = new Date(startDate);
          day.setDate(startDate.getDate() + frequency);
          if (day.getTime() === today.getTime()) {
            shouldNotify = true;
            break;
          }
        }
      } else {
        // 否则默认使用第 15 天进行提醒
        const day = new Date(startDate);
        day.setDate(startDate.getDate() + 15);
        if (day.getTime() === today.getTime()) {
          shouldNotify = true;
        }
      }
      if (!shouldNotify) {
        // 不满足提醒条件，跳过
        continue;
      }
      const notifyCardInfo = {
        meegoId: meegoInfo.id,
        meegoName: meegoInfo.name,
        meegoUrl: meegoInfo.url,
        pmOwners: meegoInfo.pmOwners ?? [],
        techOwners: meegoInfo.techOwners ?? [],
        libraInfo,
      } as LibraRemindDataReviewNotifyCardInfo;
      const card = this.larkCard.createLibraRemindDataReviewNotifyCard(notifyCardInfo);
      const libraNotifyType = LibraNotifyType.RemindDataReview;
      if (card) {
        const meegoChatId = await this.getNotifyChatId(libraInfo, meegoInfo);
        if (meegoChatId !== TEST_CHAT_ID) {
          // 说明是推送给了需求群，用日志记录一下
          this.logger.info(
            // eslint-disable-next-line max-len
            `[LibraNotifyService] sendRemindReviewDataNotifyCard, meegoChatId: ${meegoChatId}, libraNotifyType: ${libraNotifyType}`,
          );
        }
        await this.larkService.sendCardMessage(UserIdType.chatId, meegoChatId, card);
        // await this.larkService.sendCardMessage(UserIdType.openId, 'ou_4595be854888d986de5e9c8914183828', card);
        // 更新消息提醒记录
        const record = {
          type: libraNotifyType,
          receiverType: LibraNotifyReceiverType.Group,
          receiverId: meegoChatId,
          time: Math.floor(Date.now() / 1000),
          title: card.header?.title.content ?? '',
        };
        if (libraInfo.notifyRecordInfo === undefined) {
          // 事先没有消息提醒记录，创建 1 个
          libraInfo.notifyRecordInfo = [record];
        } else {
          // 更新已有的
          libraInfo.notifyRecordInfo.push(record);
        }
        await this.libraNewInfoListService.update(libraInfo);
      }
    }
  }

  // 开启灰度实验通知
  async launchGrayNotify(
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) {
    // 需要将手动点击“不再提醒”的 case 排除掉（即筛选 forbidNotifyInfo）
    const queryArgs = {
      meegoInfo: { $ne: [], $type: 'array' },
      'meegoInfo.releaseVersion': { $in: [fullVersionDesc] },
      // 'flightInfo.status': { $in: [LibraFlightStatus.InProgress, LibraFlightStatus.InDebug] },
      $nor: [
        {
          forbidNotifyInfo: {
            $elemMatch: {
              type: LibraNotifyType.LaunchGray,
              isForbid: true,
            },
          },
        },
      ],
    };
    const meegoListWithLibraMapInfo = await this.getMeegoListWithLibraMapInfoByVersion(queryArgs);
    if (meegoListWithLibraMapInfo === undefined) {
      return;
    }
    const { meegoInfoList, meegoAndLibraMap } = meegoListWithLibraMapInfo;
    for (const meegoInfo of meegoInfoList) {
      // 发送飞书卡片
      await this.sendLaunchNotifyCard({
        appId,
        version: grayVersion,
        fullVersionDesc,
        versionStageName,
        startTime,
        meegoInfo,
        libraInfos: meegoAndLibraMap.get(meegoInfo.id?.toString()) ?? [],
        cardType: LibraLaunchNotifyCardType.Gray,
      });
    }
  }

  // 灰度实验100%通知
  async gray100PercentNotify(
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
    meegoIds: number[],
  ) {
    for (const id of meegoIds) {
      const queryArgs = {
        meegoInfo: { $ne: [], $type: 'array' },
        'meegoInfo.id': id,
        'meegoInfo.releaseVersion': { $in: [fullVersionDesc] },
        $nor: [
          {
            forbidNotifyInfo: {
              $elemMatch: {
                type: LibraNotifyType.Gray100Percent,
                isForbid: true,
              },
            },
          },
        ],
      };
      const meegoListWithLibraMapInfo = await this.getMeegoListWithLibraMapInfoByVersion(queryArgs);
      if (meegoListWithLibraMapInfo === undefined || meegoListWithLibraMapInfo.meegoInfoList.length === 0) {
        // 封版时已经要求需求绑定实验，这里暂时跳过
        // await this.sendNoLibraInfoNotify(appId, grayVersion, fullVersionDesc, versionStageName, id);
        continue;
      }
      const { meegoInfoList, meegoAndLibraMap } = meegoListWithLibraMapInfo;
      for (const meegoInfo of meegoInfoList) {
        const has100Percent = await this.libraControlService.checkGrayLibra100Percent(meegoInfo.id);
        if (has100Percent.result) {
          continue;
        }
        // 发送飞书卡片
        await this.send100PercentGrayExperimentNotifyCard({
          appId,
          version: grayVersion,
          fullVersionDesc,
          versionStageName,
          startTime,
          meegoInfo,
          libraInfos: meegoAndLibraMap.get(meegoInfo.id?.toString()) ?? [],
          cardType: LibraLaunchNotifyCardType.Gray,
        });
      }
    }
  }
  private getNormalizedValueString(value: any): string {
    const normalizeValue = (v: any) => {
      if (typeof v === 'string') {
        return v.toLowerCase();
      } else if (v && typeof v === 'object' && 'name' in v) {
        return v.name.toLowerCase();
      }
      return v;
    };

    return Array.isArray(value) ? value.map(normalizeValue).join(',') : normalizeValue(value);
  }

  private convertExperimentToCreateParams(
    exp: Experiment,
    paramFilter: ParamFilters[],
  ): z.infer<typeof libraCreateParamsSchema> {
    if (exp.name.includes('灰度')) {
      exp.name = exp.name.replace('灰度', '正式');
    } else {
      exp.name = `【正式实验】${exp.name}`;
    }
    exp.name += '-auto-create';
    return {
      only_verification: false,
      skip_verification: false,
      manage_type: exp.manage_type,
      owners: exp.owners.map(owner => ({
        id: 0, // 需要从实际数据中获取
        name: owner.name,
        avatar: undefined,
      })),
      duration: 30 * 24 * 60 * 60,
      is_long_time_flight: Boolean(exp.is_long_time_flight),
      version_resource: 0.01,
      book_version_resource: exp.book_version_resource,
      enable_gradual: true, // 默认值
      gradual_traffic_duration: 60,
      name: exp.name,
      description: exp.description,
      app_id: exp.app_id,
      ad_flight_hit_rules: exp.ad_flight_hit_rules === null ? [] : exp.ad_flight_hit_rules,
      experiment_mode: 1, // 默认值
      feature_type: undefined,
      product_id: exp.product_id,
      layer_info: exp.layer_info || {
        product_id: exp.product_id,
        hash_strategy: '', // 需要从实际数据中获取
        layer_id: exp.layer,
      },
      versions: exp.versions.map(version => ({
        name: version.name,
        type: version.type,
        description: version.description,
        config: version.config,
        settings_keys: version.settings_keys?.map(key => ({
          settings_item_id: '', // 需要从实际数据中获取
          key: key.key,
          value: key.value,
          type: key.type,
          desc: key.desc,
          prefix: 'default',
          parseValue: { enable: true },
          item_id: 0, // 需要从实际数据中获取
        })),
      })),
      filter_rule: generateFilterRule(processFilterRules({ filterRules: exp.filter_rule }, true), paramFilter),
      meego_info: exp.meego_info && {
        meego_array: exp.meego_info.meego_array.map(item => ({
          meego_story: item.meego_story,
          meego_project: item.meego_project,
          meego_project_key: item.meego_project_key,
        })),
      },
      expectation: exp.expectation,
      specified_psms: exp.specified_psms,
      effected_regions: [],
      is_open_beta_combine: false,
      beta_combine_type: 0,
      short_name: undefined,
      metrics:
        exp.metrics?.map(metric => ({
          ...metric,
          bundle_id: metric.bundle_id || 0, // 确保有默认值
        })) || [], // 确保是数组
      filter_user_list: exp.filter_user_list,
    };
  }

  async createReleaseFromGray(flightId: number, tokens: Token[], appId: number) {
    // 获取用户Token
    if (tokens.length === 0) {
      return {
        code: -1,
        message: '获取用户Token失败',
      };
    }

    // 获取灰度实验详情
    const experimentResponse = await experiment({
      headers: {},
      data: {
        libra_app_id: appId,
        tokens,
        flightId,
      },
    });

    if (experimentResponse.code !== 200 || !experimentResponse.data) {
      return {
        code: -1,
        message: `获取灰度实验详情失败: ${experimentResponse.message || experimentResponse.data || '未知错误'}`,
      };
    }
    const paramFilter = await param_filters_op({
      headers: {},
      data: {
        libra_app_id: appId,
        tokens,
        manage_type: experimentResponse.data.manage_type,
        product_id: experimentResponse.data.product_id,
      },
    });
    if (paramFilter.code !== 0 || !paramFilter.data) {
      return {
        code: -1,
        message: `获取参数过滤规则失败: ${paramFilter.message || '未知错误'}`,
      };
    }
    const experimentData = experimentResponse.data;
    const paramFilterData = paramFilter.data;
    // 创建正式实验参数
    const createParams = this.convertExperimentToCreateParams(experimentData, paramFilterData);
    // 调用创建实验API
    const result = await batch_create_experiment({
      headers: {},
      data: {
        libra_app_id: appId,
        tokens,
        experiments: [createParams],
      },
    });

    if (result.code !== 200) {
      return {
        code: -1,
        message: `创建正式实验失败: ${result.message || '未知错误'}`,
      };
    } else {
      result.data?.experiments?.forEach((flight, index) => {
        const flight_id = result.data?.experiments?.[index]?.id;
        if (flight_id !== undefined) {
          createLibraSuccess({
            data: {
              appId,
              region: libraRegionFromAppId(appId),
              flightId: flight_id,
              extraInfo: {
                grayOrRelease: GrayOrRelease.release,
                isReopen: false,
                reopenReason: [],
                dataRecoveryRemind: [],
              },
            },
          }).then(() => {
            //   if (experimentData.skipControl) {
            //     // 跳过管控的时候需要记录异常信息
            //     recordAbnormalFlightReportInfo({
            //       data: {
            //         meegoId: createParams.meegoInfo?.id ?? -1,
            //         flightId,
            //       },
            //     });
            //   }
            // });
            // 对广告有影响需额外配置
            if (createParams.ad_flight_hit_rules !== undefined) {
              flight_tag_app({
                headers: {},
                data: {
                  libra_app_id: appId,
                  tokens,
                  flightId: flight_id,
                },
              }).then();
            }
          });
        }
      });
    }
    return {
      code: 0,
      message: 'success',
      data: result.data,
    };
  }
  // 关闭灰度实验通知
  async closeGrayNotify(
    appId: number,
    grayVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) {
    // 需要将手动点击“不再提醒”的 case 排除掉（即筛选 forbidNotifyInfo）
    const queryArgs = {
      meegoInfo: { $ne: [], $type: 'array' },
      'meegoInfo.releaseVersion': { $in: [fullVersionDesc] },
      'flightInfo.status': { $in: [LibraFlightStatus.InProgress] },
      'meegoInfo.businessLine': { $not: { $elemMatch: { $eq: '商业化' } } },
      $or: [
        { extraInfo: { $exists: false } },
        {
          extraInfo: { $exists: true },
          'extraInfo.grayOrRelease': { $eq: 'gray' },
        },
      ],
      $nor: [
        {
          forbidNotifyInfo: {
            $elemMatch: {
              type: LibraNotifyType.CloseGray,
              isForbid: true,
            },
          },
        },
      ],
    };
    const meegoListWithLibraMapInfo = await this.getMeegoListWithLibraMapInfoByVersion(queryArgs, true);
    if (meegoListWithLibraMapInfo === undefined) {
      return;
    }
    const { meegoInfoList, meegoAndLibraMap } = meegoListWithLibraMapInfo;
    for (const meegoInfo of meegoInfoList) {
      // 判断是否跟车版本全部全量
      let isAllFullRelease = true;
      if (meegoInfo.releaseVersion && meegoInfo.releaseVersion.length > 1) {
        // 判断是否存在跟车两个版本
        let count = 0;
        for (let i = 0; i < meegoInfo.releaseVersion.length; i++) {
          const version = meegoInfo.releaseVersion[i];
          if (version === fullVersionDesc) {
            continue;
          }
          if (version.includes('剪映') || version.includes('CC')) {
            count++;
          }
        }
        if (count > 0) {
          // 判断是否每个版本都已经全量
          for (const version of meegoInfo.releaseVersion) {
            if (version === fullVersionDesc) {
              continue;
            }
            let app_id = 0;
            if (version.includes('CC') && version.includes('Android')) {
              app_id = 300602;
            } else if (version.includes('CC') && version.includes('iOS')) {
              app_id = 300601;
            } else if (version.includes('剪映') && version.includes('Android')) {
              app_id = 177502;
            } else if (version.includes('剪映') && version.includes('iOS')) {
              app_id = 177501;
            }
            const res: any[] = await useInject(RpcProxyManager).getHost().getVersionList(app_id);
            const versionInfo = res.find(({ version: version1 }) => {
              // 分割字符串获取版本号部分
              const versionParts = version1.split('-');
              const versionNumber = versionParts.length > 2 ? versionParts[2] : version1;
              return versionNumber === version.split('-')[2];
            });
            const versionStage = versionInfo?.version_stages?.find((stage: any) => {
              if (version.includes('Android')) {
                return stage.stage_name === 'full_release_precheck';
              } else if (version.includes('iOS')) {
                return stage.stage_name === 'submit';
              }
              return false;
            });
            if (versionStage?.status === 'Not_Start') {
              isAllFullRelease = false;
              break;
            }
          }
        }
      }
      if (!isAllFullRelease) {
        continue;
      }
      await this.sendLaunchNotifyCard({
        appId,
        version: grayVersion,
        fullVersionDesc,
        versionStageName,
        startTime,
        meegoInfo,
        libraInfos: meegoAndLibraMap.get(meegoInfo.id.toString()) ?? [],
        cardType: LibraLaunchNotifyCardType.CloseGray,
      });
    }
    return meegoListWithLibraMapInfo;
  }

  // 开启正式实验通知
  async launchReleaseNotify(
    appId: number,
    releaseVersion: string,
    fullVersionDesc: string,
    versionStageName: string,
    startTime: number,
  ) {
    // 需要将手动点击“不再提醒”的 case 排除掉（即筛选 forbidNotifyInfo）
    const queryArgs = {
      meegoInfo: { $ne: [], $type: 'array' },
      'meegoInfo.releaseVersion': { $in: [fullVersionDesc] },
      // 'flightInfo.status': { $in: [LibraFlightStatus.InProgress] },
      $nor: [
        {
          forbidNotifyInfo: {
            $elemMatch: {
              type: LibraNotifyType.LaunchRelease,
              isForbid: true,
            },
          },
        },
      ],
    };
    const meegoListWithLibraMapInfo = await this.getMeegoListWithLibraMapInfoByVersion(queryArgs);
    if (meegoListWithLibraMapInfo === undefined) {
      return;
    }
    const { meegoInfoList, meegoAndLibraMap } = meegoListWithLibraMapInfo;
    for (const meegoInfo of meegoInfoList) {
      // 发送飞书卡片
      await this.sendLaunchNotifyCard({
        appId,
        version: releaseVersion,
        fullVersionDesc,
        versionStageName,
        startTime,
        meegoInfo,
        libraInfos: meegoAndLibraMap.get(meegoInfo.id.toString()) ?? [],
        cardType: LibraLaunchNotifyCardType.Release,
      });
    }
  }

  // 通过正式实验创建灰度实验
  async libraCreateReleaseByCopyGrey(libraInfo: LibraNewInfo) {}

  // 屏蔽开启灰度实验通知
  async forbidLaunchGrayNotify(libraInfo: LibraNewInfo) {
    // 手动点击“不再提醒”，更新数据库
    const record = {
      type: LibraNotifyType.LaunchGray,
      isForbid: true,
      forbidTime: Math.floor(Date.now() / 1000),
    };
    if (libraInfo.forbidNotifyInfo === undefined) {
      libraInfo.forbidNotifyInfo = [record];
    } else {
      libraInfo.forbidNotifyInfo.push(record);
    }
    await this.libraNewInfoListService.update(libraInfo);
  }

  // 屏蔽100%流量灰度实验通知
  async forbid100PercentGrayNotify(libraInfo: LibraNewInfo) {
    // 手动点击“不再提醒”，更新数据库
    const record = {
      type: LibraNotifyType.Gray100Percent,
      isForbid: true,
      forbidTime: Math.floor(Date.now() / 1000),
    };
    if (libraInfo.forbidNotifyInfo === undefined) {
      libraInfo.forbidNotifyInfo = [record];
    } else {
      libraInfo.forbidNotifyInfo.push(record);
    }
    await this.libraNewInfoListService.update(libraInfo);
  }

  // 屏蔽关闭灰度实验通知
  async forbidCloseGrayNotify(libraInfo: LibraNewInfo) {
    console.log('forbidCloseGrayNotify', libraInfo);
    // 手动点击“不再提醒”，更新数据库
    const record = {
      type: LibraNotifyType.CloseGray,
      isForbid: true,
      forbidTime: Math.floor(Date.now() / 1000),
    };
    if (libraInfo.forbidNotifyInfo === undefined) {
      libraInfo.forbidNotifyInfo = [record];
    } else {
      libraInfo.forbidNotifyInfo.push(record);
    }
    console.log('forbidCloseGrayNotify end', libraInfo);
    await this.libraNewInfoListService.update(libraInfo);
  }

  // 屏蔽开启正式实验通知
  async forbidLaunchReleaseNotify(libraInfo: LibraNewInfo) {
    // 手动点击“不再提醒”，更新数据库
    const record = {
      type: LibraNotifyType.LaunchRelease,
      isForbid: true,
      forbidTime: Math.floor(Date.now() / 1000),
    };
    if (libraInfo.forbidNotifyInfo === undefined) {
      libraInfo.forbidNotifyInfo = [record];
    } else {
      libraInfo.forbidNotifyInfo.push(record);
    }
    await this.libraNewInfoListService.update(libraInfo);
  }

  // Review 实验数据通知
  async remindDataReviewNotify() {
    // 获取正在进行中的实验
    const queryArgs = {
      'flightInfo.status': { $in: [LibraFlightStatus.InProgress] },
      libraAppId: {
        $exists: true,
        $ne: null,
      },
    };
    const meegoListWithLibraMapInfo = await this.getMeegoListWithLibraMapInfoByVersion(queryArgs);
    if (meegoListWithLibraMapInfo === undefined) {
      return;
    }
    const { meegoInfoList, meegoAndLibraMap } = meegoListWithLibraMapInfo;
    for (const meegoInfo of meegoInfoList) {
      // 发送飞书卡片
      await this.sendRemindReviewDataNotifyCard(meegoInfo, meegoAndLibraMap.get(meegoInfo.id.toString()) ?? []);
    }
  }

  /**
   * 管控类消息发送统一入口，默认发送meego群聊
   * @param msg
   * @param libraInfo
   * @param type
   */
  async sendLibraMsg(
    type: LibraNotifyType,
    msg: MsgTemplate,
    libraInfo: LibraNewInfo | null,
    givenMeegoInfo?: LibraMeegoInfo,
  ) {
    // const receiverId = libraInfo?.meegoInfo?.[0].chatId ?? TEST_CHAT_ID;
    // const receiverId = TEST_CHAT_ID; // FIXME 先都发测试群
    let meegoInfo;
    if (givenMeegoInfo) {
      // 如果指定 Meego Info，则用 givenMeegoInfo
      meegoInfo = givenMeegoInfo;
    } else {
      // 否则，使用 libra 的第一个 Meego 信息
      meegoInfo = libraInfo?.meegoInfo?.[0];
    }
    const receiverId = await this.getNotifyChatId(libraInfo, meegoInfo);
    if (receiverId !== TEST_CHAT_ID) {
      // 说明是推送给了需求群，用日志记录一下
      this.logger.info(
        // eslint-disable-next-line max-len
        `[LibraNotifyService] sendLibraMsg, meegoChatId: ${receiverId}, libraNotifyType: ${type}`,
      );
    }
    let ret = await this.messageService.sendNormalMsg(msg, UserIdType.chatId, receiverId);
    if (ret.code !== NetworkCode.Success) {
      this.logger.warn(`[sendLibraMsg] send msg failed, msg: ${JSON.stringify(msg)}, ret: ${JSON.stringify(ret)}`);
      return;
    }
    // 如果是 P00 需求，则再话题群再发送一次，目前只 For 剪映
    if (libraInfo && libraInfo.meegoTeamInfo && libraInfo.meegoTeamInfo.length > 0 && isP00Story(libraInfo)) {
      // 获取话题群 chat_id
      const meegoTeamChatIds = LarkChatIdsByMeegoTeamInfo(libraInfo);
      if (meegoTeamChatIds.length > 0) {
        ret = await this.messageService.sendNormalMsg(msg, UserIdType.chatId, meegoTeamChatIds[0]);
        if (ret.code !== NetworkCode.Success) {
          this.logger.warn(
            `[sendLibraMsg][话题群] send msg failed, msg: ${JSON.stringify(msg)}, ret: ${JSON.stringify(ret)}`,
          );
          return;
        }
      }
    }
    // 更新发送记录
    if (libraInfo) {
      const record = {
        type,
        receiverType: LibraNotifyReceiverType.Group,
        receiverId,
        time: dayjs().unix(),
        title: msg.name,
        content: msg.msgContent,
      } as LibraNotifyRecordInfo;
      if (!libraInfo.notifyRecordInfo) {
        libraInfo.notifyRecordInfo = [record];
      } else {
        libraInfo.notifyRecordInfo.push(record);
      }
      await this.libraNewInfoListService.update(libraInfo);
    }
  }

  // 超期实验巡检
  async overdueLibraNotify(appId: number, version: string, meegoVersion: string, fullReleaseTime: number) {
    const dayGap = dayjs().diff(dayjs.unix(fullReleaseTime).endOf('day'), 'day');
    this.logger.info(`[overdueLibraNotify] dayGap: ${dayGap} # ${appId} ${version} ${meegoVersion} ${fullReleaseTime}`);
    if (![30].includes(dayGap)) {
      return;
    }
    const queryArgs = {
      'meegoInfo.releaseVersion': { $in: [meegoVersion] },
      // 仅巡检进行中实验
      'flightInfo.status': { $in: [LibraFlightStatus.InProgress] },
    };
    const meegoListWithLibraMapInfo = await this.getMeegoListWithLibraMapInfoByVersion(queryArgs);
    if (meegoListWithLibraMapInfo === undefined) {
      return;
    }
    const { meegoInfoList, meegoAndLibraMap } = meegoListWithLibraMapInfo;
    for (const meegoInfo of meegoInfoList) {
      // 先用数据库配置各业务线的超期通知时间，业务线取meego业务线
      let notifyDays = 30;
      const businessLine = meegoInfo.businessLine.join('/');
      const overdueNotifyConfig = await this.libraOverdueNotifySettingDao.findOneByBusinessLine(businessLine);
      if (overdueNotifyConfig) {
        notifyDays = overdueNotifyConfig.days;
      }
      if (dayGap <= notifyDays) {
        continue;
      }
      const libras = meegoAndLibraMap.get(meegoInfo.id.toString()) ?? [];
      for (const info of libras) {
        const msg = new MsgLibraOverdue(info, meegoVersion, dayGap);
        await this.sendLibraMsg(LibraNotifyType.Overdue, msg, info);
        // 记录异常信息
        // await this.abnormalFlightReportService.generalSaveAbnormalInfo(
        //   meegoInfo.id,
        //   info,
        //   AbnormalFlightReportInfoType.StoryOverdueFlightAfterFullRelease,
        // );
      }
    }
  }

  /**
   * 实验创建成功通知
   * @param libraNewInfo
   */
  async notifyWhenLibraCreate(libraNewInfo: LibraNewInfo) {
    const msg = new MsgLibraCreateSuccess(libraNewInfo);
    for (const owner of libraNewInfo.flightInfo.owners) {
      await this.messageService.sendNormalMsg(msg, UserIdType.openId, owner.open_id);
    }
  }

  // 单个实验完成关闭归因填写发送通知
  async libraCloseAttributionFinishedNotify(flightId: number, sendPrivate?: boolean) {
    const libraInfo = await this.libraNewInfoListDao.findById(flightId.toString());
    if (!libraInfo) {
      return {
        code: -1,
        message: `[Error] libraInfo not found, flightId: ${flightId}`,
      };
    }
    const libraBaseInfo = convertLibraNewInfoToBaseInfo(libraInfo);
    const msg = new LibraFinishCloseAttribution(libraBaseInfo, libraInfo);

    if (sendPrivate) {
      // @FIXME 先临时转发给个人 <EMAIL>
      const ret = await this.messageService.sendNormalMsg(
        msg,
        UserIdType.openId,
        'ou_4595be854888d986de5e9c8914183828',
      );
      if (ret.code !== NetworkCode.Success) {
        this.logger.warn(`[sendLibraMsg] send msg failed, msg: ${JSON.stringify(msg)}, ret: ${JSON.stringify(ret)}`);
      }
      return {
        code: ret.code,
        message:
          ret.code === NetworkCode.Success ? '[sendPrivate] success' : `[sendPrivate] failed: ${JSON.stringify(ret)}`,
      };
    }

    if (!libraInfo.meegoInfo || libraInfo.meegoInfo.length === 0) {
      // 没有绑定 Meego，直接拉群
      return {
        code: -1,
        message: `[Error] meegoInfo not found, flightId: ${flightId}`,
      };
    }

    for (const meegoInfo of libraInfo.meegoInfo) {
      await this.sendLibraMsg(LibraNotifyType.DidFinishCloseAttribution, msg, libraInfo, meegoInfo);
    }

    return {
      code: 0,
      message: 'success',
    };
  }
}
