import { NetworkCode } from '@pa/shared/dist/src/core';
import McpTokenService from '../service/mcp/McpTokenService';
import { McpTokenResponse } from '@shared/mcp/mcp-info';
import { Api, Data, Post, useInject } from '@edenx/runtime/bff';
import z from 'zod';

export const getMcpToken = Api(
  Post('/get_mcp_token'),
  Data(
    z.object({
      email: z.string(),
    }),
  ),
  async ({ data }) => {
    const mcpTokenService = useInject(McpTokenService);
    let token = await mcpTokenService.getTokenByEmail(data.email);
    if (!token) {
      token = await mcpTokenService.generateToken(data.email);
    }

    return {
      code: NetworkCode.Success,
      data: { token } as McpTokenResponse,
    };
  },
);

export const refreshMcpToken = Api(
  Post('/refresh_mcp_token'),
  Data(
    z.object({
      email: z.string(),
    }),
  ),
  async ({ data }) => {
    const mcpTokenService = useInject(McpTokenService);

    const token = await mcpTokenService.generateToken(data.email);

    return {
      code: NetworkCode.Success,
      data: { token } as McpTokenResponse,
    };
  },
);
