import React, { useEffect, useState } from 'react';
import { useModel } from '@edenx/runtime/model';
import UserSettingModule from '@/model/userSettingModel';
import LibraListTable from '@/pages/libra/list/LibraListTable';
import { LibraNewInfoTablePageStatus } from '@shared/libra/LibraNewInfoTableColumnSetting';
import {
  fetchFlightTableColumnSetting,
  getMeegoTeamTreeSelectDataSource,
  getRoleTypeByEmail,
  updateFlightTableColumnSetting,
} from '@api/libra';
import CustomLibraListColumnPanelButton from '@/pages/libra/list/component/CustomLibraListColumnPanelButton';
import AddMissingExperimentButton from '@/pages/libra/list/component/AddMissingExperimentButton';
import { Empty, Space, Spin } from '@douyinfe/semi-ui';
import LibraListTableHeader, {
  TransitionKeysValuesFromTreeData,
} from '@/pages/libra/list/component/LibraListTableHeader';
import { LibraPlatformPermission, LibraPlatformRoleType } from '@shared/libra/LibraPlatformUserMemberInfo';
import { IllustrationConstruction, IllustrationConstructionDark } from '@douyinfe/semi-illustrations';
import {
  dateToTimestamp,
  LibraBusinessSelectOptions,
  LibraListQuerySearchParamsKey,
} from '@shared/libra/libraManageUtils';
import { useSearchParams } from '@edenx/runtime/router';
import { getUserInfoByEmails } from '@api/index';
import { getMeegoTreeValue } from '@shared/storyRevenueReviewPlatform/StoryRevenuePlatformUtils';
import { LibraFlightHasNoCloseAttribution, LibraFlightStatus, LibraTimeQueryType } from '@shared/libra/LibraNewInfo';
import { logger } from '@/pages/quality/metric/version/utils/Logger';

const LibraListPage: React.FC = () => {
  const [userSettingState] = useModel(UserSettingModule);
  const [pageStatus, setPageStatus] = useState<LibraNewInfoTablePageStatus>({});
  const [searchTriggerFlag, setSearchTriggerFlag] = useState<boolean>(false);

  const [roleTypeLoading, setRoleTypeLoading] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();

  const refreshUserRoleType = async () => {
    setRoleTypeLoading(true);
    if (userSettingState.info.email) {
      const roleType = await getRoleTypeByEmail({
        data: {
          email: userSettingState.info.email,
        },
      }).then(res => {
        setRoleTypeLoading(false);
        if (res?.code === 0) {
          return res.roleType;
        }
      });
      return roleType;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (userSettingState.info.email) {
        try {
          const userRoleResult = await refreshUserRoleType();
          const columnSettingResult = await fetchFlightTableColumnSetting({
            data: {
              userEmail: userSettingState.info.email,
            },
          });

          if (columnSettingResult && columnSettingResult.code === 0) {
            const flightType = searchParams.get(LibraListQuerySearchParamsKey.FlightType);
            const flightValue = searchParams.get(LibraListQuerySearchParamsKey.FlightValue);
            const appId = searchParams.get(LibraListQuerySearchParamsKey.AppId);
            const emails = searchParams.get(LibraListQuerySearchParamsKey.Owners);
            const businessTeams = searchParams.get(LibraListQuerySearchParamsKey.BusinessTeams);
            const bussinessLines = searchParams.get(LibraListQuerySearchParamsKey.BusinessLines);
            const closeAttribution = searchParams.get(LibraListQuerySearchParamsKey.CloseAttribution);
            const startTimeString = searchParams.get(LibraListQuerySearchParamsKey.StartTime);
            const endTimeString = searchParams.get(LibraListQuerySearchParamsKey.EndTime);
            const startTime = startTimeString ? dateToTimestamp(startTimeString) : 0;
            const endTime = endTimeString ? dateToTimestamp(endTimeString) : 0;
            const timeType = searchParams.get(LibraListQuerySearchParamsKey.TimeType);
            const flightStatus = searchParams.get(LibraListQuerySearchParamsKey.FlightStatus);
            const newPageStatus: LibraNewInfoTablePageStatus = {
              selectedAppIds: ['1775'],
              filter_config: {
                filter_id: 'name_search',
              },
              column_groups: columnSettingResult.groups,
              userRoleType: userRoleResult,
            };

            if (appId !== null) {
              if (appId?.length === 0) {
                newPageStatus.selectedAppIds = [];
              } else if (appId?.length > 0) {
                const appIdArray = appId?.split(',');
                newPageStatus.selectedAppIds = appIdArray?.filter(id =>
                  LibraBusinessSelectOptions.some(option => option.value === id.trim()),
                );
              }
            }

            if (emails?.length) {
              const emailArray = emails.split(',');
              try {
                const result = await getUserInfoByEmails({ data: { emails: emailArray } });
                if (result) {
                  newPageStatus.selectedOwner = result;
                }
              } catch (error) {
                logger.warn('Failed to get user info by emails:', error);
              }
            }

            if (businessTeams?.length) {
              const businessTeamArray = businessTeams.split(',');
              try {
                const meegoTeamTreeData = await getMeegoTeamTreeSelectDataSource({ data: {} });
                newPageStatus.selectedBusinessTeam = TransitionKeysValuesFromTreeData(
                  businessTeamArray,
                  meegoTeamTreeData,
                  false,
                );
              } catch (error) {
                logger.warn('Failed to get meego team tree data:', error);
              }
            }
            if (bussinessLines?.length) {
              const bussinesLinesArray = bussinessLines.split(',');
              try {
                const meegoLineTreeData = getMeegoTreeValue('business');
                newPageStatus.selectedBusinessLine = TransitionKeysValuesFromTreeData(
                  bussinesLinesArray,
                  meegoLineTreeData,
                  false,
                );
              } catch (error) {
                logger.warn('Failed to get meego line tree data:', error);
              }
            }
            if (flightStatus?.length) {
              const flightStatusArray = flightStatus.split(',');
              newPageStatus.selectedFlightStatus = flightStatusArray
                .map(statusStr => {
                  const statusNum = parseInt(statusStr, 10);
                  return Object.values(LibraFlightStatus).includes(statusNum as LibraFlightStatus)
                    ? (statusNum as LibraFlightStatus)
                    : null;
                })
                .filter((status): status is LibraFlightStatus => status !== null);
            }
            if (closeAttribution?.length) {
              const closeAttributionArray = closeAttribution.split(',');
              if (closeAttributionArray.includes(`${LibraFlightHasNoCloseAttribution}`)) {
                newPageStatus.selectedCloseAttribution = [`${LibraFlightHasNoCloseAttribution}`];
              } else {
                newPageStatus.selectedCloseAttribution = closeAttributionArray;
              }
              newPageStatus.selectedFlightStatus = [LibraFlightStatus.Ended];
            }
            if (flightValue?.length && flightType?.length) {
              newPageStatus.filter_config = {
                ...pageStatus?.filter_config,
                filter_id: flightType,
                filter_value: flightValue,
              };
            }
            if (startTime && startTime > 0) {
              newPageStatus.selectedFlightStartTime = startTime;
            }
            if (endTime && endTime > 0) {
              newPageStatus.selectedFlightEndTime = endTime;
            }
            if (timeType?.length) {
              newPageStatus.selectedFlightTimeType =
                timeType === '2'
                  ? LibraTimeQueryType.BothStartAndEndRange
                  : timeType === '1'
                    ? LibraTimeQueryType.OnlyEndRange
                    : LibraTimeQueryType.OnlyStartRange;
            } else {
              newPageStatus.selectedFlightTimeType = LibraTimeQueryType.OnlyStartRange;
            }
            setPageStatus(newPageStatus);
            setSearchTriggerFlag(!searchTriggerFlag);
          }
        } catch (error) {
          logger.error('Failed to fetch data:', error);
        }
      }
    };
    fetchData();
  }, [userSettingState.info.email]);

  const viewPermission = () => (pageStatus?.userRoleType ? pageStatus.userRoleType & LibraPlatformPermission.View : 0);

  const adminPermission = () => pageStatus?.userRoleType === LibraPlatformRoleType.Admin;

  return (
    <Spin spinning={roleTypeLoading}>
      {roleTypeLoading ? (
        <></>
      ) : (
        <>
          {!viewPermission() && (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
              <Empty
                image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                darkModeImage={<IllustrationConstructionDark style={{ width: 150, height: 150 }} />}
                title={'未授权'}
                description="请联系管理员"
              />
            </div>
          )}
          {Boolean(viewPermission()) && (
            <Space vertical={true} align={'start'} style={{ width: '100%' }}>
              <LibraListTableHeader
                pageStatus={pageStatus}
                updatePageStatus={newStatus => setPageStatus(newStatus)}
                onSearchClicked={() => {
                  setSearchTriggerFlag(!searchTriggerFlag);
                }}
              />
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%',
                  paddingLeft: 10,
                  paddingRight: 10,
                }}
              >
                <Space>
                  <CustomLibraListColumnPanelButton
                    userSetting={pageStatus.column_groups ?? []}
                    onSettingApply={async newSetting => {
                      setPageStatus({ ...pageStatus, column_groups: newSetting });
                      await updateFlightTableColumnSetting({
                        data: {
                          userEmail: userSettingState.info.email,
                          newSetting,
                        },
                      });
                    }}
                  />
                  {adminPermission() && (
                    <AddMissingExperimentButton
                      onSuccess={() => {
                        setSearchTriggerFlag(!searchTriggerFlag);
                      }}
                    />
                  )}
                </Space>
              </div>
              <LibraListTable pageStatus={pageStatus} searchTriggerFlag={searchTriggerFlag} />
            </Space>
          )}
        </>
      )}
    </Spin>
  );
};

export default LibraListPage;
