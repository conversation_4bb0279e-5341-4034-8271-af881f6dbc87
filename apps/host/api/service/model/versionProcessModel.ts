import {
  BuildMasterInfo,
  DreaminaProductType,
  ExecuteTime,
  isGrayStage,
  LVProductType,
  PCInfo,
  PippitProductType,
  ProductType,
  ProgressState,
  RetouchProductType,
  TinycutProductType,
  VersionProcess,
  VersionStage,
  VersionState,
} from '@shared/process/versionProcess';
import { PlatformType, User } from '@pa/shared/dist/src/core';
import { EventsItem } from '@shared/bits/calendar';
import { BmType } from '@shared/bits/bmInfo';
import { Temporal } from '@js-temporal/polyfill';
import { BmInfo, CustomFlavor, CustomVersion, Platform, Version } from '@shared/versionBot/version';
import { MeegoSnapshot } from '@shared/process/versionSnapshot';
import { Inject, Injectable } from '@gulux/gulux';
import BitsService from '../third/bits';
import LarkService from '@pa/backend/dist/src/third/lark';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { VersionProcessTable } from '../../model/VersionProcessInfo';
import { ModelType } from '@gulux/gulux/typegoose';
import MeegoService from '../third/meego';
import VersionModelService from './versionModel';
import versionUtils from '../../utils/versionUtils';
import timeUtil from '../../utils/timeUtil';
import commonUtils from '../../utils/commonUtils';
import VersionProcessDao from '../dao/VersionProcessDao';
import {
  AppSetting,
  AppSettingId,
  AppSettingSymbol,
  BusinessAppInfo,
  BusinessType,
} from '@pa/shared/dist/src/appSettings/appSettings';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { useBusinessInject } from '@pa/backend/dist/src/utils/business';

const productNameArray: { [key: string]: string } = {
  lv: '剪映',
  cc: 'CapCut',
};

@Injectable()
export default class VersionProcessModelService {
  @Inject()
  private versionProcess: VersionProcessDao;

  @Inject()
  private bits: BitsService;

  @Inject()
  private versionModel: VersionModelService;

  @Inject()
  private lark: LarkService;

  @Inject()
  private logger: BytedLogger;

  @Inject(VersionProcessTable)
  private versionProcessModel: ModelType<VersionProcessTable>;

  @Inject()
  private meego: MeegoService;

  @Inject()
  private businessConfigService: BusinessConfigService;

  async createNewVersionV2(appInfo: BusinessAppInfo, version: string): Promise<VersionProcess[]> {
    if (!appInfo) {
      this.logger.error(`App Info can not be null`);
      return [];
    }

    // 有的产品和日历空间的版本不是对齐的，比如CC，比原始版本小两个数,用原始版本去请求日历数据
    const originVersion = versionUtils.calOriginVersion(appInfo.bits_workspace, appInfo.product_type, version);
    const versionCalendar = await this.initCalendarV2(originVersion, appInfo.app_id);
    if (!versionCalendar) {
      this.logger.error(`No version Calendar for app info : ${JSON.stringify(appInfo)}`);
      return [];
    }

    if (version.length <= 1) {
      // 如果没传version，创建最近的版本
      version = versionCalendar?.event.name ?? '';
      // 有的产品和日历空间的版本不是对齐的，比如CC，比原始版本小两个数
      version = versionUtils.calSpecificVersion(appInfo.bits_workspace, appInfo.product_type, version);
    }
    if (version.length <= 1) {
      this.logger.warn(`No valid version to add : ${appInfo}`);
      return [];
    }
    this.logger.info(
      `[VersionProcessModelService][createNewVersionV2]app: ${appInfo.app_name}的新版本是${version}，version_calender是${JSON.stringify(versionCalendar)}`,
    );

    const pcInfoResult =
      appInfo.product_type === LVProductType.lv ||
      appInfo.product_type === LVProductType.cc ||
      appInfo.product_type === LVProductType.pc ||
      appInfo.product_type === LVProductType.cc_pc
        ? await this.initPCInfo(versionCalendar?.event.name ?? '')
        : {};
    const versionProcessInfo = await this.versionProcess.queryVersionProcessInfo(
      appInfo.product_type,
      appInfo.platform,
      version,
    );

    let versionProcess = {
      product: appInfo.product_type,
      productCN: appInfo.app_name,
      version,
      platform: appInfo.platform,
      versionNum: Number(version.replace(/\./g, '')),
      // 醒图只保留2位小版本号
      versionCode:
        appInfo.product_type !== RetouchProductType.retouch && appInfo.product_type !== RetouchProductType.hypic
          ? version.replace(/\./g, '').concat('0000')
          : version.replace(/\./g, '').concat('00'),
      versionState: [],
      calendarOriginal: versionCalendar,
      bmInfo: await this.initBMIfoV2(originVersion, appInfo),
      pcInfo: pcInfoResult,
      over: false,
    } as VersionProcess;

    versionProcess = await this.initVersionState(versionProcess);
    if (versionProcessInfo) {
      await this.versionProcess.updateVersionProcess(appInfo.platform, appInfo.product_type, version, versionProcess);
    } else {
      await this.versionProcess.addVersionInfo(versionProcess);
    }
    if (appInfo.product_type === LVProductType.lv) {
      // 创建老的版本数据库 FIXME 后续要替换掉
      await this.createOldVersionTableIfNeed(version);
    }
    return [versionProcess ?? ({} as VersionProcess)];
  }

  /**
   * @param version 10.0.0
   */
  async createNewVersion(version: string): Promise<VersionProcess[]> {
    let versionProcessArray: VersionProcess[] = [];
    // 先创建国内版本数据
    const versionCC = versionUtils.lv2ccVersion(version);
    const pcInfoResult = await this.initPCInfo(version);
    const versionProcessInfo = await this.versionProcess.queryVersionProcessInfo(
      LVProductType.lv,
      PlatformType.Android,
      version,
    );
    const versionProcessInfoOverseas = await this.versionProcess.queryVersionProcessInfo(
      LVProductType.cc,
      PlatformType.Android,
      versionCC,
    );
    let versionProcessAndroid, versionProcessAndroidCC, versionProcessIos, versionProcessIosCC;
    if (!versionProcessInfo) {
      versionProcessAndroid = {
        product: LVProductType.lv,
        productCN: productNameArray[LVProductType.lv],
        version,
        platform: PlatformType.Android,
        versionNum: Number(version.replace(/\./g, '')),
        versionCode: version.replace(/\./g, '').concat('0000'),
        versionState: [],
        calendarOriginal: await this.initCalendar(version, PlatformType.Android),
        bmInfo: await this.initBMInfo(version, PlatformType.Android),
        pcInfo: pcInfoResult,
        over: false,
      } as VersionProcess;
      versionProcessIos = {
        product: LVProductType.lv,
        productCN: productNameArray[LVProductType.lv],
        version,
        platform: PlatformType.iOS,
        versionNum: Number(version.replace(/\./g, '')),
        versionCode: version.replace(/\./g, '').concat('0000'),
        versionState: [],
        calendarOriginal: await this.initCalendar(version, PlatformType.iOS),
        bmInfo: await this.initBMInfo(version, PlatformType.iOS),
        pcInfo: pcInfoResult,
        over: false,
      } as VersionProcess;

      versionProcessIos = await this.initVersionState(versionProcessIos);
      versionProcessAndroid = await this.initVersionState(versionProcessAndroid);

      await this.versionProcess.addVersionInfo(versionProcessAndroid);
      await this.versionProcess.addVersionInfo(versionProcessIos);
      // await this.createOldVersionTable(versionProcessAndroid, versionProcessIos);
      // 利用国内版本数据，简化海外版本数据的创建
      if (!versionProcessInfoOverseas) {
        versionProcessAndroidCC = versionProcessAndroid;
        versionProcessAndroidCC.product = LVProductType.cc;
        versionProcessAndroidCC.productCN = productNameArray[LVProductType.cc];
        versionProcessAndroidCC.version = versionCC;
        versionProcessAndroidCC.versionNum = Number(versionCC.replace(/\./g, ''));
        versionProcessAndroidCC.versionCode = versionCC.replace(/\./g, '').concat('0000');
        versionProcessAndroidCC = await this.initVersionState(versionProcessAndroidCC);

        versionProcessIosCC = versionProcessIos;
        versionProcessIosCC.product = LVProductType.cc;
        versionProcessIosCC.productCN = productNameArray[LVProductType.cc];
        versionProcessIosCC.version = versionCC;
        versionProcessIosCC.versionNum = Number(versionCC.replace(/\./g, ''));
        versionProcessIosCC.versionCode = versionCC.replace(/\./g, '').concat('0000');
        versionProcessIosCC = await this.initVersionState(versionProcessIosCC);

        await this.versionProcess.addVersionInfo(versionProcessAndroidCC);
        await this.versionProcess.addVersionInfo(versionProcessIosCC);
        versionProcessArray = [versionProcessAndroidCC, versionProcessIosCC];
      }
    }
    return versionProcessArray;
  }

  /**
   * 初始化版本状态(根据版本日历)
   */
  async initVersionState(versionProcess: VersionProcess) {
    let versionState: VersionState[] = [];
    const onDaySecond = 24 * 60 * 60;
    if (versionProcess.product === LVProductType.lv || versionProcess.product === LVProductType.cc) {
      const developEndTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2DDL(versionProcess.calendarOriginal?.event.start_date).epochSeconds - onDaySecond
        : 0;
      const integrationStartTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '11').epochSeconds
        : 0;
      const integrationEndTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '21').epochSeconds
        : 0;
      versionState = versionState.concat({
        stage: VersionStage.develop,
        name: '开发',
        progress: ProgressState.OnGoing,
        executeInfo: {
          planEndTime: developEndTime,
          actualEndTime: developEndTime,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.integration,
        name: '封版',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationStartTime,
          planEndTime: integrationEndTime,
          actualStartTime: integrationStartTime,
          actualEndTime: integrationEndTime,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.integrationTest,
        name: '集成测试',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationStartTime + onDaySecond,
          planEndTime: integrationEndTime + onDaySecond,
          actualStartTime: integrationStartTime + onDaySecond,
          actualEndTime: integrationEndTime + onDaySecond,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.systemTest,
        name: '系统测试',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationStartTime + onDaySecond,
          planEndTime: integrationEndTime + onDaySecond * 6,
          actualStartTime: integrationStartTime + onDaySecond,
          actualEndTime: integrationEndTime + onDaySecond * 6,
        },
      } as VersionState);
      if (versionProcess.platform === PlatformType.Android) {
        versionState = versionState.concat({
          stage: VersionStage.normalGray,
          name: '一灰阶段',
          progress: ProgressState.NotStart,
          versionCode: (versionProcess.versionNum * 10000 + 100).toString(),
          executeInfo: {
            planStartTime: integrationStartTime + onDaySecond * 6,
            planEndTime: integrationEndTime + onDaySecond * 6,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
        versionState = versionState.concat({
          stage: VersionStage.strict,
          name: '上线前',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === LVProductType.lv
                ? integrationStartTime + onDaySecond * 13
                : integrationStartTime + onDaySecond * 15,
            planEndTime:
              versionProcess.product === LVProductType.lv
                ? integrationEndTime + onDaySecond * 14
                : integrationEndTime + onDaySecond * 16,
            actualStartTime:
              versionProcess.product === LVProductType.lv
                ? integrationStartTime + onDaySecond * 13
                : integrationStartTime + onDaySecond * 15,
            actualEndTime:
              versionProcess.product === LVProductType.lv
                ? integrationEndTime + onDaySecond * 14
                : integrationEndTime + onDaySecond * 16,
          },
        } as VersionState);
        versionState = versionState.concat({
          stage: VersionStage.submit,
          name: '正式版本',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === LVProductType.lv
                ? integrationStartTime + onDaySecond * 16
                : integrationStartTime + onDaySecond * 17,
            planEndTime:
              versionProcess.product === LVProductType.lv
                ? integrationEndTime + onDaySecond * 16
                : integrationEndTime + onDaySecond * 17,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
      }
      if (versionProcess.platform === PlatformType.iOS) {
        // iOS国内灰度时间和系统测试时间重合
        versionState = versionState.concat({
          stage: VersionStage.testFlight,
          name: '第1轮灰度',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime: integrationStartTime + onDaySecond * 6,
            planEndTime: integrationStartTime + onDaySecond * 7,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
        versionState = versionState.concat({
          // iOS国内小流量时间段跟海外灰度时间段基本重合
          stage: VersionStage.smallFlow,
          name: '小流量',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === LVProductType.lv
                ? integrationStartTime + onDaySecond * 10
                : integrationStartTime + onDaySecond * 13,
            planEndTime:
              versionProcess.product === LVProductType.lv
                ? integrationEndTime + onDaySecond * 11
                : integrationEndTime + onDaySecond * 14,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
        versionState = versionState.concat({
          stage: VersionStage.submit,
          name: '正式版本',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === LVProductType.lv
                ? integrationStartTime + onDaySecond * 16
                : integrationStartTime + onDaySecond * 20,
            planEndTime:
              versionProcess.product === LVProductType.lv
                ? integrationEndTime + onDaySecond * 16
                : integrationEndTime + onDaySecond * 20,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
      }
    } else if (
      versionProcess.product === RetouchProductType.retouch ||
      versionProcess.product === RetouchProductType.hypic
    ) {
      const developEndTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2DDL(versionProcess.calendarOriginal?.event.start_date).epochSeconds - onDaySecond
        : 0;
      const integrationStartTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '10').epochSeconds
        : 0;
      const integrationEndTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '21').epochSeconds
        : 0;
      versionState = versionState.concat({
        stage: VersionStage.develop,
        name: '开发',
        progress: ProgressState.OnGoing,
        executeInfo: {
          planEndTime: developEndTime,
          actualEndTime: developEndTime,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.integration,
        name: '封版',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationStartTime,
          planEndTime: integrationEndTime,
          actualStartTime: integrationStartTime,
          actualEndTime: integrationEndTime,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.integrationTest,
        name: '集成测试',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationEndTime,
          planEndTime: integrationEndTime + onDaySecond,
          actualStartTime: integrationEndTime,
          actualEndTime: integrationEndTime + onDaySecond,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.systemTest,
        name: '系统测试',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationStartTime + onDaySecond,
          planEndTime: integrationEndTime + onDaySecond * 4,
          actualStartTime: integrationStartTime + onDaySecond,
          actualEndTime: integrationEndTime + onDaySecond * 4,
        },
      } as VersionState);
      if (versionProcess.platform === PlatformType.Android) {
        if (versionProcess.product === RetouchProductType.retouch) {
          versionState = versionState.concat({
            stage: VersionStage.normalGray,
            name: '一灰阶段',
            progress: ProgressState.NotStart,
            versionCode: (versionProcess.versionNum * 100 + 1).toString(),
            executeInfo: {
              planStartTime: integrationStartTime + onDaySecond * 1.25,
              planEndTime: integrationEndTime + onDaySecond,
              actualStartTime: 0,
              actualEndTime: 0,
            },
          } as VersionState);
        }
        versionState = versionState.concat({
          stage: VersionStage.strict,
          name: '上线前',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationStartTime + onDaySecond * 8
                : integrationStartTime + onDaySecond * 4,
            planEndTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationEndTime + onDaySecond * 9
                : integrationEndTime + onDaySecond * 7,
            actualStartTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationStartTime + onDaySecond * 9
                : integrationStartTime + onDaySecond * 4,
            actualEndTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationEndTime + onDaySecond * 9
                : integrationEndTime + onDaySecond * 7,
          },
        } as VersionState);
        versionState = versionState.concat({
          stage: VersionStage.submit,
          name: '正式版本',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationStartTime + onDaySecond * 10
                : integrationStartTime + onDaySecond * 8,
            planEndTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationEndTime + onDaySecond * 10
                : integrationEndTime + onDaySecond * 8,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
      }
      if (versionProcess.platform === PlatformType.iOS) {
        if (versionProcess.product === RetouchProductType.retouch) {
          // iOS国内灰度时间为系统测试第三天
          versionState = versionState.concat({
            stage: VersionStage.testFlight,
            name: 'TF灰度',
            progress: ProgressState.NotStart,
            executeInfo: {
              planStartTime: integrationStartTime + onDaySecond * 3.25,
              planEndTime: integrationEndTime + onDaySecond * 8,
              actualStartTime: 0,
              actualEndTime: 0,
            },
          } as VersionState);
        }
        versionState = versionState.concat({
          stage: VersionStage.strict,
          name: '上线前',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationStartTime + onDaySecond * 8
                : integrationStartTime + onDaySecond * 4,
            planEndTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationEndTime + onDaySecond * 8
                : integrationEndTime + onDaySecond * 4,
            actualStartTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationStartTime + onDaySecond * 8
                : integrationStartTime + onDaySecond * 4,
            actualEndTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationEndTime + onDaySecond * 8
                : integrationEndTime + onDaySecond * 4,
          },
        } as VersionState);
        versionState = versionState.concat({
          stage: VersionStage.submit,
          name: '正式版本',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationStartTime + onDaySecond * 9
                : integrationStartTime + onDaySecond * 7,
            planEndTime:
              versionProcess.product === RetouchProductType.retouch
                ? integrationEndTime + onDaySecond * 9
                : integrationEndTime + onDaySecond * 7,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
      }
    } else if (versionProcess.product !== LVProductType.cc_pc && versionProcess.product !== LVProductType.pc) {
      const developEndTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2DDL(versionProcess.calendarOriginal?.event.start_date).epochSeconds - onDaySecond
        : 0;
      const integrationStartTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '10').epochSeconds
        : 0;
      const integrationEndTime = versionProcess.calendarOriginal
        ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '21').epochSeconds
        : 0;
      versionState = versionState.concat({
        stage: VersionStage.develop,
        name: '开发',
        progress: ProgressState.OnGoing,
        executeInfo: {
          planEndTime: developEndTime,
          actualEndTime: developEndTime,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.integration,
        name: '封版',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationStartTime,
          planEndTime: integrationEndTime,
          actualStartTime: integrationStartTime,
          actualEndTime: integrationEndTime,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.integrationTest,
        name: '集成测试',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationEndTime,
          planEndTime: integrationEndTime + onDaySecond,
          actualStartTime: integrationEndTime,
          actualEndTime: integrationEndTime + onDaySecond,
        },
      } as VersionState);
      versionState = versionState.concat({
        stage: VersionStage.systemTest,
        name: '系统测试',
        progress: ProgressState.NotStart,
        executeInfo: {
          planStartTime: integrationStartTime + onDaySecond,
          planEndTime: integrationEndTime + onDaySecond * 4,
          actualStartTime: integrationStartTime + onDaySecond,
          actualEndTime: integrationEndTime + onDaySecond * 4,
        },
      } as VersionState);
      if (versionProcess.platform === PlatformType.Android) {
        if (versionProcess.product === DreaminaProductType.dreamina) {
          versionState = versionState.concat({
            stage: VersionStage.normalGray,
            name: '一灰阶段',
            progress: ProgressState.NotStart,
            versionCode: (versionProcess.versionNum * 100 + 1).toString(),
            executeInfo: {
              planStartTime: integrationStartTime + onDaySecond * 1.25,
              planEndTime: integrationEndTime + onDaySecond,
              actualStartTime: 0,
              actualEndTime: 0,
            },
          } as VersionState);
        }
        versionState = versionState.concat({
          stage: VersionStage.strict,
          name: '上线前',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationStartTime + onDaySecond * 8
                : integrationStartTime + onDaySecond * 4,
            planEndTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationEndTime + onDaySecond * 9
                : integrationEndTime + onDaySecond * 7,
            actualStartTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationStartTime + onDaySecond * 9
                : integrationStartTime + onDaySecond * 4,
            actualEndTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationEndTime + onDaySecond * 9
                : integrationEndTime + onDaySecond * 7,
          },
        } as VersionState);
        versionState = versionState.concat({
          stage: VersionStage.submit,
          name: '正式版本',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationStartTime + onDaySecond * 10
                : integrationStartTime + onDaySecond * 8,
            planEndTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationEndTime + onDaySecond * 10
                : integrationEndTime + onDaySecond * 8,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
      }
      if (versionProcess.platform === PlatformType.iOS) {
        if (versionProcess.product === DreaminaProductType.dreamina) {
          // iOS国内灰度时间为系统测试第三天
          versionState = versionState.concat({
            stage: VersionStage.testFlight,
            name: 'TF灰度',
            progress: ProgressState.NotStart,
            executeInfo: {
              planStartTime: integrationStartTime + onDaySecond * 3.25,
              planEndTime: integrationEndTime + onDaySecond * 8,
              actualStartTime: 0,
              actualEndTime: 0,
            },
          } as VersionState);
        }
        versionState = versionState.concat({
          stage: VersionStage.strict,
          name: '上线前',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationStartTime + onDaySecond * 8
                : integrationStartTime + onDaySecond * 4,
            planEndTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationEndTime + onDaySecond * 8
                : integrationEndTime + onDaySecond * 4,
            actualStartTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationStartTime + onDaySecond * 8
                : integrationStartTime + onDaySecond * 4,
            actualEndTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationEndTime + onDaySecond * 8
                : integrationEndTime + onDaySecond * 4,
          },
        } as VersionState);
        versionState = versionState.concat({
          stage: VersionStage.submit,
          name: '正式版本',
          progress: ProgressState.NotStart,
          executeInfo: {
            planStartTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationStartTime + onDaySecond * 9
                : integrationStartTime + onDaySecond * 7,
            planEndTime:
              versionProcess.product === DreaminaProductType.dreamina
                ? integrationEndTime + onDaySecond * 9
                : integrationEndTime + onDaySecond * 7,
            actualStartTime: 0,
            actualEndTime: 0,
          },
        } as VersionState);
      }
    }
    versionProcess.versionState = versionState;
    versionProcess.versionState = await this.updateVersionStageInfoFromBits(
      versionProcess.platform,
      versionProcess.product,
      versionProcess.version,
      versionProcess,
    );
    return versionProcess;
  }

  async initCalendarV2(version: string, appID: number): Promise<EventsItem | undefined> {
    // 支持向前向后跨越2周查询版本信息，优先查询此刻的next数据
    const singleWeekIterationApp = [
      // NOTE 多端接入配置，小包默认单周迭代，按需修改
      AppSettingId.DREAMINA_ANDROID,
      AppSettingId.DREAMINA_IOS,
      AppSettingId.PIPPIT_ANDROID,
      AppSettingId.PIPPIT_IOS,
      AppSettingId.TINYCUT_IOS,
      AppSettingId.TINYCUT_ANDROID,
    ];
    const currentSeconds = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    const onWeek = !singleWeekIterationApp.includes(appID) ? 7 * 24 * 60 * 60 : 24 * 60 * 60; // 简单处理dreamina单周迭代
    let calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds);
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds + onWeek);
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds - onWeek);
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendarV2(version, appID, currentSeconds - onWeek * 2);
    }
    if (!calendar) {
      const versionOld = await this.versionModel.findVersion({
        version,
      } as Version);
      if (versionOld && versionOld.keyNode) {
        calendar = versionOld.keyNode;
      }
    }
    return calendar;
  }

  /**
   * 根据版本号和平台查询日历信息,支持跨越前后2周
   * @param version
   * @param platform
   */
  async initCalendar(version: string, platform: PlatformType): Promise<EventsItem | undefined> {
    // 支持向前向后跨越2周查询版本信息，优先查询此刻的next数据
    const currentSeconds = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    const onWeek = 7 * 24 * 60 * 60;
    let calendar = await this.bits.versionProgressCalendar(version, platform, currentSeconds);
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendar(version, platform, currentSeconds + onWeek);
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendar(version, platform, currentSeconds - onWeek);
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendar(version, platform, currentSeconds - onWeek * 2);
    }
    if (!calendar) {
      const versionOld = await this.versionModel.findVersion({
        version,
      } as Version);
      if (versionOld && versionOld.keyNode) {
        calendar = versionOld.keyNode;
      }
    }
    return calendar;
  }

  async initBMIfoV2(version: string, appInfo: BusinessAppInfo): Promise<Record<number, BuildMasterInfo>> {
    return await this.initBMInfo(version, appInfo.platform, appInfo.group_name);
  }

  /**
   * 根据版本查询当前BM信息
   * @param version
   * @param platform
   * @param groupName
   */
  async initBMInfo(
    version: string,
    platform: PlatformType,
    groupName: string | undefined = undefined,
  ): Promise<Record<number, BuildMasterInfo>> {
    if (!groupName) {
      groupName = platform === PlatformType.Android ? 'LV-Android' : 'LV-iOS';
      if (platform === PlatformType.PC) {
        groupName = 'LV-Windows';
      }
    }
    const bmInfo = await this.bits.requestVersionMaster(groupName, version);
    const bmRecord: Record<number, BuildMasterInfo> = {};
    for (const bm of bmInfo) {
      const userInfo = await this.lark.searchUserInfoByEmail(bm.email);
      const _avatarUrl = typeof userInfo?.avatar === 'string' ? userInfo?.avatar : userInfo?.avatar?.avatar_240;
      const _nameCN = userInfo ? userInfo.name : '';
      const _openId = userInfo ? userInfo.open_id : '';
      if (bm.type === BmType.crash) {
        bmRecord[BmType.crash] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.crash,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.rd) {
        bmRecord[BmType.rd] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.rd,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
      if (bm.type === BmType.qa) {
        bmRecord[BmType.qa] = {
          avatarUrl: _avatarUrl ? _avatarUrl : '',
          nameCN: _nameCN ? _nameCN : '',
          type: BmType.qa,
          email: bm.email,
          openId: _openId ? _openId : '',
        };
      }
    }
    return bmRecord;
  }

  async initPCInfo(version: string): Promise<PCInfo | undefined> {
    const result = await this.bits.getPCVersion(version);
    if (result) {
      const bmInfoResult = await this.initBMInfo(result.lvpro_version, PlatformType.PC);
      result.bmInfo = bmInfoResult;
    }
    return result;
  }

  /**
   * 新版本版本创建接口触发的时候检查versionTable是否创建，如果没有的话自动创建一个版本
   */
  async createOldVersionTableIfNeed(lvVersion: string) {
    const versionProcessAndroid = await this.versionProcess.queryVersionProcessInfo(
      LVProductType.lv,
      PlatformType.Android,
      lvVersion,
    );
    const versionProcessIos = await this.versionProcess.queryVersionProcessInfo(
      LVProductType.lv,
      PlatformType.iOS,
      lvVersion,
    );
    if (!versionProcessAndroid || !versionProcessIos) {
      return;
    }
    const versionTable = await this.versionModel.findVersion({
      version: versionProcessAndroid.version,
    } as Version);
    if (versionTable) {
      return;
    }
    const platform: Platform[] = [
      {
        name: 'LV-Android',
        type: PlatformType.Android,
        developBranch: 'develop',
        releaseBranch: `release/${versionProcessAndroid.version}`,
      },
      {
        name: 'LV-iOS',
        type: PlatformType.iOS,
        developBranch: 'develop',
        releaseBranch: `release/${versionProcessAndroid.version}`,
      },
      {
        name: 'LV-Windows',
        type: PlatformType.PC,
        customVersion: versionProcessAndroid.pcInfo ? versionProcessAndroid.pcInfo.lvpro_version : '',
        developBranch: 'develop',
        releaseBranch: `release/${versionProcessAndroid.pcInfo ? versionProcessAndroid.pcInfo.lvpro_version : ''}`,
      },
    ];
    const customVersionList: CustomVersion[] = [
      {
        version: versionProcessAndroid.pcInfo ? versionProcessAndroid.pcInfo.cc_version : '',
        flavor: CustomFlavor.oversea,
        platform: PlatformType.iOS,
      },
      {
        version: versionProcessAndroid.pcInfo ? versionProcessAndroid.pcInfo.cc_version : '',
        flavor: CustomFlavor.oversea,
        platform: PlatformType.Android,
      },
      {
        version: versionProcessAndroid.pcInfo ? versionProcessAndroid.pcInfo.lvpro_version : '',
        flavor: CustomFlavor.prod,
        platform: PlatformType.PC,
      },
      {
        version: versionProcessAndroid.pcInfo ? versionProcessAndroid.pcInfo.ccpc_version : '',
        flavor: CustomFlavor.oversea,
        platform: PlatformType.PC,
      },
    ];
    const bmInfoList = this.builderMasterInfo2BmInfo(
      versionProcessAndroid.bmInfo ? versionProcessAndroid.bmInfo[BmType.rd] : undefined,
      versionProcessIos.bmInfo ? versionProcessIos.bmInfo[BmType.rd] : undefined,
      versionProcessAndroid.pcInfo && versionProcessAndroid.pcInfo.bmInfo
        ? versionProcessAndroid.pcInfo.bmInfo[BmType.rd]
        : undefined,
    );
    await this.versionModel.saveVersion({
      bms: bmInfoList,
      version: versionProcessAndroid.version,
      pcVersion: versionProcessAndroid.pcInfo ? versionProcessAndroid.pcInfo.lvpro_version : '',
      customVersionList,
      currentVersionCode: Number(versionProcessAndroid.versionCode),
      grayCount: 0,
      prepareManager: 'lizhengda.da',
      platform,
      defaultLabel: '剪映工具',
    } as Version);
  }

  builderMasterInfo2BmInfo(
    bmInfoAndroid: BuildMasterInfo | undefined,
    bmInfoIos: BuildMasterInfo | undefined,
    bmInfoPC: BuildMasterInfo | undefined,
  ): BmInfo[] {
    let result: BmInfo[] = [];
    if (bmInfoAndroid) {
      result = result.concat({
        name: bmInfoAndroid.email.replace('@bytedance.com', ''),
        platform: PlatformType.Android,
        userInfo: {
          name: bmInfoAndroid.nameCN,
          open_id: bmInfoAndroid.openId,
          avatar: bmInfoAndroid.avatarUrl,
        } as User,
      });
    }
    if (bmInfoIos) {
      result = result.concat({
        name: bmInfoIos.email.replace('@bytedance.com', ''),
        platform: PlatformType.iOS,
        userInfo: {
          name: bmInfoIos.nameCN,
          open_id: bmInfoIos.openId,
          avatar: bmInfoIos.avatarUrl,
        } as User,
      });
    }
    if (bmInfoPC) {
      result = result.concat({
        name: bmInfoPC.email.replace('@bytedance.com', ''),
        platform: PlatformType.PC,
        userInfo: {
          name: bmInfoPC.nameCN,
          open_id: bmInfoPC.openId,
          avatar: bmInfoPC.avatarUrl,
        } as User,
      });
    }
    return result;
  }

  getTimePassWeekend(timestamp: number) {
    const onDaySecond = 24 * 60 * 60;
    while (new Date(timestamp * 1000).getDay() === 0 || new Date(timestamp * 1000).getDay() === 6) {
      timestamp += onDaySecond;
    }
    return timestamp;
  }

  getProgressState(type: number) {
    if (type === 1) {
      return ProgressState.NotStart;
    } else if (type === 2) {
      return ProgressState.OnGoing;
    } else if (type === 3) {
      return ProgressState.Complete;
    }
    return ProgressState.Failed;
  }

  // NOTE 多端接入配置-bits空间名称
  getAppIdAndName(platform: PlatformType, product: ProductType) {
    let bits_app_id = 0;
    let app_name = '';
    if (platform === PlatformType.Android && product === LVProductType.lv) {
      bits_app_id = 177502;
      app_name = 'LV-Android';
    } else if (platform === PlatformType.Android && product === LVProductType.cc) {
      bits_app_id = 2000001157;
      app_name = 'CapCut-Android';
    } else if (platform === PlatformType.iOS && product === LVProductType.lv) {
      bits_app_id = 177501;
      app_name = 'LV-iOS';
    } else if (platform === PlatformType.iOS && product === LVProductType.cc) {
      bits_app_id = 2020092636;
      app_name = 'CapCut-iOS';
    } else if (platform === PlatformType.Android && product === RetouchProductType.retouch) {
      bits_app_id = 251501;
      app_name = 'retouch';
    } else if (platform === PlatformType.iOS && product === RetouchProductType.retouch) {
      bits_app_id = 251502;
      app_name = 'retouch_iOS';
    } else if (platform === PlatformType.Android && product === RetouchProductType.hypic) {
      bits_app_id = 2020093924;
      app_name = 'retouchOversea';
    } else if (platform === PlatformType.iOS && product === RetouchProductType.hypic) {
      bits_app_id = 2020093988;
      app_name = 'RetouchOversea-iOS';
    } else if (platform === PlatformType.iOS && product === DreaminaProductType.dreamina) {
      bits_app_id = 225469550850;
      app_name = 'dreamina_app';
    } else if (platform === PlatformType.Android && product === DreaminaProductType.dreamina) {
      bits_app_id = 244127338754;
      app_name = 'dreamina_app_android';
    } else if (platform === PlatformType.Android && product === PippitProductType.pippit) {
      bits_app_id = AppSettingId.PIPPIT_ANDROID;
      app_name = 'pippit';
    } else if (platform === PlatformType.iOS && product === PippitProductType.pippit) {
      bits_app_id = AppSettingId.PIPPIT_IOS;
      app_name = 'pippit_ios';
    } else if (platform === PlatformType.Android && product === TinycutProductType.tinycut) {
      bits_app_id = AppSettingId.TINYCUT_ANDROID;
      app_name = 'tinycut';
    } else if (platform === PlatformType.iOS && product === TinycutProductType.tinycut) {
      bits_app_id = AppSettingId.TINYCUT_IOS;
      app_name = 'tinycut_ios';
    }
    return { bits_app_id, app_name };
  }

  getVersionStatePlanTime(versionStates: VersionState[], new_state: VersionState): VersionState {
    if (versionStates.findIndex(e => e.name === new_state.name) !== -1) {
      new_state.executeInfo.planStartTime =
        versionStates[versionStates.findIndex(e => e.name === new_state.name)].executeInfo.planStartTime;
      new_state.executeInfo.planEndTime =
        versionStates[versionStates.findIndex(e => e.name === new_state.name)].executeInfo.planEndTime;
    }
    return new_state;
  }

  async updateVersionStageInfoFromBits(
    platform: PlatformType,
    product: ProductType,
    version: string,
    versionProcess: VersionProcess,
  ) {
    const { bits_app_id, app_name } = this.getAppIdAndName(platform, product);

    // 原来的流程信息
    const { versionState } = versionProcess;

    // 获取bits的流程进度信息
    const stage_infos = await this.bits.getReleaseStage(bits_app_id, version, app_name);

    if (stage_infos.length === 0) {
      return versionState;
    }

    if (product in RetouchProductType) {
      return this.updateStateOncalendar(platform, versionState, BusinessType.Retouch);
    }

    if (product in DreaminaProductType) {
      return this.updateStateOncalendar(platform, versionState, BusinessType.Dreamina);
    }

    // NOTE 多端接入配置
    if (product in PippitProductType) {
      return this.updateStateOncalendar(platform, versionState, BusinessType.Pippit);
    }

    if (product in TinycutProductType) {
      return this.updateStateOncalendar(platform, versionState, BusinessType.TinyCut);
    }

    // Android-LV: 开发，封版，封版测试，系统测试，众测，一二三四。。。灰，上线前，提审
    // Android-CC: 开发，封版，封版测试，系统测试, 众测，一二三四。。。灰，上线前，提审
    // iOS-LV: 开发，封版，封版测试，一灰前预审，系统测试，三轮灰度，TF全量前预审，上线前，提审
    // iOS-CC: 开发，封版，封版测试，系统测试，一灰，上线前，提审

    const all_stages_on_calander = [
      VersionStage.develop,
      VersionStage.integration,
      VersionStage.integrationTest,
      VersionStage.systemTest,
      VersionStage.submit,
    ];

    // iOS没有上线前
    if (platform === PlatformType.Android) {
      all_stages_on_calander.push(VersionStage.strict);
    }

    const stage_to_index: Map<VersionStage, number> = new Map();
    const gray_to_index: number[] = [];
    for (let i = 0; i < versionState.length; i++) {
      if (isGrayStage(versionState[i].stage)) {
        gray_to_index.push(i);
      } else {
        stage_to_index.set(versionState[i].stage, i);
      }
    }
    const crowdTest_to_Index = versionState.findIndex(e => e.stage === VersionStage.crowdTest);

    let new_versionState: VersionState[] = [];

    // 开发、封版、封版测试、系统测试
    for (let i = 0; i < 4; i++) {
      new_versionState.push(versionState[stage_to_index.get(all_stages_on_calander[i])!]);
    }

    const onDaySecond = 24 * 60 * 60;
    const integrationEndTime = versionProcess.calendarOriginal
      ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '21').epochSeconds
      : 0;
    const integrationStartTime = versionProcess.calendarOriginal
      ? timeUtil.keyNode2Hour(versionProcess.calendarOriginal?.event.start_date, '08').epochSeconds
      : 0;

    let gray_update_finished = false;
    for (let i = 0; i < stage_infos.length; i++) {
      // 安卓的灰度
      // iOS的一灰前预审(lv)、灰度、全量TF(lv)
      if (stage_infos[i].stage_type === 0 && !gray_update_finished) {
        for (let j = i, gray_count = 0; j < stage_infos.length && stage_infos[j].stage_type === 0; j++) {
          if (stage_infos[j].stage_name === '回归' || stage_infos[j].stage_name === '集成') {
            continue;
          }

          // iOS lv 一灰前预审
          if (j === i && platform === PlatformType.iOS && product === LVProductType.lv) {
            let new_state = {
              name: stage_infos[j].stage_name,
              stage: VersionStage.submitBeforeFirstGray,
              progress: this.getProgressState(stage_infos[j].stage_status),
              executeInfo: {
                planStartTime: integrationStartTime,
                planEndTime: integrationEndTime,
                actualStartTime:
                  stage_infos[j].stage_status === 2 || stage_infos[j].stage_status === 3
                    ? stage_infos[j].start_time
                    : 0,
                actualEndTime: stage_infos[j].stage_status === 3 ? stage_infos[j].end_time : 0,
              } as ExecuteTime,
            } as VersionState;

            new_state = this.getVersionStatePlanTime(versionState, new_state);

            new_versionState = new_versionState
              .slice(0, 2)
              .concat(new_state)
              .concat(new_versionState.slice(2, new_versionState.length));

            // iOS LV的全量TF预审
          } else if (
            j + 1 < stage_infos.length &&
            stage_infos[j + 1].stage_type === 4 &&
            platform === PlatformType.iOS &&
            product === LVProductType.lv
          ) {
            let new_state = {
              name: stage_infos[j].stage_name,
              stage: VersionStage.submitBeforeFulltestFlight,
              progress: this.getProgressState(stage_infos[j].stage_status),
              executeInfo: {
                planStartTime: new_versionState[new_versionState.length - 1].executeInfo.planEndTime + onDaySecond,
                planEndTime: new_versionState[new_versionState.length - 1].executeInfo.planEndTime + onDaySecond,
                actualStartTime:
                  stage_infos[j].stage_status === 2 || stage_infos[j].stage_status === 3
                    ? stage_infos[j].start_time
                    : 0,
                actualEndTime: stage_infos[j].stage_status === 3 ? stage_infos[j].end_time : 0,
              } as ExecuteTime,
            } as VersionState;

            new_state = this.getVersionStatePlanTime(versionState, new_state);

            new_versionState.push(new_state);

            // 小流量阶段，iOS cc在正式的前面，lv在正式的前面的前面
          } else if (
            platform === PlatformType.iOS &&
            ((product === LVProductType.lv && j + 2 < stage_infos.length && stage_infos[j + 2].stage_type === 4) ||
              (product === LVProductType.cc && j + 1 < stage_infos.length && stage_infos[j + 1].stage_type === 4))
          ) {
            let new_state = {
              name: stage_infos[j].stage_name,
              stage: VersionStage.smallFlow,
              progress: this.getProgressState(stage_infos[j].stage_status),
              executeInfo: {
                planStartTime:
                  versionProcess.product === LVProductType.lv
                    ? integrationStartTime + onDaySecond * 6
                    : integrationStartTime + onDaySecond * 7,
                planEndTime:
                  versionProcess.product === LVProductType.lv
                    ? integrationEndTime + onDaySecond * 7
                    : integrationEndTime + onDaySecond * 10,
                actualStartTime:
                  stage_infos[j].stage_status === 2 || stage_infos[j].stage_status === 3
                    ? stage_infos[i].start_time
                    : 0,
                actualEndTime: stage_infos[j].stage_status === 3 ? stage_infos[j].end_time : 0,
              } as ExecuteTime,
            } as VersionState;

            new_state = this.getVersionStatePlanTime(versionState, new_state);

            const index = versionState.findIndex(e => e.stage === VersionStage.smallFlow);
            if (index !== -1) {
              new_state.versionCode = versionState[index].versionCode;
            }

            new_versionState.push(new_state);

            // 安卓的灰度和iOS的testFlight灰度
          } else {
            let new_state = {
              name: stage_infos[j].stage_name,
              stage: platform === PlatformType.Android ? VersionStage.normalGray : VersionStage.testFlight,
              progress: this.getProgressState(stage_infos[j].stage_status),
              executeInfo: {
                planStartTime: this.getTimePassWeekend(
                  new_versionState[new_versionState.length - 1].executeInfo.planEndTime + onDaySecond,
                ),
                planEndTime: this.getTimePassWeekend(
                  new_versionState[new_versionState.length - 1].executeInfo.planEndTime + onDaySecond,
                ),
                actualStartTime:
                  stage_infos[j].stage_status === 2 || stage_infos[j].stage_status === 3
                    ? stage_infos[j].start_time
                    : 0,
                actualEndTime: stage_infos[j].stage_status === 3 ? stage_infos[j].end_time : 0,
              } as ExecuteTime,
            } as VersionState;

            // 安卓第一轮灰度的计划时间
            if (platform === PlatformType.Android && j === i) {
              new_state.executeInfo.planStartTime =
                versionProcess.product === LVProductType.lv
                  ? integrationStartTime + onDaySecond * 5
                  : integrationStartTime + onDaySecond * 6;
              new_state.executeInfo.planEndTime =
                versionProcess.product === LVProductType.lv
                  ? integrationEndTime + onDaySecond * 5
                  : integrationEndTime + onDaySecond * 6;
            }
            // iOS第一轮灰度的计划时间（lv前面有个预审）
            if (
              platform === PlatformType.iOS &&
              ((product === LVProductType.lv && j === i + 1) || (product === LVProductType.cc && j === i))
            ) {
              new_state.executeInfo.planStartTime =
                versionProcess.product === LVProductType.lv
                  ? integrationStartTime + onDaySecond * 4
                  : integrationStartTime + onDaySecond * 4;
              new_state.executeInfo.planEndTime =
                versionProcess.product === LVProductType.lv
                  ? integrationEndTime + onDaySecond * 5
                  : integrationEndTime + onDaySecond * 5;
            }

            new_state = this.getVersionStatePlanTime(versionState, new_state);

            if (gray_count < gray_to_index.length) {
              new_state.versionCode = versionState[gray_to_index[gray_count]].versionCode;
              new_state.release_rates = versionState[gray_to_index[gray_count]].release_rates;
            }
            gray_count++;
            new_versionState.push(new_state);
          }
        }
        gray_update_finished = true;
      } else if (stage_infos[i].stage_type === 4) {
        // 正式版本
        if (platform === PlatformType.Android) {
          new_versionState.push(versionState[stage_to_index.get(VersionStage.strict)!]);
        }
        let new_state = {
          name: stage_infos[i].stage_name,
          stage: VersionStage.submit,
          progress: this.getProgressState(stage_infos[i].stage_status),
          executeInfo: {
            planStartTime: versionState[stage_to_index.get(VersionStage.submit)!].executeInfo.planStartTime,
            planEndTime: versionState[stage_to_index.get(VersionStage.submit)!].executeInfo.planEndTime,
            actualStartTime:
              stage_infos[i].stage_status === 2 || stage_infos[i].stage_status === 3 ? stage_infos[i].start_time : 0,
            actualEndTime: stage_infos[i].stage_status === 3 ? stage_infos[i].end_time : 0,
          } as ExecuteTime,
        } as VersionState;

        new_state = this.getVersionStatePlanTime(versionState, new_state);

        if (stage_to_index.has(VersionStage.submit)) {
          new_state.versionCode = versionState[stage_to_index.get(VersionStage.submit)!].versionCode;
          new_state.release_rates = versionState[stage_to_index.get(VersionStage.submit)!].release_rates;
        }
        new_versionState.push(new_state);
      } else if (
        stage_infos[i].stage_type === 2 &&
        platform === PlatformType.Android &&
        stage_infos[i].stage_name === '众测阶段'
      ) {
        let new_state = {
          name: stage_infos[i].stage_name,
          stage: VersionStage.crowdTest,
          progress: this.getProgressState(stage_infos[i].stage_status),
          executeInfo: {
            planStartTime:
              versionProcess.product === LVProductType.lv
                ? integrationStartTime + onDaySecond * 4
                : integrationStartTime + onDaySecond * 4,
            planEndTime:
              versionProcess.product === LVProductType.lv
                ? integrationEndTime + onDaySecond * 4
                : integrationEndTime + onDaySecond * 4,
            actualStartTime:
              stage_infos[i].stage_status === 2 || stage_infos[i].stage_status === 3 ? stage_infos[i].start_time : 0,
            actualEndTime: stage_infos[i].stage_status === 3 ? stage_infos[i].end_time : 0,
          } as ExecuteTime,
        } as VersionState;

        new_state = this.getVersionStatePlanTime(versionState, new_state);

        if (crowdTest_to_Index !== -1) {
          new_state.versionCode = versionState[crowdTest_to_Index].versionCode;
          new_state.release_rates = versionState[crowdTest_to_Index].release_rates;
        }

        new_versionState.push(new_state);
      }
    }

    // 防止bits返回的信息里没有正式版本
    if (new_versionState[new_versionState.length - 1].stage !== VersionStage.submit) {
      // iOS没有上线前
      if (platform === PlatformType.iOS) {
        new_versionState.push(versionState[stage_to_index.get(VersionStage.submit)!]);
      } else {
        new_versionState = new_versionState.concat(
          ...[
            versionState[stage_to_index.get(VersionStage.strict)!],
            versionState[stage_to_index.get(VersionStage.submit)!],
          ],
        );
      }
    }

    new_versionState = this.updateStateOncalendar(platform, new_versionState);

    return new_versionState;
  }

  updateStateOncalendar(
    platform: PlatformType,
    new_versionState: VersionState[],
    businessType: BusinessType = BusinessType.LV,
  ): VersionState[] {
    const stage_AccordingTo_calendar: VersionStage[] = [
      VersionStage.develop,
      VersionStage.integration,
      VersionStage.integrationTest,
      VersionStage.systemTest,
    ];

    if (platform === PlatformType.Android && businessType === BusinessType.LV) {
      stage_AccordingTo_calendar.push(VersionStage.strict);
    }
    // 醒图bits流程暂未规范，直接根据日历来走流程
    else if (businessType !== BusinessType.LV && businessType !== BusinessType.PC) {
      stage_AccordingTo_calendar.push(VersionStage.normalGray);
      stage_AccordingTo_calendar.push(VersionStage.strict);
      stage_AccordingTo_calendar.push(VersionStage.submit);
      stage_AccordingTo_calendar.push(VersionStage.testFlight);
    }

    const curTimeStamp = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    for (let i = 0; i < new_versionState.length; i++) {
      if (stage_AccordingTo_calendar.indexOf(new_versionState[i].stage) !== -1) {
        if (curTimeStamp >= new_versionState[i].executeInfo.planEndTime) {
          new_versionState[i].progress = ProgressState.Complete;
        } else if (curTimeStamp >= new_versionState[i].executeInfo.planStartTime) {
          new_versionState[i].progress = ProgressState.OnGoing;
        } else if (new_versionState[i].stage !== VersionStage.develop) {
          new_versionState[i].progress = ProgressState.NotStart;
        }
      }
    }

    return new_versionState;
  }

  async getLatestGray(product: ProductType, platform: PlatformType, version: string) {
    this.logger.info(`[getLatestGray] [product]:${product} [platform]:${platform} [version]:${version}`);

    const versionProcess = await this.versionProcess.queryVersionProcessInfo(product, platform, version);

    this.logger.info(
      `[getLatestGray] [product]:${product} [platform]:${platform} [version]:${version} [versionProcess]: ${JSON.stringify(
        versionProcess,
      )}`,
    );

    if (versionProcess) {
      // 获取灰度里面最新的一个
      const grayStates = versionProcess.versionState.filter(e => isGrayStage(e.stage));

      this.logger.info(
        `[getLatestGray] [product]:${product} [platform]:${platform} [version]:${version} [graystat]: ${JSON.stringify(
          grayStates,
        )}`,
      );
      const latest_gray_index = grayStates.findIndex(e => e.progress === ProgressState.OnGoing);
      this.logger.info(
        `[getLatestGray] [product]:${product} [platform]:${platform} [version]:${version} [grayIndex]:${latest_gray_index}`,
      );
      if (latest_gray_index !== -1) {
        return {
          versionCode: grayStates[latest_gray_index].versionCode,
          gray_count: latest_gray_index + 1,
        };
      } else {
        const latest_gray_index2 = grayStates.findIndex(e => e.progress === ProgressState.NotStart);
        this.logger.info(
          `[latest_gray_index2] [getLatestGray] [product]:${product} [platform]:${platform} [version]:${version} [grayIndex]:${latest_gray_index2}`,
        );
        if (latest_gray_index2 !== -1) {
          return {
            versionCode: grayStates[latest_gray_index2].versionCode,
            gray_count: latest_gray_index2 + 1,
          };
        }
      }
    }

    return {
      versionCode: '',
      gray_count: -1,
    };
  }

  async getGrayCount(platform: PlatformType, product: ProductType, version: string) {
    const versionProcess = await this.versionProcess.queryVersionProcessInfo(product, platform, version);
    if (versionProcess) {
      return versionProcess.versionState.filter(e => isGrayStage(e.stage)).length;
    }

    return 0;
  }

  // 自定义产物接口更新灰度、正式包、小流量包的versionCode
  async updateVersionCodeFromBits(bits_app_id: string, stage_name: string, version_code: string, version_name: string) {
    try {
      const { platform, product } = versionUtils.appId2PlatAndProduct(Number(bits_app_id));

      this.logger.info(
        `[自定义产物接口更新包号] platform: ${platform} product: ${product} version: ${version_name} version_code: ${version_code} stage_name: ${stage_name}`,
      );

      const versionProcess = await this.versionProcess.queryVersionProcessInfo(product, platform, version_name);

      this.logger.info(`[自定义产物接口更新包号] [原versionProcess] ${JSON.stringify(versionProcess)} `);

      if (versionProcess) {
        // 接口来了之后先更新数据库（有可能新增阶段但还定时器还没更新到数据库）
        versionProcess.versionState = await this.updateVersionStageInfoFromBits(
          platform,
          product,
          version_name,
          versionProcess,
        );
        await this.versionProcess.updateVersionProcess(platform, product, version_name, versionProcess);

        let lastGrayTime = 0;
        for (const state of versionProcess.versionState) {
          if (state.name === stage_name) {
            state.versionCode = version_code;
            versionProcess.versionCode = version_code;
            await this.versionProcess.updateVersionProcess(platform, product, version_name, versionProcess);
            this.logger.info(`[自定义产物接口更新包号] [更新版本号到数据库中] ${stage_name} 版本号: ${version_code}`);
            if (isGrayStage(state.stage)) {
              // 更新meegoSnapShot
              const snapshot = await this.recordGraySnapshot(
                product,
                platform,
                version_name,
                version_code,
                lastGrayTime,
                state.executeInfo.actualStartTime,
              );
              this.logger.info(
                `[自定义产物接口更新包号] [更新 ${stage_name} 灰度meegoSnapsShot]: ${JSON.stringify(snapshot)}`,
              );
            }
            return;
          }
          if (isGrayStage(state.stage)) {
            lastGrayTime = state.executeInfo.actualStartTime;
          }
        }
      }
    } catch (e: any) {
      this.logger.info(
        `versionCode更新失败 报错信息${JSON.stringify({
          message: e.message,
          stack: e.stack,
        })}`,
      );
    }
  }

  getVersionStateInfo(versionProcess: VersionProcess, versionStage: VersionStage, grayCount?: number) {
    for (let i = 0; i < versionProcess.versionState.length; i++) {
      if (versionProcess.versionState[i].stage === versionStage) {
        if (isGrayStage(versionStage) && grayCount !== undefined) {
          i += grayCount - 1;
        }
        i = Math.min(i, versionProcess.versionState.length);
        return { index: i, versionState: versionProcess.versionState[i] };
      }
    }
  }

  async updateReleaseRates(
    version: string,
    platform: PlatformType,
    product: ProductType,
    rate: string,
    versionStage: VersionStage,
    grayCount?: number,
  ) {
    const versionProcess = await this.versionProcess.queryVersionProcessInfo(product, platform, version);

    if (versionProcess) {
      const versionStateInfo = this.getVersionStateInfo(versionProcess, versionStage, grayCount);

      if (versionStateInfo) {
        const { index, versionState } = versionStateInfo;
        if (versionState.release_rates === undefined) {
          versionState.release_rates = [];
        }
        versionState.release_rates.push(rate);

        versionProcess.versionState[index] = versionState;
        await this.versionProcess.updateVersionProcess(platform, product, version, versionProcess);

        return versionState;
      }
    }
  }

  async updateVersionProcessPlanTime(
    platform: PlatformType,
    product: ProductType,
    version: string,
    stageName: string,
    isEnd: boolean,
    newTime: number,
  ) {
    const versionProcess = await this.versionProcess.queryVersionProcessInfo(product, platform, version);

    const stageAccordingToBits = [
      VersionStage.crowdTest,
      VersionStage.normalGray,
      VersionStage.testFlight,
      VersionStage.smallFlow,
      VersionStage.submit,
      VersionStage.submitBeforeFirstGray,
      VersionStage.submitBeforeFulltestFlight,
    ];

    if (versionProcess) {
      let { versionState } = versionProcess;
      const index: number = versionState.findIndex(e => e.name === stageName);
      if (index !== -1) {
        if (isEnd) {
          versionState[index].executeInfo.planEndTime = newTime;
          if (stageAccordingToBits.indexOf(versionState[index].stage) === -1) {
            versionState[index].executeInfo.actualEndTime = newTime;
          }
        } else {
          versionState[index].executeInfo.planStartTime = newTime;
          if (stageAccordingToBits.indexOf(versionState[index].stage) === -1) {
            versionState[index].executeInfo.actualStartTime = newTime;
          }
        }
      }
      versionState = this.updateStateOncalendar(versionProcess.platform, versionState);
      versionProcess.versionState = versionState;
      this.versionProcess.updateVersionProcess(platform, product, version, versionProcess);
    }
  }

  /**
   * @param product
   * @param platform
   * @param version
   * @param versionCode
   * @param lastGrayTime 上一轮灰度发布的时间，没发过传0
   * @param currentGrayTime
   */
  async recordGraySnapshot(
    product: ProductType,
    platform: PlatformType,
    version: string,
    versionCode: string,
    lastGrayTime: number,
    currentGrayTime: number,
  ): Promise<MeegoSnapshot> {
    const meegoType = versionUtils.productType2MeegoType(product);
    const meegoVersionName = `${meegoType}-${platform}-${version}`;
    const meegoSnapshot = await this.meego.getGrayMeegoInfo(product, platform, version, versionCode, meegoVersionName);

    await this.versionProcess.addGaryMeegoInfo(product, platform, version, versionCode, meegoSnapshot);
    const mrSnapshot = await this.bits.getGrayMrInfo(
      product,
      platform,
      version,
      versionCode,
      lastGrayTime,
      currentGrayTime,
    );
    await this.versionProcess.addGrayMrInfo(product, platform, version, versionCode, mrSnapshot);
    return meegoSnapshot;
  }

  /**
   * 谨慎谨慎使用
   */
  async deleteVersionInfo(product: ProductType, platform: PlatformType, version: string): Promise<any> {
    return await this.versionProcess.deleteVersionInfo(product, platform, version);
  }

  async updateCurrentVersionCalendarInfo(
    platform: PlatformType,
    normalVersion: string,
    segmentName: string,
  ): Promise<any> {
    const appInfo = useBusinessInject<AppSetting>(AppSettingSymbol);
    const currentSeconds = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    const onWeek = 7 * 24 * 60 * 60;
    const bitsWorkspaceID = appInfo?.businessInfo.bits_workspace ?? 30;
    let calendar = await this.bits.versionProgressCalendar(
      normalVersion,
      platform,
      currentSeconds,
      segmentName,
      bitsWorkspaceID,
    );
    console.info(calendar);
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendar(
        normalVersion,
        platform,
        currentSeconds + onWeek,
        segmentName,
        bitsWorkspaceID,
      );
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendar(
        normalVersion,
        platform,
        currentSeconds - onWeek,
        segmentName,
        bitsWorkspaceID,
      );
    }
    if (!calendar) {
      calendar = await this.bits.versionProgressCalendar(
        normalVersion,
        platform,
        currentSeconds - onWeek * 2,
        segmentName,
        bitsWorkspaceID,
      );
    }
    if (calendar) {
      const productTye = appInfo?.productType ?? LVProductType.lv;
      const versionProcessInfo = await this.versionProcess.queryVersionProcessInfo(productTye, platform, normalVersion);
      if (versionProcessInfo) {
        versionProcessInfo.calendarOriginal = calendar;
        await this.versionProcess.updateVersionProcess(platform, productTye, normalVersion, versionProcessInfo);
      }
    }
    return calendar;
  }
}
