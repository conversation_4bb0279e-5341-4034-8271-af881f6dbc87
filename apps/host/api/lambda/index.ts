import { Api, Data, Get, Post, useInject, useReq } from '@edenx/runtime/bff';
import {
  BranchList,
  LoginUserInfo,
  NetworkCode,
  NetworkResult,
  transTaskListArg,
  User,
} from '@pa/shared/dist/src/core';
import { MrSearchRequest } from '@shared/bits/mrSearchRequest';
import { queryMrInfoSchema, queryTransTaskSchema, searchBranchSchema } from '@shared/schema';
import { z } from 'zod';
import { VersionSetting } from '@shared/appSetting';
import VersionProcessDao from '../service/dao/VersionProcessDao';
import BitsService from '../service/third/bits';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import StarlingService from '../service/third/starling';
import LarkService from '@pa/backend/dist/src/third/lark';
import UserService from '@pa/backend/dist/src/service/user';
import { GuluXSession } from '@gulux/gulux/session';
import { LoginInfo } from '@pa/shared/dist/src/oauthInfo';
import AirplaneConfigService from '../service/AirplaneConfigService';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import { BytedEnv } from '@gulux/gulux/byted-env';
import { teaCollectWithUser } from '../tea';
import { AppSetting } from '@pa/shared/dist/src/appSettings/appSettings';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import { AppSettingService } from '@pa/backend/dist/src/service/appSettingService';
import CryptoJS from 'crypto-js';
import McpTokenService from 'api/service/mcp/McpTokenService';

export const queryUserAndSetting = Api(Post('/app_setting_user_info'), async () => {
  const userInfo = await useInject(GuluXSession).get<LoginInfo>('user');
  const { email, name } = userInfo;
  const model = useInject(AppSettingService);
  const appList = await model.queryAppSettingList();
  const userSetting = (await model.queryAppUserSetting(email)) ?? {
    email,
    selectApp: appList[0].id,
    selectVersion: (await useInject(VersionProcessDao).queryLvVersionList())[0].version,
  };
  const selectInfo = appList.find(value => value.id === userSetting?.selectApp);
  let versionOwner;
  if (selectInfo) {
    versionOwner = (await useInject(AirplaneConfigService).queryConfigItem(
      '1775',
      VersionConfigKeys.versionOwnerUsers,
      selectInfo.platform,
    )) as User[];
  }
  const env = useInject(BytedEnv);
  return {
    loginUser: {
      avatar: userInfo.avatar_url,
      name,
      email: userInfo.email,
    } as LoginUserInfo,
    appList,
    userSetting,
    versionOwner,
    ppe: env.isPPE() ? env.getEnv() : '',
  };
});

export const queryVersionListV2 = Api(
  Post('/app_setting_version_list_v2'),
  Data(
    z.object({
      app_list: z.array(z.any()),
    }),
  ),
  async ({ data }): Promise<{ [key: number]: VersionSetting[] }> => {
    // console.log(data);
    const versionProcess = useInject(VersionProcessDao);
    const result: { [key: number]: VersionSetting[] } = {};
    const versionListLV = (await versionProcess.queryLvVersionList()) as any[];
    const versionListCC = (await versionProcess.queryCcVersionList()) as any[];
    for (let i = 0; i < data.app_list.length; i++) {
      const appSetting: AppSetting = data.app_list[i] as AppSetting;
      const { platform } = appSetting;
      const versionList = (await versionProcess.queryVersionList(appSetting.productType, platform)) as any[];
      const versionSettingList: VersionSetting[] = [];
      for (let j = 0; j < versionList.length; j++) {
        const versionQuery = versionList[j];
        const versionSetting = {
          normalVersion: versionQuery.version,
        } as VersionSetting;
        if (
          (appSetting.name === '剪映' || appSetting.name === 'CapCut') &&
          j < versionListLV.length &&
          j < versionListCC.length
        ) {
          versionSetting.lvVersion = versionListLV[j].version;
          versionSetting.ccVersion = versionListCC[j].version;
          versionSetting.pcVersion =
            versionListLV[j].pcInfo && versionListLV[j].pcInfo.lvpro_version
              ? versionListLV[j].pcInfo.lvpro_version
              : '';
        }
        versionSettingList.push(versionSetting);
        result[appSetting.id] = versionSettingList;
      }
    }
    return result;
  },
);

export const queryVersionList = Api(Post('/app_setting_version_list'), async () => {
  const versionProcess = useInject(VersionProcessDao);
  const versionListLV = await versionProcess.queryLvVersionList();
  const versionListCC = await versionProcess.queryCcVersionList();
  let result: VersionSetting[] = [];
  for (let i = 0; i < versionListLV.length && i < versionListCC.length; i++) {
    const vLv = versionListLV[i];
    const vCC = versionListCC[i];

    const versionSetting = {
      lvVersion: vLv.version,
      ccVersion: vCC.version,
      pcVersion: vLv.pcInfo && vLv.pcInfo.lvpro_version ? vLv.pcInfo.lvpro_version : '',
    } as VersionSetting;
    result = result.concat(versionSetting);
  }
  return result;
});

export const setUserDefaultApp = Api(
  Post('/set_user_default_app'),
  Data(
    z.object({
      select_app: z.number(),
      select_version: z.string(),
    }),
  ),
  async ({ data }) => {
    const email = await useInject(UserService).queryLoginEmail();
    await useInject(AppSettingService).updateAppUserSetting({
      email,
      selectApp: data.select_app,
      selectVersion: data.select_version,
    });
    return {};
  },
);

export const searchBranch = Api(Post('/search_branch'), Data(searchBranchSchema), async ({ data }) => {
  const logger = useInject(BytedLogger);
  const bits = useInject(BitsService);
  // 去除value的空格
  data.value = data.value.trim();
  const branchInfo = await bits.searchBranch(data.projectId, data.value);
  logger.info('searchBranch:');
  logger.info(`${JSON.stringify(branchInfo)}`);
  return {
    data: branchInfo,
  } as BranchList;
});

export const searchAllMrIdAndTitle = Api(Post('/search_MrIdAndTitle'), Data(queryMrInfoSchema), async ({ data }) => {
  const bits = useInject(BitsService);
  const mrSearchArgs: MrSearchRequest = {
    group_name: data.group_name,
    target_project_id: data.target_project_id,
    state: 'opened',
    keyword: data.keyword,
  };
  const info = await bits.getMrIdAndTitle(mrSearchArgs, data.target_project_id);
  return info;
});

export const searchAllTransTask = Api(Post('/search_TransTask'), Data(queryTransTaskSchema), async ({ data }) => {
  const taskSearchArg: transTaskListArg = {
    projectId: data.projectId,
    name: data.name,
  };

  const info = await useInject(StarlingService).getAllTaskList(taskSearchArg);
  return info;
});

export const searchUser = Api(Post('/search_user'), Data(z.object({ value: z.string() })), async ({ data }) => {
  const lark = useInject(LarkService);
  const accessToken = await useInject(UserService).queryAccessToken();
  const result = await lark.searchUser(data.value, accessToken);
  if (typeof result === 'number' && result !== NetworkCode.Success) {
    return {
      code: result,
      message: '查询用户失败',
      data: [],
    } as NetworkResult<User[]>;
  } else {
    return {
      code: NetworkCode.Success,
      message: 'ok',
      data: result,
    } as NetworkResult<User[]>;
  }
});

export const getUserInfoByOpenId = Api(
  Post('/get_user_info_by_open_id'),
  Data(z.object({ value: z.string() })),
  async ({ data }) => {
    const lark = useInject(LarkService);
    return await lark.getUserIdByOpenId(UserIdType.openId, data.value);
  },
);

export const batchGetUserInfoByOpenIds = Api(
  Post('/batch_get_user_info_by_open_id'),
  Data(z.object({ openIds: z.array(z.string()).optional() })),
  async ({ data }) => {
    const lark = useInject(LarkService);
    const openIds = data?.openIds;
    if (openIds) {
      return await Promise.all(openIds.map(openId => lark.getUserIdByOpenId(UserIdType.openId, openId)));
    }
    return [];
  },
);

export const getUserInfo = Api(Post('/get_user_info'), Data(z.object({ value: z.string() })), async ({ data }) => {
  const lark = useInject(LarkService);
  const result = await lark.getUserInfoByEmail(data.value);
  return result;
});

export const batchGetUserInfo = Api(
  Post('/batch_get_user_info'),
  Data(z.object({ emails: z.array(z.string()).optional() })),
  async ({ data }) => {
    const { emails } = data;
    if (!emails) {
      return undefined;
    }
    const lark = useInject(LarkService);
    return await Promise.all(emails.map(email => lark.getUserInfoByEmail(email)));
  },
);

export const getMrReviewerInfo = Api(
  Post('/get_mr_reviewer_info'),
  Data(z.object({ value: z.number() })),
  async ({ data }) => {
    const bits = useInject(BitsService);
    const result = await bits.getMrReviewerInfo(data.value);
    return result;
  },
);

export const searchLarkChat = Api(
  Post('/search_lark_chat'),
  Data(z.object({ keyWords: z.string() })),
  async ({ data }) => await useInject(LarkService).searchChatInfo(data.keyWords),
);

// export const queryTitanList = Api(
//   Get('/quality/cn/titan/alarm'),
//   async () => {},
// );

export const debug_return_host_cookies = Api(Get('/get_host_cookies'), async () => {
  const NAME = 'titan_passport_id';
  const req = useReq();
  let token = process.env[NAME];
  if (token !== undefined) {
    return { token: CryptoJS.AES.encrypt(token, 'airplane').toString() };
  } else {
    const hostCookies = req.headers.cookie?.split(';').reduce((cookies: Record<string, string>, cookie) => {
      const [key, value] = cookie.split('=').map(part => part.trim());
      if (key && value) {
        cookies[key] = value;
      }
      return cookies;
    }, {});
    token = hostCookies?.['titan_passport_id'];
    return { token: token !== undefined ? CryptoJS.AES.encrypt(token, 'airplane').toString() : null };
  }
});

export const getBusinessConfig = Api(Post('/business_config'), async () => {
  const businessConfigService = useInject(BusinessConfigService);
  const configList = await businessConfigService.getConfigList();
  return configList;
});

export const reportTeaEvent = Api(
  Post('/report_tea_event'),
  Data(z.object({ eventName: z.string(), params: z.any() })),
  async ({ data }) => {
    const { eventName, params } = data;
    const user = await useInject(UserService).queryLoginName();
    teaCollectWithUser(eventName, params, user);
    return {};
  },
);

// MCP Token 相关 API
export const getMcpToken = Api(
  Post('/mcp/token'),
  Data(
    z.object({
      email: z.string(),
    }),
  ),
  async ({ data }) => {
    try {
      const result = await useInject(McpTokenService).getTokenByEmail(data.email);
      return {
        code: NetworkCode.Success,
        message: 'ok',
        data: result,
      };
    } catch (error) {
      return {
        code: NetworkCode.Error,
        message: `获取 MCP Token 失败`,
      };
    }
  },
);

export const refreshMcpToken = Api(
  Post('/mcp/token/refresh'),
  Data(
    z.object({
      email: z.string(),
    }),
  ),
  async ({ data }) => {
    try {
      const result = await useInject(McpTokenService).generateToken(data.email);
      return {
        code: NetworkCode.Success,
        message: 'ok',
        data: result,
      };
    } catch (error) {
      return {
        code: NetworkCode.Error,
        message: `刷新 MCP Token 失败`,
      };
    }
  },
);
